"""
刃煞类神煞计算

包含羊刃、飞刃、血刃、金神等刃煞类神煞的计算函数。
"""


def yangren(day_gan: str, dizhi: str) -> int:
    """
    羊刃
    甲羊刃在卯, 乙羊刃在寅,
    丙戊羊刃在午, 丁己羊刃在巳,
    庚羊刃在酉, 辛羊刃在申,
    壬羊刃在子, 癸羊刃在亥.
    查法: 以日干为主, 四支见之者为是.
    
    Args:
        day_gan: 日干
        dizhi: 地支
        
    Returns:
        1表示是羊刃，0表示不是
    """
    conditions = {
        "甲": "卯",
        "乙": "寅",
        "丙": "午",
        "丁": "巳",
        "戊": "午",
        "己": "巳",
        "庚": "酉",
        "辛": "申",
        "壬": "子",
        "癸": "亥"
    }
    
    return 1 if conditions.get(day_gan) == dizhi else 0


def feiren(day_gan: str, dizhi: str) -> int:
    """
    飞刃
    甲羊刃在卯，如果地支见酉，即为飞刃；
    乙刃在寅，寅申相冲，即为飞刃；
    丙戊羊刃在午，地支见子，即为飞刃；
    丁己羊刃在未，地支见丑，即为飞刃；
    庚羊刃在酉，地支见卯，即为飞刃；
    辛羊刃在戌， 地支见辰，即为羊刃；
    壬羊刃在子，地支见午，即为飞刃；
    癸羊刃在丑，地支见未，即为飞刃。
    查法: 以日干查四地支，羊刃的六冲
    
    Args:
        day_gan: 日干
        dizhi: 地支
        
    Returns:
        1表示是飞刃，0表示不是
    """
    conditions = {
        "甲": "酉",
        "乙": "申",
        "丙": "子",
        "戊": "子",
        "丁": "丑",
        "己": "丑",
        "庚": "卯",
        "辛": "辰",
        "壬": "午",
        "癸": "未"
    }
    
    return 1 if conditions.get(day_gan) == dizhi else 0


def xueren(month_zhi: str, dizhi: str) -> int:
    """
    血刃
    寅月丑。卯月未。辰月寅。巳月申。午月卯。未月酉。
    申月辰。酉月戌。戌月巳。亥月亥。子月午。丑月子。
    查法：以月支查四柱干支
    
    Args:
        month_zhi: 月支
        dizhi: 地支
        
    Returns:
        1表示是血刃，0表示不是
    """
    conditions = {
        "子": "午",
        "丑": "子",
        "寅": "丑",
        "卯": "未",
        "辰": "寅",
        "巳": "申",
        "午": "卯",
        "未": "酉",
        "申": "辰",
        "酉": "戌",
        "戌": "巳",
        "亥": "亥"
    }
    
    return 1 if conditions.get(month_zhi) == dizhi else 0


def jinshen(tiangan: str, dizhi: str) -> int:
    """
    金神
    金神者，乙丑，己巳，癸酉三组干支。
    查法：日柱或时柱见者为是。
    
    Args:
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是金神，0表示不是
    """
    valid_combinations = {"乙丑", "己巳", "癸酉"}
    return 1 if tiangan + dizhi in valid_combinations else 0
