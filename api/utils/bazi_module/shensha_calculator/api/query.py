"""
主查询接口

实现主要的query<PERSON><PERSON><PERSON>ha函数，提供统一的八字神煞查询接口。
"""

from typing import List, Optional
from ..utils.validators import validate_input_complete
from ..utils.helpers import get_tiangan_yinyang
from ..core.noble_spirits import (
    tian<PERSON><PERSON><PERSON>n, taijiguiren, wenchang, guoying, jingyu, 
    fuxing, tianchu, dexiuguiren
)
from ..core.virtue_harmony import (
    tiandegu<PERSON>n, yuede, tiandehe, yuedehe, tianshe
)
from ..core.star_spirits import (
    yima, huagai, jiangxing, lushen, hongluan, tianxi,
    wugui, tianyi, xuetang, ciguan
)
from ..core.blade_spirits import (
    yangren, feiren, xueren, jinshen
)
from ..core.malefic_spirits import (
    kongwang, jiesha, zaisha, wangshen, gucheng, guashu,
    tianluo, diwang, shiedabai, taohua, guluan, y<PERSON><PERSON><PERSON><PERSON><PERSON>,
    si<PERSON><PERSON>, shang<PERSON>, di<PERSON><PERSON>, pima, tong<PERSON>, liu<PERSON>, ho<PERSON><PERSON>,
    k<PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON>, ji<PERSON><PERSON>, shiling, yuan<PERSON>
)


def query_shensha(ganzhi: str, bazi: List[str], is_man: bool, witch: int, 
                 nian_nayin: Optional[str] = None) -> List[str]:
    """
    根据干支和八字信息，查询神煞组合。
    
    Args:
        ganzhi: 要查询神煞的某柱干支，例如年柱为甲寅，则参数值为：甲寅
        bazi: 八字数组，数组元素从0-7，分别是年干、年支、月干、月支、日干、日支、时干、时支
        is_man: 性别，True为男，False为女
        witch: 查的是哪一柱，1，2，3，4分别代表年/月/日/时柱。其它分别是5大运，6流年，7流月，8流时。
        nian_nayin: 年柱纳音,查询学堂、词馆神煞时用。例如：海中金
        
    Returns:
        返回神煞数组
        
    Raises:
        ValueError: 当输入参数不正确时
    """
    # 输入验证
    is_valid, error_msg = validate_input_complete(ganzhi, bazi, is_man, witch, nian_nayin or "")
    if not is_valid:
        raise ValueError(error_msg)
    
    shensha_list = []
    
    # 提取八字信息
    nian_gan, nian_zhi = bazi[0], bazi[1]
    yue_gan, yue_zhi = bazi[2], bazi[3]
    ri_gan, ri_zhi = bazi[4], bazi[5]
    shi_gan, shi_zhi = bazi[6], bazi[7]
    
    # 提取当前干支
    gan = ganzhi[0]
    zhi = ganzhi[1]
    
    # 天乙贵人
    if tianyiguiren(ri_gan, zhi) or tianyiguiren(nian_gan, zhi):
        shensha_list.append("天乙")
    
    # 太极贵人
    if taijiguiren(ri_gan, zhi) or taijiguiren(nian_gan, zhi):
        shensha_list.append("太极")
    
    # 天德贵人
    if tiandeguiren(yue_zhi, gan) or tiandeguiren(yue_zhi, zhi):
        shensha_list.append("天德")
    
    # 月德贵人
    if yuede(yue_zhi, gan):
        shensha_list.append("月德")
    
    # 德秀贵人
    if dexiuguiren(yue_zhi, [nian_gan, yue_gan, ri_gan, shi_gan]):
        shensha_list.append("德秀")
    
    # 天德合
    if tiandehe(yue_zhi, gan) or tiandehe(yue_zhi, zhi):
        shensha_list.append("天德合")
    
    # 月德合
    if yuedehe(yue_zhi, gan):
        shensha_list.append("月德合")
    
    # 福星
    if fuxing(nian_gan, zhi) or fuxing(ri_gan, zhi):
        shensha_list.append("福星")
    
    # 文昌贵人
    if wenchang(ri_gan, zhi) or wenchang(nian_gan, zhi):
        shensha_list.append("文昌")
    
    # 学堂（不查日柱）
    if witch != 3 and nian_nayin and xuetang(nian_nayin, gan, zhi):
        shensha_list.append("学堂")
    
    # 词馆（不查日柱）
    if witch != 3 and nian_nayin and ciguan(nian_nayin, gan, zhi):
        shensha_list.append("词馆")
    
    # 魁罡（仅查本命局日柱）
    if witch == 3 and kuigang(ri_gan, ri_zhi):
        shensha_list.append("魁罡")
    
    # 国印贵人
    if guoying(ri_gan, zhi) or guoying(nian_gan, zhi):
        shensha_list.append("国印")
    
    # 驿马
    if (witch != 3 and yima(ri_zhi, zhi)) or (witch != 1 and yima(nian_zhi, zhi)):
        shensha_list.append("驿马")
    
    # 华盖
    if (witch != 3 and huagai(ri_zhi, zhi)) or (witch != 1 and huagai(nian_zhi, zhi)):
        shensha_list.append("华盖")
    
    # 将星
    if (witch != 3 and jiangxing(ri_zhi, zhi)) or (witch != 1 and jiangxing(nian_zhi, zhi)):
        shensha_list.append("将星")
    
    # 金舆
    if jingyu(ri_gan, zhi) or jingyu(nian_gan, zhi):
        shensha_list.append("金舆")
    
    # 金神
    if (witch == 3 and jinshen(ri_gan, ri_zhi)) or (witch == 4 and jinshen(shi_gan, shi_zhi)):
        shensha_list.append("金神")
    
    # 五鬼
    if witch != 2 and wugui(yue_zhi, zhi):
        shensha_list.append("五鬼")
    
    # 天医
    if witch != 2 and tianyi(yue_zhi, zhi):
        shensha_list.append("天医")
    
    # 禄神
    if lushen(ri_gan, zhi):
        shensha_list.append("禄神")
    
    # 天赦
    if tianshe(yue_zhi, ri_gan, ri_zhi):
        shensha_list.append("天赦")
    
    # 红鸾
    if witch != 1 and hongluan(nian_zhi, zhi):
        shensha_list.append("红鸾")
    
    # 天喜
    if witch != 1 and tianxi(nian_zhi, zhi):
        shensha_list.append("天喜")
    
    # 流霞
    if liuxia(ri_gan, zhi):
        shensha_list.append("流霞")
    
    # 红艳
    if hongyan(ri_gan, zhi):
        shensha_list.append("红艳")
    
    # 天罗
    if (witch != 3 and tianluo(ri_zhi, zhi)) or (witch != 1 and tianluo(nian_zhi, zhi)):
        shensha_list.append("天罗")
    
    # 地网
    if (witch != 3 and diwang(ri_zhi, zhi)) or (witch != 1 and diwang(nian_zhi, zhi)):
        shensha_list.append("地网")
    
    # 羊刃
    if yangren(ri_gan, zhi):
        shensha_list.append("羊刃")
    
    # 飞刃
    if feiren(ri_gan, zhi):
        shensha_list.append("飞刃")
    
    # 血刃
    if xueren(yue_zhi, zhi):
        shensha_list.append("血刃")
    
    # 八专（仅查日柱）
    if witch == 3 and bazhuan(ri_gan, ri_zhi):
        shensha_list.append("八专")
    
    # 九丑（仅查日柱）
    if witch == 3 and jiuchou(ri_gan, ri_zhi):
        shensha_list.append("九丑")
    
    # 劫煞
    if jiesha(ri_zhi, zhi) or jiesha(nian_zhi, zhi):
        shensha_list.append("劫煞")
    
    # 灾煞
    if zaisha(nian_zhi, zhi):
        shensha_list.append("灾煞")
    
    # 元辰
    if witch != 1 and yuancheng(nian_zhi, zhi, is_man, get_tiangan_yinyang(nian_gan)):
        shensha_list.append("元辰")
    
    # 空亡
    if (witch != 3 and kongwang(ri_gan + ri_zhi, zhi)) or (witch != 1 and kongwang(nian_gan + nian_zhi, zhi)):
        shensha_list.append("空亡")
    
    # 童子
    if ((witch == 3 and tongzi(yue_zhi, nian_nayin or "", ri_zhi)) or 
        (witch == 4 and tongzi(yue_zhi, nian_nayin or "", shi_zhi))):
        shensha_list.append("童子")
    
    # 天厨
    if tianchu(nian_gan, ri_gan, zhi):
        shensha_list.append("天厨")
    
    # 孤辰
    if witch != 1 and gucheng(nian_zhi, zhi):
        shensha_list.append("孤辰")
    
    # 寡宿
    if witch != 1 and guashu(nian_zhi, zhi):
        shensha_list.append("寡宿")
    
    # 亡神
    if (witch != 3 and wangshen(ri_zhi, zhi)) or (witch != 1 and wangshen(nian_zhi, zhi)):
        shensha_list.append("亡神")
    
    # 十恶大败（仅查日柱）
    if witch == 3 and shiedabai(ri_gan, ri_zhi):
        shensha_list.append("十恶大败")
    
    # 桃花
    if taohua(ri_zhi, zhi) or taohua(nian_zhi, zhi):
        shensha_list.append("桃花")
    
    # 孤鸾（仅查日柱）
    if witch == 3 and guluan(ri_gan, ri_zhi):
        shensha_list.append("孤鸾")
    
    # 阴差阳错（仅查日柱）
    if witch == 3 and yingyangchacuo(ri_gan, ri_zhi):
        shensha_list.append("阴差阳错")
    
    # 四废（仅查日柱）
    if witch == 3 and sifei(yue_zhi, ri_gan, ri_zhi):
        shensha_list.append("四废")
    
    # 丧门
    if witch != 1 and shangmen(nian_zhi, zhi):
        shensha_list.append("丧门")
    
    # 吊客
    if witch != 1 and diaoke(nian_zhi, zhi):
        shensha_list.append("吊客")
    
    # 披麻
    if witch != 1 and pima(nian_zhi, zhi):
        shensha_list.append("披麻")
    
    # 十灵（仅查日柱）
    if witch == 3 and shiling(ri_gan, ri_zhi):
        shensha_list.append("十灵")
    
    return shensha_list
