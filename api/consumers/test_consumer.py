# api/consumers/test_consumer.py
"""
测试WebSocket消费者
用于WebSocket连接测试和调试
"""

import json
import asyncio
import traceback
import logging
from .base import BaseWebSocketConsumer

logger = logging.getLogger(__name__)


class TestStreamConsumer(BaseWebSocketConsumer):
    """
    测试流式WebSocket消费者
    用于验证WebSocket连接和基本功能
    """
    
    async def connect(self):
        try:
            # 详细记录连接信息，帮助调试
            logger.info("开始WebSocket连接")
            logger.info(f"连接路径: {self.scope['path']}")
            
            if 'headers' in self.scope:
                auth_header = dict(self.scope['headers']).get(b'authorization', b'').decode()
                logger.info(f"认证信息: {auth_header}")
            
            await self.accept()
            logger.info("WebSocket连接已接受")
            
            # 发送测试消息
            for i in range(9):
                message = f"这是第 {i+1} 条测试消息"
                await self.send(text_data=json.dumps({
                    "message": message
                }))
                await asyncio.sleep(1)
                logger.info(f"已发送消息 {i+1}")
            
        except Exception as e:
            logger.error(f"WebSocket连接错误: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    async def disconnect(self, close_code):
        logger.info(f"WebSocket连接断开，关闭代码: {close_code}")

    async def receive(self, text_data):
        try:
            logger.info(f"收到消息: {text_data}")
            await self.send(text_data=json.dumps({
                "message": "消息已收到"
            }))
        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
            logger.error(traceback.format_exc()) 