"""
输入验证函数

包含各种输入验证函数，确保输入数据的正确性。
"""

from typing import List
from ..data.constants import TIANGAN, DIZHI


def validate_ganzhi(ganzhi: str) -> bool:
    """
    验证干支格式是否正确
    
    Args:
        ganzhi: 干支字符串，如"甲子"
        
    Returns:
        True表示格式正确，False表示格式错误
    """
    if not isinstance(ganzhi, str) or len(ganzhi) != 2:
        return False
    
    gan, zhi = ganzhi[0], ganzhi[1]
    return gan in TIANGAN and zhi in DIZHI


def validate_bazi(bazi: List[str]) -> bool:
    """
    验证八字格式是否正确
    
    Args:
        bazi: 八字列表，包含8个元素[年干,年支,月干,月支,日干,日支,时干,时支]
        
    Returns:
        True表示格式正确，False表示格式错误
    """
    if not isinstance(bazi, list) or len(bazi) != 8:
        return False
    
    # 检查奇数位置是否为天干
    for i in range(0, 8, 2):
        if bazi[i] not in TIANGAN:
            return False
    
    # 检查偶数位置是否为地支
    for i in range(1, 8, 2):
        if bazi[i] not in DIZHI:
            return False
    
    return True


def validate_witch(witch: int) -> bool:
    """
    验证柱位参数是否正确
    
    Args:
        witch: 柱位参数，1-4表示年月日时柱，5-8表示大运流年等
        
    Returns:
        True表示参数正确，False表示参数错误
    """
    return isinstance(witch, int) and 1 <= witch <= 8


def validate_gender(is_man: bool) -> bool:
    """
    验证性别参数是否正确
    
    Args:
        is_man: 性别，True表示男性，False表示女性
        
    Returns:
        True表示参数正确，False表示参数错误
    """
    return isinstance(is_man, bool)


def validate_nayin(nayin: str) -> bool:
    """
    验证纳音格式是否正确
    
    Args:
        nayin: 纳音字符串，如"海中金"
        
    Returns:
        True表示格式正确，False表示格式错误
    """
    if not isinstance(nayin, str):
        return False
    
    # 纳音通常以五行结尾
    valid_endings = ["金", "木", "水", "火", "土"]
    return any(nayin.endswith(ending) for ending in valid_endings)


def validate_input_complete(ganzhi: str, bazi: List[str], is_man: bool, witch: int, nayin: str) -> tuple:
    """
    完整验证所有输入参数
    
    Args:
        ganzhi: 干支
        bazi: 八字
        is_man: 性别
        witch: 柱位
        nayin: 纳音
        
    Returns:
        (is_valid, error_message) 元组
    """
    if not validate_ganzhi(ganzhi):
        return False, f"干支格式错误: {ganzhi}"
    
    if not validate_bazi(bazi):
        return False, f"八字格式错误: {bazi}"
    
    if not validate_gender(is_man):
        return False, f"性别参数错误: {is_man}"
    
    if not validate_witch(witch):
        return False, f"柱位参数错误: {witch}"
    
    if nayin and not validate_nayin(nayin):
        return False, f"纳音格式错误: {nayin}"
    
    return True, "验证通过"
