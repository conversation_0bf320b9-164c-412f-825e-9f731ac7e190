#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试安全检查工具
在开始性能测试前检查配置安全性，防止高风险操作
"""

from performance_test_api_config import (
    PERFORMANCE_TEST_APIS,
    LOW_RISK_APIS,
    MEDIUM_RISK_APIS,
    HIGH_RISK_APIS_DICT,
    RISK_CONFIGS,
    get_api_safe_config
)

def check_api_safety():
    """检查API配置的安全性"""
    print("🔍 性能测试安全检查")
    print("=" * 60)
    
    # 统计风险等级
    risk_stats = {"低": 0, "中": 0, "高": 0}
    
    print("📊 API风险等级分析:")
    print("-" * 40)
    
    for api_name, config in PERFORMANCE_TEST_APIS.items():
        risk_level = config.get('risk_level', '低')
        risk_stats[risk_level] += 1
        
        # 显示高风险和中风险API
        if risk_level in ['高', '中']:
            max_concurrent = config.get('max_concurrent', 'N/A')
            max_requests = config.get('max_requests', 'N/A')
            print(f"  ⚠️  {api_name}")
            print(f"      风险等级: {risk_level}")
            print(f"      最大并发: {max_concurrent}")
            print(f"      最大请求: {max_requests}")
            print(f"      描述: {config.get('description', 'N/A')}")
            print()
    
    print(f"📈 风险统计:")
    print(f"  🟢 低风险API: {risk_stats['低']} 个")
    print(f"  🟡 中风险API: {risk_stats['中']} 个") 
    print(f"  🔴 高风险API: {risk_stats['高']} 个")
    
    return risk_stats

def check_removed_dangerous_apis():
    """检查是否已移除危险API"""
    print("\n🚫 危险API移除检查:")
    print("-" * 40)
    
    # 检查是否包含已知的危险API
    dangerous_patterns = [
        "deepseek", "ai", "chat", "wechat-login", "phone-login",
        "sms", "payment", "pay", "analysis_with"
    ]
    
    found_dangerous = []
    
    for api_name, config in PERFORMANCE_TEST_APIS.items():
        url = config.get('url', '').lower()
        description = config.get('description', '').lower()
        
        for pattern in dangerous_patterns:
            if pattern in url or pattern in description or pattern in api_name.lower():
                found_dangerous.append({
                    'api_name': api_name,
                    'pattern': pattern,
                    'url': config.get('url'),
                    'description': config.get('description')
                })
    
    if found_dangerous:
        print("  ❌ 发现可能的危险API:")
        for item in found_dangerous:
            print(f"    - {item['api_name']}: 匹配模式 '{item['pattern']}'")
            print(f"      URL: {item['url']}")
            print(f"      描述: {item['description']}")
        return False
    else:
        print("  ✅ 未发现已知的危险API模式")
        return True

def show_safe_test_recommendations():
    """显示安全测试建议"""
    print("\n💡 安全测试建议:")
    print("-" * 40)
    
    print("🎯 推荐测试顺序:")
    print("  1. 先测试低风险API (验证基本功能)")
    print("  2. 再测试中风险API (控制并发数)")
    print("  3. 避免测试高风险API (除非必要)")
    
    print(f"\n📊 各风险等级配置:")
    for risk_level, config in RISK_CONFIGS.items():
        print(f"  {risk_level}风险:")
        print(f"    并发数: {config['concurrent_users']}")
        print(f"    最大请求: {config['max_requests']}")
        print(f"    说明: {config['description']}")
    
    print(f"\n🔧 推荐测试场景:")
    print(f"  - 安全基础测试: {len(LOW_RISK_APIS)} 个低风险API")
    print(f"  - 核心业务测试: 包含重要业务功能")
    print(f"  - 完整安全测试: 所有API但使用安全配置")

def check_specific_api_safety(api_name):
    """检查特定API的安全配置"""
    if api_name not in PERFORMANCE_TEST_APIS:
        print(f"❌ API '{api_name}' 不存在")
        return False
    
    config = PERFORMANCE_TEST_APIS[api_name]
    safe_config = get_api_safe_config(api_name)
    
    print(f"\n🔍 API安全检查: {api_name}")
    print("-" * 50)
    print(f"URL: {config.get('url')}")
    print(f"方法: {config.get('method')}")
    print(f"描述: {config.get('description')}")
    print(f"风险等级: {safe_config['risk_level']}")
    print(f"建议并发数: {safe_config['concurrent_users']}")
    print(f"最大请求数: {safe_config['max_requests']}")
    
    # 安全建议
    risk_level = safe_config['risk_level']
    if risk_level == '高':
        print("⚠️  高风险API，建议:")
        print("   - 仅在必要时测试")
        print("   - 严格控制并发数 ≤ 3")
        print("   - 监控外部服务调用")
        print("   - 准备紧急停止机制")
    elif risk_level == '中':
        print("🟡 中风险API，建议:")
        print("   - 控制并发数 ≤ 15")
        print("   - 监控系统资源使用")
        print("   - 注意数据库连接池")
    else:
        print("✅ 低风险API，可以正常测试")
    
    return True

def interactive_safety_check():
    """交互式安全检查"""
    print("🛡️  交互式安全检查")
    print("=" * 60)
    
    while True:
        print("\n选择检查项目:")
        print("1. 全面安全检查")
        print("2. 检查特定API")
        print("3. 显示测试建议")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            risk_stats = check_api_safety()
            is_safe = check_removed_dangerous_apis()
            
            print(f"\n📋 检查结果:")
            if risk_stats['高'] == 0 and is_safe:
                print("✅ 配置安全，可以开始测试")
            else:
                print("⚠️  发现安全风险，请谨慎测试")
                
        elif choice == "2":
            api_name = input("请输入API名称: ").strip()
            check_specific_api_safety(api_name)
            
        elif choice == "3":
            show_safe_test_recommendations()
            
        elif choice == "4":
            print("👋 安全检查完成")
            break
        else:
            print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🔒 性能测试安全检查工具")
    print("=" * 60)
    print("用途: 在性能测试前检查API配置安全性")
    print("目标: 防止高成本、高风险的API被误测试")
    
    # 快速安全检查
    risk_stats = check_api_safety()
    is_safe = check_removed_dangerous_apis()
    show_safe_test_recommendations()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 安全检查总结:")
    
    if risk_stats['高'] > 0:
        print(f"⚠️  发现 {risk_stats['高']} 个高风险API，请谨慎测试")
    
    if risk_stats['中'] > 0:
        print(f"🟡 发现 {risk_stats['中']} 个中风险API，建议控制并发")
    
    print(f"✅ {risk_stats['低']} 个低风险API可以正常测试")
    
    if is_safe and risk_stats['高'] == 0:
        print("\n🎉 总体评估: 配置安全，可以开始性能测试")
        print("💡 建议: 从'安全基础测试'场景开始")
    else:
        print("\n⚠️  总体评估: 存在安全风险，请仔细检查配置")
    
    # 询问是否需要交互式检查
    if input("\n是否需要详细的交互式检查? (y/N): ").strip().lower() in ['y', 'yes']:
        interactive_safety_check()

if __name__ == "__main__":
    main()
