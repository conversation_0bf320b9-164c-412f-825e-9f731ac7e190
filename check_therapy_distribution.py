#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查疗法分布脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/home/<USER>/riyue-llm/myproject/demo_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

from api.models import PrognosisTherapyCategory, PrognosisTherapyClassification, PrognosisUserTherapy
from django.db.models import Q

def main():
    print("=== 疗法分布分析 ===")
    
    # 1. 查看包含"心"字的系统疗法分布
    print("\n1. 包含'心'字的系统疗法:")
    heart_therapies = PrognosisTherapyCategory.objects.filter(
        Q(name__icontains='心') | Q(description__icontains='心'),
        is_active=True
    ).select_related('classification')
    
    print(f"总数: {heart_therapies.count()}")
    for therapy in heart_therapies:
        print(f"- {therapy.name} (分类: {therapy.classification.name if therapy.classification else '无分类'}, 代码: {therapy.classification.code if therapy.classification else '无代码'})")
    
    # 2. 查看包含"心"字的用户疗法分布
    print("\n2. 包含'心'字的用户疗法:")
    user_heart_therapies = PrognosisUserTherapy.objects.filter(
        Q(name__icontains='心') | Q(description__icontains='心') | Q(related_symptoms__icontains='心'),
        is_public=True
    )
    
    print(f"总数: {user_heart_therapies.count()}")
    for therapy in user_heart_therapies[:10]:  # 只显示前10个
        print(f"- {therapy.name} (类别: {therapy.category})")
    
    # 3. 查看所有系统疗法分类的分布
    print("\n3. 所有系统疗法分类分布:")
    classifications = PrognosisTherapyClassification.objects.all()
    for cls in classifications:
        count = PrognosisTherapyCategory.objects.filter(classification=cls, is_active=True).count()
        print(f"- {cls.name} ({cls.code}): {count} 个疗法")
    
    # 4. 查看用户疗法类别分布
    print("\n4. 用户疗法类别分布:")
    from django.db.models import Count
    user_categories = PrognosisUserTherapy.objects.filter(is_public=True).values('category').annotate(count=Count('id')).order_by('-count')
    for cat in user_categories:
        print(f"- {cat['category']}: {cat['count']} 个疗法")
    
    # 5. 检查允许的疗法类别
    print("\n5. 允许的疗法类别:")
    allowed_codes = [
        'massage',           # 按摩疗法
        'moxibustion',       # 艾灸疗法
        'food_therapy',      # 食疗疗法
        'exercise',          # 中医功法疗法
        'tea_therapy',       # 茶疗疗法
        'music_therapies',   # 音乐疗法
        'xiang_therapies',   # 香薰疗法
        'xiangshu_therapies', # 象数疗法
        'ear_acup_therapies' # 中医耳穴疗法
    ]
    
    for code in allowed_codes:
        # 系统疗法
        sys_count = PrognosisTherapyCategory.objects.filter(
            classification__code=code, is_active=True
        ).count()
        # 用户疗法
        user_count = PrognosisUserTherapy.objects.filter(
            category=code, is_public=True
        ).count()
        print(f"- {code}: 系统疗法 {sys_count} 个, 用户疗法 {user_count} 个")
    
    # 6. 检查"心"相关的具体匹配情况
    print("\n6. '心'相关疗法的详细匹配情况:")
    
    # 系统疗法中包含"心"的
    for therapy in heart_therapies:
        match_reasons = []
        if '心' in therapy.name:
            match_reasons.append(f"名称匹配: {therapy.name}")
        if therapy.description and '心' in therapy.description:
            match_reasons.append(f"描述匹配: {therapy.description[:50]}...")
        
        print(f"系统疗法: {therapy.name}")
        print(f"  分类: {therapy.classification.name if therapy.classification else '无分类'} ({therapy.classification.code if therapy.classification else '无代码'})")
        print(f"  匹配原因: {'; '.join(match_reasons)}")
        print()

if __name__ == "__main__":
    main()
