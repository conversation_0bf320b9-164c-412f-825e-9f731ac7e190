# api/management/commands/monitor_websockets.py
"""
WebSocket连接监控命令
用于监控WebSocket连接状态和性能
"""

from django.core.management.base import BaseCommand
import redis
import json
import time
from django.conf import settings


class Command(BaseCommand):
    help = '监控WebSocket连接状态'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=10,
            help='监控间隔（秒）'
        )
        parser.add_argument(
            '--duration',
            type=int,
            default=60,
            help='监控持续时间（秒）'
        )

    def handle(self, *args, **options):
        interval = options['interval']
        duration = options['duration']
        
        self.stdout.write(
            self.style.SUCCESS(f'🔍 开始监控WebSocket连接状态')
        )
        self.stdout.write(f'监控间隔: {interval}秒, 持续时间: {duration}秒')
        
        # 连接Redis
        try:
            redis_config = settings.CHANNEL_LAYERS['default']['CONFIG']['hosts'][0]
            r = redis.Redis(
                host=redis_config['host'],
                port=redis_config['port'],
                password=redis_config['password'],
                decode_responses=True
            )
            
            # 测试连接
            r.ping()
            self.stdout.write(self.style.SUCCESS('✅ Redis连接成功'))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Redis连接失败: {e}')
            )
            return
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            try:
                # 获取Redis信息
                info = r.info()
                
                # 获取连接数
                connected_clients = info.get('connected_clients', 0)
                used_memory = info.get('used_memory_human', 'N/A')
                
                # 获取Channels相关的键
                channel_keys = r.keys('asgi:*')
                group_keys = r.keys('asgi:group:*')
                
                # 显示统计信息
                self.stdout.write(f'\n📊 {time.strftime("%H:%M:%S")} 连接状态:')
                self.stdout.write(f'  Redis连接数: {connected_clients}')
                self.stdout.write(f'  内存使用: {used_memory}')
                self.stdout.write(f'  Channel键数量: {len(channel_keys)}')
                self.stdout.write(f'  Group键数量: {len(group_keys)}')
                
                # 检查是否有异常的长时间连接
                if len(channel_keys) > 100:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Channel键数量过多: {len(channel_keys)}')
                    )
                
                if connected_clients > 50:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Redis连接数过多: {connected_clients}')
                    )
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.stdout.write('\n🛑 监控被用户中断')
                break
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ 监控出错: {e}')
                )
                time.sleep(interval)
        
        self.stdout.write(
            self.style.SUCCESS('\n✅ 监控完成')
        )
