# 🎯 性能测试URL清单 - 待审核

## 📋 测试说明
- **测试环境**: http://127.0.0.1:8000
- **认证方式**: 使用test_token_user_2.txt中的token
- **测试工具**: test_concurrent_performance.py
- **结果保存**: 自动保存到performance_test_results目录

---

## 🟢 第一批：核心业务API（推荐优先测试）

### 1. 基础页面API
```
✅ ${BASE_URL}/api/HomePage/ - GET - 首页静态内容（有2小时缓存）
```

### 2. 会员系统核心API
```
✅ ${BASE_URL}/api/bank/EmptyRequestView/ - POST - 空请求保持会话
✅ ${BASE_URL}/api/async-bank/checkmembership/ - POST - 检查会员状态
✅ ${BASE_URL}/api/async-bank/abstract-goal/ - GET - 获取抽象目标
✅ ${BASE_URL}/api/async-bank/user-goal/ - GET - 用户目标
✅ ${BASE_URL}/api/async-bank/activity-list/ - GET - 活动列表
✅ ${BASE_URL}/api/async-bank/user-stats/ - GET - 用户统计
```

### 3. 问卷系统API
```
✅ ${BASE_URL}/api/questionnaire/v1/questionnaire/ - GET - 获取问卷
✅ ${BASE_URL}/api/questionnaire/v1/user_info/ - GET - 获取用户信息
✅ ${BASE_URL}/api/questionnaire/calculation_histories/ - GET - 计算历史
```

### 4. 预后系统API（重点测试缓存效果）
```
✅ ${BASE_URL}/api/routertest1/prognosis/popular-keywords - GET - 热门关键词
✅ ${BASE_URL}/api/routertest1/prognosis/search-suggestions - GET - 搜索建议
✅ ${BASE_URL}/api/routertest1/prognosis/therapy-classifications - GET - 疗法分类（有缓存）
✅ ${BASE_URL}/api/routertest1/prognosis/therapies - GET - 所有疗法（有缓存）
```

---

## 🟡 第二批：扩展功能API（中等优先级）

### 5. 论坛系统API
```
✅ ${BASE_URL}/api/doubao_aichat/chat/posts - GET - 获取论坛帖子
✅ ${BASE_URL}/api/doubao_aichat/chat/sections - GET - 获取论坛版块
✅ ${BASE_URL}/api/doubao_aichat/chat/user_posts - GET - 用户帖子
```

### 6. 医案系统API
```
✅ ${BASE_URL}/api/doubao_aichat/chat/medical_case_exp_ranking - GET - 经验值排行榜
✅ ${BASE_URL}/api/doubao_aichat/chat/medical_case_total_score_ranking - GET - 总得分排行榜
✅ ${BASE_URL}/api/doubao_aichat/chat/medical_case_user_stats - GET - 用户统计信息
✅ ${BASE_URL}/api/doubao_aichat/chat/get_health_records - GET - 获取健康记录
```

### 7. 症状管理API
```
✅ ${BASE_URL}/api/tcmchat/get_all_symptoms/ - GET - 获取所有症状
✅ ${BASE_URL}/api/tcmchat/get_custom_symptoms/ - GET - 获取自定义症状
```

### 8. 问答系统API
```
✅ ${BASE_URL}/api/async-bank/daily-tcm-questions/ - GET - 每日中医问题
✅ ${BASE_URL}/api/async-bank/quiz-ranking/ - GET - 问答排行
✅ ${BASE_URL}/api/async-bank/user-quiz-history/ - GET - 用户问答历史
```

---

## 🟡 第三批：谨慎测试API（需要特殊配置）

### 9. 认证系统API（仅Token刷新）
```
⚠️ ${BASE_URL}/api/auth/refresh-token - POST - 刷新访问令牌（低并发测试）
```

### 10. 邀请系统API
```
⚠️ ${BASE_URL}/api/invite_url/invite_api/my-code - GET - 获取我的邀请码
⚠️ ${BASE_URL}/api/invite_url/invite_api/records - GET - 邀请记录
```

### 11. 八字相关API（可能涉及计算成本）
```
⚠️ ${BASE_URL}/api/routertest1/bazi/basic - POST - 获取八字基本信息
```

---

## 🔴 明确禁止测试的API

### AI聊天功能（成本极高）
```
❌ ${BASE_URL}/api/doubao_aichat/chat/chat - AI聊天对话
❌ ${BASE_URL}/api/doubao_aichat/chat/chat_YiAnStudy - 医案学习AI对话
❌ ${BASE_URL}/api/tcmNLP/aichat/ - 传统AI聊天接口
❌ ${BASE_URL}/api/tcmNLP/ai_analysis/ - 中医健康分析
❌ ${BASE_URL}/api/tcmNLP/ai_analysis_symptoms/ - 症状相关健康分析
```

### 支付系统（风险极高）
```
❌ ${BASE_URL}/api/wechatV3_pay_app/ - 微信支付
❌ ${BASE_URL}/api/tcmNLP/AlipayView/ - 支付宝支付
❌ ${BASE_URL}/api/tcmNLP/CheckPaymentStatusView/ - 检查支付状态
```

### WebSocket连接（成本高）
```
❌ wss://riyuetcm.com/api/ws/bazi_advice/ - 八字建议WebSocket
❌ wss://riyuetcm.com/api/ws/deepseek_chat_vip/ - VIP深度分析WebSocket
❌ wss://riyuetcm.com/api/ws/liushi_health_reminder/ - 健康提醒WebSocket
```

### 废弃API
```
❌ ${BASE_URL}/api/tcm_books/v2/books/ - 书籍管理（已废弃）
```

---

## 📊 测试配置建议

### 并发配置
- **第一批（核心API）**: 1, 5, 10, 20, 40, 80 并发
- **第二批（扩展API）**: 1, 5, 10, 20 并发
- **第三批（谨慎API）**: 1, 3, 5 并发

### 请求数量
- **缓存API**: 200 请求（验证缓存效果）
- **普通API**: 100 请求
- **谨慎API**: 50 请求

---

## 🎯 预期性能目标

基于你之前提到的"首页和几个预后API，QPS200"：

### 高性能API（有缓存）
- **首页**: QPS > 200
- **疗法分类**: QPS > 150
- **热门关键词**: QPS > 100

### 中等性能API
- **用户相关查询**: QPS > 50
- **排行榜**: QPS > 30
- **统计信息**: QPS > 20

### 响应时间目标
- **缓存API**: < 50ms
- **数据库查询**: < 100ms
- **复杂查询**: < 200ms

---

## ✅ 请审核确认

请检查以上URL清单，确认：
1. ✅ 第一批核心API是否可以开始测试？
2. ✅ 第二批扩展API是否需要调整？
3. ✅ 第三批谨慎API是否需要特殊处理？
4. ✅ 禁止测试的API分类是否正确？

确认后我将开始执行性能测试！
