#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第五批API清单 - 修复版
去除AI接口和高风险同步异步混用API
专注于测试连接池修复效果
"""

import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# 测试配置
BASE_URL = "http://127.0.0.1:8000"

# 读取测试token
try:
    with open("test_token_user_2.txt", "r") as f:
        TOKEN = f.read().strip()
except FileNotFoundError:
    TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VhDgOlaTZFjxZaGUdPhe7r658Vqfq48TOc6jTH-6zSA"

headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

# 第五批API清单 - 安全版本（去除AI接口和高风险API）
FIFTH_BATCH_APIS = {
    # 🔹 会员系统 (1个API) - 去除高风险API
    "检查支付状态": {
        "url": f"{BASE_URL}/api/async-bank/checkmembership/",  # 使用异步版本
        "method": "POST",
        "data": {},
        "category": "会员系统",
        "risk_level": "低",
        "expected_qps": 80,
        "description": "检查会员状态（异步版本）"
    },
    
    # 🔹 论坛系统 (2个API) - 已验证为异步实现
    "获取特定帖子": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/posts/1",
        "method": "GET",
        "category": "论坛系统",
        "risk_level": "低",
        "expected_qps": 100,
        "description": "获取特定帖子详情"
    },
    "帖子点赞": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/posts/1/like",
        "method": "POST",
        "data": {},
        "category": "论坛系统", 
        "risk_level": "中",
        "expected_qps": 60,
        "description": "帖子点赞功能"
    },
    
    # 🔹 医案系统 (3个API) - 已验证为异步实现
    "用户排名": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_user_ranking/exp",
        "method": "GET",
        "category": "医案系统",
        "risk_level": "低", 
        "expected_qps": 80,
        "description": "用户在医案排行榜中的排名"
    },
    "医案评分": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/chat_YiAnPingfen/case123",
        "method": "POST",
        "data": {
            "time_spent": 300,
            "user_answer": "这是一个测试答案"
        },
        "category": "医案系统",
        "risk_level": "中",
        "expected_qps": 40,
        "description": "医案评分功能"
    },
    "获取健康记录": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/get_health_records",
        "method": "GET",
        "category": "医案系统",
        "risk_level": "低",
        "expected_qps": 90,
        "description": "获取用户健康记录"
    },
    
    # 🔹 邀请系统 (1个API)
    "扫描邀请码": {
        "url": f"{BASE_URL}/api/invite_api/invite_api/scan_invite_code/TEST123",
        "method": "GET",
        "category": "邀请系统",
        "risk_level": "中",
        "expected_qps": 50,
        "description": "扫描邀请码"
    },
    
    # 🔹 预后系统 (3个API) - 已验证为异步实现
    "分类下的疗法": {
        "url": f"{BASE_URL}/api/prognosis_api/therapies/category/1",
        "method": "GET",
        "category": "预后系统",
        "risk_level": "低",
        "expected_qps": 100,
        "description": "获取指定分类下的疗法"
    },
    "疗法详情": {
        "url": f"{BASE_URL}/api/prognosis_api/therapies/1",
        "method": "GET", 
        "category": "预后系统",
        "risk_level": "低",
        "expected_qps": 120,
        "description": "获取疗法详情"
    },
    "疗法点赞": {
        "url": f"{BASE_URL}/api/prognosis_api/therapies/1/like",
        "method": "POST",
        "data": {},
        "category": "预后系统",
        "risk_level": "中",
        "expected_qps": 60,
        "description": "疗法点赞功能"
    },
    
    # 🔹 医疗健康 (6个API) - 选择低风险API
    "获取用户问卷列表": {
        "url": f"{BASE_URL}/api/questionnaire_api/user-questionnaires",
        "method": "GET",
        "category": "医疗健康",
        "risk_level": "低",
        "expected_qps": 100,
        "description": "获取用户问卷列表"
    },
    "获取最新计算结果": {
        "url": f"{BASE_URL}/api/questionnaire_api/latest-calculation",
        "method": "GET",
        "category": "医疗健康",
        "risk_level": "低",
        "expected_qps": 90,
        "description": "获取最新计算结果"
    },
    "特定计算历史": {
        "url": f"{BASE_URL}/api/questionnaire_api/calculation-history/1",
        "method": "GET",
        "category": "医疗健康",
        "risk_level": "低",
        "expected_qps": 80,
        "description": "获取特定计算历史"
    },
    "获取用户问卷视图": {
        "url": f"{BASE_URL}/api/questionnaire_api/user-questionnaire-view",
        "method": "GET",
        "category": "医疗健康",
        "risk_level": "低",
        "expected_qps": 85,
        "description": "获取用户问卷视图"
    },
    "体质历史记录": {
        "url": f"{BASE_URL}/api/questionnaire_api/constitution-history/1",
        "method": "GET",
        "category": "医疗健康",
        "risk_level": "低",
        "expected_qps": 75,
        "description": "获取体质历史记录"
    },
    "健康经验排行榜": {
        "url": f"{BASE_URL}/api/questionnaire_api/health-experience-ranking",
        "method": "GET",
        "category": "医疗健康",
        "risk_level": "低",
        "expected_qps": 70,
        "description": "健康经验排行榜"
    },
    
    # 🔹 其他功能 (4个API) - 选择低风险API
    "流式传输测试": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/stream_test",
        "method": "GET",
        "category": "其他功能",
        "risk_level": "中",
        "expected_qps": 30,
        "description": "流式传输测试"
    },
    "生成二维码": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/generate_qr/test123",
        "method": "GET",
        "category": "其他功能",
        "risk_level": "低",
        "expected_qps": 80,
        "description": "生成二维码"
    },
    "获取药物详情": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medicine_detail/1",
        "method": "GET",
        "category": "其他功能",
        "risk_level": "低",
        "expected_qps": 90,
        "description": "获取药物详情"
    },
    "获取用户反馈": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/user_feedback",
        "method": "GET",
        "category": "其他功能",
        "risk_level": "低",
        "expected_qps": 85,
        "description": "获取用户反馈"
    }
}

def test_single_api(api_name, api_config):
    """测试单个API"""
    start_time = time.time()
    try:
        method = api_config["method"]
        url = api_config["url"]
        data = api_config.get("data", {})

        if method == "GET":
            response = requests.get(url, headers=headers, timeout=30)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=30)
        else:
            return {
                "api_name": api_name,
                "success": False,
                "error": f"不支持的方法: {method}",
                "error_type": "METHOD_NOT_SUPPORTED",
                "status_code": 0,
                "response_time": 0
            }

        end_time = time.time()
        response_time = end_time - start_time

        # 详细分析HTTP状态码
        error_info = None
        error_type = None

        if response.status_code != 200:
            if response.status_code == 404:
                error_info = "API路径不存在"
                error_type = "NOT_FOUND"
            elif response.status_code == 401:
                error_info = "认证失败"
                error_type = "UNAUTHORIZED"
            elif response.status_code == 403:
                error_info = "权限不足"
                error_type = "FORBIDDEN"
            elif response.status_code == 405:
                error_info = "HTTP方法不允许"
                error_type = "METHOD_NOT_ALLOWED"
            elif response.status_code == 500:
                error_info = "服务器内部错误"
                error_type = "INTERNAL_SERVER_ERROR"
            elif response.status_code == 502:
                error_info = "网关错误"
                error_type = "BAD_GATEWAY"
            elif response.status_code == 503:
                error_info = "服务不可用"
                error_type = "SERVICE_UNAVAILABLE"
            elif response.status_code == 504:
                error_info = "网关超时"
                error_type = "GATEWAY_TIMEOUT"
            else:
                error_info = f"HTTP {response.status_code}"
                error_type = "HTTP_ERROR"

            # 尝试获取响应内容
            try:
                response_text = response.text[:200] if response.text else "无响应内容"
            except:
                response_text = "无法读取响应内容"

        return {
            "api_name": api_name,
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "response_time": response_time,
            "category": api_config["category"],
            "risk_level": api_config["risk_level"],
            "expected_qps": api_config["expected_qps"],
            "error": error_info,
            "error_type": error_type,
            "response_text": response_text if response.status_code != 200 else None
        }

    except requests.exceptions.ConnectionError as e:
        return {
            "api_name": api_name,
            "success": False,
            "error": f"连接错误: {str(e)}",
            "error_type": "CONNECTION_ERROR",
            "status_code": 0,
            "response_time": time.time() - start_time,
            "category": api_config["category"],
            "risk_level": api_config["risk_level"],
            "expected_qps": api_config["expected_qps"]
        }
    except requests.exceptions.Timeout as e:
        return {
            "api_name": api_name,
            "success": False,
            "error": f"请求超时: {str(e)}",
            "error_type": "TIMEOUT",
            "status_code": 0,
            "response_time": time.time() - start_time,
            "category": api_config["category"],
            "risk_level": api_config["risk_level"],
            "expected_qps": api_config["expected_qps"]
        }
    except requests.exceptions.RequestException as e:
        return {
            "api_name": api_name,
            "success": False,
            "error": f"请求异常: {str(e)}",
            "error_type": "REQUEST_EXCEPTION",
            "status_code": 0,
            "response_time": time.time() - start_time,
            "category": api_config["category"],
            "risk_level": api_config["risk_level"],
            "expected_qps": api_config["expected_qps"]
        }
    except Exception as e:
        return {
            "api_name": api_name,
            "success": False,
            "error": f"未知异常: {str(e)}",
            "error_type": "UNKNOWN_EXCEPTION",
            "status_code": 0,
            "response_time": time.time() - start_time,
            "category": api_config["category"],
            "risk_level": api_config["risk_level"],
            "expected_qps": api_config["expected_qps"]
        }

def test_concurrent_api(api_name, api_config, concurrent=5, total=25):
    """测试API并发性能"""
    print(f"🧪 测试 {api_name} - 并发{concurrent}, 总请求{total}")
    
    start_time = time.time()
    results = []
    
    with ThreadPoolExecutor(max_workers=concurrent) as executor:
        futures = [executor.submit(test_single_api, api_name, api_config) for _ in range(total)]
        for future in as_completed(futures):
            results.append(future.result())
    
    end_time = time.time()
    total_time = end_time - start_time
    
    success_count = sum(1 for r in results if r["success"])
    qps = total / total_time if total_time > 0 else 0
    error_rate = ((total - success_count) / total) * 100
    avg_response_time = sum(r["response_time"] for r in results) / len(results)
    
    return {
        "api_name": api_name,
        "category": api_config["category"],
        "concurrent_level": concurrent,
        "total_requests": total,
        "success_count": success_count,
        "qps": qps,
        "error_rate": error_rate,
        "avg_response_time": avg_response_time,
        "expected_qps": api_config["expected_qps"],
        "risk_level": api_config["risk_level"]
    }

def main():
    """主函数"""
    print("🚀 第五批API连接池修复验证测试")
    print("=" * 60)
    print(f"📊 总API数量: {len(FIFTH_BATCH_APIS)} 个")
    print("🔥 已去除AI接口和高风险同步异步混用API")
    print("=" * 60)
    
    # 按类别统计
    categories = {}
    for api_name, api_config in FIFTH_BATCH_APIS.items():
        category = api_config["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append(api_name)
    
    print("📋 按业务模块分组:")
    for category, apis in categories.items():
        print(f"   🔹 {category}: {len(apis)}个API")
    print()
    
    # 开始测试
    all_results = []
    successful_apis = 0
    
    for api_name, api_config in FIFTH_BATCH_APIS.items():
        print(f"\n📊 测试 {api_name}")
        print(f"   分类: {api_config['category']}")
        print(f"   风险: {api_config['risk_level']}")
        print(f"   期望QPS: {api_config['expected_qps']}")
        
        # 先测试单个请求
        single_result = test_single_api(api_name, api_config)
        if not single_result["success"]:
            error_type = single_result.get("error_type", "UNKNOWN")
            status_code = single_result.get("status_code", 0)
            error_msg = single_result.get("error", "未知错误")

            # 根据错误类型显示不同的图标和信息
            if error_type == "NOT_FOUND":
                icon = "🔍"
                print(f"   {icon} API路径不存在 (404): {error_msg}")
            elif error_type == "UNAUTHORIZED":
                icon = "🔐"
                print(f"   {icon} 认证失败 (401): {error_msg}")
            elif error_type == "FORBIDDEN":
                icon = "🚫"
                print(f"   {icon} 权限不足 (403): {error_msg}")
            elif error_type == "METHOD_NOT_ALLOWED":
                icon = "⚠️"
                print(f"   {icon} HTTP方法不允许 (405): {error_msg}")
            elif error_type == "INTERNAL_SERVER_ERROR":
                icon = "💥"
                print(f"   {icon} 服务器内部错误 (500): {error_msg}")
            elif error_type == "CONNECTION_ERROR":
                icon = "🔌"
                print(f"   {icon} 连接错误: {error_msg}")
            elif error_type == "TIMEOUT":
                icon = "⏰"
                print(f"   {icon} 请求超时: {error_msg}")
            else:
                icon = "❌"
                print(f"   {icon} API不可用 ({status_code}): {error_msg}")

            # 如果有响应内容，显示部分内容
            if single_result.get("response_text"):
                response_preview = single_result["response_text"][:100]
                print(f"      响应内容: {response_preview}...")

            continue
        
        print(f"   ✅ API可用，响应时间: {single_result['response_time']:.3f}s")
        
        # 测试并发性能
        result = test_concurrent_api(api_name, api_config, 5, 25)
        all_results.append(result)
        
        # 评估结果
        improvement_needed = result["expected_qps"] * 0.5  # 至少达到期望QPS的50%
        
        if result["qps"] >= improvement_needed and result["error_rate"] < 20:
            successful_apis += 1
            status = "✅ 通过"
        else:
            status = "⚠️ 需优化"
        
        print(f"   {status} QPS: {result['qps']:.1f}, 错误率: {result['error_rate']:.1f}%")
    
    # 生成总结报告
    print(f"\n🎯 第五批API测试总结:")
    print(f"   测试通过: {successful_apis}/{len(FIFTH_BATCH_APIS)} 个API")
    
    success_rate = (successful_apis / len(FIFTH_BATCH_APIS)) * 100 if FIFTH_BATCH_APIS else 0
    
    if success_rate >= 80:
        print("   🎉 连接池修复效果优秀！")
    elif success_rate >= 60:
        print("   👍 连接池修复效果良好")
    else:
        print("   ⚠️ 仍需进一步优化连接池配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
