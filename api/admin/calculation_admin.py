"""
问卷计算过程相关的Admin配置
包括对问卷计算过程的监控和管理
"""

from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.db.models import Count
from django.db.models.functions import TruncDate
from django.utils import timezone
from django.contrib import messages
import json
from datetime import timedelta, datetime

from ..models.questionnaire_models import (
    QuestionnaireCalculationHistory, 
    EnhancedQuestionnaireResponse,
    Questionnaire
)
from ..models.user_models import UserInfo

# 注意：移除装饰器，因为模型已经在questionnaire_admin.py中注册
class CalculationProcessAdmin(admin.ModelAdmin):
    """问卷计算过程管理"""
    list_display = [
        'id', 
        'user_info', 
        'calculation_type', 
        'result_preview', 
        'filled_questionnaire_count',
        'created_at'
    ]
    list_filter = ['calculation_type', 'created_at']
    search_fields = ['user__nickname', 'user__wx_phone_new', 'calculation_type']
    readonly_fields = [
        'user_info_detail', 
        'created_at', 
        'result_data_formatted', 
        'params_formatted',
        'questionnaire_data_preview',
        'user_answers_formatted'
    ]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user_info_detail', 'calculation_type', 'created_at')
        }),
        ('计算结果', {
            'fields': ('result_data_formatted',)
        }),
        ('问卷信息', {
            'fields': ('filled_questionnaire_count', 'questionnaire_data_preview')
        }),
        ('用户回答', {
            'fields': ('user_answers_formatted',)
        }),
        ('计算参数', {
            'fields': ('params_formatted',)
        }),
    )
    
    def get_queryset(self, request):
        """优化查询，预加载关联对象"""
        return super().get_queryset(request).select_related('user')
    
    def user_info(self, obj):
        """显示用户基本信息"""
        if obj.user:
            return f"{obj.user.nickname or '未知'} (ID: {obj.user.id})"
        return "未关联用户"
    user_info.short_description = '用户'
    
    def user_info_detail(self, obj):
        """显示详细用户信息"""
        if not obj.user:
            return "未关联用户"
        
        user = obj.user
        user_info = [
            f"用户ID: {user.id}",
            f"昵称: {user.nickname or '未设置'}",
            f"性别: {user.gender or '未知'}",
            f"年龄: {user.age or '未知'}"
        ]
        
        if hasattr(user, 'wx_phone_new') and user.wx_phone_new:
            user_info.append(f"手机号: {user.wx_phone_new}")
            
        if hasattr(user, 'height') and user.height:
            user_info.append(f"身高: {user.height}cm")
            
        if hasattr(user, 'weight') and user.weight:
            user_info.append(f"体重: {user.weight}kg")
            
        return mark_safe("<br>".join(user_info))
    user_info_detail.short_description = '用户详细信息'
    
    def result_preview(self, obj):
        """结果数据预览"""
        if not obj.result_data or not isinstance(obj.result_data, list):
            return "无有效数据"
        
        result_count = len(obj.result_data)
        preview_items = []
        
        # 最多显示前3个结果
        for i, item in enumerate(obj.result_data[:3]):
            if isinstance(item, dict):
                # 兼容不同格式的结果数据
                type_name = item.get("type", item.get("text", "未知"))
                
                # 获取百分比/权重值
                percent = None
                if "percent" in item:
                    percent = item["percent"]
                elif "adjustedWeight" in item:
                    percent = item["adjustedWeight"]
                
                if percent is not None:
                    # 根据百分比值设置颜色
                    color = "#5cb85c"  # 默认绿色
                    if percent >= 70:
                        color = "#d9534f"  # 红色
                    elif percent >= 50:
                        color = "#f0ad4e"  # 黄色
                    
                    preview_items.append(
                        f'<span style="color:{color}"><b>{type_name}</b>: {percent}%</span>'
                    )
        
        if result_count > 3:
            preview_items.append(f"... 共{result_count}项")
            
        return mark_safe(", ".join(preview_items))
    result_preview.short_description = '计算结果预览'
    
    def filled_questionnaire_count(self, obj):
        """显示填写的问卷数量"""
        if not obj.params or not isinstance(obj.params, dict):
            return "未知"
        
        # 从参数中获取问卷ID列表
        questionnaire_ids = obj.params.get('questionnaire_ids', [])
        if questionnaire_ids:
            return len(questionnaire_ids)
            
        # 兼容旧格式
        questionnaire_data = obj.params.get('questionnaire_data', {})
        if questionnaire_data and isinstance(questionnaire_data, dict):
            return len(questionnaire_data)
            
        return "未知"
    filled_questionnaire_count.short_description = '问卷填写数量'
    
    def result_data_formatted(self, obj):
        """格式化显示结果数据"""
        if not obj.result_data:
            return mark_safe("无结果数据")
        
        # 将结果数据转换为带有颜色标识的HTML表格
        result_html = ['<table class="table" style="width:100%; border-collapse:collapse;">']
        result_html.append('<tr style="background-color:#f8f8f8;">')
        result_html.append('<th style="padding:8px; border:1px solid #ddd; text-align:left;">类型/名称</th>')
        result_html.append('<th style="padding:8px; border:1px solid #ddd; text-align:left;">百分比</th>')
        
        # 检查是否有更多字段
        has_weight = False
        has_probability = False
        
        if obj.result_data and isinstance(obj.result_data, list) and len(obj.result_data) > 0:
            first_item = obj.result_data[0]
            if isinstance(first_item, dict):
                has_weight = "totalWeight" in first_item
                has_probability = "totalProbability" in first_item
        
        if has_weight:
            result_html.append('<th style="padding:8px; border:1px solid #ddd; text-align:left;">权重</th>')
        if has_probability:
            result_html.append('<th style="padding:8px; border:1px solid #ddd; text-align:left;">概率</th>')
            
        result_html.append('</tr>')
        
        # 按百分比/权重值排序
        sorted_results = sorted(
            obj.result_data, 
            key=lambda x: x.get("percent", x.get("adjustedWeight", 0)) if isinstance(x, dict) else 0,
            reverse=True
        )
        
        for item in sorted_results:
            if not isinstance(item, dict):
                continue
                
            type_name = item.get("type", item.get("text", "未知"))
                
            # 获取百分比/权重值
            percent = item.get("percent", item.get("adjustedWeight", 0))
            
            # 根据百分比值设置背景颜色
            bg_color = "#ffffff"  # 默认白色
            text_color = "#000000"  # 默认黑色
            
            if percent >= 70:
                bg_color = "#f2dede"  # 红色背景
            elif percent >= 50:
                bg_color = "#fcf8e3"  # 黄色背景
            elif percent >= 30:
                bg_color = "#dff0d8"  # 绿色背景
            
            result_html.append(f'<tr style="background-color:{bg_color};">')
            result_html.append(f'<td style="padding:8px; border:1px solid #ddd;"><b>{type_name}</b></td>')
            result_html.append(f'<td style="padding:8px; border:1px solid #ddd;"><b>{percent}%</b></td>')
            
            if has_weight:
                weight = item.get("totalWeight", "N/A")
                result_html.append(f'<td style="padding:8px; border:1px solid #ddd;">{weight}</td>')
                
            if has_probability:
                probability = item.get("totalProbability", "N/A") 
                result_html.append(f'<td style="padding:8px; border:1px solid #ddd;">{probability}</td>')
                
            result_html.append('</tr>')
            
        result_html.append('</table>')
        
        return mark_safe(''.join(result_html))
    result_data_formatted.short_description = '结果数据详情'
    
    def questionnaire_data_preview(self, obj):
        """显示问卷数据预览"""
        if not obj.params or not isinstance(obj.params, dict):
            return mark_safe("无问卷数据")
            
        # 从参数中获取问卷ID列表
        questionnaire_ids = obj.params.get('questionnaire_ids', [])
        
        if not questionnaire_ids:
            # 兼容旧格式
            questionnaire_data = obj.params.get('questionnaire_data', {})
            if not questionnaire_data:
                return mark_safe("无问卷数据")
            questionnaire_ids = list(questionnaire_data.keys())
        
        # 构建问卷列表HTML
        html = ['<ul style="list-style-type:none; padding-left:0;">']
        
        for q_id in questionnaire_ids:
            try:
                # 尝试获取问卷名称
                questionnaire_name = "未知问卷"
                try:
                    q_obj = Questionnaire.objects.get(id=q_id)
                    questionnaire_name = q_obj.name
                except:
                    pass
                
                html.append(f'<li style="margin-bottom:5px;"><b>问卷ID {q_id}:</b> {questionnaire_name}</li>')
            except Exception as e:
                html.append(f'<li>问卷ID {q_id} (错误: {str(e)})</li>')
        
        html.append('</ul>')
        
        return mark_safe(''.join(html))
    questionnaire_data_preview.short_description = '问卷信息'
    
    def user_answers_formatted(self, obj):
        """显示用户的问卷回答"""
        if not obj.params or not isinstance(obj.params, dict):
            return mark_safe("无答案数据")
            
        # 从参数中获取响应文档ID列表
        response_doc_ids = obj.params.get('latest_response_doc_ids', [])
        
        if not response_doc_ids:
            return mark_safe("无法获取用户答案记录")
            
        # 构建答案预览HTML
        html = ['<div style="max-height:500px; overflow-y:auto;">']
        
        for doc_id in response_doc_ids:
            try:
                # 尝试获取问卷响应记录
                response = EnhancedQuestionnaireResponse.objects.get(id=doc_id)
                q_id = response.questionnaire_id
                
                # 获取问卷名称
                questionnaire_name = "未知问卷"
                try:
                    q_obj = Questionnaire.objects.get(id=q_id)
                    questionnaire_name = q_obj.name
                except:
                    pass
                
                # 添加问卷标题
                html.append(f'<h4 style="margin-top:20px; border-bottom:1px solid #ccc; padding-bottom:5px;">')
                html.append(f'问卷: {questionnaire_name} (ID: {q_id})')
                html.append('</h4>')
                
                # 添加用户答案
                if response.answers and isinstance(response.answers, dict):
                    html.append('<table style="width:100%; border-collapse:collapse; margin-bottom:15px;">')
                    html.append('<tr style="background-color:#f0f0f0;">')
                    html.append('<th style="padding:5px; border:1px solid #ddd; text-align:left;">问题ID</th>')
                    html.append('<th style="padding:5px; border:1px solid #ddd; text-align:left;">选择的选项ID</th>')
                    html.append('</tr>')
                    
                    for question_id, option_id in response.answers.items():
                        html.append('<tr>')
                        html.append(f'<td style="padding:5px; border:1px solid #ddd;">{question_id}</td>')
                        html.append(f'<td style="padding:5px; border:1px solid #ddd;">{option_id}</td>')
                        html.append('</tr>')
                        
                    html.append('</table>')
                else:
                    html.append('<p>此问卷无有效的答案数据</p>')
            except Exception as e:
                html.append(f'<p>获取响应ID {doc_id} 的答案时出错: {str(e)}</p>')
                
        html.append('</div>')
        
        return mark_safe(''.join(html))
    user_answers_formatted.short_description = '用户问卷答案'
    
    def params_formatted(self, obj):
        """格式化显示参数数据"""
        if not obj.params:
            return mark_safe("无参数数据")
            
        # 转为JSON并格式化显示
        try:
            params_str = json.dumps(obj.params, indent=4, ensure_ascii=False)
            return mark_safe(f'<pre style="max-height:400px; overflow-y:auto;">{params_str}</pre>')
        except:
            return mark_safe("参数数据格式错误")
    params_formatted.short_description = '计算参数详情'
    
    # 统计视图
    change_list_template = 'admin/calculation_process_change_list.html'
    
    def changelist_view(self, request, extra_context=None):
        """自定义列表视图，添加统计数据"""
        # 获取统计数据
        
        # 计算过去30天的统计数据
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        # 按计算类型统计
        calculation_types = list(
            QuestionnaireCalculationHistory.objects
            .values('calculation_type')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        # 计算总数
        total_count = sum(item['count'] for item in calculation_types)
        
        # 添加占比字段
        for item in calculation_types:
            item['percentage'] = round(item['count'] / total_count * 100, 2) if total_count > 0 else 0
        
        # 按日期统计
        daily_calculations = list(
            QuestionnaireCalculationHistory.objects
            .filter(created_at__gte=thirty_days_ago)
            .annotate(date=TruncDate('created_at'))
            .values('date')
            .annotate(count=Count('id'))
            .order_by('date')
        )
        
        # 转换为图表数据格式
        daily_data = [
            {'date': item['date'].strftime('%Y-%m-%d'), 'count': item['count']}
            for item in daily_calculations
        ]
        
        # 用户统计
        user_calculations = list(
            QuestionnaireCalculationHistory.objects
            .values('user_id')
            .annotate(count=Count('id'))
            .order_by('-count')[:10]  # 仅获取前10位用户
        )
        
        # 获取用户信息
        user_stats = []
        for item in user_calculations:
            try:
                user = UserInfo.objects.get(id=item['user_id'])
                user_stats.append({
                    'user_id': user.id,
                    'nickname': user.nickname or f"用户{user.id}",
                    'count': item['count']
                })
            except:
                user_stats.append({
                    'user_id': item['user_id'],
                    'nickname': f"未知用户{item['user_id']}",
                    'count': item['count']
                })
        
        # 准备额外的上下文数据
        if extra_context is None:
            extra_context = {}
            
        extra_context.update({
            'calculation_types': calculation_types,
            'daily_data': mark_safe(json.dumps(daily_data)),
            'user_stats': user_stats,
            'total_calculations': total_count,
            'recent_30d_calculations': QuestionnaireCalculationHistory.objects.filter(
                created_at__gte=thirty_days_ago
            ).count()
        })
        
        # 调用父类方法
        return super().changelist_view(request, extra_context=extra_context)
        
    # 自定义actions
    actions = ['delete_by_user', 'export_calculation_results', 'analyze_calculation_data']
    
    def delete_by_user(self, request, queryset):
        """删除用户的所有计算记录"""
        # 获取用户ID列表
        user_ids = set()
        for record in queryset:
            if record.user_id:
                user_ids.add(record.user_id)
                
        if not user_ids:
            messages.warning(request, "未找到有效的用户ID，无法执行删除操作")
            return
            
        # 获取要删除的记录数量
        total_count = QuestionnaireCalculationHistory.objects.filter(user_id__in=user_ids).count()
        
        # 执行删除
        deleted, _ = QuestionnaireCalculationHistory.objects.filter(user_id__in=user_ids).delete()
        
        # 显示结果
        messages.success(
            request, 
            f"已成功删除 {deleted} 条计算记录（涉及 {len(user_ids)} 位用户）。"
        )
    delete_by_user.short_description = "删除所选用户的所有计算记录"
    
    def export_calculation_results(self, request, queryset):
        """导出计算结果"""
        # 这是一个占位函数，实际导出功能通过Django admin的ImportExportModelAdmin实现
        selected_count = queryset.count()
        messages.info(
            request,
            f"您选择了 {selected_count} 条记录。请使用页面顶部的'导出'按钮完成导出操作。"
        )
    export_calculation_results.short_description = "导出所选计算结果"
    
    def analyze_calculation_data(self, request, queryset):
        """分析计算数据"""
        # 收集统计信息
        type_counts = {}
        min_date = None
        max_date = None
        
        for record in queryset:
            # 计算类型统计
            calc_type = record.calculation_type
            if calc_type not in type_counts:
                type_counts[calc_type] = 0
            type_counts[calc_type] += 1
            
            # 日期范围
            if min_date is None or record.created_at < min_date:
                min_date = record.created_at
            if max_date is None or record.created_at > max_date:
                max_date = record.created_at
        
        # 构建统计消息
        message_parts = [f"所选 {queryset.count()} 条记录的分析结果:"]
        
        # 日期范围
        if min_date and max_date:
            message_parts.append(
                f"日期范围: {min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}"
            )
            
        # 计算类型分布
        if type_counts:
            message_parts.append("计算类型分布:")
            for calc_type, count in type_counts.items():
                percentage = round(count / queryset.count() * 100, 2)
                message_parts.append(f" - {calc_type}: {count}条 ({percentage}%)")
                
        # 显示分析结果
        messages.info(request, mark_safe("<br>".join(message_parts)))
    analyze_calculation_data.short_description = "分析所选计算数据" 