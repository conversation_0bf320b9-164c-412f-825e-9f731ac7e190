# api/prompts/categories.py
"""
八字分析提示词分类定义
支持前端参数排列组合的提示词工程
"""

from enum import Enum


class AnalysisDirection(Enum):
    """分析方向枚举"""
    BASIC_ANALYSIS = "basic_analysis"           # 基础分析
    CONTRADICTION_ANALYSIS = "contradiction_analysis"  # 矛盾分析
    PATTERN_ANALYSIS = "pattern_analysis"       # 模式分析
    COMPREHENSIVE_PATTERN = "comprehensive_pattern"  # 综合格局分析
    DECISION_SUPPORT = "decision_support"       # 决策支持


class Domain(Enum):
    """分析领域枚举"""
    HEALTH = "health"           # 健康
    WEALTH = "wealth"           # 财富
    EDUCATION = "education"     # 教育
    CAREER = "career"           # 事业
    MARRIAGE = "marriage"       # 婚姻
    FORTUNE = "fortune"         # 吉运
    MISFORTUNE = "misfortune"   # 凶运
    OVERALL_PATTERN = "overall_pattern"  # 整体格局


class TimeScope(Enum):
    """时间范围枚举"""
    YEARLY = "yearly"           # 年度
    MONTHLY = "monthly"         # 月度
    YEARLY_MONTHLY = "yearly_monthly"  # 年月结合
    DAILY = "daily"             # 日度
    SHORT_TERM = "short_term"   # 短期
    LONG_TERM = "long_term"     # 长期
    LIFETIME = "lifetime"       # 终生


class PromptCategory(Enum):
    """提示词分类枚举（保持向后兼容）"""

    # 基础分析类
    HEALTH = "health"           # 健康养生
    WEALTH = "wealth"           # 财运分析
    CAREER = "career"           # 事业发展
    EDUCATION = "education"     # 学业运势
    MARRIAGE = "marriage"       # 婚恋情感
    FORTUNE = "fortune"         # 祸福吉凶

    # 专业分析类
    WUXING = "wuxing"          # 五行调理
    SHISHEN = "shishen"        # 十神分析
    SHENSHA = "shensha"        # 神煞解读
    LIUYAO = "liuyao"          # 六爻预测
    QIMEN = "qimen"            # 奇门遁甲

    # 时运分析类
    YEARLY = "yearly"          # 流年运势
    MONTHLY = "monthly"        # 流月运势
    DAILY = "daily"            # 每日运势

    # 综合分析类
    COMPREHENSIVE = "comprehensive"  # 综合分析
    PERSONALITY = "personality"      # 性格特征
    RELATIONSHIP = "relationship"    # 人际关系
    
    @classmethod
    def get_display_name(cls, category):
        """获取分类的显示名称"""
        display_names = {
            cls.HEALTH: "健康养生",
            cls.WEALTH: "财运分析", 
            cls.CAREER: "事业发展",
            cls.EDUCATION: "学业运势",
            cls.MARRIAGE: "婚恋情感",
            cls.FORTUNE: "祸福吉凶",
            cls.WUXING: "五行调理",
            cls.SHISHEN: "十神分析",
            cls.SHENSHA: "神煞解读",
            cls.LIUYAO: "六爻预测",
            cls.QIMEN: "奇门遁甲",
            cls.YEARLY: "流年运势",
            cls.MONTHLY: "流月运势",
            cls.DAILY: "每日运势",
            cls.COMPREHENSIVE: "综合分析",
            cls.PERSONALITY: "性格特征",
            cls.RELATIONSHIP: "人际关系",
        }
        return display_names.get(category, category.value)
    
    @classmethod
    def get_all_categories(cls):
        """获取所有分类"""
        return [category for category in cls]
    
    @classmethod
    def get_basic_categories(cls):
        """获取基础分析分类"""
        return [cls.HEALTH, cls.WEALTH, cls.CAREER, cls.EDUCATION, cls.MARRIAGE, cls.FORTUNE]
    
    @classmethod
    def get_professional_categories(cls):
        """获取专业分析分类"""
        return [cls.WUXING, cls.SHISHEN, cls.SHENSHA, cls.LIUYAO, cls.QIMEN]
    
    @classmethod
    def get_timing_categories(cls):
        """获取时运分析分类"""
        return [cls.YEARLY, cls.MONTHLY, cls.DAILY]
