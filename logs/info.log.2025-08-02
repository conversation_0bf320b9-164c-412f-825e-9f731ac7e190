INFO 2025-08-02 01:10:33,105 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 1
INFO 2025-08-02 01:10:33,129 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 2
INFO 2025-08-02 01:10:33,441 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 3
INFO 2025-08-02 01:10:35,020 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 4
INFO 2025-08-02 01:10:49,031 views 437310 140400047150656 收到微信支付回调: {"event": "wechat_callback_attempt", "client_ip": "**************", "timestamp": "2025-08-02 01:10:49", "user_agent": "Mozilla/4.0", "content_type": "application/json", "method": "POST", "body_size": 911}
INFO 2025-08-02 01:10:49,067 views 437310 140400047150656 微信支付回调处理成功: {"event": "wechat_callback_attempt", "client_ip": "**************", "timestamp": "2025-08-02 01:10:49", "user_agent": "Mozilla/4.0", "content_type": "application/json", "method": "POST", "body_size": 911, "status": "success", "processing_time": "45.90ms"}
INFO 2025-08-02 01:10:50,071 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 5
INFO 2025-08-02 01:10:50,110 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 6
INFO 2025-08-02 01:11:02,847 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 7
INFO 2025-08-02 01:11:12,390 payment 437310 140400731862592 支付宝回调处理成功: {"event": "alipay_callback_attempt", "client_ip": "**************", "timestamp": "2025-08-02T01:11:12.318274", "user_agent": "Mozilla/4.0", "content_type": "application/x-www-form-urlencoded; charset=utf-8", "method": "POST", "params_count": 25, "has_sign": true, "status": "success", "processing_time": "72.28ms"}
INFO 2025-08-02 01:11:14,439 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 8
INFO 2025-08-02 01:11:14,634 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 9
ERROR 2025-08-02 01:11:58,809 exception 437310 140400731862592 Invalid HTTP_HOST header: 'pts-bj.paquapp.com'. You may need to add 'pts-bj.paquapp.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'pts-bj.paquapp.com'. You may need to add 'pts-bj.paquapp.com' to ALLOWED_HOSTS.
ERROR 2025-08-02 01:29:22,758 exception 437310 140400731862592 Invalid HTTP_HOST header: 'pts-bj.paquapp.com'. You may need to add 'pts-bj.paquapp.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'pts-bj.paquapp.com'. You may need to add 'pts-bj.paquapp.com' to ALLOWED_HOSTS.
INFO 2025-08-02 01:57:30,553 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 10
INFO 2025-08-02 01:57:31,908 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 11
INFO 2025-08-02 01:57:33,354 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 12
INFO 2025-08-02 01:57:33,360 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 13
INFO 2025-08-02 01:57:37,593 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 14
INFO 2025-08-02 01:57:40,693 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 15
INFO 2025-08-02 01:57:42,193 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 16
INFO 2025-08-02 01:57:42,270 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 17
WARNING 2025-08-02 01:57:42,656 base 448382 140413494190080 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-08-02 01:57:42,709 base 448382 140413494190080 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.053秒
INFO 2025-08-02 01:57:44,529 ark_chat_consumer 448382 140413494190080 [ARK_CHAT] WebSocket断开连接: {'close_code': 1000, 'user_id': 1, 'disconnect_reason': '正常关闭'}
INFO 2025-08-02 01:57:44,666 rate_limiter 448382 140413494190080 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 18
INFO 2025-08-02 02:31:45,570 rate_limiter 455506 140313602883584 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 19
INFO 2025-08-02 19:49:07,120 rate_limiter 455506 140313602883584 [IP_RATE_LIMIT] IP:************ API:phone_login_api 调用次数: 1/20
