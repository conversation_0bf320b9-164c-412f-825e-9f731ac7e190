"""
通用模型的Admin配置
包括Order、Ingredient、Category、Announcement等常用模型的admin设置
"""

from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.utils import timezone

from ..models import Order, Ingredient, Category, Announcement
from ..serializers.serializers import AnnouncementSerializer
from rest_framework.views import APIView
from rest_framework.response import Response


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('order_id', 'user_id', 'status', 'subject', 'total_amount', 'telephoneidstar','pay_time')
    search_fields = ('order_id', 'user_id','telephoneidstar')
    list_filter = ('status',)


class IngredientResource(resources.ModelResource):
    class Meta:
        model = Ingredient
        fields = ('name', 'description', 'category', 'type', 'image', 'price', 'stock',
                 'suitable_constitutions', 'unsuitable_constitutions', 'nature_effect',
                 'plain_effect', 'merge_category', 'content', 'extracted_effect')


@admin.register(Ingredient)
class IngredientAdmin(ImportExportModelAdmin):
    resource_class = IngredientResource
    list_display = ('name', 'category', 'type', 'price', 'stock')
    list_filter = ('category', 'type')
    search_fields = ('name', 'description')

    def get_fieldsets(self, request, obj=None):
        common_fields = (
            (None, {
                'fields': ('name', 'description', 'image', 'category', 'type', 'price', 'stock')
            }),
            ('体质相关', {
                'fields': ('suitable_constitutions', 'unsuitable_constitutions')
            }),
        )

        if obj and obj.type == 'medicine':
            return common_fields + (
                ('药材特有信息', {
                    'fields': ('nature_effect', 'plain_effect')
                }),
            )
        elif obj and obj.type == 'food':
            return common_fields + (
                ('食材特有信息', {
                    'fields': ('merge_category', 'content', 'extracted_effect')
                }),
            )
        else:
            return common_fields + (
                ('药材特有信息', {
                    'fields': ('nature_effect', 'plain_effect')
                }),
                ('食材特有信息', {
                    'fields': ('merge_category', 'content', 'extracted_effect')
                }),
            )

    def get_list_display(self, request):
        return ('name', 'category', 'type', 'price', 'stock', 'suitable_constitutions', 'unsuitable_constitutions')


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    list_display = ('title', 'is_active', 'start_date', 'end_date')
    list_filter = ('is_active', 'start_date', 'end_date')
    search_fields = ('title', 'content')


class ActiveAnnouncementView(APIView):
    def get(self, request):
        now = timezone.now()
        active_announcement = Announcement.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        ).first()

        if active_announcement:
            serializer = AnnouncementSerializer(active_announcement)
            return Response(serializer.data)
        return Response({"message": "No active announcements"})


# 注意：PrognosisTherapyCategoryAdmin 已移动到 prognosis_admin.py
# 以提供更完整的功能和更好的组织结构 