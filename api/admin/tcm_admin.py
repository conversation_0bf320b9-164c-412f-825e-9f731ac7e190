"""
TCM(中医)相关模型的Admin配置
包括TCMQuestion、TCMForumPost和TCMComment的admin设置
"""

from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.utils.html import format_html
from django.urls import reverse, NoReverseMatch
from django.db import transaction
import sys
import traceback

from ..models import TCMQuestion, TCMForumPost, TCMComment


class TCMQuestionResource(resources.ModelResource):
    class Meta:
        model = TCMQuestion
        fields = ('content', 'option_a', 'option_b', 'option_c', 'option_d', 'answer', 
                 'explanation', 'difficulty', 'category', 'question_type', 'image')


@admin.register(TCMQuestion)
class TCMQuestionAdmin(ImportExportModelAdmin):
    resource_class = TCMQuestionResource
    list_display = ('content', 'option_a', 'option_b', 'option_c', 'option_d', 
                   'answer', 'has_explanation', 'difficulty', 'category', 'question_type', 'image')
    list_filter = ('difficulty', 'category', 'question_type')
    search_fields = ('content', 'category__name', 'explanation')
    fieldsets = (
        (None, {
            'fields': ('content', 'image')
        }),
        ('Options', {
            'fields': ('option_a', 'option_b', 'option_c', 'option_d', 'answer')
        }),
        ('解释说明', {
            'fields': ('explanation',),
            'description': '答案解释说明，原有题目可以留空'
        }),
        ('Classification', {
            'fields': ('difficulty', 'category', 'question_type')
        }),
    )
    
    def has_explanation(self, obj):
        """显示是否有解释说明"""
        return bool(obj.explanation)
    has_explanation.boolean = True
    has_explanation.short_description = '有解释'


# [警告] TCMForumPost管理类 - 可能存在section_link的URL反向解析问题
# 如果出现500错误，请检查TCMForumSection模型和URL配置
@admin.register(TCMForumPost)
class TCMForumPostAdmin(admin.ModelAdmin):
    list_display = [
        'id', 
        'title', 
        'author_link', 
        'section_link',
        'status',
        'is_pinned',
        'view_count', 
        'comment_count',
        'like_count',
        'collect_count',
        'created_at',
        'last_active'
    ]
    
    list_filter = ['status', 'is_pinned', 'created_at', 'section']
    search_fields = ['title', 'content', 'author__nickname', 'tags']
    readonly_fields = [
        'created_at', 'updated_at', 'last_active',
        'view_count', 'comment_count', 'like_count', 'collect_count'
    ]
    
    actions = ['soft_delete_posts', 'hide_posts', 'restore_posts', 'toggle_pin']

    def author_link(self, obj):
        try:
            if obj.author:
                try:
                    url = reverse("admin:api_userinfo_change", args=[obj.author.id])
                    return format_html('<a href="{}">{}</a>', 
                        url, 
                        getattr(obj.author, 'nickname', str(obj.author.id))
                    )
                except NoReverseMatch as e:
                    print(f"URL反向解析错误: {e}", file=sys.stderr)
                    return str(obj.author.id)
            return "已删除用户"
        except Exception as e:
            print(f"生成作者链接错误: {e}", file=sys.stderr)
            print(traceback.format_exc(), file=sys.stderr)
            return "错误"

    def section_link(self, obj):
        try:
            if not obj.section:
                return "版块已删除"
            
            try:
                section_model_name = obj.section.__class__.__name__.lower()
                url = reverse(f"admin:api_{section_model_name}_change", args=[obj.section.id])
                return format_html('<a href="{}">{}</a>', 
                    url, 
                    getattr(obj.section, 'name', str(obj.section.id))
                )
            except NoReverseMatch as e:
                print(f"版块URL反向解析错误: {e}", file=sys.stderr)
                return getattr(obj.section, 'name', str(obj.section.id))
        except Exception as e:
            print(f"生成版块链接错误: {e}", file=sys.stderr)
            print(traceback.format_exc(), file=sys.stderr)
            return "错误"

    def save_model(self, request, obj, form, change):
        try:
            super().save_model(request, obj, form, change)
        except Exception as e:
            print(f"保存帖子错误: {e}", file=sys.stderr)
            print(traceback.format_exc(), file=sys.stderr)
            raise


# [警告] TCMComment管理类 - 关联模型的链接可能存在URL反向解析问题
# 如果出现500错误，请检查相关模型和URL配置
@admin.register(TCMComment)
class TCMCommentAdmin(admin.ModelAdmin):
    list_display = [
        'id',
        'content_preview',
        'author_link',
        'post_link',
        'parent_comment_link',
        'like_count',
        'is_deleted',
        'created_at'
    ]
    
    list_filter = ['is_deleted', 'created_at']
    search_fields = ['content', 'author__nickname', 'post__title']
    readonly_fields = ['created_at', 'like_count']
    actions = ['soft_delete_comments', 'restore_comments']

    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '评论内容'

    def author_link(self, obj):
        try:
            if obj.author:
                try:
                    url = reverse("admin:api_userinfo_change", args=[obj.author.id])
                    return format_html('<a href="{}">{}</a>', 
                        url, 
                        getattr(obj.author, 'nickname', str(obj.author.id))
                    )
                except NoReverseMatch as e:
                    print(f"评论作者URL反向解析错误: {e}", file=sys.stderr)
                    return str(obj.author.id)
            return "已删除用户"
        except Exception as e:
            print(f"生成评论作者链接错误: {e}", file=sys.stderr)
            print(traceback.format_exc(), file=sys.stderr)
            return "错误"

    def post_link(self, obj):
        try:
            if not obj.post:
                return "帖子已删除"
            try:
                url = reverse("admin:api_tcmforumpost_change", args=[obj.post.id])
                return format_html('<a href="{}">{}</a>', 
                    url, 
                    obj.post.title[:30] if obj.post.title else str(obj.post.id)
                )
            except NoReverseMatch as e:
                print(f"帖子URL反向解析错误: {e}", file=sys.stderr)
                return obj.post.title[:30] if obj.post.title else str(obj.post.id)
        except Exception as e:
            print(f"生成帖子链接错误: {e}", file=sys.stderr)
            print(traceback.format_exc(), file=sys.stderr)
            return "错误"

    def parent_comment_link(self, obj):
        try:
            if obj.parent:
                try:
                    url = reverse("admin:api_tcmcomment_change", args=[obj.parent.id])
                    return format_html('<a href="{}">{}</a>', 
                        url, 
                        obj.parent.content[:30] if obj.parent.content else str(obj.parent.id)
                    )
                except NoReverseMatch as e:
                    print(f"父评论URL反向解析错误: {e}", file=sys.stderr)
                    return obj.parent.content[:30] if obj.parent.content else str(obj.parent.id)
            return "无"
        except Exception as e:
            print(f"生成父评论链接错误: {e}", file=sys.stderr)
            print(traceback.format_exc(), file=sys.stderr)
            return "错误"

    def save_model(self, request, obj, form, change):
        try:
            with transaction.atomic():
                super().save_model(request, obj, form, change)
                if obj.post:
                    obj.post.comment_count = obj.post.comments.filter(is_deleted=False).count()
                    obj.post.save()
        except Exception as e:
            print(f"保存评论错误: {e}", file=sys.stderr)
            print(traceback.format_exc(), file=sys.stderr)
            raise 