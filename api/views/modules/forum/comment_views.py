import traceback
import json
import sys
import datetime as dt
from typing import List
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.http import Http404
from ninja import Router
from ninja.errors import HttpError
from django.conf import settings
from django.db.models import F
from django.core.cache import cache
from asgiref.sync import sync_to_async
from api.models import TCMForumPost, TCMComment
from api.views.schemas import CommentCreateRequest, CommentResponse
from api.views.utils import get_user_by_id
from api.views.content_review import HuaweiContentReview
from api.ninja_apis.questionnaire.utils import api_timer

router = Router()

class JSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime等无法直接序列化的对象"""
    def default(self, obj):
        if isinstance(obj, dt.datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        return super().default(obj)

def format_comment(comment: TCMComment) -> dict:
    """格式化评论数据"""
    formatted = {
        "id": comment.id,
        "content": comment.content,
        "author": {
            "id": comment.author.id,
            "nickname": comment.author.nickname,
            "avatar": comment.author.avatar
        },
        "post_id": comment.post_id,
        "parent_id": comment.parent_id,
        "like_count": comment.like_count,
        "is_deleted": comment.is_deleted,
        "created_at": comment.created_at
    }
    print(f'格式化评论: ID={comment.id}, parent_id={comment.parent_id}')
    return formatted

@router.get("/posts/{post_id}/comments", response=List[CommentResponse])
@api_timer("获取评论列表")
async def get_comments(request, post_id: int, page: int = 1, size: int = 20):
    """
    获取评论列表 - 支持分页
    
    参数说明:
    - page: 页码，从1开始 (默认: 1)
    - size: 每页数量 (默认: 20, 最大: 50)
    """
    try:
        print(f"[DEBUG] 获取评论列表 - post_id: {post_id}, page: {page}, size: {size}")
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 50:  # 评论列表限制每页最多50条
            size = 20
            
        # 计算偏移量
        offset = (page - 1) * size
        
        # 检查缓存中是否有数据
        cache_key = f"forum_comments:{post_id}:p{page}:s{size}"
        cached_comments = await sync_to_async(cache.get)(cache_key)
        
        if cached_comments:
            print(f"[CACHE_HIT] ✅ {cache_key} - 返回帖子 {post_id} 的评论列表缓存数据")
            return json.loads(cached_comments)
            
        # 缓存未命中，从数据库获取 - 支持分页
        from api.ninja_apis.async_utils import filter_async
        all_comments = await filter_async(
            TCMComment,
            post_id=post_id,
            is_deleted=False,
            select_related=['author'],
            order_by=['-created_at'],
            offset=offset,
            limit=size
        )
        
        # 使用同步格式化函数
        format_comment_async = sync_to_async(format_comment)
        formatted_comments = []
        for comment in all_comments:
            formatted_comment = await format_comment_async(comment)
            
            # 处理datetime序列化
            if isinstance(formatted_comment, dict) and 'created_at' in formatted_comment and formatted_comment['created_at']:
                if isinstance(formatted_comment['created_at'], dt.datetime):
                    formatted_comment['created_at'] = formatted_comment['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    
            formatted_comments.append(formatted_comment)
        
        # 设置缓存，缓存5分钟，使用自定义JSON编码器处理datetime
        await sync_to_async(cache.set)(
            cache_key, 
            json.dumps(formatted_comments, cls=JSONEncoder), 
            timeout=300  # 分页数据缓存时间较短
        )
        print(f"[CACHE_SET] 💾 {cache_key} - 帖子 {post_id} 第{page}页的评论列表数据已缓存")
        
        print(f"[DEBUG] 帖子 {post_id} 第{page}页查询完成，返回 {len(formatted_comments)} 条评论")
        return formatted_comments
    except Exception as e:
        print('获取评论列表错误:', str(e), file=sys.stderr)
        print('详细错误信息:', traceback.format_exc(), file=sys.stderr)
        raise HttpError(500, {"error": "获取评论列表失败", "detail": str(e)})

@router.post("/posts/{post_id}/comments", response=CommentResponse)
@api_timer("创建评论")
async def create_comment(request, post_id: int, comment_data: CommentCreateRequest):
    """创建评论"""
    try:
        # 使用transaction.atomic需要特殊处理，因为它不能直接在异步函数中使用
        # 我们将事务操作封装在一个同步函数中，然后使用sync_to_async调用它
        
        @sync_to_async
        def create_comment_sync():
            with transaction.atomic():
                # 获取用户信息
                user = get_user_by_id(request.user_id)
                if not user:
                    print(f"用户认证失败: user_id={request.user_id}")
                    raise HttpError(401, "用户未认证")
                
                # 验证帖子存在且未删除
                try:
                    post = get_object_or_404(
                        TCMForumPost, 
                        id=post_id,
                        status='published'
                    )
                except Http404:
                    print(f"帖子不存在或已删除: post_id={post_id}")
                    raise HttpError(404, "帖子不存在或已删除")
                
                # 如果是回复，验证父评论存在且未删除
                if comment_data.parent_id:
                    try:
                        parent_comment = get_object_or_404(
                            TCMComment,
                            id=comment_data.parent_id,
                            post_id=post_id,
                            is_deleted=False
                        )
                    except Http404:
                        print(f"父评论不存在或已删除: parent_id={comment_data.parent_id}, post_id={post_id}")
                        raise HttpError(404, "父评论不存在或已删除")
                
                # 内容审核
                content_reviewer = HuaweiContentReview(project_id=settings.HUAWEI_PROJECT_ID)
                review_result = content_reviewer.text_moderation(comment_data.content)
                print(f"内容审核结果: {json.dumps(review_result, ensure_ascii=False)}")
                
                if review_result['status'] == 'error':
                    # 审核服务异常时，为了用户体验，仍然允许评论
                    print(f"内容审核服务异常: {review_result['message']}")
                elif review_result['status'] == 'success' and review_result['data']['suggestion'] == 'block':
                    # 审核不通过，记录日志但不抛出错误
                    print(f"内容审核不通过，不保存评论: {json.dumps(review_result['data'], ensure_ascii=False)}")
                    # 返回一个空的成功响应
                    fake_comment = {
                        "id": 0,  # 使用0作为虚拟ID
                        "content": comment_data.content,
                        "author": {
                            "id": user.id,
                            "nickname": user.nickname if hasattr(user, 'nickname') else str(user.id),
                        },
                        "post_id": post_id,
                        "parent_id": comment_data.parent_id,
                        "like_count": 0,
                        "is_deleted": False,
                        "created_at": dt.datetime.now(),
                        "is_fake": True  # 标记这是一个虚拟评论
                    }
                    return fake_comment, True  # 返回一个标记，表示这是虚拟评论
                
                # 内容审核通过或服务异常，创建评论
                try:
                    comment = TCMComment.objects.create(
                        post=post,
                        author=user,
                        content=comment_data.content,
                        parent_id=comment_data.parent_id
                    )
                except Exception as e:
                    print(f"创建评论失败: {str(e)}")
                    print(f"详细错误: {traceback.format_exc()}")
                    raise HttpError(500, "创建评论失败")
                
                # 更新帖子的评论计数和最后活动时间
                try:
                    post.comment_count = F('comment_count') + 1
                    post.last_active = dt.datetime.now()
                    post.save()
                except Exception as e:
                    print(f"更新帖子统计信息失败: {str(e)}")
                    print(f"详细错误: {traceback.format_exc()}")
                    raise HttpError(500, "更新帖子统计信息失败")
                
                # 清除相关缓存
                cache_key = f"forum_comments:{post_id}"
                cache.delete(cache_key)
                print(f"[CACHE_DELETE] 🗑️ {cache_key} - 创建评论后清除帖子评论缓存")
                
                return format_comment(comment), False

        # 调用同步函数
        result, is_fake = await create_comment_sync()
        
        # 如果是虚拟评论，直接返回
        if is_fake:
            return result
            
        return result
    
    except HttpError as e:
        raise e
    except Exception as e:
        print(f"创建评论时发生未预期错误: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        raise HttpError(500, "系统错误，请稍后重试") 