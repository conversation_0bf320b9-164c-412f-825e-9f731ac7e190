"""
八字AI解读服务
为不同的八字分析模块提供专业的AI解读
"""

import openai
import json
from django.conf import settings
from api.utils.config import MODEL_ID
from api.utils.logging import safe_print


class BaziAIInterpreter:
    """八字AI解读器"""
    
    def __init__(self):
        """初始化AI解读器"""
        self.model_id = MODEL_ID
    
    def _call_ai_api(self, system_prompt, user_prompt, max_tokens=1000):
        """
        调用AI API获取解读
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            max_tokens: 最大token数
            
        Returns:
            str: AI解读结果
        """
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = openai.ChatCompletion.create(
                model=self.model_id,
                messages=messages,
                max_tokens=max_tokens,
                temperature=0.3  # 降低温度参数，提高八字AI解读稳定性
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            safe_print(f"AI解读调用失败: {e}")
            return f"AI解读暂时不可用: {str(e)}"
    
    def interpret_shishen(self, shishen_data, basic_info):
        """
        十神专业解读
        
        Args:
            shishen_data: 十神分析数据
            basic_info: 基本信息
            
        Returns:
            str: 十神解读结果
        """
        system_prompt = """你是专业的八字命理师，专门解读十神关系。请根据提供的十神信息，从以下角度进行专业分析：

1. 天干十神组合的意义和影响
2. 地支藏干十神的作用
3. 十神之间的生克制化关系
4. 对性格、事业、财运、感情的影响
5. 优势和需要注意的方面

请用专业但易懂的语言，条理清晰地进行解读。"""

        user_prompt = f"""请解读以下八字的十神信息：

基本信息：
- 八字：{basic_info.get('eight_char', '')}
- 性别：{basic_info.get('gender', '')}

十神分析：
天干十神：
- 年干：{shishen_data['gan_shishen']['year']}
- 月干：{shishen_data['gan_shishen']['month']}  
- 日干：{shishen_data['gan_shishen']['day']}
- 时干：{shishen_data['gan_shishen']['time']}

地支十神：
- 年支：{', '.join(shishen_data['zhi_shishen']['year'])}
- 月支：{', '.join(shishen_data['zhi_shishen']['month'])}
- 日支：{', '.join(shishen_data['zhi_shishen']['day'])}
- 时支：{', '.join(shishen_data['zhi_shishen']['time'])}

请进行专业的十神解读。"""

        return self._call_ai_api(system_prompt, user_prompt, max_tokens=1500)
    
    def interpret_shensha(self, shensha_data, basic_info):
        """
        神煞专业解读
        
        Args:
            shensha_data: 神煞分析数据
            basic_info: 基本信息
            
        Returns:
            str: 神煞解读结果
        """
        system_prompt = """你是专业的八字命理师，专门解读神煞信息。请根据提供的神煞信息，从以下角度进行分析：

1. 吉神的作用和影响
2. 凶煞的化解和注意事项
3. 神煞组合的特殊意义
4. 对人生各方面的具体影响
5. 趋吉避凶的建议

请重点关注神煞的实际作用，避免过度迷信，给出实用的指导建议。"""

        # 构建神煞信息
        shensha_summary = []
        if "pillars" in shensha_data:
            for pillar_name, pillar_info in shensha_data["pillars"].items():
                if "shenshas" in pillar_info and pillar_info["shenshas"]:
                    shensha_summary.append(f"{pillar_name}({pillar_info['ganzhi']}): {', '.join(pillar_info['shenshas'])}")
        
        categories_info = ""
        if "categories" in shensha_data:
            for category, shenshas in shensha_data["categories"].items():
                categories_info += f"{category}: {', '.join(shenshas)}\n"

        user_prompt = f"""请解读以下八字的神煞信息：

基本信息：
- 八字：{basic_info.get('eight_char', '')}
- 性别：{basic_info.get('gender', '')}

神煞分布：
{chr(10).join(shensha_summary)}

神煞分类：
{categories_info}

总计：共有{shensha_data.get('total_count', 0)}个神煞，其中{shensha_data.get('unique_count', 0)}个不重复

请进行专业的神煞解读。"""

        return self._call_ai_api(system_prompt, user_prompt, max_tokens=1500)
    
    def interpret_fortune(self, fortune_data, basic_info):
        """
        运势专业解读
        
        Args:
            fortune_data: 运势分析数据
            basic_info: 基本信息
            
        Returns:
            str: 运势解读结果
        """
        system_prompt = """你是专业的八字命理师，专门解读大运流年信息。请根据提供的运势信息，从以下角度进行分析：

1. 起运时间的意义
2. 各步大运的特点和影响
3. 人生不同阶段的运势走向
4. 重要流年的机遇和挑战
5. 人生规划和发展建议

请结合实际情况，给出具体可行的人生指导。"""

        # 构建大运信息
        dayun_info = []
        for dayun in fortune_data.get("da_yun", []):
            if dayun.get("is_pre_start"):
                dayun_info.append(f"{dayun['start_year']}-{dayun['end_year']}年({dayun['start_age']}-{dayun['end_age']}岁): 起运前")
            else:
                dayun_info.append(f"{dayun['start_year']}-{dayun['end_year']}年({dayun['start_age']}-{dayun['end_age']}岁): {dayun['ganzhi']}")

        user_prompt = f"""请解读以下八字的运势信息：

基本信息：
- 八字：{basic_info.get('eight_char', '')}
- 性别：{basic_info.get('gender', '')}

起运信息：
- 起运时间：{fortune_data['start_info']['start_year']}年{fortune_data['start_info']['start_month']}个月{fortune_data['start_info']['start_day']}天后
- 起运阳历：{fortune_data['start_info']['start_solar']}

大运信息：
{chr(10).join(dayun_info)}

请进行专业的运势解读。"""

        return self._call_ai_api(system_prompt, user_prompt, max_tokens=1500)
    
    def interpret_dishi(self, dishi_data, basic_info):
        """
        长生十二神解读
        
        Args:
            dishi_data: 长生十二神数据
            basic_info: 基本信息
            
        Returns:
            str: 长生十二神解读结果
        """
        system_prompt = """你是专业的八字命理师，专门解读长生十二神。请根据提供的地势信息，从以下角度进行分析：

1. 各柱地势的含义和影响
2. 生命力和发展潜力
3. 不同人生阶段的状态
4. 性格特质和行为模式
5. 发展建议和注意事项

请结合长生十二神的传统含义，给出现代化的实用解读。"""

        user_prompt = f"""请解读以下八字的长生十二神信息：

基本信息：
- 八字：{basic_info.get('eight_char', '')}
- 性别：{basic_info.get('gender', '')}

长生十二神：
- 年柱地势：{dishi_data.get('year_dishi', '')}
- 月柱地势：{dishi_data.get('month_dishi', '')}
- 日柱地势：{dishi_data.get('day_dishi', '')}
- 时柱地势：{dishi_data.get('time_dishi', '')}

请进行专业的长生十二神解读。"""

        return self._call_ai_api(system_prompt, user_prompt, max_tokens=1200)
    
    def interpret_nine_star(self, nine_star_data, basic_info):
        """
        九星解读
        
        Args:
            nine_star_data: 九星数据
            basic_info: 基本信息
            
        Returns:
            str: 九星解读结果
        """
        system_prompt = """你是专业的九星命理师，专门解读九星信息。请根据提供的九星信息，从以下角度进行分析：

1. 年月日时九星的特点和影响
2. 九星五行属性的作用
3. 方位和颜色的意义
4. 在不同术数体系中的吉凶
5. 实际生活中的应用建议

请结合九星的传统理论，给出实用的现代解读。"""

        user_prompt = f"""请解读以下八字的九星信息：

基本信息：
- 八字：{basic_info.get('eight_char', '')}
- 性别：{basic_info.get('gender', '')}

九星信息：
- 年九星：{nine_star_data['year_nine_star']['name']} ({nine_star_data['year_nine_star']['wuxing']})
- 月九星：{nine_star_data['month_nine_star']['name']} ({nine_star_data['month_nine_star']['wuxing']})
- 日九星：{nine_star_data['day_nine_star']['name']} ({nine_star_data['day_nine_star']['wuxing']})
- 时九星：{nine_star_data['time_nine_star']['name']} ({nine_star_data['time_nine_star']['wuxing']})

请进行专业的九星解读。"""

        return self._call_ai_api(system_prompt, user_prompt, max_tokens=1200)


# 创建全局解读器实例
bazi_ai_interpreter = BaziAIInterpreter()
