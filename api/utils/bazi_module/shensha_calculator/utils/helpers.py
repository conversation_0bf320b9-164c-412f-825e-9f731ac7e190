"""
辅助计算函数

包含各种辅助计算函数，如甲子顺序查询、地支五行查询等。
"""

from typing import Optional
from ..data.constants import JIAZI
from ..data.mappings import DIZHI_WUXING_MAP, TIANGAN_YINYANG_MAP, NIANZHI_ARRAYS, NAYIN_WUXING_MAP


def get_jiazi_order(ganzhi: str) -> int:
    """
    获取干支在六十甲子中的顺序
    
    Args:
        ganzhi: 干支组合，如"甲子"
        
    Returns:
        在六十甲子中的顺序（1-60），如果不存在返回0
    """
    try:
        return JIAZI.index(ganzhi)
    except ValueError:
        return 0


def get_dizhi_wuxing(dizhi: str) -> str:
    """
    查询地支对应的五行
    
    Args:
        dizhi: 地支，如"子"
        
    Returns:
        对应的五行，如"水"，如果不存在返回空字符串
    """
    return DIZHI_WUXING_MAP.get(dizhi, "")


def get_tiangan_yinyang(tiangan: str) -> bool:
    """
    判断天干的阴阳属性
    
    Args:
        tiangan: 天干，如"甲"
        
    Returns:
        True表示阳干，False表示阴干
    """
    return TIANGAN_YINYANG_MAP.get(tiangan, False)


def check_relation(year_zhi: str, target_zhi: str, relation_type: str) -> int:
    """
    检查年支与目标地支的关系（用于丧门、吊客、披麻）
    
    Args:
        year_zhi: 年支
        target_zhi: 目标地支
        relation_type: 关系类型（"shangmen"、"diaoke"、"pima"）
        
    Returns:
        1表示匹配，0表示不匹配
    """
    try:
        nianzhi_list = NIANZHI_ARRAYS["nianzhi"]
        target_list = NIANZHI_ARRAYS.get(relation_type)
        
        if not target_list:
            return 0
            
        index = nianzhi_list.index(year_zhi)
        return 1 if target_list[index] == target_zhi else 0
    except (ValueError, IndexError):
        return 0


def get_nayin_wuxing(nayin: str) -> str:
    """
    获取纳音对应的五行
    
    Args:
        nayin: 纳音，如"海中金"
        
    Returns:
        对应的五行，如"金"
    """
    if len(nayin) >= 3:
        return nayin[-1]  # 取最后一个字符作为五行
    return NAYIN_WUXING_MAP.get(nayin, "")


def is_in_group(zhi: str, group: list) -> bool:
    """
    判断地支是否在指定组合中
    
    Args:
        zhi: 地支
        group: 地支组合列表
        
    Returns:
        True表示在组合中，False表示不在
    """
    return zhi in group


def get_season_by_month(month_zhi: str) -> str:
    """
    根据月支获取季节
    
    Args:
        month_zhi: 月支
        
    Returns:
        季节名称（"春"、"夏"、"秋"、"冬"）
    """
    from ..data.mappings import SEASON_MONTHS
    
    for season, months in SEASON_MONTHS.items():
        if month_zhi in months:
            return season
    return ""


def has_tiangan_in_list(target_gans: list, tiangans: list) -> bool:
    """
    检查天干列表中是否包含目标天干
    
    Args:
        target_gans: 目标天干列表
        tiangans: 要检查的天干列表
        
    Returns:
        True表示包含，False表示不包含
    """
    return any(gan in target_gans for gan in tiangans)


def has_tiangan_combination(gan1: str, gan2: str, tiangans: list) -> bool:
    """
    检查天干列表中是否同时包含两个特定天干
    
    Args:
        gan1: 第一个天干
        gan2: 第二个天干
        tiangans: 要检查的天干列表
        
    Returns:
        True表示同时包含，False表示不同时包含
    """
    return gan1 in tiangans and gan2 in tiangans
