INFO 2025-07-31 00:02:36,317 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:02:39,779 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 322
INFO 2025-07-31 00:02:39,998 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 323
WARNING 2025-07-31 00:02:40,202 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:02:40,250 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:02:51,948 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:02:55,088 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 324
INFO 2025-07-31 00:02:55,149 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 325
WARNING 2025-07-31 00:02:55,460 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:02:55,507 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:03:17,400 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:03:20,544 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 326
INFO 2025-07-31 00:03:20,577 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 327
WARNING 2025-07-31 00:03:20,912 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:03:20,960 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:03:38,228 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:03:41,380 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 328
INFO 2025-07-31 00:03:41,480 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 329
WARNING 2025-07-31 00:03:41,729 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:03:41,777 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:03:57,325 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:04:00,556 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 330
INFO 2025-07-31 00:04:00,818 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 331
WARNING 2025-07-31 00:04:00,920 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:04:00,968 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:06:42,383 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:07:18,813 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 332
INFO 2025-07-31 00:07:18,960 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 333
WARNING 2025-07-31 00:07:19,159 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:07:19,207 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:07:37,769 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:07:40,953 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 334
INFO 2025-07-31 00:07:40,995 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 335
WARNING 2025-07-31 00:07:41,340 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:07:41,388 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:09:25,837 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:09:29,078 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 336
INFO 2025-07-31 00:09:29,306 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 337
WARNING 2025-07-31 00:09:29,437 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:09:29,485 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:14:54,391 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:15:14,651 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 338
INFO 2025-07-31 00:15:14,827 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 339
WARNING 2025-07-31 00:15:15,002 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:15:15,050 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:15:33,923 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:16:32,024 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 340
INFO 2025-07-31 00:16:32,219 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 341
WARNING 2025-07-31 00:16:32,594 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:16:32,642 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:17:11,620 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:17:14,955 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 342
INFO 2025-07-31 00:17:15,000 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 343
WARNING 2025-07-31 00:17:15,447 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:17:15,494 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-31 00:19:59,188 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-31 00:21:06,332 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 344
INFO 2025-07-31 00:21:06,381 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 345
INFO 2025-07-31 00:21:06,980 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 346
INFO 2025-07-31 00:21:09,963 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 347
INFO 2025-07-31 00:21:47,644 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 348
INFO 2025-07-31 00:21:47,661 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 349
INFO 2025-07-31 00:21:48,324 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 350
INFO 2025-07-31 00:21:58,583 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 351
INFO 2025-07-31 00:21:58,628 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 352
INFO 2025-07-31 00:21:59,170 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 353
INFO 2025-07-31 00:22:06,743 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 354
INFO 2025-07-31 00:22:06,808 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 355
WARNING 2025-07-31 00:22:07,239 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:22:07,287 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:22:32,750 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:22:35,969 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 356
INFO 2025-07-31 00:22:36,015 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 357
WARNING 2025-07-31 00:22:36,296 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:22:36,344 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:22:57,864 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:23:02,993 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 358
INFO 2025-07-31 00:23:03,181 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 359
INFO 2025-07-31 00:23:03,587 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 360
INFO 2025-07-31 00:25:40,262 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 361
INFO 2025-07-31 00:25:40,524 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 362
WARNING 2025-07-31 00:25:40,999 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:25:41,047 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:29:51,774 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-31 00:47:52,638 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 363
INFO 2025-07-31 00:47:52,639 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 363
INFO 2025-07-31 00:47:53,079 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 364
INFO 2025-07-31 00:48:40,121 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 365
INFO 2025-07-31 00:48:40,172 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 366
WARNING 2025-07-31 00:48:40,493 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:48:40,540 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:51:22,690 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:52:23,081 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 367
INFO 2025-07-31 00:52:23,149 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 368
INFO 2025-07-31 00:52:23,591 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 369
INFO 2025-07-31 00:52:37,612 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 370
INFO 2025-07-31 00:52:37,637 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 371
INFO 2025-07-31 00:52:38,278 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 372
INFO 2025-07-31 00:54:29,507 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 373
INFO 2025-07-31 00:54:29,527 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 374
INFO 2025-07-31 00:54:30,075 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 375
INFO 2025-07-31 00:55:12,713 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 376
INFO 2025-07-31 00:55:14,817 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 377
INFO 2025-07-31 00:55:14,860 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 378
INFO 2025-07-31 00:55:16,350 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 379
INFO 2025-07-31 00:55:16,378 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 380
WARNING 2025-07-31 00:55:16,675 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 00:55:16,723 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 00:56:44,998 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 00:58:11,850 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 381
INFO 2025-07-31 00:58:11,883 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 382
INFO 2025-07-31 00:58:12,376 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 383
INFO 2025-07-31 00:59:22,104 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 384
INFO 2025-07-31 00:59:22,144 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 385
INFO 2025-07-31 00:59:22,748 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 386
INFO 2025-07-31 01:00:15,791 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 387
INFO 2025-07-31 01:00:15,840 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 388
WARNING 2025-07-31 01:00:16,195 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:00:16,244 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:01:34,708 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:02:22,654 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 389
INFO 2025-07-31 01:02:22,719 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 390
INFO 2025-07-31 01:02:23,183 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 391
INFO 2025-07-31 01:03:10,433 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 392
INFO 2025-07-31 01:03:10,501 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 393
WARNING 2025-07-31 01:03:10,816 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:03:10,863 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:03:56,988 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
WARNING 2025-07-31 01:03:59,159 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:03:59,207 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:04:00,510 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
WARNING 2025-07-31 01:05:44,108 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:05:44,156 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:05:55,793 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:06:01,397 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 394
INFO 2025-07-31 01:06:01,418 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 395
INFO 2025-07-31 01:06:01,830 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 396
INFO 2025-07-31 01:06:04,424 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 397
INFO 2025-07-31 01:06:04,501 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 398
WARNING 2025-07-31 01:06:04,811 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:06:04,859 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:06:06,592 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:06:09,592 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 399
INFO 2025-07-31 01:06:09,630 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 400
WARNING 2025-07-31 01:06:09,928 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:06:09,976 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:06:16,360 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:06:19,186 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 401
INFO 2025-07-31 01:06:19,244 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 402
WARNING 2025-07-31 01:06:19,521 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:06:19,569 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:06:26,275 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:07:32,302 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 403
INFO 2025-07-31 01:07:32,315 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 404
INFO 2025-07-31 01:07:32,695 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 405
INFO 2025-07-31 01:11:35,782 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 406
INFO 2025-07-31 01:11:35,982 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 407
WARNING 2025-07-31 01:11:36,322 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:11:36,370 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:12:56,252 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:14:04,703 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 408
INFO 2025-07-31 01:14:04,718 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 409
INFO 2025-07-31 01:14:05,289 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 410
INFO 2025-07-31 01:14:47,379 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 411
INFO 2025-07-31 01:14:47,410 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 412
INFO 2025-07-31 01:14:48,037 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 413
INFO 2025-07-31 01:15:40,972 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 414
INFO 2025-07-31 01:15:41,143 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 415
INFO 2025-07-31 01:15:41,668 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 416
INFO 2025-07-31 01:17:27,593 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 417
INFO 2025-07-31 01:17:27,763 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 418
WARNING 2025-07-31 01:17:28,102 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:17:28,150 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:19:07,233 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:19:12,791 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 419
INFO 2025-07-31 01:19:12,866 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 420
INFO 2025-07-31 01:19:13,394 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 421
INFO 2025-07-31 01:19:52,161 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 422
INFO 2025-07-31 01:19:52,191 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 423
INFO 2025-07-31 01:19:52,925 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 424
INFO 2025-07-31 01:25:09,502 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 425
INFO 2025-07-31 01:25:09,671 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 426
WARNING 2025-07-31 01:25:10,101 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:25:10,150 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.049秒
INFO 2025-07-31 01:26:41,225 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 01:27:03,071 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 427
INFO 2025-07-31 01:27:03,182 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 428
INFO 2025-07-31 01:27:03,631 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 429
INFO 2025-07-31 01:27:53,809 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 430
INFO 2025-07-31 01:27:53,837 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 431
INFO 2025-07-31 01:27:54,503 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 432
INFO 2025-07-31 01:28:15,817 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 433
INFO 2025-07-31 01:28:15,966 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 434
INFO 2025-07-31 01:28:16,438 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 435
INFO 2025-07-31 01:28:42,142 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 436
INFO 2025-07-31 01:28:42,194 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 437
INFO 2025-07-31 01:28:42,913 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 438
INFO 2025-07-31 01:31:22,804 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 439
INFO 2025-07-31 01:31:22,943 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 440
WARNING 2025-07-31 01:31:23,298 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 01:31:23,346 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 01:33:00,627 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-31 01:33:16,019 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 441
INFO 2025-07-31 01:33:16,051 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 442
INFO 2025-07-31 01:33:16,619 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 443
INFO 2025-07-31 01:41:08,465 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 444
INFO 2025-07-31 01:41:08,467 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 445
INFO 2025-07-31 01:41:08,869 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 446
INFO 2025-07-31 02:26:20,747 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 447
INFO 2025-07-31 02:26:22,460 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 448
INFO 2025-07-31 02:26:22,953 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 449
WARNING 2025-07-31 02:26:23,151 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 02:26:23,199 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 02:34:02,265 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 02:34:26,259 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 450
INFO 2025-07-31 02:34:26,277 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 451
INFO 2025-07-31 02:34:26,810 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 452
INFO 2025-07-31 02:35:43,866 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 453
INFO 2025-07-31 02:35:43,918 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 454
INFO 2025-07-31 02:35:44,506 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 455
INFO 2025-07-31 02:37:25,040 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 456
INFO 2025-07-31 02:37:25,055 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 457
INFO 2025-07-31 02:37:25,477 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 458
INFO 2025-07-31 02:37:45,173 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 459
INFO 2025-07-31 02:37:45,221 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 460
INFO 2025-07-31 02:37:45,804 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 461
INFO 2025-07-31 02:38:18,730 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 462
INFO 2025-07-31 02:38:18,769 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 463
INFO 2025-07-31 02:38:19,302 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 464
INFO 2025-07-31 02:55:25,230 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 465
INFO 2025-07-31 02:55:25,486 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 466
WARNING 2025-07-31 02:55:25,840 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 02:55:25,888 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 02:58:06,171 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:00:16,596 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 467
INFO 2025-07-31 03:00:16,682 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 468
INFO 2025-07-31 03:00:17,037 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 469
INFO 2025-07-31 03:00:44,142 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 470
INFO 2025-07-31 03:00:44,251 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 471
WARNING 2025-07-31 03:00:44,643 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:00:44,691 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 03:01:10,328 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:01:15,608 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 472
INFO 2025-07-31 03:01:15,651 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 473
INFO 2025-07-31 03:01:16,182 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 474
INFO 2025-07-31 03:01:21,261 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 475
INFO 2025-07-31 03:01:21,322 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 476
WARNING 2025-07-31 03:01:21,631 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:01:21,678 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-31 03:01:51,283 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:02:25,229 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 477
INFO 2025-07-31 03:02:25,337 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 478
INFO 2025-07-31 03:02:25,823 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 479
INFO 2025-07-31 03:03:55,970 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 480
INFO 2025-07-31 03:03:56,144 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 481
WARNING 2025-07-31 03:03:56,392 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:03:56,440 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 03:05:24,748 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:05:54,291 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 482
INFO 2025-07-31 03:05:54,605 rate_limiter 3979481 140681208229888 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 483
WARNING 2025-07-31 03:05:54,649 base 3979481 140681208229888 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:05:54,697 base 3979481 140681208229888 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 03:06:01,407 ark_chat_consumer 3979481 140681208229888 [ARK_CHAT] WebSocket断开连接: {'close_code': 1012, 'user_id': 1, 'disconnect_reason': '未知代码(1012)'}
INFO 2025-07-31 03:06:27,975 rate_limiter 4078712 140716861530112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 484
INFO 2025-07-31 03:06:28,039 rate_limiter 4078712 140716861530112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 485
WARNING 2025-07-31 03:06:28,449 base 4078712 140716861530112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
ERROR 2025-07-31 03:06:28,450 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket连接失败: {'error_type': 'ValueError', 'error_message': "Unknown scheme for proxy URL URL('socks5h://127.0.0.1:7890')", 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/ark_chat_consumer.py", line 59, in connect\n    await self.initialize_ark_client()\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/base.py", line 117, in initialize_ark_client\n    self.client = Ark(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_client.py", line 78, in __init__\n    super().__init__(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 389, in __init__\n    self._client = http_client or SyncHttpxClientWrapper(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 54, in __init__\n    super().__init__(**kwargs)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 683, in __init__\n    proxy_map = self._get_proxy_map(proxies or proxy, allow_env_proxies)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 217, in _get_proxy_map\n    return {\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 218, in <dictcomp>\n    key: None if url is None else Proxy(url=url)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_config.py", line 336, in __init__\n    raise ValueError(f"Unknown scheme for proxy URL {url!r}")\nValueError: Unknown scheme for proxy URL URL(\'socks5h://127.0.0.1:7890\')\n', 'scope_info': {'path': '/api/ws/deepseek_chat/', 'method': None, 'query_string': '', 'headers': {b'upgrade': b'websocket', b'connection': b'upgrade', b'host': b'riyuetcm.com', b'x-real-ip': b'*************', b'x-forwarded-for': b'**************, *************', b'x-forwarded-proto': b'https', b'user-agent': b'okhttp/3.12.11', b'authorization': b'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1NiwiZXhwIjoxNzUzOTAzNDM5fQ.ebRqeU9IySic6iL1vOV721L9_hEu196dzdMlPpkQ0wM', b'origin': b'http://localhost', b'sec-websocket-key': b'oixCSJoT3/DVWqCnnjxl+Q==', b'sec-websocket-version': b'13', b'accept-encoding': b'gzip', b'x-request-id': b'790F75F1-7C0E-4D9D-99FD-F995FA4F01F2', b'x-via': b'l1=lHXrsNRhpGeh1Hmq'}}}
ERROR 2025-07-31 03:06:28,450 base 4078712 140716861530112 ❌ [ARK_CHAT] WebSocket连接建立失败，用户ID: 1，耗时: 0.000秒
INFO 2025-07-31 03:06:28,451 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
WARNING 2025-07-31 03:06:29,787 base 4078712 140716861530112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
ERROR 2025-07-31 03:06:29,788 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket连接失败: {'error_type': 'ValueError', 'error_message': "Unknown scheme for proxy URL URL('socks5h://127.0.0.1:7890')", 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/ark_chat_consumer.py", line 59, in connect\n    await self.initialize_ark_client()\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/base.py", line 117, in initialize_ark_client\n    self.client = Ark(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_client.py", line 78, in __init__\n    super().__init__(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 389, in __init__\n    self._client = http_client or SyncHttpxClientWrapper(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 54, in __init__\n    super().__init__(**kwargs)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 683, in __init__\n    proxy_map = self._get_proxy_map(proxies or proxy, allow_env_proxies)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 217, in _get_proxy_map\n    return {\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 218, in <dictcomp>\n    key: None if url is None else Proxy(url=url)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_config.py", line 336, in __init__\n    raise ValueError(f"Unknown scheme for proxy URL {url!r}")\nValueError: Unknown scheme for proxy URL URL(\'socks5h://127.0.0.1:7890\')\n', 'scope_info': {'path': '/api/ws/deepseek_chat/', 'method': None, 'query_string': '', 'headers': {b'upgrade': b'websocket', b'connection': b'upgrade', b'host': b'riyuetcm.com', b'x-real-ip': b'*************', b'x-forwarded-for': b'**************, *************', b'x-forwarded-proto': b'https', b'user-agent': b'okhttp/3.12.11', b'authorization': b'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1NiwiZXhwIjoxNzUzOTAzNDM5fQ.ebRqeU9IySic6iL1vOV721L9_hEu196dzdMlPpkQ0wM', b'origin': b'http://localhost', b'sec-websocket-key': b'6qdVW647HQes2kff1ARXXg==', b'sec-websocket-version': b'13', b'accept-encoding': b'gzip', b'x-request-id': b'2B14EEA6-3965-412F-8BB3-7D6CB310EBEC', b'x-via': b'l1=lHXrsNRhpGeh1Hmq'}}}
ERROR 2025-07-31 03:06:29,788 base 4078712 140716861530112 ❌ [ARK_CHAT] WebSocket连接建立失败，用户ID: 1，耗时: 0.000秒
INFO 2025-07-31 03:06:29,789 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-31 03:06:53,013 rate_limiter 4078712 140716861530112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 486
INFO 2025-07-31 03:06:53,068 rate_limiter 4078712 140716861530112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 487
WARNING 2025-07-31 03:06:53,471 base 4078712 140716861530112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
ERROR 2025-07-31 03:06:53,472 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket连接失败: {'error_type': 'ValueError', 'error_message': "Unknown scheme for proxy URL URL('socks5h://127.0.0.1:7890')", 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/ark_chat_consumer.py", line 59, in connect\n    await self.initialize_ark_client()\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/base.py", line 117, in initialize_ark_client\n    self.client = Ark(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_client.py", line 78, in __init__\n    super().__init__(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 389, in __init__\n    self._client = http_client or SyncHttpxClientWrapper(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 54, in __init__\n    super().__init__(**kwargs)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 683, in __init__\n    proxy_map = self._get_proxy_map(proxies or proxy, allow_env_proxies)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 217, in _get_proxy_map\n    return {\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 218, in <dictcomp>\n    key: None if url is None else Proxy(url=url)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_config.py", line 336, in __init__\n    raise ValueError(f"Unknown scheme for proxy URL {url!r}")\nValueError: Unknown scheme for proxy URL URL(\'socks5h://127.0.0.1:7890\')\n', 'scope_info': {'path': '/api/ws/deepseek_chat/', 'method': None, 'query_string': '', 'headers': {b'upgrade': b'websocket', b'connection': b'upgrade', b'host': b'riyuetcm.com', b'x-real-ip': b'*************', b'x-forwarded-for': b'**************, *************', b'x-forwarded-proto': b'https', b'user-agent': b'okhttp/3.12.11', b'authorization': b'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1NiwiZXhwIjoxNzUzOTAzNDM5fQ.ebRqeU9IySic6iL1vOV721L9_hEu196dzdMlPpkQ0wM', b'origin': b'http://localhost', b'sec-websocket-key': b'3WnUFG0214mjnC2HRYupAg==', b'sec-websocket-version': b'13', b'accept-encoding': b'gzip', b'x-request-id': b'A174102E-B90A-4D27-B9A4-132BD6C8D68A', b'x-via': b'l1=lHXrsNRhpGeh1Hmq'}}}
ERROR 2025-07-31 03:06:53,472 base 4078712 140716861530112 ❌ [ARK_CHAT] WebSocket连接建立失败，用户ID: 1，耗时: 0.000秒
INFO 2025-07-31 03:06:53,473 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
WARNING 2025-07-31 03:06:54,805 base 4078712 140716861530112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
ERROR 2025-07-31 03:06:54,806 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket连接失败: {'error_type': 'ValueError', 'error_message': "Unknown scheme for proxy URL URL('socks5h://127.0.0.1:7890')", 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/ark_chat_consumer.py", line 59, in connect\n    await self.initialize_ark_client()\n  File "/home/<USER>/riyue-llm/myproject/demo_api/api/consumers/base.py", line 117, in initialize_ark_client\n    self.client = Ark(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_client.py", line 78, in __init__\n    super().__init__(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 389, in __init__\n    self._client = http_client or SyncHttpxClientWrapper(\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/volcenginesdkarkruntime/_base_client.py", line 54, in __init__\n    super().__init__(**kwargs)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 683, in __init__\n    proxy_map = self._get_proxy_map(proxies or proxy, allow_env_proxies)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 217, in _get_proxy_map\n    return {\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_client.py", line 218, in <dictcomp>\n    key: None if url is None else Proxy(url=url)\n  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/httpx/_config.py", line 336, in __init__\n    raise ValueError(f"Unknown scheme for proxy URL {url!r}")\nValueError: Unknown scheme for proxy URL URL(\'socks5h://127.0.0.1:7890\')\n', 'scope_info': {'path': '/api/ws/deepseek_chat/', 'method': None, 'query_string': '', 'headers': {b'upgrade': b'websocket', b'connection': b'upgrade', b'host': b'riyuetcm.com', b'x-real-ip': b'*************', b'x-forwarded-for': b'**************, *************', b'x-forwarded-proto': b'https', b'user-agent': b'okhttp/3.12.11', b'authorization': b'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1NiwiZXhwIjoxNzUzOTAzNDM5fQ.ebRqeU9IySic6iL1vOV721L9_hEu196dzdMlPpkQ0wM', b'origin': b'http://localhost', b'sec-websocket-key': b'tyB2xMny9qdmXKbC6gJbkg==', b'sec-websocket-version': b'13', b'accept-encoding': b'gzip', b'x-request-id': b'A45198A7-84AE-41D0-ACB9-C5961B785B5F', b'x-via': b'l1=lHXrsNRhpGeh1Hmq'}}}
ERROR 2025-07-31 03:06:54,806 base 4078712 140716861530112 ❌ [ARK_CHAT] WebSocket连接建立失败，用户ID: 1，耗时: 0.000秒
INFO 2025-07-31 03:06:54,807 ark_chat_consumer 4078712 140716861530112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1006, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-31 03:07:24,243 rate_limiter 4079093 140704766631936 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 488
INFO 2025-07-31 03:07:24,309 rate_limiter 4079093 140704766631936 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 489
WARNING 2025-07-31 03:07:24,618 base 4079093 140704766631936 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:07:24,666 base 4079093 140704766631936 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 03:09:40,567 rate_limiter 4079093 140704766631936 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 490
INFO 2025-07-31 03:14:25,705 ark_chat_consumer 4079093 140704766631936 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:15:11,188 rate_limiter 4079093 140704766631936 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 491
INFO 2025-07-31 03:15:11,356 rate_limiter 4079093 140704766631936 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 492
WARNING 2025-07-31 03:15:11,739 base 4079093 140704766631936 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:15:11,785 base 4079093 140704766631936 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-31 03:15:33,744 ark_chat_consumer 4079093 140704766631936 [ARK_CHAT] WebSocket断开连接: {'close_code': 1012, 'user_id': 1, 'disconnect_reason': '未知代码(1012)'}
INFO 2025-07-31 03:15:59,860 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 493
INFO 2025-07-31 03:15:59,869 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 494
INFO 2025-07-31 03:16:00,442 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 495
INFO 2025-07-31 03:16:00,584 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 496
INFO 2025-07-31 03:16:00,611 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 497
WARNING 2025-07-31 03:16:00,998 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:16:01,046 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-31 03:17:04,607 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:17:07,906 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 498
INFO 2025-07-31 03:17:08,197 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 499
WARNING 2025-07-31 03:17:08,259 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:17:08,305 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:20:07,612 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:20:10,875 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 500
INFO 2025-07-31 03:20:11,067 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 501
WARNING 2025-07-31 03:20:11,216 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:20:11,263 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:20:44,636 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:20:47,924 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 502
INFO 2025-07-31 03:20:47,938 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 503
WARNING 2025-07-31 03:20:48,305 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:20:48,351 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:21:24,183 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:21:27,613 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 504
INFO 2025-07-31 03:21:27,625 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 505
WARNING 2025-07-31 03:21:27,968 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:21:28,014 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:21:50,084 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:21:53,182 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 506
INFO 2025-07-31 03:21:53,211 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 507
WARNING 2025-07-31 03:21:53,557 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:21:53,603 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:22:06,179 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:22:19,569 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 508
INFO 2025-07-31 03:22:19,600 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 509
WARNING 2025-07-31 03:22:19,929 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:22:19,975 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:22:44,086 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:23:03,339 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 510
INFO 2025-07-31 03:23:03,366 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 511
WARNING 2025-07-31 03:23:03,693 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:23:03,739 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:24:14,507 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
WARNING 2025-07-31 03:24:18,399 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:24:18,445 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-31 03:27:31,061 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:27:34,349 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 512
INFO 2025-07-31 03:27:34,530 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 513
WARNING 2025-07-31 03:27:34,795 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:27:34,841 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:27:47,743 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-31 03:31:48,721 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 514
INFO 2025-07-31 03:31:48,748 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 515
INFO 2025-07-31 03:31:49,328 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 516
INFO 2025-07-31 03:32:05,858 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 517
INFO 2025-07-31 03:32:05,938 rate_limiter 4081150 140236479066112 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 518
WARNING 2025-07-31 03:32:06,363 base 4081150 140236479066112 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-31 03:32:06,410 base 4081150 140236479066112 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-31 03:36:13,499 ark_chat_consumer 4081150 140236479066112 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
