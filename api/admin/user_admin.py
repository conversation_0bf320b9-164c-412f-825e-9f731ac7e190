"""
用户相关模型的Admin配置
包括UserInfo、BannedToken和BannedAccount的admin设置
"""

from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.contrib import messages
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.db import models
import json
from datetime import timedelta, datetime

from ..models import UserInfo
from ..models.user_models import BannedToken, BannedAccount


@admin.register(UserInfo)
class UserInfoAdmin(admin.ModelAdmin):
    # 定义在列表页显示的字段
    list_display = ('id', 'nickname', 'wx_phone_new', 'wx_openid_new', 'is_member', 'is_banned', 'created_at', 'last_login_date')
    
    # 定义可以搜索的字段
    search_fields = ('nickname', 'wx_phone_new', 'wx_openid_new')
    
    # 定义可以过滤的字段
    list_filter = ('is_member', 'is_banned', 'created_at', 'last_login_date')
    
    # 定义每页显示的记录数
    list_per_page = 50
    
    # 定义可以编辑的字段
    list_editable = ('is_member',)
    
    # 定义默认排序字段
    ordering = ('-created_at',)
    
    # 添加自定义视图
    change_list_template = 'admin/userinfo_change_list.html'
    
    # 添加解除封禁操作
    actions = ['unban_users']
    
    def unban_users(self, request, queryset):
        # 解除所选用户的封禁
        updated = queryset.filter(is_banned=True).update(
            is_banned=False,
            ban_reason=None,
            ban_until=None
        )
        
        if updated:
            # 同时删除关联的token封禁和账号封禁记录
            for user in queryset.filter(is_banned=False):
                try:
                    # 删除与用户相关的所有BannedToken记录
                    from ..models.user_models import BannedToken, BannedAccount
                    # 删除直接关联到用户的token
                    tokens_deleted = BannedToken.objects.filter(user=user).delete()[0]
                    
                    # 删除与用户手机号和微信ID相关的token
                    if user.wx_phone_new:
                        BannedToken.objects.filter(related_phone=user.wx_phone_new).delete()
                    if user.wx_openid_new:
                        BannedToken.objects.filter(related_openid=user.wx_openid_new).delete()
                    
                    # 删除与用户相关的BannedAccount记录
                    accounts_deleted = 0
                    query = models.Q()
                    if user.wx_phone_new:
                        query |= models.Q(phone_number=user.wx_phone_new)
                    if user.wx_openid_new:
                        query |= models.Q(wx_openid=user.wx_openid_new)
                    if user.wx_unionid_new:
                        query |= models.Q(wx_unionid=user.wx_unionid_new)
                    query |= models.Q(user_id=user.id)
                    
                    if query:
                        accounts_deleted = BannedAccount.objects.filter(query).delete()[0]
                    
                    print(f"用户 {user.id} 解除封禁: 删除了 {tokens_deleted} 个Token记录和 {accounts_deleted} 个账号封禁记录")
                except Exception as e:
                    print(f"解除用户 {user.id} 的封禁记录删除失败: {e}")
            
            messages.success(request, f"成功解除 {updated} 个用户的封禁状态，并彻底删除了相关的封禁记录")
        else:
            messages.info(request, "所选用户中没有被封禁的用户")
    
    unban_users.short_description = "解除选中用户的封禁"
    
    def changelist_view(self, request, extra_context=None):
        # 获取过去60天的统计数据（从30天改为60天）
        sixty_days_ago = timezone.now() - timedelta(days=60)

        # 日活统计 - 基于last_login_date字段
        daily_active_users = UserInfo.objects.filter(
            last_login_date__gte=sixty_days_ago
        ).annotate(
            login_date=models.functions.TruncDate('last_login_date')
        ).values('login_date').annotate(
            count=models.Count('id')
        ).order_by('login_date')

        # 新增用户统计
        new_users = UserInfo.objects.filter(
            created_at__gte=sixty_days_ago
        ).annotate(
            register_date=models.functions.TruncDate('created_at')
        ).values('register_date').annotate(
            count=models.Count('id')
        ).order_by('register_date')

        # 转化为图表数据格式
        dau_data = [
            {'date': item['login_date'].strftime('%Y-%m-%d'), 'count': item['count']}
            for item in daily_active_users
        ]

        new_user_data = [
            {'date': item['register_date'].strftime('%Y-%m-%d'), 'count': item['count']}
            for item in new_users
        ]

        # 计算每周活跃用户数（WAU）
        weeks_data = []
        current_date = timezone.now().date()
        for i in range(8):  # 显示8周数据
            start_date = current_date - timedelta(days=current_date.weekday(), weeks=i)
            end_date = start_date + timedelta(days=6)
            count = UserInfo.objects.filter(
                last_login_date__date__gte=start_date,
                last_login_date__date__lte=end_date
            ).count()
            weeks_data.append({
                'week': f'{start_date.strftime("%m.%d")}-{end_date.strftime("%m.%d")}',
                'count': count
            })
        weeks_data.reverse()  # 按时间顺序排列

        # 总用户数
        total_users = UserInfo.objects.count()
        # 会员用户数
        member_users = UserInfo.objects.filter(is_member=True).count()
        # 最近60天活跃用户
        active_users_60d = UserInfo.objects.filter(last_login_date__gte=sixty_days_ago).count()
        # 最近7天活跃用户
        seven_days_ago = timezone.now() - timedelta(days=7)
        active_users_7d = UserInfo.objects.filter(last_login_date__gte=seven_days_ago).count()

        # 🔥 当日关键指标 - 您最关心的数据
        # 处理时区问题：使用东八区时间
        import pytz
        beijing_tz = pytz.timezone('Asia/Shanghai')
        now_beijing = timezone.now().astimezone(beijing_tz)
        today = now_beijing.date()
        yesterday = today - timedelta(days=1)

        print(f"当前北京时间: {now_beijing}")
        print(f"查询日期: 今日={today}, 昨日={yesterday}")

        # 当日登录人数 (基于last_login_date)
        today_login_users = UserInfo.objects.filter(
            last_login_date__date=today
        ).count()

        # 昨日登录人数 (用于对比)
        yesterday_login_users = UserInfo.objects.filter(
            last_login_date__date=yesterday
        ).count()

        # 当日新增用户数 (基于created_at)
        today_new_users = UserInfo.objects.filter(
            created_at__date=today
        ).count()

        # 昨日新增用户数 (用于对比)
        yesterday_new_users = UserInfo.objects.filter(
            created_at__date=yesterday
        ).count()

        # 🔥 当日新增会员数 - 基于支付成功时间 (处理时区问题)
        from api.models import PaymentOrder_Wechat, Order
        from django.db.models import Sum

        # 计算今日的时间范围 (东八区转UTC，因为数据库存储的是UTC时间)
        today_start_beijing = beijing_tz.localize(datetime.combine(today, datetime.min.time()))
        today_end_beijing = beijing_tz.localize(datetime.combine(today, datetime.max.time()))
        yesterday_start_beijing = beijing_tz.localize(datetime.combine(yesterday, datetime.min.time()))
        yesterday_end_beijing = beijing_tz.localize(datetime.combine(yesterday, datetime.max.time()))

        # 转换为UTC时间进行数据库查询
        today_start = today_start_beijing.astimezone(pytz.UTC)
        today_end = today_end_beijing.astimezone(pytz.UTC)
        yesterday_start = yesterday_start_beijing.astimezone(pytz.UTC)
        yesterday_end = yesterday_end_beijing.astimezone(pytz.UTC)

        print(f"今日北京时间范围: {today_start_beijing} ~ {today_end_beijing}")
        print(f"今日UTC时间范围: {today_start} ~ {today_end}")
        print(f"昨日北京时间范围: {yesterday_start_beijing} ~ {yesterday_end_beijing}")
        print(f"昨日UTC时间范围: {yesterday_start} ~ {yesterday_end}")

        # 微信支付新增会员 (基于success_time，使用时间范围查询)
        today_wechat_orders = PaymentOrder_Wechat.objects.filter(
            success_time__gte=today_start,
            success_time__lte=today_end,
            trade_state='SUCCESS'
        )
        today_new_members_wechat = today_wechat_orders.count()
        print(f"今日微信支付订单数: {today_new_members_wechat}")

        # 支付宝新增会员 (基于pay_time，使用时间范围查询)
        today_alipay_orders = Order.objects.filter(
            pay_time__gte=today_start,
            pay_time__lte=today_end,
            status='Completed'
        )
        today_new_members_alipay = today_alipay_orders.count()
        print(f"今日支付宝支付订单数: {today_new_members_alipay}")

        # 当日新增会员总数
        today_new_members_total = today_new_members_wechat + today_new_members_alipay

        # 🔥 当日付费金额统计
        # 微信支付金额 (amount字段单位是分，需要转换为元)
        today_wechat_amount = today_wechat_orders.aggregate(
            total=Sum('amount')
        )['total'] or 0
        today_wechat_amount_yuan = today_wechat_amount / 100  # 转换为元

        # 支付宝支付金额 (total_amount字段单位是元)
        today_alipay_amount_yuan = today_alipay_orders.aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        # 当日总付费金额
        today_total_amount_yuan = today_wechat_amount_yuan + float(today_alipay_amount_yuan)

        # 昨日新增会员数 (用于对比，使用时间范围查询)
        yesterday_wechat_orders = PaymentOrder_Wechat.objects.filter(
            success_time__gte=yesterday_start,
            success_time__lte=yesterday_end,
            trade_state='SUCCESS'
        )
        yesterday_new_members_wechat = yesterday_wechat_orders.count()

        yesterday_alipay_orders = Order.objects.filter(
            pay_time__gte=yesterday_start,
            pay_time__lte=yesterday_end,
            status='Completed'
        )
        yesterday_new_members_alipay = yesterday_alipay_orders.count()

        yesterday_new_members_total = yesterday_new_members_wechat + yesterday_new_members_alipay

        # 🔥 昨日付费金额统计 (用于对比)
        yesterday_wechat_amount = yesterday_wechat_orders.aggregate(
            total=Sum('amount')
        )['total'] or 0
        yesterday_wechat_amount_yuan = yesterday_wechat_amount / 100

        yesterday_alipay_amount_yuan = yesterday_alipay_orders.aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        yesterday_total_amount_yuan = yesterday_wechat_amount_yuan + float(yesterday_alipay_amount_yuan)

        # 🔥 当日活跃会员数 (登录的会员用户)
        today_active_members = UserInfo.objects.filter(
            last_login_date__date=today,
            is_member=True
        ).count()

        # 🔥 当日用户登录时间分布统计
        # 获取今日所有登录用户的登录时间
        today_login_times = UserInfo.objects.filter(
            last_login_date__date=today
        ).values_list('last_login_date', flat=True)

        # 按小时统计登录分布
        hourly_login_distribution = {}
        for hour in range(24):
            hourly_login_distribution[hour] = 0

        for login_time in today_login_times:
            if login_time:
                # 转换为北京时间
                beijing_time = login_time.astimezone(beijing_tz)
                hour = beijing_time.hour
                hourly_login_distribution[hour] += 1

        # 转换为图表数据格式
        login_time_chart_data = [
            {
                'hour': f"{hour:02d}:00",
                'count': count,
                'hour_num': hour
            }
            for hour, count in hourly_login_distribution.items()
        ]

        # 计算登录高峰时段
        peak_hour = max(hourly_login_distribution.items(), key=lambda x: x[1])
        peak_hour_info = {
            'hour': f"{peak_hour[0]:02d}:00-{peak_hour[0]+1:02d}:00",
            'count': peak_hour[1],
            'percentage': round(peak_hour[1] / max(1, today_login_users) * 100, 1)
        }

        # 计算活跃时段分布
        morning_count = sum(hourly_login_distribution[h] for h in range(6, 12))    # 6-12点
        afternoon_count = sum(hourly_login_distribution[h] for h in range(12, 18)) # 12-18点
        evening_count = sum(hourly_login_distribution[h] for h in range(18, 24))   # 18-24点
        night_count = sum(hourly_login_distribution[h] for h in range(0, 6))       # 0-6点

        time_period_distribution = {
            'morning': {'count': morning_count, 'label': '上午(6-12点)'},
            'afternoon': {'count': afternoon_count, 'label': '下午(12-18点)'},
            'evening': {'count': evening_count, 'label': '晚上(18-24点)'},
            'night': {'count': night_count, 'label': '深夜(0-6点)'}
        }

        # 计算增长率
        login_growth_rate = 0
        if yesterday_login_users > 0:
            login_growth_rate = round((today_login_users - yesterday_login_users) / yesterday_login_users * 100, 1)

        new_user_growth_rate = 0
        if yesterday_new_users > 0:
            new_user_growth_rate = round((today_new_users - yesterday_new_users) / yesterday_new_users * 100, 1)

        new_member_growth_rate = 0
        if yesterday_new_members_total > 0:
            new_member_growth_rate = round((today_new_members_total - yesterday_new_members_total) / yesterday_new_members_total * 100, 1)

        # 付费金额增长率计算
        payment_growth_rate = 0
        if yesterday_total_amount_yuan > 0:
            payment_growth_rate = round((today_total_amount_yuan - yesterday_total_amount_yuan) / yesterday_total_amount_yuan * 100, 1)

        # 会员活跃率计算
        member_active_rate = 0
        if today_login_users > 0:
            member_active_rate = round(today_active_members / today_login_users * 100, 1)

        # 🔥 用户付费金额分布统计 (基于会员类型，使用时间范围查询)
        # 微信支付分布
        wechat_payment_distribution = PaymentOrder_Wechat.objects.filter(
            success_time__gte=today_start,
            success_time__lte=today_end,
            trade_state='SUCCESS'
        ).values('attach').annotate(
            count=models.Count('id'),
            total_amount=Sum('amount')
        ).order_by('attach')

        # 支付宝支付分布
        alipay_payment_distribution = Order.objects.filter(
            pay_time__gte=today_start,
            pay_time__lte=today_end,
            status='Completed'
        ).values('attach').annotate(
            count=models.Count('id'),
            total_amount=Sum('total_amount')
        ).order_by('attach')

        # 合并分布数据
        payment_distribution = {}
        for item in wechat_payment_distribution:
            attach = item['attach']
            payment_distribution[attach] = {
                'count': item['count'],
                'amount': item['total_amount'] / 100,  # 转换为元
                'platform': 'wechat'
            }

        for item in alipay_payment_distribution:
            attach = item['attach']
            if attach in payment_distribution:
                payment_distribution[attach]['count'] += item['count']
                payment_distribution[attach]['amount'] += float(item['total_amount'])
                payment_distribution[attach]['platform'] = 'mixed'
            else:
                payment_distribution[attach] = {
                    'count': item['count'],
                    'amount': float(item['total_amount']),
                    'platform': 'alipay'
                }

        # 🔥 数据库健康状态检查 (同步方式，避免异步混用)
        db_health_status = {}
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    db_health_status = {
                        'status': 'healthy',
                        'message': '数据库连接正常',
                        'timestamp': timezone.now().strftime('%H:%M:%S')
                    }
                else:
                    db_health_status = {
                        'status': 'warning',
                        'message': '数据库查询结果异常',
                        'timestamp': timezone.now().strftime('%H:%M:%S')
                    }
        except Exception as e:
            db_health_status = {
                'status': 'error',
                'message': f'数据库连接失败: {str(e)}',
                'timestamp': timezone.now().strftime('%H:%M:%S')
            }

        # 🔥 系统资源监控 (简单实用)
        import psutil
        import os
        system_stats = {}
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100

            system_stats = {
                'cpu_percent': round(cpu_percent, 1),
                'memory_percent': round(memory_percent, 1),
                'disk_percent': round(disk_percent, 1),
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'disk_free_gb': round(disk.free / (1024**3), 2)
            }
        except Exception as e:
            system_stats = {
                'error': f'系统监控获取失败: {str(e)}'
            }

        # 🔥 今日业务关键指标
        # 问卷完成数 (如果有相关模型)
        business_stats = {}
        try:
            # 这里可以根据您的实际业务模型调整
            from api.models import DailyTCMQuizRecord
            today_quiz_count = DailyTCMQuizRecord.objects.filter(
                created_at__gte=today_start,
                created_at__lte=today_end
            ).count()

            business_stats['today_quiz_count'] = today_quiz_count

            # 付费转化率计算
            if today_new_users > 0:
                conversion_rate = round((today_new_members_total / today_new_users) * 100, 1)
            else:
                conversion_rate = 0
            business_stats['conversion_rate'] = conversion_rate

            # 🔥 用户平均付费金额计算
            if today_new_members_total > 0:
                avg_payment_amount = round(today_total_amount_yuan / today_new_members_total, 2)
            else:
                avg_payment_amount = 0
            business_stats['avg_payment_amount'] = avg_payment_amount

            # 🔥 付费用户相关统计
            business_stats['today_total_payment'] = round(today_total_amount_yuan, 2)
            business_stats['today_paying_users'] = today_new_members_total
            business_stats['wechat_avg_amount'] = round(today_wechat_amount_yuan / max(1, today_new_members_wechat), 2)
            business_stats['alipay_avg_amount'] = round(float(today_alipay_amount_yuan) / max(1, today_new_members_alipay), 2)

        except Exception as e:
            business_stats = {
                'error': f'业务指标获取失败: {str(e)}'
            }

        # 🔥 API性能监控 - 使用统一的性能监控工具
        # ✅ 安全性说明：此功能只读取日志文件，不涉及数据库操作，不会影响连接池
        api_performance_stats = {}
        try:
            from api.utils.api_performance_monitor import APIPerformanceAnalyzer
            import time
            start_time = time.time()

            api_performance_stats = APIPerformanceAnalyzer.analyze_performance_logs(
                today, 'logs/api_performance.log'
            )
            # 如果性能日志没有数据，尝试从info.log获取
            if api_performance_stats.get('total_calls', 0) == 0:
                api_performance_stats = APIPerformanceAnalyzer.analyze_performance_logs(
                    today, 'logs/info.log'
                )

            # 记录性能监控本身的耗时
            analysis_time = time.time() - start_time
            api_performance_stats['analysis_time'] = round(analysis_time * 1000, 2)  # 毫秒

        except Exception as e:
            api_performance_stats = {
                'error': f'API性能分析失败: {str(e)}'
            }
        
        # 准备额外的上下文数据
        if extra_context is None:
            extra_context = {}

        extra_context.update({
            'dau_data': mark_safe(json.dumps(dau_data)),
            'new_user_data': mark_safe(json.dumps(new_user_data)),
            'weeks_data': mark_safe(json.dumps(weeks_data)),
            'total_users': total_users,
            'member_users': member_users,
            'active_users_60d': active_users_60d,
            'active_users_7d': active_users_7d,

            # 🔥 当日关键指标 - 您最关心的数据
            'today_login_users': today_login_users,
            'yesterday_login_users': yesterday_login_users,
            'login_growth_rate': login_growth_rate,

            'today_new_users': today_new_users,
            'yesterday_new_users': yesterday_new_users,
            'new_user_growth_rate': new_user_growth_rate,

            'today_new_members_total': today_new_members_total,
            'today_new_members_wechat': today_new_members_wechat,
            'today_new_members_alipay': today_new_members_alipay,
            'yesterday_new_members_total': yesterday_new_members_total,
            'new_member_growth_rate': new_member_growth_rate,

            # 🔥 付费金额数据
            'today_total_amount_yuan': round(today_total_amount_yuan, 2),
            'today_wechat_amount_yuan': round(today_wechat_amount_yuan, 2),
            'today_alipay_amount_yuan': round(float(today_alipay_amount_yuan), 2),
            'yesterday_total_amount_yuan': round(yesterday_total_amount_yuan, 2),
            'payment_growth_rate': payment_growth_rate,

            'today_active_members': today_active_members,
            'member_active_rate': member_active_rate,

            # 🔥 付费金额分布数据
            'payment_distribution': payment_distribution,

            # 🔥 数据库健康状态
            'db_health_status': db_health_status,

            # 🔥 系统资源监控
            'system_stats': system_stats,

            # 🔥 业务关键指标
            'business_stats': business_stats,

            # 🔥 API性能监控
            'api_performance_stats': api_performance_stats,

            # 🔥 用户登录时间分布统计
            'login_time_chart_data': login_time_chart_data,
            'peak_hour_info': peak_hour_info,
            'time_period_distribution': time_period_distribution,
            'hourly_login_distribution': hourly_login_distribution,

            # 保持原有数据
            'today_active_users': today_login_users,  # 兼容原模板
            'yesterday_active_users': yesterday_login_users,  # 兼容原模板
            'member_ratio': round(member_users / total_users * 100, 2) if total_users else 0
        })
        
        # 调用父类方法
        return super().changelist_view(request, extra_context=extra_context)





# 封禁Token管理
class BannedTokenResource(resources.ModelResource):
    class Meta:
        model = BannedToken
        fields = ('id', 'token', 'user__nickname', 'user__wx_phone_new', 'reason', 'detail', 
                 'expire_at', 'is_permanent', 'related_phone', 'related_openid', 
                 'ip_address', 'admin_operator', 'created_at')
        export_order = fields


@admin.register(BannedToken)
class BannedTokenAdmin(ImportExportModelAdmin):
    resource_class = BannedTokenResource
    list_display = ('token_preview', 'user_display', 'reason_display', 'expire_status', 
                   'ip_address', 'admin_operator', 'created_at')
    list_filter = ('reason', 'is_permanent', 'created_at')
    search_fields = ('token', 'detail', 'related_phone', 'related_openid', 'ip_address', 
                    'user__nickname', 'user__wx_phone_new')
    readonly_fields = ('created_at',)
    autocomplete_fields = ['user']
    actions = ['remove_ban', 'make_permanent', 'extend_ban_30_days']
    
    # 添加调试语句，记录请求过程
    def changelist_view(self, request, extra_context=None):
        try:
            return super().changelist_view(request, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedTokenAdmin错误: {error_msg}")
            # 记录到日志
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedTokenAdmin错误: {error_msg}")
            # 重新抛出异常，让Django处理
            raise
    
    def add_view(self, request, form_url='', extra_context=None):
        try:
            return super().add_view(request, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedTokenAdmin添加视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedTokenAdmin添加视图错误: {error_msg}")
            raise
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        try:
            return super().change_view(request, object_id, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedTokenAdmin修改视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedTokenAdmin修改视图错误: {error_msg}")
            raise
    
    fieldsets = (
        ('封禁信息', {
            'fields': ('token', 'user', 'reason', 'detail', 'admin_operator')
        }),
        ('时间设置', {
            'fields': ('expire_at', 'is_permanent', 'created_at')
        }),
        ('关联信息', {
            'fields': ('related_phone', 'related_openid', 'ip_address')
        }),
    )
    
    def token_preview(self, obj):
        """显示Token的前10个字符"""
        if obj.token:
            return f"{obj.token[:10]}..."
        return "无Token"
    token_preview.short_description = 'Token'
    
    def user_display(self, obj):
        """显示用户信息"""
        if obj.user:
            return f"{obj.user.nickname or '未知'} (ID: {obj.user.id})"
        elif obj.related_phone:
            return f"手机: {obj.related_phone}"
        elif obj.related_openid:
            return f"OpenID: {obj.related_openid[:8]}..."
        return "未关联用户"
    user_display.short_description = '用户'
    
    def reason_display(self, obj):
        """显示封禁原因"""
        return obj.get_reason_display()
    reason_display.short_description = '原因'
    
    def expire_status(self, obj):
        """显示封禁状态"""
        if obj.is_permanent:
            return "永久封禁"
        elif obj.expire_at:
            if obj.expire_at > timezone.now():
                days_left = (obj.expire_at - timezone.now()).days
                return f"还有{days_left}天到期"
            else:
                return "已过期"
        return "未设置"
    expire_status.short_description = '状态'
    
    def remove_ban(self, request, queryset):
        """解除封禁 - 彻底删除记录"""
        count = queryset.count()
        # 获取相关用户，用于记录日志
        user_ids = []
        for token in queryset:
            if token.user:
                user_ids.append(str(token.user.id))
        
        # 删除记录
        deleted = queryset.delete()[0]
        
        user_info = f"(用户ID: {', '.join(user_ids)})" if user_ids else ""
        messages.success(request, f"成功删除 {deleted} 个Token的封禁记录 {user_info}")
    remove_ban.short_description = "解除并删除选中Token的封禁"
    
    def make_permanent(self, request, queryset):
        """设为永久封禁"""
        updated = queryset.update(is_permanent=True)
        messages.success(request, f"成功将{updated}个Token设为永久封禁")
    make_permanent.short_description = "将选中Token设为永久封禁"
    
    def extend_ban_30_days(self, request, queryset):
        """延长封禁30天"""
        for token in queryset:
            if token.expire_at and token.expire_at > timezone.now():
                token.expire_at = token.expire_at + timedelta(days=30)
            else:
                token.expire_at = timezone.now() + timedelta(days=30)
            token.save()
        messages.success(request, f"成功延长{queryset.count()}个Token的封禁时间30天")
    extend_ban_30_days.short_description = "延长选中Token的封禁时间30天"


# 封禁账号管理
class BannedAccountResource(resources.ModelResource):
    class Meta:
        model = BannedAccount
        fields = ('id', 'phone_number', 'wx_openid', 'wx_unionid', 'user_id', 
                 'reason', 'detail', 'banned_at', 'expire_at', 'is_permanent', 'ban_count', 
                 'ip_address', 'admin_operator')
        export_order = fields


@admin.register(BannedAccount)
class BannedAccountAdmin(ImportExportModelAdmin):
    resource_class = BannedAccountResource
    list_display = ('account_info', 'reason_display', 'expire_status', 
                   'ban_count', 'admin_operator', 'banned_at')
    list_filter = ('reason', 'is_permanent', 'banned_at')
    search_fields = ('phone_number', 'wx_openid', 'wx_unionid', 'detail', 
                    'ip_address', 'user_id')
    readonly_fields = ('ban_count', 'banned_at')
    actions = ['remove_ban', 'make_permanent', 'extend_ban_30_days']
    
    # 添加调试语句，记录请求过程
    def changelist_view(self, request, extra_context=None):
        try:
            return super().changelist_view(request, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedAccountAdmin错误: {error_msg}")
            # 记录到日志
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedAccountAdmin错误: {error_msg}")
            # 重新抛出异常，让Django处理
            raise
    
    def add_view(self, request, form_url='', extra_context=None):
        try:
            return super().add_view(request, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedAccountAdmin添加视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedAccountAdmin添加视图错误: {error_msg}")
            raise
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        try:
            return super().change_view(request, object_id, form_url, extra_context)
        except Exception as e:
            import traceback
            error_msg = f"错误类型: {type(e).__name__}, 错误信息: {str(e)}\n"
            error_msg += traceback.format_exc()
            print(f"BannedAccountAdmin修改视图错误: {error_msg}")
            import logging
            logger = logging.getLogger('django')
            logger.error(f"BannedAccountAdmin修改视图错误: {error_msg}")
            raise
    
    fieldsets = (
        ('账号信息', {
            'fields': ('phone_number', 'wx_openid', 'wx_unionid', 'user_id')
        }),
        ('封禁信息', {
            'fields': ('reason', 'detail', 'admin_operator', 'ban_count')
        }),
        ('时间设置', {
            'fields': ('banned_at', 'expire_at', 'is_permanent')
        }),
        ('其他信息', {
            'fields': ('ip_address', 'device_info')
        }),
    )
    
    def account_info(self, obj):
        """显示账号信息"""
        info = []
        if obj.user_id:
            info.append(f"用户ID: {obj.user_id}")
        if obj.phone_number:
            info.append(f"手机: {obj.phone_number}")
        if obj.wx_openid:
            info.append(f"OpenID: {obj.wx_openid[:8]}...")
        return " | ".join(info) if info else "未知账号"
    account_info.short_description = '账号信息'
    
    def reason_display(self, obj):
        """显示封禁原因"""
        return obj.get_reason_display()
    reason_display.short_description = '原因'
    
    def expire_status(self, obj):
        """显示封禁状态"""
        if obj.is_permanent:
            return "永久封禁"
        elif obj.expire_at:
            if obj.expire_at > timezone.now():
                days_left = (obj.expire_at - timezone.now()).days
                return f"还有{days_left}天到期"
            else:
                return "已过期"
        return "未设置"
    expire_status.short_description = '状态'
    
    def remove_ban(self, request, queryset):
        """解除封禁 - 彻底删除记录"""
        # 获取相关用户ID，用于记录日志
        user_ids = []
        for account in queryset:
            if account.user_id:
                user_ids.append(str(account.user_id))
            
            # 如果有关联的用户ID，同时更新用户的is_banned状态
            if account.user_id:
                try:
                    from ..models import UserInfo
                    user = UserInfo.objects.filter(id=account.user_id).first()
                    if user and user.is_banned:
                        user.is_banned = False
                        user.ban_reason = None
                        user.ban_until = None
                        user.save()
                except Exception as e:
                    print(f"更新用户 {account.user_id} 的封禁状态失败: {e}")
        
        # 删除记录
        deleted = queryset.delete()[0]
        
        user_info = f"(用户ID: {', '.join(user_ids)})" if user_ids else ""
        messages.success(request, f"成功删除 {deleted} 个账号的封禁记录 {user_info}")
    remove_ban.short_description = "解除并删除选中账号的封禁"
    
    def make_permanent(self, request, queryset):
        """设为永久封禁"""
        updated = queryset.update(is_permanent=True)
        messages.success(request, f"成功将{updated}个账号设为永久封禁")
    make_permanent.short_description = "将选中账号设为永久封禁"
    
    def extend_ban_30_days(self, request, queryset):
        """延长封禁30天"""
        for account in queryset:
            if account.expire_at and account.expire_at > timezone.now():
                account.expire_at = account.expire_at + timedelta(days=30)
            else:
                account.expire_at = timezone.now() + timedelta(days=30)
            account.save()
        messages.success(request, f"成功延长{queryset.count()}个账号的封禁时间30天")
    extend_ban_30_days.short_description = "延长选中账号的封禁时间30天" 