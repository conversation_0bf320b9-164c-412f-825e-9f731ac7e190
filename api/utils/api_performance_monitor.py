# -*- coding: utf-8 -*-
"""
统一的API性能监控工具
提供api_timer装饰器，既保持现有print输出（兼容性），又增加日志记录功能
"""
import time
import logging
import functools
from typing import Optional, Callable, Any

# 创建专门的性能监控日志器
performance_logger = logging.getLogger('api_performance')

def api_timer(func_name: Optional[str] = None):
    """
    统一的API耗时计算装饰器
    - 保持现有的print输出（确保兼容性）
    - 增加日志记录功能（用于性能统计）
    - 支持同步和异步函数
    
    Args:
        func_name: 自定义函数名，如果不提供则使用实际函数名
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            name = func_name or func.__name__
            
            # 保持原有的print输出（兼容性）
            print(f"[API_TIMER] 🚀 {name} 开始执行")
            
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                duration = end_time - start_time
                
                # 保持原有的print输出（兼容性）
                print(f"[API_TIMER] ✅ {name} 执行完成，耗时: {duration:.3f}秒")
                
                # 新增：记录到专门的性能日志（用于统计分析）
                performance_logger.info(f"API_SUCCESS|{name}|{duration:.3f}")
                
                return result
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                
                # 保持原有的print输出（兼容性）
                print(f"[API_TIMER] ❌ {name} 执行失败，耗时: {duration:.3f}秒，错误: {str(e)}")
                
                # 新增：记录到专门的性能日志（用于统计分析）
                performance_logger.error(f"API_ERROR|{name}|{duration:.3f}|{str(e)}")
                
                raise
                
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            name = func_name or func.__name__
            
            # 保持原有的print输出（兼容性）
            print(f"[API_TIMER] 🚀 {name} 开始执行")
            
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                duration = end_time - start_time
                
                # 保持原有的print输出（兼容性）
                print(f"[API_TIMER] ✅ {name} 执行完成，耗时: {duration:.3f}秒")
                
                # 新增：记录到专门的性能日志（用于统计分析）
                performance_logger.info(f"API_SUCCESS|{name}|{duration:.3f}")
                
                return result
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                
                # 保持原有的print输出（兼容性）
                print(f"[API_TIMER] ❌ {name} 执行失败，耗时: {duration:.3f}秒，错误: {str(e)}")
                
                # 新增：记录到专门的性能日志（用于统计分析）
                performance_logger.error(f"API_ERROR|{name}|{duration:.3f}|{str(e)}")
                
                raise
        
        # 自动检测函数类型并返回对应的wrapper
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator


def async_api_timer(func_name: Optional[str] = None):
    """
    专门的异步API耗时计算装饰器（向后兼容）
    实际上调用统一的api_timer
    """
    return api_timer(func_name)


class APIPerformanceAnalyzer:
    """API性能分析器 - 分析性能日志"""
    
    @staticmethod
    def analyze_performance_logs(target_date, log_file_path='logs/info.log'):
        """
        分析API性能日志
        
        Args:
            target_date: 目标日期 (datetime.date)
            log_file_path: 日志文件路径
            
        Returns:
            dict: 性能统计数据
        """
        import re
        import os
        from collections import defaultdict, Counter
        
        api_stats = {
            'total_calls': 0,
            'avg_response_time': 0,
            'slowest_apis': [],
            'fastest_apis': [],
            'error_count': 0,
            'success_count': 0,
            'success_rate': 0
        }
        
        if not os.path.exists(log_file_path):
            api_stats['error'] = f'日志文件不存在: {log_file_path}'
            return api_stats
        
        # API调用统计
        api_times = defaultdict(list)  # {api_name: [time1, time2, ...]}
        api_calls = Counter()  # {api_name: count}
        total_calls = 0
        error_count = 0
        success_count = 0
        
        # 正则表达式匹配性能日志
        # 格式: API_SUCCESS|function_name|0.123
        success_pattern = r'API_SUCCESS\|(.+?)\|([\d.]+)'
        # 格式: API_ERROR|function_name|0.123|error_message
        error_pattern = r'API_ERROR\|(.+?)\|([\d.]+)\|'
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    # 检查是否是目标日期的日志
                    if target_date.strftime('%Y-%m-%d') not in line:
                        continue
                    
                    # 匹配成功的API调用
                    success_match = re.search(success_pattern, line)
                    if success_match:
                        api_name = success_match.group(1)
                        response_time = float(success_match.group(2))
                        
                        api_times[api_name].append(response_time)
                        api_calls[api_name] += 1
                        total_calls += 1
                        success_count += 1
                        continue
                    
                    # 匹配失败的API调用
                    error_match = re.search(error_pattern, line)
                    if error_match:
                        api_name = error_match.group(1)
                        response_time = float(error_match.group(2))
                        
                        api_times[api_name].append(response_time)
                        api_calls[api_name] += 1
                        total_calls += 1
                        error_count += 1
            
            # 计算统计数据
            if api_times:
                # 计算每个API的平均响应时间
                api_avg_times = {}
                for api_name, times in api_times.items():
                    api_avg_times[api_name] = {
                        'avg_time': round(sum(times) / len(times), 3),
                        'max_time': round(max(times), 3),
                        'min_time': round(min(times), 3),
                        'call_count': api_calls[api_name]
                    }
                
                # 最慢的5个API
                slowest_apis = sorted(
                    api_avg_times.items(), 
                    key=lambda x: x[1]['avg_time'], 
                    reverse=True
                )[:5]
                
                # 最快的5个API
                fastest_apis = sorted(
                    api_avg_times.items(), 
                    key=lambda x: x[1]['avg_time']
                )[:5]
                
                # 总体平均响应时间
                all_times = []
                for times in api_times.values():
                    all_times.extend(times)
                avg_response_time = round(sum(all_times) / len(all_times), 3) if all_times else 0
                
                api_stats.update({
                    'total_calls': total_calls,
                    'avg_response_time': avg_response_time,
                    'slowest_apis': slowest_apis,
                    'fastest_apis': fastest_apis,
                    'error_count': error_count,
                    'success_count': success_count,
                    'success_rate': round(success_count / total_calls * 100, 1) if total_calls > 0 else 0
                })
            
        except Exception as e:
            api_stats['error'] = f'分析性能日志失败: {str(e)}'
        
        return api_stats
