# 五运六气健康分析系统使用文档

## 🌟 功能概述

五运六气健康分析系统基于《黄帝内经》五运六气理论，以天地运气为外因，个体八字体质为内因，分析健康状态和疾病变化趋势。

## 📋 系统特色

- **内外因结合**：八字体质（内因）+ 五运六气环境（外因）
- **时间医学**：精准预测疾病易发时间节点
- **个性化调理**：基于运气变化的精准调理方案
- **WebSocket流式**：实时AI解读，用户体验佳

## 🚀 API接口

### 1. 五运六气健康分析

**接口地址**：`POST /routertest1/wuyun-liuqi-health/analyze`

**请求参数**：
```json
{
  "eight_char": "甲子 乙丑 丙寅 丁卯",
  "birth_date": "1990-05-15",
  "analysis_date": "2024-12-25",  // 可选，默认当前日期
  "gender": "male"  // 可选
}
```

**响应示例**（前端接口）：
```json
{
  "success": true,
  "message": "五运六气健康分析数据准备完成",
  "data": {
    "analysis_info": {
      "eight_char": "甲子 乙丑 丙寅 丁卯",
      "birth_date": "1990-05-15",
      "analysis_date": "2024-12-25",
      "gender": "male"
    },
    "wuyun_liuqi_summary": {
      "current_date": "2024-12-25",
      "qi_phase": "终之气",
      "season_influence": "太阳寒水司天，少阴君火在泉",
      "solar_term": "小寒"
    },
    "websocket_message": {
      "type": "health_wuyun_liuqi_analysis",
      "data": {
        "eight_char": "甲子 乙丑 丙寅 丁卯",
        "birth_date": "1990-05-15",
        "analysis_date": "2024-12-25",
        "gender": "male",
        "wuyun_liuqi": {
          "sitian": "太阳寒水",
          "zaiquan": "少阴君火",
          "dayun": "太阴湿土",
          "keqi": "少阴君火",
          "zhuqi": "阳明燥金",
          "qi_shunxu": "终之气",
          "jieqi": "小寒",
          "zaigong": "灾五宫"
        }
      }
    }
  }
}
```

### 2. 获取当前五运六气

**接口地址**：`GET /routertest1/wuyun-liuqi-health/current`

**响应示例**：
```json
{
  "success": true,
  "message": "获取当前五运六气信息成功",
  "data": {
    "success": true,
    "date": "2024-12-25",
    "current_time": "2024-12-25 14:30:00",
    "data": {
      "sitian": "太阳寒水",
      "zaiquan": "少阴君火",
      "dayun": "太阴湿土",
      "qi_shunxu": "终之气",
      "jieqi": "小寒"
    }
  }
}
```

### 3. 系统状态检查

**接口地址**：`GET /routertest1/wuyun-liuqi-health/status`

### 4. 获取提示词模板

**接口地址**：`GET /routertest1/wuyun-liuqi-health/template`

### 5. 测试接口

**接口地址**：`POST /routertest1/wuyun-liuqi-health/test`

### 6. 开发者调试接口

**接口地址**：`POST /routertest1/wuyun-liuqi-health/debug`

**说明**：
- 🔧 **仅供开发调试使用**
- 📋 返回完整的分析数据和使用说明
- 🚫 不包含在API文档中
- ⚠️ 包含敏感的系统信息，生产环境请谨慎使用

**与前端接口的区别**：
- 前端接口：只返回必要的数据，适合生产使用
- 调试接口：返回完整数据，包含提示词模板、使用说明等

## 🔌 WebSocket使用方式

### 1. 连接WebSocket

```javascript
const ws = new WebSocket('ws://服务器地址/ws/bazi_advice/');
```

### 2. 发送五运六气健康分析请求

```javascript
const message = {
  "type": "health_wuyun_liuqi_analysis",
  "data": {
    "eight_char": "甲子 乙丑 丙寅 丁卯",
    "birth_date": "1990-05-15",
    "analysis_date": "2024-12-25",
    "gender": "male",
    "wuyun_liuqi": {
      "sitian": "太阳寒水",
      "zaiquan": "少阴君火",
      "dayun": "太阴湿土",
      "keqi": "少阴君火",
      "zhuqi": "阳明燥金",
      "qi_shunxu": "终之气",
      "jieqi": "小寒",
      "zaigong": "灾五宫"
    }
  }
};

ws.send(JSON.stringify(message));
```

### 3. 接收流式响应

```javascript
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  if (data.error) {
    console.error('错误:', data.error);
    return;
  }
  
  if (data.content) {
    // 实时显示AI分析内容
    console.log('分析内容:', data.content);
  }
  
  if (data.done) {
    console.log('分析完成');
  }
};
```

## 📊 分析结果格式

AI返回的分析结果按以下结构组织：

### 🌟 体质概况
一句话概括体质特点

### ⚠️ 当前健康风险（按时间排序）
- **近期风险（1-2周）**
- **本季度风险（3个月内）**
- **年度趋势**

### 📈 疾病变化趋势
- **恶化时段**
- **缓解时段**
- **调理最佳期**

### 🎯 精准调理方案
- **饮食调理**
- **起居调理**
- **情志调理**

### ⏰ 时间医学指导
- **每日最佳调理时辰**
- **本月关键节气**
- **下个月预防重点**

## 🔧 技术实现

### 核心组件

1. **五运六气服务**：`api/services/wuyun_liuqi_service.py`
2. **WebSocket消费者**：`api/consumers/bazi_advice_consumer.py`
3. **提示词模板**：`api/prompts/templates/health_wuyun_liuqi_v1.yaml`
4. **API接口**：`api/ninja_apis/routertest1/wuyun_liuqi_health_api.py`

### 数据流程

1. **HTTP API** → 准备分析数据 → 返回WebSocket消息格式
2. **WebSocket** → 接收消息 → 调用五运六气服务 → AI分析 → 流式返回

### 集成方式

- 集成到现有的`routertest1`模块中
- 使用现有的八字分析WebSocket消费者
- 复用现有的提示词管理系统

## 💡 使用建议

### 前端开发者
1. **调用分析接口**：`POST /routertest1/wuyun-liuqi-health/analyze`
2. **提取WebSocket消息**：使用返回的`websocket_message`
3. **发送到WebSocket**：`ws://服务器/ws/bazi_advice/`
4. **接收流式结果**：实时显示AI分析内容

### 后端开发者
1. **使用调试接口**：`POST /routertest1/wuyun-liuqi-health/debug`
2. **查看完整数据**：包含提示词模板、详细五运六气数据
3. **检查系统状态**：`GET /routertest1/wuyun-liuqi-health/status`
4. **运行测试**：`POST /routertest1/wuyun-liuqi-health/test`

### 最佳实践
- ✅ 前端只使用`analyze`接口，数据简洁
- ✅ 开发调试使用`debug`接口，信息完整
- ✅ 先检查系统状态，确保功能可用
- ✅ WebSocket消息格式严格按照返回的格式

## ⚠️ 注意事项

- 五运六气系统依赖特定的计算模块，需确保模块可用
- WebSocket消息格式必须严格按照规范
- 分析结果仅供参考，不替代专业医疗诊断
- 建议结合传统中医理论理解分析结果
