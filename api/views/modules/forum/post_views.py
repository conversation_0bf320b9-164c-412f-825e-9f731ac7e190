import traceback
import json
import sys
import datetime as dt
from typing import List
from django.shortcuts import get_object_or_404
from django.http import Http404
from ninja import Router
from ninja.errors import HttpError
from django.conf import settings
from django.core.cache import cache
from asgiref.sync import sync_to_async
from api.models import TCMForumPost, TCMForumInteraction, UserInfo
from api.views.schemas import TCMPostCreateRequest, TCMPostResponse, TCMPostUpdateRequest
from api.views.modules.forum.post_utils import format_post
from api.views.utils import get_user_by_id
from api.views.content_review import HuaweiContentReview
from api.ninja_apis.questionnaire.utils import api_timer

router = Router()

class JSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime等无法直接序列化的对象"""
    def default(self, obj):
        if isinstance(obj, dt.datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        return super().default(obj)

@router.post("/posts", response=TCMPostResponse)
@api_timer("创建帖子")
async def create_post(request, post_data: TCMPostCreateRequest):
    try:
        # 使用异步工具函数获取用户信息
        from api.ninja_apis.async_utils import get_async
        user = await get_async(UserInfo, id=request.user_id)
        
        # 内容审核
        content_reviewer = HuaweiContentReview(project_id=settings.HUAWEI_PROJECT_ID)
        
        # 合并标题和内容进行审核
        combined_content = f"{post_data.title}\n{post_data.content}"
        try:
            review_result = await sync_to_async(content_reviewer.text_moderation)(combined_content)
            print(f"审核API响应: {json.dumps(review_result, ensure_ascii=False)}")
            
            # 检查审核结果，如果 suggestion 是 block，返回虚拟响应
            if review_result.get('result', {}).get('suggestion') == 'block':
                fake_post = {
                    "id": 0,  # 使用0作为虚拟ID
                    "author": {
                        "id": user.id,
                        "nickname": user.nickname if hasattr(user, 'nickname') else str(user.id),
                    },
                    "section_id": post_data.section_id,
                    "title": post_data.title,
                    "content": post_data.content,
                    "tags": post_data.tags or [],
                    "view_count": 0,
                    "comment_count": 0,
                    "like_count": 0,
                    "is_deleted": False,
                    "created_at": dt.datetime.now(),
                    "last_active": dt.datetime.now(),
                    "is_fake": True  # 标记这是一个虚拟帖子
                }
                return fake_post
                
        except Exception as e:
            # 审核服务异常时，为了用户体验，仍然允许发帖
            print(f"内容审核服务异常: {str(e)}")
            print(f"详细错误信息: {traceback.format_exc()}")
        
        # 内容审核通过或服务异常，创建帖子
        from api.ninja_apis.async_utils import create_async
        post = await create_async(TCMForumPost,
            author=user,
            section_id=post_data.section_id,
            title=post_data.title,
            content=post_data.content,
            tags=post_data.tags or []
        )

        # 清除帖子列表缓存
        await sync_to_async(cache.delete)("forum_posts_list")
        print(f"[CACHE_DELETE] 🗑️ forum_posts_list - 创建帖子后清除帖子列表缓存")
        
        if hasattr(format_post, '__awaitable__'):
            return await format_post(post, request.user_id)
        else:
            return await sync_to_async(format_post)(post, request.user_id)
        
    except UserInfo.DoesNotExist:
        print(f'用户不存在, user_id: {request.user_id}', file=sys.stderr)
        raise HttpError(401, {"error": "用户不存在"})
    except Exception as e:
        print('创建帖子错误:', str(e), file=sys.stderr)
        print('详细错误信息:', traceback.format_exc(), file=sys.stderr)
        raise HttpError(500, {"error": "创建帖子失败", "detail": str(e)})


@router.get("/posts/{post_id}", response=TCMPostResponse)
@api_timer("获取单个帖子")
async def get_post(request, post_id: int):
    try:
        # 检查缓存中是否有数据
        cache_key = f"forum_post:{post_id}:{request.user_id}"
        cached_post = await sync_to_async(cache.get)(cache_key)

        if cached_post:
            print(f"[CACHE_HIT] ✅ {cache_key} - 返回帖子 {post_id} 的缓存数据")
            return json.loads(cached_post)

        # 缓存未命中，从数据库获取
        from api.ninja_apis.async_utils import get_async, save_async
        post = await get_async(TCMForumPost, status='published', id=post_id)
        if not post:
            raise Http404("帖子不存在")

        # 更新浏览计数
        post.view_count += 1
        await save_async(post)
        
        # 格式化帖子
        if hasattr(format_post, '__awaitable__'):
            formatted_post = await format_post(post, request.user_id)
        else:
            formatted_post = await sync_to_async(format_post)(post, request.user_id)
        
        # 处理datetime序列化
        if isinstance(formatted_post, dict):
            if 'created_at' in formatted_post and formatted_post['created_at']:
                if isinstance(formatted_post['created_at'], dt.datetime):
                    formatted_post['created_at'] = formatted_post['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    
            if 'updated_at' in formatted_post and formatted_post['updated_at']:
                if isinstance(formatted_post['updated_at'], dt.datetime):
                    formatted_post['updated_at'] = formatted_post['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        # 设置缓存，缓存5分钟，使用自定义JSON编码器处理datetime
        await sync_to_async(cache.set)(
            cache_key, 
            json.dumps(formatted_post, cls=JSONEncoder), 
            timeout=300
        )
        print(f"[CACHE_SET] 💾 {cache_key} - 帖子 {post_id} 的数据已缓存")
        
        return formatted_post
    except Exception as e:
        print('获取帖子错误:', str(e))
        print('详细错误信息:', traceback.format_exc())
        raise HttpError(500, {"error": "获取帖子失败", "detail": str(e)})

@router.put("/posts/{post_id}", response=TCMPostResponse)
@api_timer("更新帖子")
async def update_post(request, post_id: int, post_data: TCMPostUpdateRequest):
    try:
        post = await sync_to_async(get_object_or_404)(TCMForumPost, id=post_id, author=request.user)
        update_fields = {}
        if post_data.title is not None:
            update_fields['title'] = post_data.title
        if post_data.content is not None:
            update_fields['content'] = post_data.content
        if post_data.tags is not None:
            update_fields['tags'] = post_data.tags

        if update_fields:
            for field, value in update_fields.items():
                setattr(post, field, value)
            await sync_to_async(post.save)()
        
        # 清除相关缓存
        cache_key = f"forum_post:{post_id}:{request.user_id}"
        await sync_to_async(cache.delete)(cache_key)
        print(f"[CACHE_DELETE] 🗑️ {cache_key} - 更新帖子后清除帖子缓存")
        
        if hasattr(format_post, '__awaitable__'):
            return await format_post(post)
        else:
            return await sync_to_async(format_post)(post)
    except Exception as e:
        print('更新帖子错误:', str(e), file=sys.stderr)
        print('详细错误信息:', traceback.format_exc(), file=sys.stderr)
        raise HttpError(500, {"error": "更新帖子失败", "detail": str(e)})

@router.post("/posts/{post_id}/verify")
@api_timer("验证帖子")
async def verify_post(request, post_id: int):
    try:
        if not request.user.is_staff:
            raise HttpError(403, {"error": "Unauthorized"})
        post = await sync_to_async(get_object_or_404)(TCMForumPost, id=post_id)
        post.is_verified = True
        await sync_to_async(post.save)()
        
        # 清除相关缓存
        cache_key = f"forum_post:{post_id}:{request.user_id}"
        await sync_to_async(cache.delete)(cache_key)
        print(f"[CACHE_DELETE] 🗑️ {cache_key} - 验证帖子后清除帖子缓存")
        
        return {"success": True, "status": 200}
    except HttpError:
        raise
    except Exception as e:
        print('验证帖子错误:', str(e), file=sys.stderr)
        print('详细错误信息:', traceback.format_exc(), file=sys.stderr)
        raise HttpError(500, {"error": "验证帖子失败", "detail": str(e)})

@router.get("/posts", response=List[TCMPostResponse])
@api_timer("获取帖子列表")
async def get_posts(request, page: int = 1, size: int = 10):
    """获取帖子列表，支持分页"""
    try:
        # 检查缓存中是否有数据
        cache_key = f"forum_posts_list:{page}:{size}:{request.user_id}"
        cached_posts = await sync_to_async(cache.get)(cache_key)
        
        if cached_posts:
            print(f"[CACHE_HIT] ✅ {cache_key} - 返回帖子列表缓存数据")
            return json.loads(cached_posts)
            
        # 缓存未命中，从数据库获取
        # 计算偏移量
        offset = (page - 1) * size

        # 获取帖子总数
        from api.ninja_apis.async_utils import count_async, filter_async
        total_posts = await count_async(TCMForumPost, status='published')

        # 获取指定页的帖子
        posts = await filter_async(
            TCMForumPost,
            status='published',
            order_by=['-is_pinned', '-last_active'],
            select_related=['author'],
            offset=offset,
            limit=size
        )
        
        # 格式化帖子
        formatted_posts = []
        for post in posts:
            if hasattr(format_post, '__awaitable__'):
                formatted_post = await format_post(post, request.user_id)
            else:
                formatted_post = await sync_to_async(format_post)(post, request.user_id)
                
            # 处理datetime序列化
            if isinstance(formatted_post, dict):
                if 'created_at' in formatted_post and formatted_post['created_at']:
                    if isinstance(formatted_post['created_at'], dt.datetime):
                        formatted_post['created_at'] = formatted_post['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
                if 'updated_at' in formatted_post and formatted_post['updated_at']:
                    if isinstance(formatted_post['updated_at'], dt.datetime):
                        formatted_post['updated_at'] = formatted_post['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
            formatted_posts.append(formatted_post)
        
        # 设置缓存，缓存5分钟，使用自定义JSON编码器处理datetime
        await sync_to_async(cache.set)(
            cache_key, 
            json.dumps(formatted_posts, cls=JSONEncoder), 
            timeout=300
        )
        print(f"[CACHE_SET] 💾 {cache_key} - 帖子列表数据已缓存")
        
        return formatted_posts
        
    except Exception as e:
        print('获取帖子列表错误:', str(e), file=sys.stderr)
        print('详细错误信息:', traceback.format_exc(), file=sys.stderr)
        raise HttpError(500, {"error": "获取帖子列表失败", "detail": str(e)}) 