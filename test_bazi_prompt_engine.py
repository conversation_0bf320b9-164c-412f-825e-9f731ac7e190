#!/usr/bin/env python3
"""
测试八字提示词工程
验证参数排列组合功能
"""

import json
import asyncio
import websockets
import sys
import os
import django
from django.conf import settings

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置Django设置
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

# 测试数据
TEST_CASES = [
    {
        "name": "基础健康分析-年度",
        "data": {
            "analysis_direction": "basic_analysis",
            "domain": "health",
            "time_scope": "yearly",
            "bazi_data": {
                "eight_char": "甲子 乙丑 丙寅 丁卯",
                "gender": "男",
                "birth_date": "1984-01-01",
                "wuxing": {"木": 2, "火": 2, "土": 2, "金": 1, "水": 1},
                "shishen": {"正官": 1, "偏财": 2, "比肩": 1},
                "shensha": {"天乙贵人": 1, "文昌": 1}
            }
        }
    },
    {
        "name": "矛盾分析-事业-短期",
        "data": {
            "analysis_direction": "contradiction_analysis",
            "domain": "career",
            "time_scope": "short_term",
            "bazi_data": {
                "eight_char": "戊戌 甲寅 庚申 辛酉",
                "gender": "女",
                "birth_date": "1988-02-15",
                "wuxing": {"木": 1, "火": 0, "土": 2, "金": 3, "水": 0},
                "shishen": {"七杀": 2, "伤官": 1, "劫财": 1}
            }
        }
    },
    {
        "name": "决策支持-婚姻-月度",
        "data": {
            "analysis_direction": "decision_support",
            "domain": "marriage",
            "time_scope": "monthly",
            "user_question": "我应该在什么时候结婚比较好？",
            "bazi_data": {
                "eight_char": "壬水 癸亥 甲子 乙丑",
                "gender": "女",
                "birth_date": "1992-11-20",
                "wuxing": {"木": 2, "火": 0, "土": 1, "金": 0, "水": 3},
                "shishen": {"正财": 1, "偏印": 2, "食神": 1}
            }
        }
    },
    {
        "name": "格局分析-财富-长期",
        "data": {
            "analysis_direction": "pattern_analysis",
            "domain": "wealth",
            "time_scope": "long_term",
            "bazi_data": {
                "eight_char": "丙午 己亥 辛丑 庚寅",
                "gender": "男",
                "birth_date": "1990-12-05",
                "wuxing": {"木": 1, "火": 2, "土": 2, "金": 2, "水": 1},
                "shishen": {"偏财": 2, "正印": 1, "比肩": 1}
            }
        }
    }
]

async def test_websocket_connection():
    """测试WebSocket连接和消息发送"""
    uri = "ws://localhost:8000/api/ws/bazi_advice/"
    
    try:
        print("🚀 开始测试八字提示词工程...")
        print("=" * 60)
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            for i, test_case in enumerate(TEST_CASES, 1):
                print(f"\n📋 测试用例 {i}: {test_case['name']}")
                print("-" * 40)
                
                # 发送测试数据
                message = json.dumps(test_case['data'], ensure_ascii=False)
                print(f"📤 发送数据: {json.dumps(test_case['data'], ensure_ascii=False, indent=2)}")
                
                await websocket.send(message)
                
                # 接收响应
                print("⏳ 等待AI回复...")
                full_response = ""
                
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(response)
                        
                        if data.get('type') == 'stream':
                            content = data.get('content', '')
                            full_response += content
                            print(content, end='', flush=True)
                        elif data.get('type') == 'complete':
                            print(f"\n✅ 回复完成")
                            break
                        elif data.get('type') == 'error':
                            print(f"\n❌ 错误: {data.get('message')}")
                            break
                            
                except asyncio.TimeoutError:
                    print("\n⏰ 响应超时")
                    break
                
                print(f"\n📝 完整回复长度: {len(full_response)} 字符")
                print("=" * 60)
                
                # 等待一下再发送下一个请求
                await asyncio.sleep(2)
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_prompt_engine_directly():
    """直接测试提示词工程"""
    print("🔧 直接测试提示词工程...")
    print("=" * 60)
    
    # 导入提示词工程
    try:
        from api.prompts.prompt_engine import prompt_engine
        
        for i, test_case in enumerate(TEST_CASES, 1):
            print(f"\n📋 测试用例 {i}: {test_case['name']}")
            print("-" * 40)
            
            data = test_case['data']
            system_prompt, user_prompt = prompt_engine.generate_prompt(
                data['analysis_direction'],
                data['domain'],
                data['time_scope'],
                data.get('user_question')
            )
            
            print("🤖 系统提示词:")
            print(system_prompt[:300] + "..." if len(system_prompt) > 300 else system_prompt)
            print("\n👤 用户提示词模板:")
            print(user_prompt[:300] + "..." if len(user_prompt) > 300 else user_prompt)
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ 直接测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def print_parameter_combinations():
    """打印所有可能的参数组合"""
    from api.prompts.categories import AnalysisDirection, Domain, TimeScope
    
    print("📊 所有可能的参数组合:")
    print("=" * 60)
    
    total_combinations = 0
    
    for direction in AnalysisDirection:
        for domain in Domain:
            for time_scope in TimeScope:
                total_combinations += 1
                print(f"{total_combinations:3d}. {direction.value} + {domain.value} + {time_scope.value}")
    
    print(f"\n📈 总计: {total_combinations} 种组合")
    print("=" * 60)

if __name__ == "__main__":
    print("🎯 八字提示词工程测试")
    print("=" * 60)
    
    # 打印参数组合
    print_parameter_combinations()
    
    # 直接测试提示词工程
    test_prompt_engine_directly()
    
    # 测试WebSocket连接
    print("\n🌐 开始WebSocket测试...")
    asyncio.run(test_websocket_connection())
