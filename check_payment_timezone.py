#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查支付数据的时区问题
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('/home/<USER>/riyue-llm/myproject/demo_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

from api.models import PaymentOrder_Wechat, Order
from django.utils import timezone
from datetime import timedelta, datetime
import pytz

def check_payment_timezone():
    print("=== 支付数据时区检查 ===")
    
    # 使用和admin相同的时区处理逻辑
    beijing_tz = pytz.timezone('Asia/Shanghai')
    now_beijing = timezone.now().astimezone(beijing_tz)
    today = now_beijing.date()
    yesterday = today - timedelta(days=1)
    
    print(f'当前北京时间: {now_beijing}')
    print(f'查询日期: 今日={today}, 昨日={yesterday}')
    
    # 计算今日的时间范围 (东八区转UTC，因为数据库存储的是UTC时间)
    today_start_beijing = beijing_tz.localize(datetime.combine(today, datetime.min.time()))
    today_end_beijing = beijing_tz.localize(datetime.combine(today, datetime.max.time()))
    yesterday_start_beijing = beijing_tz.localize(datetime.combine(yesterday, datetime.min.time()))
    yesterday_end_beijing = beijing_tz.localize(datetime.combine(yesterday, datetime.max.time()))

    # 转换为UTC时间进行数据库查询
    today_start = today_start_beijing.astimezone(pytz.UTC)
    today_end = today_end_beijing.astimezone(pytz.UTC)
    yesterday_start = yesterday_start_beijing.astimezone(pytz.UTC)
    yesterday_end = yesterday_end_beijing.astimezone(pytz.UTC)

    print(f'今日北京时间范围: {today_start_beijing} ~ {today_end_beijing}')
    print(f'今日UTC时间范围: {today_start} ~ {today_end}')
    print(f'昨日北京时间范围: {yesterday_start_beijing} ~ {yesterday_end_beijing}')
    print(f'昨日UTC时间范围: {yesterday_start} ~ {yesterday_end}')
    
    # 查询今日支付数据
    print('\n=== 今日微信支付 ===')
    today_wechat = PaymentOrder_Wechat.objects.filter(
        success_time__gte=today_start,
        success_time__lte=today_end,
        trade_state='SUCCESS'
    )
    print(f'今日微信成功支付: {today_wechat.count()}条')
    for order in today_wechat:
        print(f'  订单: {order.out_trade_no}, 金额: ¥{order.amount/100}, 时间: {order.success_time}, 类型: {order.attach}')
    
    print('\n=== 今日支付宝支付 ===')
    today_alipay = Order.objects.filter(
        pay_time__gte=today_start,
        pay_time__lte=today_end,
        status='Completed'
    )
    print(f'今日支付宝成功支付: {today_alipay.count()}条')
    for order in today_alipay:
        print(f'  订单: {order.order_id}, 金额: ¥{order.total_amount}, 时间: {order.pay_time}, 类型: {order.attach}')
    
    # 检查所有支付记录的时间
    print('\n=== 最近支付记录时间检查 ===')
    all_wechat = PaymentOrder_Wechat.objects.filter(trade_state='SUCCESS').order_by('-success_time')[:5]
    for order in all_wechat:
        beijing_time = order.success_time.astimezone(beijing_tz) if order.success_time else None
        print(f'微信订单 {order.out_trade_no}: 原时间={order.success_time}, 北京时间={beijing_time}')
    
    all_alipay = Order.objects.filter(status='Completed').order_by('-pay_time')[:5]
    for order in all_alipay:
        beijing_time = order.pay_time.astimezone(beijing_tz) if order.pay_time else None
        print(f'支付宝订单 {order.order_id}: 原时间={order.pay_time}, 北京时间={beijing_time}')
    
    # 尝试昨日查询 (使用UTC时间)
    print('\n=== 尝试昨日查询 ===')
    yesterday_wechat = PaymentOrder_Wechat.objects.filter(
        success_time__gte=yesterday_start,
        success_time__lte=yesterday_end,
        trade_state='SUCCESS'
    )
    print(f'昨日微信成功支付: {yesterday_wechat.count()}条')
    for order in yesterday_wechat:
        beijing_time = order.success_time.astimezone(beijing_tz)
        print(f'  订单: {order.out_trade_no}, 金额: ¥{order.amount/100}, 北京时间: {beijing_time}, 类型: {order.attach}')

    yesterday_alipay = Order.objects.filter(
        pay_time__gte=yesterday_start,
        pay_time__lte=yesterday_end,
        status='Completed'
    )
    print(f'昨日支付宝成功支付: {yesterday_alipay.count()}条')
    for order in yesterday_alipay:
        beijing_time = order.pay_time.astimezone(beijing_tz)
        print(f'  订单: {order.order_id}, 金额: ¥{order.total_amount}, 北京时间: {beijing_time}, 类型: {order.attach}')

if __name__ == '__main__':
    check_payment_timezone()
