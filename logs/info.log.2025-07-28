INFO 2025-07-28 21:07:33,911 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 1
INFO 2025-07-28 21:08:14,028 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 2
INFO 2025-07-28 21:08:14,250 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 3
INFO 2025-07-28 21:08:14,489 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 4
INFO 2025-07-28 21:08:19,271 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 5
INFO 2025-07-28 21:08:19,774 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 6
WARNING 2025-07-28 21:08:20,494 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 21:08:20,542 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 21:08:33,152 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 7
INFO 2025-07-28 21:08:34,712 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 8
INFO 2025-07-28 21:08:35,030 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 9
WARNING 2025-07-28 21:08:35,797 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 21:08:35,844 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-28 21:14:11,864 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-28 21:14:46,432 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-28 22:24:29,376 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 10
INFO 2025-07-28 22:24:29,384 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 11
INFO 2025-07-28 22:24:31,051 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 12
INFO 2025-07-28 22:27:45,411 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 13
INFO 2025-07-28 22:27:47,458 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 14
INFO 2025-07-28 22:27:47,703 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 15
WARNING 2025-07-28 22:27:47,860 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 22:27:47,906 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-28 22:27:55,384 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 16
INFO 2025-07-28 22:28:13,357 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 17
INFO 2025-07-28 22:28:37,721 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 18
INFO 2025-07-28 22:28:38,177 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 19
INFO 2025-07-28 22:28:39,718 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 20
INFO 2025-07-28 22:28:39,753 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 21
INFO 2025-07-28 22:28:43,637 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 22
INFO 2025-07-28 22:28:45,094 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 23
INFO 2025-07-28 22:28:45,162 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 24
WARNING 2025-07-28 22:28:45,496 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 22:28:45,543 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-28 22:29:10,771 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-28 22:29:10,772 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-28 22:30:36,972 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 25
INFO 2025-07-28 22:31:51,209 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 26
INFO 2025-07-28 22:32:17,387 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 27
INFO 2025-07-28 22:32:17,570 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 28
WARNING 2025-07-28 22:32:17,977 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 22:32:18,023 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-28 22:32:23,651 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-28 22:34:50,361 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 29
INFO 2025-07-28 22:37:30,763 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 30
INFO 2025-07-28 22:48:42,132 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 31
INFO 2025-07-28 22:49:12,289 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 32
INFO 2025-07-28 22:49:14,115 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 33
INFO 2025-07-28 22:49:14,157 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 34
INFO 2025-07-28 22:49:15,623 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 35
INFO 2025-07-28 22:49:23,113 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 36
INFO 2025-07-28 22:49:24,309 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 37
INFO 2025-07-28 22:49:24,703 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 38
WARNING 2025-07-28 22:49:25,470 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 22:49:25,516 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-28 22:49:43,626 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 39
INFO 2025-07-28 22:50:09,524 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 40
INFO 2025-07-28 22:50:28,339 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 41
INFO 2025-07-28 22:50:47,458 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-28 23:05:41,489 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 42
INFO 2025-07-28 23:07:24,422 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 43
INFO 2025-07-28 23:07:24,839 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 44
INFO 2025-07-28 23:07:24,991 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 45
INFO 2025-07-28 23:07:29,788 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 46
INFO 2025-07-28 23:07:29,915 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 47
WARNING 2025-07-28 23:07:30,440 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:07:30,486 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-28 23:07:50,561 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 48
WARNING 2025-07-28 23:08:03,388 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:08:03,434 base 3363624 140070837526528 ✅ [ARK_CHAT_VIP] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-28 23:08:03,608 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户1(每周) API:chat_api 调用次数: 1
INFO 2025-07-28 23:08:24,752 base 3363624 140070837526528 ✅ [ARK_CHAT_VIP] 消息处理完成，用户ID: 1，耗时: 21.165秒
INFO 2025-07-28 23:09:58,832 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT_VIP] WebSocket断开连接: {'close_code': 1000, 'user_id': 1, 'disconnect_reason': '正常关闭'}
INFO 2025-07-28 23:10:01,233 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 49
INFO 2025-07-28 23:17:09,482 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:17:12,979 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 50
INFO 2025-07-28 23:17:13,179 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 51
WARNING 2025-07-28 23:17:13,343 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:17:13,390 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:17:26,392 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:17:48,589 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 52
INFO 2025-07-28 23:17:48,655 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 53
WARNING 2025-07-28 23:17:49,088 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:17:49,135 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:18:07,152 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:18:09,963 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 54
INFO 2025-07-28 23:18:10,339 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 55
WARNING 2025-07-28 23:18:10,833 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:18:10,881 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:19:04,842 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:20:55,177 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 56
WARNING 2025-07-28 23:20:55,735 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:20:55,783 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:20:56,660 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 57
INFO 2025-07-28 23:21:51,628 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:21:54,674 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 58
INFO 2025-07-28 23:21:55,507 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 59
WARNING 2025-07-28 23:21:56,097 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:21:56,145 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:30:24,744 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:31:02,615 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 60
INFO 2025-07-28 23:31:02,795 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 61
WARNING 2025-07-28 23:31:03,198 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:31:03,246 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:31:49,438 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:32:10,571 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 62
INFO 2025-07-28 23:32:10,774 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 63
WARNING 2025-07-28 23:32:11,376 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:32:11,423 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-28 23:34:01,332 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:34:04,810 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 64
INFO 2025-07-28 23:34:05,045 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 65
WARNING 2025-07-28 23:34:05,208 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:34:05,255 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:34:22,317 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:34:25,305 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 66
INFO 2025-07-28 23:34:25,359 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 67
WARNING 2025-07-28 23:34:25,698 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:34:25,746 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:37:39,546 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:38:01,292 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 68
INFO 2025-07-28 23:38:01,523 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 69
WARNING 2025-07-28 23:38:01,940 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:38:01,989 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.049秒
INFO 2025-07-28 23:38:30,764 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:38:51,813 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 70
INFO 2025-07-28 23:38:51,869 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 71
WARNING 2025-07-28 23:38:52,270 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:38:52,318 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-28 23:39:28,119 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:39:31,198 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 72
INFO 2025-07-28 23:39:31,249 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 73
WARNING 2025-07-28 23:39:31,601 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:39:31,649 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:39:39,739 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:39:42,779 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 74
INFO 2025-07-28 23:39:42,835 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 75
WARNING 2025-07-28 23:39:43,240 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:39:43,288 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:40:44,653 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:40:47,721 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 76
INFO 2025-07-28 23:40:47,957 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 77
WARNING 2025-07-28 23:40:48,104 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:40:48,152 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:40:48,479 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:40:51,480 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 78
INFO 2025-07-28 23:40:51,532 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 79
WARNING 2025-07-28 23:40:51,881 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:40:51,929 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:41:25,298 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:41:28,334 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 80
INFO 2025-07-28 23:41:28,437 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 81
WARNING 2025-07-28 23:41:28,746 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:41:28,794 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:45:17,372 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:45:54,183 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 82
INFO 2025-07-28 23:45:54,387 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 83
WARNING 2025-07-28 23:45:54,515 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:45:54,562 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:51:51,684 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:51:55,955 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 84
INFO 2025-07-28 23:51:56,118 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 85
WARNING 2025-07-28 23:51:56,685 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:51:56,732 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:52:09,905 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:53:18,344 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 86
INFO 2025-07-28 23:53:18,934 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 87
WARNING 2025-07-28 23:53:19,559 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:53:19,606 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-28 23:53:44,808 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-28 23:54:05,517 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 88
INFO 2025-07-28 23:54:05,590 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 89
WARNING 2025-07-28 23:54:06,087 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-28 23:54:06,136 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.049秒
INFO 2025-07-28 23:59:57,012 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
