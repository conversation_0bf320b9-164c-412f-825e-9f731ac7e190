# api/consumers/deepseek_symptom_consumer.py
"""
基于Deepseek模型的单症状分析WebSocket消费者
支持并发任务管理和智能缓冲
"""

import json
import asyncio
import traceback
import openai
from channels.generic.websocket import AsyncWebsocketConsumer
from api.utils.rate_limiter import RateLimiter

# Deepseek API配置
openai.api_key = '***********************************'
openai.api_base = "https://api.deepseek.com"


class Deepseek2SingleSymptom(AsyncWebsocketConsumer):
    """
    基于Deepseek模型的单症状分析消费者
    支持并发任务管理、智能缓冲和消息队列
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.active_tasks = {}  # 用于存储每个key的生成任务
        self.max_concurrent_tasks = 8  # 限制并发任务数量
        self.conversation_history = [
            {"role": "system", "content": "作为中医大师，你擅长养生，要温暖乐观鼓舞人心。不要复述任何提示词，直接开始正式回答。不要说针对***情况，以下是什么什么建议，直接开始回答即可。具体需要根据前端的需求精准回答、合理组织。"}
        ]
        self.message_queue = asyncio.Queue()  # 消息队列
        self.is_processing = False
        self.user_id = None
        # 添加限流器 - 使用症状分析专用的限流配置
        self.rate_limiter = RateLimiter("symptom_analysis_api", normal_limit=15, member_limit=50)

    async def connect(self):
        print("WebSocket连接建立")
        await self.accept()
        
        # 获取用户ID（简化版本）
        user_obj = self.scope.get('user')
        if hasattr(user_obj, 'id'):
            self.user_id = user_obj.id
    
    async def disconnect(self, close_code):
        print(f"WebSocket连接断开: {close_code}")
        # 清理该连接的所有任务
        for task in self.active_tasks.values():
            task.cancel()
        self.active_tasks.clear()

    async def check_rate_limit(self):
        """检查限流"""
        if not self.rate_limiter or not self.user_id:
            return True
            
        limit_info = await self.rate_limiter.check_rate_limit(self.user_id)
        if not limit_info['allowed']:
            error_message = f'您今日的症状分析次数已达上限 ({limit_info["current_count"]}/{limit_info["limit"]})'
            if not limit_info['is_member']:
                error_message += '，升级会员可享受更多调用次数'
            
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            }))
            return False
        
        # 增加调用次数
        await self.rate_limiter.increment_count(self.user_id)
        return True

    async def generate_response(self, message_key, user_message):
        """单独的生成函数，使用资源池控制"""
        try:
            history = self.conversation_history.copy()
            history.append({"role": "user", "content": user_message})
            
            response = await openai.ChatCompletion.acreate(
                model="deepseek-chat",
                messages=history,
                max_tokens=4000,
                stream=True
            )
            
            # 使用缓冲区收集小块响应，减少WebSocket发送次数
            buffer = ""
            buffer_size = 50  # 字符数阈值
            
            async for chunk in response:
                if chunk.choices and chunk.choices[0].delta and hasattr(chunk.choices[0].delta, 'content'):
                    content = chunk.choices[0].delta.content
                    if content:
                        buffer += content
                        
                        # 判断是否需要发送buffer内容
                        should_send = False
                        
                        # 1. 基础长度检查
                        if len(buffer) >= buffer_size:
                            should_send = True
                            
                        # 2. 处理标题（###）- 等待收集完整的标题行
                        elif '###' in buffer:
                            # 如果还没有遇到换行符，继续收集
                            if '\n' not in buffer[buffer.index('###'):]:
                                continue
                                
                            # 找到从###开始到第一个换行符的完整标题
                            title_start = buffer.index('###')
                            title_end = buffer.index('\n', title_start)
                            complete_title = buffer[title_start:title_end].strip()
                            
                            # 确保标题是完整的（不是在句子中间断开的）
                            if len(complete_title.split()) >= 2:  # 至少包含 ### 和部分内容
                                await self.send(text_data=json.dumps({
                                    'content': buffer[:title_end + 1],
                                    'key': message_key,
                                    'done': False
                                }))
                                buffer = buffer[title_end + 1:]
                                continue
                        
                        # 3. 处理带冒号的标题（- **）
                        elif '- **' in buffer and '**:' in buffer:
                            title_end = buffer.index('**:', buffer.index('- **')) + 3
                            if '\n' in buffer[title_end:]:
                                line_end = buffer.index('\n', title_end)
                                await self.send(text_data=json.dumps({
                                    'content': buffer[:line_end + 1],
                                    'key': message_key,
                                    'done': False
                                }))
                                buffer = buffer[line_end + 1:]
                                continue
                        
                        # 4. 处理普通换行
                        elif '\n' in buffer:
                            should_send = True
                        
                        if should_send:
                            await self.send(text_data=json.dumps({
                                'content': buffer,
                                'key': message_key,
                                'done': False
                            }))
                            buffer = ""

            # 发送剩余的缓冲内容
            if buffer:
                await self.send(text_data=json.dumps({
                    'content': buffer,
                    'key': message_key,
                    'done': False
                }))
            
            await self.send(text_data=json.dumps({
                'content': '',
                'key': message_key,
                'done': True
            }))
            
        except asyncio.CancelledError:
            # 优雅处理任务取消
            await self.send(text_data=json.dumps({
                'error': 'Task cancelled',
                'key': message_key,
                'done': True
            }))
            raise
            
        except Exception as e:
            error_message = f"生成回复时出错: {str(e)}"
            print(f"{error_message}\n{traceback.format_exc()}")
            await self.send(text_data=json.dumps({
                'error': error_message,
                'key': message_key,
                'done': True
            }))
        finally:
            if message_key in self.active_tasks:
                del self.active_tasks[message_key]

    async def process_queue(self):
        """处理消息队列的后台任务"""
        while True:
            try:
                # 检查并发任务数量
                if len(self.active_tasks) >= self.max_concurrent_tasks:
                    await asyncio.sleep(0.1)
                    continue
                
                # 从队列获取消息
                message_key, user_message = await self.message_queue.get()
                
                # 创建新的生成任务
                task = asyncio.create_task(self.generate_response(message_key, user_message))
                self.active_tasks[message_key] = task
                
                self.message_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"队列处理错误: {e}")
                await asyncio.sleep(1)

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            user_message = data.get('prompt', '')
            message_key = data.get('key', '')
            
            if not message_key:
                raise ValueError("Missing message key")
            
            # 检查限流
            if not await self.check_rate_limit():
                return
            
            # 将消息添加到队列
            await self.message_queue.put((message_key, user_message))
            
            # 启动队列处理（如果尚未启动）
            if not self.is_processing:
                self.is_processing = True
                asyncio.create_task(self.process_queue())
            
        except Exception as e:
            error_message = f"处理消息时出错: {str(e)}"
            print(f"{error_message}\n{traceback.format_exc()}")
            await self.send(text_data=json.dumps({
                'error': error_message,
                'key': message_key if 'message_key' in locals() else None,
                'done': True
            })) 