<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字分析提示词管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .prompt-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .prompt-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }
        
        .prompt-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .prompt-card h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .version-tags {
            margin: 10px 0;
        }
        
        .version-tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .version-tag:hover {
            background: #764ba2;
        }
        
        .version-tag.latest {
            background: #28a745;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            background: #764ba2;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .prompt-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 八字分析提示词管理系统</h1>
            <p>专业的提示词版本管理和优化平台</p>
        </div>
        
        <div class="main-content">
            <div class="section">
                <h2>📊 系统状态</h2>
                <button class="btn btn-success" onclick="loadPrompts()">🔄 刷新数据</button>
                <button class="btn btn-warning" onclick="reloadCache()">♻️ 重新加载缓存</button>
                <div id="statusMessage" class="status-message"></div>
            </div>
            
            <div class="section">
                <h2>📋 提示词列表</h2>
                <div id="promptGrid" class="prompt-grid">
                    <div class="loading">正在加载提示词数据...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 提示词内容查看模态框 -->
    <div id="promptModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">提示词内容</h2>
            <div id="modalContent" class="prompt-content"></div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let promptsData = {};
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPrompts();
        });
        
        // 加载提示词数据
        async function loadPrompts() {
            try {
                showStatus('正在加载提示词数据...', 'info');
                
                const response = await fetch('/api/bazi_analysis_api/prompts/available');
                const data = await response.json();
                
                if (data.success) {
                    promptsData = data.data;
                    renderPrompts();
                    showStatus('✅ 提示词数据加载成功', 'success');
                } else {
                    showStatus('❌ 加载失败: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('❌ 网络错误: ' + error.message, 'error');
            }
        }
        
        // 渲染提示词卡片
        function renderPrompts() {
            const grid = document.getElementById('promptGrid');
            
            if (!promptsData.prompts || promptsData.prompts.length === 0) {
                grid.innerHTML = '<div class="loading">暂无提示词数据</div>';
                return;
            }
            
            grid.innerHTML = promptsData.prompts.map(prompt => `
                <div class="prompt-card">
                    <h3>${prompt.name} (${prompt.category})</h3>
                    <p>可用版本:</p>
                    <div class="version-tags">
                        ${prompt.versions.map(version => `
                            <span class="version-tag ${version === prompt.latest_version ? 'latest' : ''}" 
                                  onclick="viewPrompt('${prompt.category}', '${version}')">
                                ${version} ${version === prompt.latest_version ? '(最新)' : ''}
                            </span>
                        `).join('')}
                    </div>
                    <button class="btn" onclick="viewPrompt('${prompt.category}', '${prompt.latest_version}')">
                        📖 查看最新版本
                    </button>
                </div>
            `).join('');
        }
        
        // 查看提示词内容
        async function viewPrompt(category, version) {
            try {
                showStatus('正在加载提示词内容...', 'info');
                
                const response = await fetch(`/api/bazi_analysis_api/prompts/content/${category}/${version}`);
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('modalTitle').textContent = 
                        `${data.data.display_name} - ${version}`;
                    document.getElementById('modalContent').textContent = data.data.content;
                    document.getElementById('promptModal').style.display = 'block';
                    showStatus('', '');
                } else {
                    showStatus('❌ 获取提示词内容失败: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('❌ 网络错误: ' + error.message, 'error');
            }
        }
        
        // 重新加载缓存
        async function reloadCache() {
            try {
                showStatus('正在重新加载缓存...', 'info');
                
                const response = await fetch('/api/bazi_analysis_api/prompts/reload', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    showStatus('✅ 缓存重新加载成功', 'success');
                    // 重新加载提示词数据
                    setTimeout(loadPrompts, 1000);
                } else {
                    showStatus('❌ 缓存重新加载失败: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('❌ 网络错误: ' + error.message, 'error');
            }
        }
        
        // 显示状态消息
        function showStatus(message, type) {
            const statusEl = document.getElementById('statusMessage');
            
            if (!message) {
                statusEl.style.display = 'none';
                return;
            }
            
            statusEl.textContent = message;
            statusEl.className = `status-message status-${type}`;
            statusEl.style.display = 'block';
            
            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);
            }
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('promptModal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('promptModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
