"""
问卷相关模型的Admin配置
包括Questionnaire、Question、Option和问卷计算历史记录的admin设置
"""

from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.contrib import messages
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.db.models import Count
from django.db.models.functions import TruncDate
import json
from datetime import timedelta, datetime

from ..models import Questionnaire, Question, Option
from ..models.questionnaire_models import QuestionnaireCalculationHistory, EnhancedQuestionnaireResponse


class OptionInline(admin.TabularInline):
    model = Option
    extra = 1
    fields = ('text', 'probability', 'weight', 'type')


class QuestionAdmin(admin.ModelAdmin):
    list_display = ('text', 'get_options')
    inlines = [OptionInline]
    search_fields = ['text']  # 添加搜索字段

    def get_options(self, obj):
        options = Option.objects.filter(question=obj)
        option_list = []
        for option in options:
            option_str = f"{option.text} (Prob: {option.probability}, Weight: {option.weight}, Type: {option.type})"
            option_list.append(option_str)
        return "\n".join(option_list)

    get_options.short_description = 'Options'


class QuestionInline(admin.TabularInline):
    model = Question
    extra = 1


class QuestionnaireAdmin(admin.ModelAdmin):
    list_display = ('name',)
    inlines = [QuestionInline]
    actions = ['delete_selected_questionnaires']

    def delete_selected_questionnaires(self, request, queryset):
        for questionnaire in queryset:
            questions = Question.objects.filter(questionnaire=questionnaire)
            for question in questions:
                Option.objects.filter(question=question).delete()
            questions.delete()
        queryset.delete()
        messages.success(request, "Selected questionnaires and associated questions and options have been deleted.")

    delete_selected_questionnaires.short_description = "Delete selected questionnaires"


# 问卷计算历史记录导入导出资源类
class QuestionnaireCalculationHistoryResource(resources.ModelResource):
    class Meta:
        model = QuestionnaireCalculationHistory
        fields = ('id', 'user__nickname', 'user__wx_phone_new', 'calculation_type', 'created_at')
        export_order = fields


@admin.register(QuestionnaireCalculationHistory)
class QuestionnaireCalculationHistoryAdmin(ImportExportModelAdmin):
    resource_class = QuestionnaireCalculationHistoryResource
    list_display = ('id', 'user_display', 'calculation_type', 'result_preview', 'created_at')
    list_filter = ('calculation_type', 'created_at')
    search_fields = ('user__nickname', 'user__wx_phone_new', 'calculation_type')
    readonly_fields = ('created_at', 'result_data_formatted', 'params_formatted')
    raw_id_fields = ('user',)
    actions = ['delete_selected_calculations', 'delete_all_by_user', 'clear_all_calculations']
    
    fieldsets = (
        ('基础信息', {
            'fields': ('user', 'calculation_type', 'created_at')
        }),
        ('计算结果', {
            'fields': ('result_data_formatted',)
        }),
        ('计算参数', {
            'fields': ('params_formatted',)
        }),
    )
    
    def get_queryset(self, request):
        # 添加用户关联信息预加载，提高性能
        return super().get_queryset(request).select_related('user')
    
    def user_display(self, obj):
        """显示用户信息"""
        if obj.user:
            return f"{obj.user.nickname or '未知'} (ID: {obj.user.id})" 
        return "未关联用户"
    user_display.short_description = '用户'
    
    def result_preview(self, obj):
        """显示计算结果预览"""
        if not obj.result_data:
            return "无数据"
        
        # 计算结果可能有不同格式，尝试提取关键信息
        result_count = len(obj.result_data)
        preview = []
        
        # 最多显示前3个结果
        for i, item in enumerate(obj.result_data[:3]):
            if "type" in item and ("percent" in item or "adjustedWeight" in item):
                percent = item.get("percent", item.get("adjustedWeight", 0))
                preview.append(f"{item['type']}: {percent}%")
            elif "text" in item and "adjustedWeight" in item:
                preview.append(f"{item['text']}: {item['adjustedWeight']}%")
        
        if result_count > 3:
            preview.append(f"... 共{result_count}项")
            
        return ", ".join(preview)
    result_preview.short_description = '结果预览'
    
    def result_data_formatted(self, obj):
        """格式化展示结果数据"""
        if not obj.result_data:
            return "无数据"
        
        data_str = json.dumps(obj.result_data, indent=4, ensure_ascii=False)
        return mark_safe(f'<pre style="max-height:400px;overflow-y:auto;">{data_str}</pre>')
    result_data_formatted.short_description = '计算结果详情'
    
    def params_formatted(self, obj):
        """格式化展示参数数据"""
        if not obj.params:
            return "无参数数据"
        
        params_str = json.dumps(obj.params, indent=4, ensure_ascii=False)
        return mark_safe(f'<pre style="max-height:400px;overflow-y:auto;">{params_str}</pre>')
    params_formatted.short_description = '计算参数详情'
    
    def delete_selected_calculations(self, request, queryset):
        """批量删除计算历史记录"""
        # 获取用户信息，用于消息提示
        user_info = []
        for calc in queryset.select_related('user')[:10]:  # 只获取前10个用户信息
            if calc.user:
                user_info.append(f"用户{calc.user.nickname or '未知'}(ID:{calc.user.id})")
        
        # 删除所选记录
        count = queryset.count()
        queryset.delete()
        
        # 构建消息
        msg = f"成功删除 {count} 条体质分析记录"
        if user_info:
            msg += f"，包含: {', '.join(user_info)}"
            if len(user_info) < count:
                msg += f" 等{count}位用户的记录"
        
        messages.success(request, msg)
    delete_selected_calculations.short_description = "删除所选体质分析记录"
    
    def delete_all_by_user(self, request, queryset):
        """删除所选用户的所有计算历史记录"""
        # 提取需要删除的用户ID
        user_ids = set()
        user_info = []
        
        for calc in queryset.select_related('user'):
            if calc.user and calc.user.id:
                user_ids.add(calc.user.id)
                if len(user_info) < 10:  # 只显示前10个用户信息
                    user_info.append(f"用户{calc.user.nickname or '未知'}(ID:{calc.user.id})")
        
        if not user_ids:
            messages.warning(request, "未找到有效的用户ID，无法执行删除操作")
            return
        
        # 获取这些用户的所有计算历史记录数量
        total_count = QuestionnaireCalculationHistory.objects.filter(user_id__in=user_ids).count()
        
        # 删除这些用户的所有计算历史记录
        deleted, _ = QuestionnaireCalculationHistory.objects.filter(user_id__in=user_ids).delete()
        
        # 构建消息
        msg = f"成功删除 {deleted} 条体质分析记录，涉及 {len(user_ids)} 位用户"
        if user_info:
            msg += f"，包括: {', '.join(user_info)}"
            if len(user_info) < len(user_ids):
                msg += f" 等用户"
        
        messages.success(request, msg)
    delete_all_by_user.short_description = "删除所选用户的所有体质分析记录"
    
    # 添加统计数据视图
    change_list_template = 'admin/questionnaire_calculation_change_list.html'
    
    def changelist_view(self, request, extra_context=None):
        # 处理确认清空所有数据的请求
        if 'confirm_clear' in request.GET and request.session.get('pending_clear_all'):
            pending_data = request.session.get('pending_clear_all')
            
            # 检查时间戳，确保确认请求不超过5分钟
            last_timestamp = datetime.fromisoformat(pending_data['timestamp'])
            if (timezone.now() - last_timestamp).total_seconds() > 300:
                messages.error(request, "确认已过期，如需清空所有数据，请重新发起请求")
            else:
                # 执行删除操作
                deleted, _ = QuestionnaireCalculationHistory.objects.all().delete()
                messages.success(request, f"已成功清空所有体质分析数据，共删除 {deleted} 条记录")
            
            # 清除会话中的确认数据
            if 'pending_clear_all' in request.session:
                del request.session['pending_clear_all']
                request.session.modified = True
        
        # 获取过去30天的统计数据
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        # 按计算类型分组统计
        calculation_types = list(
            QuestionnaireCalculationHistory.objects
            .values('calculation_type')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        # 计算总数
        total_count = sum(item['count'] for item in calculation_types)
        
        # 添加占比字段
        if total_count > 0:
            for item in calculation_types:
                item['percentage'] = round(item['count'] / total_count * 100, 2)
        else:
            for item in calculation_types:
                item['percentage'] = 0
        
        # 按日期统计分析次数
        daily_calculations = (
            QuestionnaireCalculationHistory.objects
            .filter(created_at__gte=thirty_days_ago)
            .annotate(date=TruncDate('created_at'))
            .values('date')
            .annotate(count=Count('id'))
            .order_by('date')
        )
        
        # 转为图表数据格式
        daily_data = [
            {'date': item['date'].strftime('%Y-%m-%d'), 'count': item['count']}
            for item in daily_calculations
        ]
        
        # 准备额外的上下文数据
        if extra_context is None:
            extra_context = {}
            
        extra_context.update({
            'calculation_types': calculation_types,
            'daily_data': mark_safe(json.dumps(daily_data)),
            'total_calculations': QuestionnaireCalculationHistory.objects.count(),
            'recent_30d_calculations': QuestionnaireCalculationHistory.objects.filter(
                created_at__gte=thirty_days_ago
            ).count()
        })
        
        # 调用父类方法
        return super().changelist_view(request, extra_context=extra_context)

    def clear_all_calculations(self, request, queryset):
        """清空所有体质分析数据 (危险操作)"""
        # 获取总记录数
        total_count = QuestionnaireCalculationHistory.objects.count()
        
        if total_count == 0:
            messages.info(request, "当前没有任何体质分析记录，无需清空")
            return
            
        # 确认框内容，需要在模板中处理
        request.session['pending_clear_all'] = {
            'action': 'clear_all_calculations',
            'total_count': total_count,
            'timestamp': timezone.now().isoformat()
        }
        
        # 构建消息提示用户需要确认
        messages.warning(
            request, 
            mark_safe(
                f'<strong>危险操作！</strong> 您即将删除所有 {total_count} 条体质分析记录，此操作不可恢复！<br>'
                f'<a href="?confirm_clear=1" class="button" '
                f'style="background-color:#ba2121;color:white;padding:5px 10px;margin-top:10px;display:inline-block;">'
                f'确认删除所有记录</a>'
            )
        )
    clear_all_calculations.short_description = "⚠️ 清空所有体质分析数据 (危险!)"


# 增强问卷响应Admin
@admin.register(EnhancedQuestionnaireResponse)
class EnhancedQuestionnaireResponseAdmin(admin.ModelAdmin):
    # 步骤7: 回到已知的好状态，然后只引入 user_custom_display (无 select_related)
    list_display = (
        'id', 
        'user_custom_display', # 测试这个方法，不使用 select_related
        'questionnaire_id', 
        'created_at', 
        'updated_at', 
        'latest',
        'version'
    )
    
    # 全部注释掉，以最大限度隔离
    # list_filter = (
    #     'questionnaire__name', 
    #     'created_at', 
    #     'updated_at', 
    #     'latest',
    # )
    # search_fields = ('questionnaire__name', 'user__id')
    # readonly_fields = (
    #     'user_custom_display',
    #     'questionnaire_id', 
    #     'created_at', 
    #     'updated_at', 
    # )
    # list_select_related = ('user', 'questionnaire') # 保持注释
    # date_hierarchy = 'created_at'
    # ordering = ('-created_at',)
    # fieldsets = ( 
    #    # ... 
    # )

    def user_custom_display(self, obj):
        try:
            if obj.user: # 这里会触发对 UserInfo 的单独查询
                return f"{obj.user.nickname or '未知昵称'} - ID:{obj.user.id}"
            elif hasattr(obj, 'user_id') and obj.user_id is not None:
                 return f"用户ID: {obj.user_id} (无法加载用户对象)"
            return "N/A (无关联用户)"
        except Exception as e:
            print(f"[ADMIN_ERROR] EnhancedQuestionnaireResponseAdmin.user_custom_display (obj.id={obj.id if obj else 'N/A'}): {type(e).__name__} - {str(e)}")
            return f"错误 (用户ID: {obj.user_id if obj and hasattr(obj, 'user_id') else '未知'})"
    user_custom_display.short_description = '用户 (昵称 - ID)'
    # user_custom_display.admin_order_field = 'user__nickname' # 暂时移除排序测试

    # 其他自定义方法暂时注释掉
    # def questionnaire_name_display(self, obj): ...
    # def answers_formatted(self, obj): ...
    # def total_scores_formatted(self, obj): ...
    # def formatted_total_scores_preview(self, obj): ...

# 注册相关模型到Admin
admin.site.register(Questionnaire, QuestionnaireAdmin)
admin.site.register(Question, QuestionAdmin) 