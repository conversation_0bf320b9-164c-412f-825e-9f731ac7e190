# 八字API文档

## 概述

本文档介绍lunar-python项目中八字相关的API接口。八字是中国传统命理学的重要组成部分，通过年、月、日、时四柱的天干地支组合来分析命理。本系统还集成了神煞计算功能，提供完整的八字分析体验。

## 快速开始

```python
from lunar_python import Solar, Lunar

# 通过阳历日期创建八字
solar = Solar.fromYmdHms(2005, 12, 23, 8, 37, 0)
lunar = solar.getLunar()
eight_char = lunar.getEightChar()

# 或通过阴历日期创建八字
lunar = Lunar.fromYmdHms(2005, 11, 22, 8, 37, 0)
eight_char = lunar.getEightChar()

print(eight_char)  # 输出：乙酉 戊子 辛巳 壬辰
```

## 核心类

### EightChar 类

八字主类，提供完整的八字分析功能。

#### 基础方法

##### 创建八字对象

```python
@staticmethod
def fromLunar(lunar) -> EightChar
```
- **功能**: 从农历对象创建八字
- **参数**: `lunar` - Lunar对象
- **返回**: EightChar对象

##### 获取完整八字

```python
def toString() -> str
def __str__() -> str
```
- **功能**: 获取完整八字字符串
- **返回**: 年柱 月柱 日柱 时柱，如"乙酉 戊子 辛巳 壬辰"

#### 年柱相关方法

##### 基础信息

```python
def getYear() -> str
```
- **功能**: 获取年柱
- **返回**: 年柱干支，如"乙酉"

```python
def getYearGan() -> str
```
- **功能**: 获取年干
- **返回**: 年干，如"乙"

```python
def getYearZhi() -> str
```
- **功能**: 获取年支
- **返回**: 年支，如"酉"

##### 年柱扩展信息

```python
def getYearHideGan() -> list
```
- **功能**: 获取年柱地支藏干
- **返回**: 藏干列表，包含主气、余气、杂气

```python
def getYearWuXing() -> str
```
- **功能**: 获取年柱五行
- **返回**: 年柱五行，如"木金"

```python
def getYearNaYin() -> str
```
- **功能**: 获取年柱纳音
- **返回**: 年柱纳音，如"泉中水"

```python
def getYearShiShenGan() -> str
```
- **功能**: 获取年柱天干十神
- **返回**: 十神，如"偏印"

```python
def getYearShiShenZhi() -> list
```
- **功能**: 获取年柱地支十神
- **返回**: 十神列表，对应藏干的十神

```python
def getYearDiShi() -> str
```
- **功能**: 获取年柱地势（长生十二神）
- **返回**: 地势，如"长生"、"沐浴"等

##### 年柱旬空

```python
def getYearXun() -> str
```
- **功能**: 获取年柱所在旬
- **返回**: 旬，如"甲午"

```python
def getYearXunKong() -> str
```
- **功能**: 获取年柱旬空(空亡)
- **返回**: 旬空，如"辰巳"

#### 月柱相关方法

##### 基础信息

```python
def getMonth() -> str
```
- **功能**: 获取月柱
- **返回**: 月柱干支，如"戊子"

```python
def getMonthGan() -> str
```
- **功能**: 获取月干
- **返回**: 月干，如"戊"

```python
def getMonthZhi() -> str
```
- **功能**: 获取月支
- **返回**: 月支，如"子"

##### 月柱扩展信息

```python
def getMonthHideGan() -> list
```
- **功能**: 获取月柱地支藏干
- **返回**: 藏干列表

```python
def getMonthWuXing() -> str
```
- **功能**: 获取月柱五行
- **返回**: 月柱五行，如"土水"

```python
def getMonthNaYin() -> str
```
- **功能**: 获取月柱纳音
- **返回**: 月柱纳音

```python
def getMonthShiShenGan() -> str
```
- **功能**: 获取月柱天干十神
- **返回**: 十神

```python
def getMonthShiShenZhi() -> list
```
- **功能**: 获取月柱地支十神
- **返回**: 十神列表

```python
def getMonthDiShi() -> str
```
- **功能**: 获取月柱地势（长生十二神）
- **返回**: 地势

##### 月柱旬空

```python
def getMonthXun() -> str
```
- **功能**: 获取月柱所在旬
- **返回**: 旬

```python
def getMonthXunKong() -> str
```
- **功能**: 获取月柱旬空(空亡)
- **返回**: 旬空

#### 日柱相关方法

##### 基础信息

```python
def getDay() -> str
```
- **功能**: 获取日柱
- **返回**: 日柱干支，如"辛巳"

```python
def getDayGan() -> str
```
- **功能**: 获取日干（日元）
- **返回**: 日干，如"辛"

```python
def getDayZhi() -> str
```
- **功能**: 获取日支
- **返回**: 日支，如"巳"

##### 日柱扩展信息

```python
def getDayHideGan() -> list
```
- **功能**: 获取日柱地支藏干
- **返回**: 藏干列表

```python
def getDayWuXing() -> str
```
- **功能**: 获取日柱五行
- **返回**: 日柱五行，如"金火"

```python
def getDayNaYin() -> str
```
- **功能**: 获取日柱纳音
- **返回**: 日柱纳音

```python
def getDayShiShenGan() -> str
```
- **功能**: 获取日柱天干十神（日元）
- **返回**: "日主"

```python
def getDayShiShenZhi() -> list
```
- **功能**: 获取日柱地支十神
- **返回**: 十神列表

```python
def getDayDiShi() -> str
```
- **功能**: 获取日柱地势（长生十二神）
- **返回**: 地势

##### 日柱旬空

```python
def getDayXun() -> str
```
- **功能**: 获取日柱所在旬
- **返回**: 旬

```python
def getDayXunKong() -> str
```
- **功能**: 获取日柱旬空(空亡)
- **返回**: 旬空

#### 时柱相关方法

##### 基础信息

```python
def getTime() -> str
```
- **功能**: 获取时柱
- **返回**: 时柱干支，如"壬辰"

```python
def getTimeGan() -> str
```
- **功能**: 获取时干
- **返回**: 时干，如"壬"

```python
def getTimeZhi() -> str
```
- **功能**: 获取时支
- **返回**: 时支，如"辰"

##### 时柱扩展信息

```python
def getTimeHideGan() -> list
```
- **功能**: 获取时柱地支藏干
- **返回**: 藏干列表

```python
def getTimeWuXing() -> str
```
- **功能**: 获取时柱五行
- **返回**: 时柱五行，如"水土"

```python
def getTimeNaYin() -> str
```
- **功能**: 获取时柱纳音
- **返回**: 时柱纳音

```python
def getTimeShiShenGan() -> str
```
- **功能**: 获取时柱天干十神
- **返回**: 十神

```python
def getTimeShiShenZhi() -> list
```
- **功能**: 获取时柱地支十神
- **返回**: 十神列表

```python
def getTimeDiShi() -> str
```
- **功能**: 获取时柱地势（长生十二神）
- **返回**: 地势

##### 时柱旬空

```python
def getTimeXun() -> str
```
- **功能**: 获取时柱所在旬
- **返回**: 旬

```python
def getTimeXunKong() -> str
```
- **功能**: 获取时柱旬空(空亡)
- **返回**: 旬空

#### 特殊宫位

```python
def getTaiYuan() -> str
```
- **功能**: 获取胎元
- **返回**: 胎元干支

```python
def getTaiYuanNaYin() -> str
```
- **功能**: 获取胎元纳音
- **返回**: 胎元纳音

```python
def getTaiXi() -> str
```
- **功能**: 获取胎息
- **返回**: 胎息干支

```python
def getTaiXiNaYin() -> str
```
- **功能**: 获取胎息纳音
- **返回**: 胎息纳音

```python
def getMingGong() -> str
```
- **功能**: 获取命宫
- **返回**: 命宫干支

```python
def getMingGongNaYin() -> str
```
- **功能**: 获取命宫纳音
- **返回**: 命宫纳音

```python
def getShenGong() -> str
```
- **功能**: 获取身宫
- **返回**: 身宫干支

```python
def getShenGongNaYin() -> str
```
- **功能**: 获取身宫纳音
- **返回**: 身宫纳音

#### 运势分析

```python
def getYun(gender: int, sect: int = 1) -> Yun
```
- **功能**: 获取运势对象
- **参数**: 
  - `gender`: 性别，1为男，0为女
  - `sect`: 流派，1按天数和时辰数计算，2按分钟数计算
- **返回**: Yun运势对象

#### 配置方法

```python
def getSect() -> int
```
- **功能**: 获取流派
- **返回**: 流派数值

```python
def setSect(sect: int)
```
- **功能**: 设置流派
- **参数**: `sect` - 流派，1或2

```python
def getLunar() -> Lunar
```
- **功能**: 获取关联的农历对象
- **返回**: Lunar对象

#### 九星相关方法

通过关联的Lunar对象可以获取九星信息：

```python
# 通过八字获取九星
lunar = eight_char.getLunar()

# 获取年九星
year_nine_star = lunar.getYearNineStar(sect=2)

# 获取月九星
month_nine_star = lunar.getMonthNineStar(sect=2)

# 获取日九星
day_nine_star = lunar.getDayNineStar()

# 获取时九星
time_nine_star = lunar.getTimeNineStar()
```

## 九星相关类

### NineStar 类

九星类，提供完整的九星分析功能。

#### 创建九星对象

```python
@staticmethod
def fromIndex(index: int) -> NineStar
```
- **功能**: 从索引创建九星对象
- **参数**: `index` - 九星索引（0-8）
- **返回**: NineStar对象

#### 基础信息

```python
def getNumber() -> str
```
- **功能**: 获取九星数字
- **返回**: 数字，如"一"、"二"等

```python
def getColor() -> str
```
- **功能**: 获取九星颜色
- **返回**: 颜色，如"白"、"黑"、"碧"等

```python
def getWuXing() -> str
```
- **功能**: 获取九星五行
- **返回**: 五行，如"水"、"土"、"木"等

```python
def getPosition() -> str
```
- **功能**: 获取九星方位
- **返回**: 方位，如"坎"、"坤"、"震"等

```python
def getPositionDesc() -> str
```
- **功能**: 获取九星方位描述
- **返回**: 方位描述，如"正北"、"西南"等

#### 不同体系的九星名称

```python
def getNameInBeiDou() -> str
```
- **功能**: 获取北斗九星名称
- **返回**: 北斗星名，如"天枢"、"天璇"等

```python
def getNameInXuanKong() -> str
```
- **功能**: 获取玄空九星名称
- **返回**: 玄空星名，如"贪狼"、"巨门"等

```python
def getNameInQiMen() -> str
```
- **功能**: 获取奇门九星名称
- **返回**: 奇门星名，如"天蓬"、"天芮"等

```python
def getNameInTaiYi() -> str
```
- **功能**: 获取太乙九星名称
- **返回**: 太乙星名，如"太乙"、"摄提"等

#### 吉凶属性

```python
def getLuckInXuanKong() -> str
```
- **功能**: 获取玄空九星吉凶
- **返回**: 吉凶，如"吉"、"凶"

```python
def getLuckInQiMen() -> str
```
- **功能**: 获取奇门九星吉凶
- **返回**: 吉凶，如"吉"、"凶"

```python
def getTypeInTaiYi() -> str
```
- **功能**: 获取太乙九星类型
- **返回**: 类型，如"吉神"、"凶神"、"安神"

#### 奇门相关

```python
def getBaMenInQiMen() -> str
```
- **功能**: 获取奇门八门
- **返回**: 八门，如"休"、"死"、"伤"等

```python
def getYinYangInQiMen() -> str
```
- **功能**: 获取奇门阴阳属性
- **返回**: 阴阳属性

#### 输出方法

```python
def toString() -> str
```
- **功能**: 获取九星简要信息
- **返回**: 如"六白金开阳"

```python
def toFullString() -> str
```
- **功能**: 获取九星完整信息
- **返回**: 包含所有体系信息的完整字符串

### Lunar 类中的九星方法

#### 年九星

```python
def getYearNineStar(sect: int = 2) -> NineStar
```
- **功能**: 获取年九星
- **参数**: `sect` - 流派，1为农历年，2为立春年，3为精确年
- **返回**: NineStar对象

#### 月九星

```python
def getMonthNineStar(sect: int = 2) -> NineStar
```
- **功能**: 获取月九星
- **参数**: `sect` - 流派，1为农历月，2为节气月，3为精确月
- **返回**: NineStar对象

#### 日九星

```python
def getDayNineStar() -> NineStar
```
- **功能**: 获取日九星
- **返回**: NineStar对象

#### 时九星

```python
def getTimeNineStar() -> NineStar
```
- **功能**: 获取时九星
- **返回**: NineStar对象

### LunarTime 类中的九星方法

```python
def getNineStar() -> NineStar
```
- **功能**: 获取时辰九星
- **返回**: NineStar对象

## 神煞相关功能

### 神煞计算器

本系统集成了完整的神煞计算功能，支持40+种神煞的计算。

#### 主要接口

```python
from shensha_calculator import query_shensha

def query_shensha(ganzhi: str, bazi: List[str], is_man: bool, witch: int, nian_nayin: str = None) -> List[str]
```

**参数说明:**
- `ganzhi`: 要查询的干支，如"甲子"
- `bazi`: 八字数组，8个元素分别是年干、年支、月干、月支、日干、日支、时干、时支
- `is_man`: 性别，True为男性，False为女性
- `witch`: 柱位，1-4分别代表年/月/日/时柱
- `nian_nayin`: 年柱纳音（可选），查询学堂、词馆时需要

**返回值:**
- 返回神煞名称列表

#### 支持的神煞类型

##### 贵人类神煞
- **天乙贵人**: 命中贵人，逢凶化吉
- **太极贵人**: 聪明好学，有宗教缘分
- **文昌贵人**: 利于学业和文化事业
- **国印贵人**: 有权威，利于从政
- **金舆**: 富贵之象
- **福星**: 福气深厚
- **天厨贵人**: 衣食无忧
- **德秀贵人**: 品德高尚

##### 德合类神煞
- **天德贵人**: 天德护佑，逢凶化吉
- **月德贵人**: 月德护佑，品德高尚
- **天德合**: 与天德贵人相合
- **月德合**: 与月德贵人相合
- **天赦**: 天赦之日，大吉

##### 星煞类神煞
- **驿马**: 奔波劳碌，利于远行
- **华盖**: 孤高清雅，有艺术天分
- **将星**: 有领导才能
- **禄神**: 财禄丰厚
- **红鸾**: 利于婚姻感情
- **天喜**: 喜庆之事
- **五鬼**: 小人是非
- **天医**: 利于医药行业
- **学堂**: 利于学业
- **词馆**: 文学才华

##### 刃煞类神煞
- **羊刃**: 性格刚烈，易有血光
- **飞刃**: 意外伤害
- **血刃**: 血光之灾
- **金神**: 性格刚硬

##### 凶煞类神煞
- **空亡**: 虚空不实
- **劫煞**: 破财劫难
- **灾煞**: 灾难疾病
- **亡神**: 死亡之神
- **孤辰**: 男命孤独
- **寡宿**: 女命孤独
- **天罗地网**: 困顿不通
- **十恶大败**: 大凶之日
- **桃花**: 异性缘分
- **孤鸾**: 婚姻不利
- **阴差阳错**: 婚姻波折
- **四废**: 无用之时
- **丧门**: 丧事孝服
- **吊客**: 悲伤哭泣
- **披麻**: 孝服缠身
- **童子**: 童子命
- **流霞**: 血光之灾
- **红艳**: 桃花劫
- **魁罡**: 性格刚烈
- **八专**: 专一固执
- **九丑**: 相貌不佳
- **十灵**: 灵异体质
- **元辰**: 运势低迷

#### 使用示例

```python
from shensha_calculator import query_shensha

# 构造八字数组
bazi = ["戊", "辰", "甲", "寅", "庚", "子", "戊", "子"]

# 查询日柱神煞
day_shenshas = query_shensha("庚子", bazi, True, 3, "大林木")
print(f"日柱神煞: {day_shenshas}")

# 查询年柱神煞
year_shenshas = query_shensha("戊辰", bazi, True, 1, "大林木")
print(f"年柱神煞: {year_shenshas}")
```

## 运势相关类

### Yun 类（运）

运势主类，用于分析大运、流年等。

```python
def __init__(eight_char: EightChar, gender: int, sect: int = 1)
```
- **功能**: 创建运势对象
- **参数**: 
  - `eight_char`: 八字对象
  - `gender`: 性别，1为男，0为女
  - `sect`: 流派

#### 起运信息

```python
def getStartYear() -> int
```
- **功能**: 获取起运年数
- **返回**: 起运年数

```python
def getStartMonth() -> int
```
- **功能**: 获取起运月数
- **返回**: 起运月数

```python
def getStartDay() -> int
```
- **功能**: 获取起运天数
- **返回**: 起运天数

```python
def getStartSolar() -> Solar
```
- **功能**: 获取起运的阳历日期
- **返回**: Solar阳历对象

#### 大运

```python
def getDaYun(n: int = 10) -> list
```
- **功能**: 获取大运列表
- **参数**: `n` - 轮数，默认10轮
- **返回**: DaYun大运对象列表

### DaYun 类（大运）

大运类，每个大运管10年。

#### 基础信息

```python
def getStartYear() -> int
```
- **功能**: 获取大运开始年份
- **返回**: 开始年份

```python
def getEndYear() -> int
```
- **功能**: 获取大运结束年份
- **返回**: 结束年份

```python
def getStartAge() -> int
```
- **功能**: 获取大运开始年龄
- **返回**: 开始年龄

```python
def getEndAge() -> int
```
- **功能**: 获取大运结束年龄
- **返回**: 结束年龄

```python
def getIndex() -> int
```
- **功能**: 获取大运索引
- **返回**: 索引

```python
def getGanZhi() -> str
```
- **功能**: 获取大运干支
- **返回**: 干支

#### 旬空信息

```python
def getXun() -> str
```
- **功能**: 获取所在旬
- **返回**: 旬

```python
def getXunKong() -> str
```
- **功能**: 获取旬空(空亡)
- **返回**: 旬空

#### 流年和小运

```python
def getLiuNian(n: int = 10) -> list
```
- **功能**: 获取流年列表
- **参数**: `n` - 轮数，默认10轮
- **返回**: LiuNian流年对象列表

```python
def getXiaoYun(n: int = 10) -> list
```
- **功能**: 获取小运列表
- **参数**: `n` - 轮数，默认10轮
- **返回**: XiaoYun小运对象列表

### LiuNian 类（流年）

流年类，每个流年代表一年。

#### 基础信息

```python
def getIndex() -> int
```
- **功能**: 获取流年索引
- **返回**: 索引

```python
def getYear() -> int
```
- **功能**: 获取流年年份
- **返回**: 年份

```python
def getAge() -> int
```
- **功能**: 获取流年年龄
- **返回**: 年龄

```python
def getGanZhi() -> str
```
- **功能**: 获取流年干支
- **返回**: 干支

#### 旬空信息

```python
def getXun() -> str
```
- **功能**: 获取所在旬
- **返回**: 旬

```python
def getXunKong() -> str
```
- **功能**: 获取旬空(空亡)
- **返回**: 旬空

#### 流月

```python
def getLiuYue() -> list
```
- **功能**: 获取流月列表
- **返回**: LiuYue流月对象列表（12个月）

### XiaoYun 类（小运）

小运类，每个小运代表一年。

#### 基础信息

```python
def getIndex() -> int
```
- **功能**: 获取小运索引
- **返回**: 索引

```python
def getYear() -> int
```
- **功能**: 获取小运年份
- **返回**: 年份

```python
def getAge() -> int
```
- **功能**: 获取小运年龄
- **返回**: 年龄

```python
def getGanZhi() -> str
```
- **功能**: 获取小运干支
- **返回**: 干支

#### 旬空信息

```python
def getXun() -> str
```
- **功能**: 获取所在旬
- **返回**: 旬

```python
def getXunKong() -> str
```
- **功能**: 获取旬空(空亡)
- **返回**: 旬空

### LiuYue 类（流月）

流月类，每个流月代表一个月。

#### 基础信息

```python
def getIndex() -> int
```
- **功能**: 获取流月索引
- **返回**: 索引

```python
def getMonthInChinese() -> str
```
- **功能**: 获取中文月份
- **返回**: 中文月份，如"正"、"二"等

```python
def getGanZhi() -> str
```
- **功能**: 获取流月干支（使用五虎遁法）
- **返回**: 干支

#### 旬空信息

```python
def getXun() -> str
```
- **功能**: 获取所在旬
- **返回**: 旬

```python
def getXunKong() -> str
```
- **功能**: 获取旬空(空亡)
- **返回**: 旬空

## 使用示例

### 基础八字分析

```python
from lunar_python import Solar

# 创建八字
solar = Solar.fromYmdHms(1988, 2, 15, 23, 30, 0)
lunar = solar.getLunar()
eight_char = lunar.getEightChar()

# 获取四柱
print(f"年柱: {eight_char.getYear()}")
print(f"月柱: {eight_char.getMonth()}")
print(f"日柱: {eight_char.getDay()}")
print(f"时柱: {eight_char.getTime()}")

# 获取五行
print(f"年柱五行: {eight_char.getYearWuXing()}")
print(f"月柱五行: {eight_char.getMonthWuXing()}")
print(f"日柱五行: {eight_char.getDayWuXing()}")
print(f"时柱五行: {eight_char.getTimeWuXing()}")

# 获取十神
print(f"年干十神: {eight_char.getYearShiShenGan()}")
print(f"月干十神: {eight_char.getMonthShiShenGan()}")
print(f"日干十神: {eight_char.getDayShiShenGan()}")
print(f"时干十神: {eight_char.getTimeShiShenGan()}")
```

### 运势分析

```python
# 女命运势分析
yun = eight_char.getYun(0)  # 0表示女性

print(f"起运时间: {yun.getStartYear()}年{yun.getStartMonth()}个月{yun.getStartDay()}天后")
print(f"起运阳历: {yun.getStartSolar().toYmd()}")

# 获取大运
da_yun_list = yun.getDaYun(8)  # 获取8步大运
for i, da_yun in enumerate(da_yun_list):
    print(f"大运[{i}]: {da_yun.getStartYear()}年-{da_yun.getEndYear()}年 "
          f"{da_yun.getStartAge()}岁-{da_yun.getEndAge()}岁 {da_yun.getGanZhi()}")

# 获取第一步大运的流年
liu_nian_list = da_yun_list[0].getLiuNian()
for i, liu_nian in enumerate(liu_nian_list):
    print(f"流年[{i}]: {liu_nian.getYear()}年 {liu_nian.getAge()}岁 {liu_nian.getGanZhi()}")

# 获取流年的流月
liu_yue_list = liu_nian_list[0].getLiuYue()
for i, liu_yue in enumerate(liu_yue_list):
    print(f"流月[{i}]: {liu_yue.getMonthInChinese()}月 {liu_yue.getGanZhi()}")
```

### 特殊宫位分析

```python
# 获取特殊宫位
print(f"胎元: {eight_char.getTaiYuan()} 纳音: {eight_char.getTaiYuanNaYin()}")
print(f"胎息: {eight_char.getTaiXi()} 纳音: {eight_char.getTaiXiNaYin()}")
print(f"命宫: {eight_char.getMingGong()} 纳音: {eight_char.getMingGongNaYin()}")
print(f"身宫: {eight_char.getShenGong()} 纳音: {eight_char.getShenGongNaYin()}")

# 获取旬空信息
print(f"年柱旬空: {eight_char.getYearXunKong()}")
print(f"月柱旬空: {eight_char.getMonthXunKong()}")
print(f"日柱旬空: {eight_char.getDayXunKong()}")
print(f"时柱旬空: {eight_char.getTimeXunKong()}")
```

### 九星分析

```python
# 获取农历对象
lunar = eight_char.getLunar()

# 获取年月日时九星
year_nine_star = lunar.getYearNineStar()
month_nine_star = lunar.getMonthNineStar()
day_nine_star = lunar.getDayNineStar()
time_nine_star = lunar.getTimeNineStar()

print(f"年九星: {year_nine_star.toString()}")
print(f"月九星: {month_nine_star.toString()}")
print(f"日九星: {day_nine_star.toString()}")
print(f"时九星: {time_nine_star.toString()}")

# 获取九星详细信息
print(f"年九星详细: {year_nine_star.toFullString()}")

# 获取九星各种属性
print(f"年九星数字: {year_nine_star.getNumber()}")
print(f"年九星颜色: {year_nine_star.getColor()}")
print(f"年九星五行: {year_nine_star.getWuXing()}")
print(f"年九星方位: {year_nine_star.getPosition()}")
print(f"年九星方位描述: {year_nine_star.getPositionDesc()}")

# 获取不同体系的九星名称
print(f"北斗九星: {year_nine_star.getNameInBeiDou()}")
print(f"玄空九星: {year_nine_star.getNameInXuanKong()}")
print(f"奇门九星: {year_nine_star.getNameInQiMen()}")
print(f"太乙九星: {year_nine_star.getNameInTaiYi()}")

# 获取吉凶属性
print(f"玄空吉凶: {year_nine_star.getLuckInXuanKong()}")
print(f"奇门吉凶: {year_nine_star.getLuckInQiMen()}")
print(f"太乙类型: {year_nine_star.getTypeInTaiYi()}")
```

### 神煞分析

```python
from shensha_calculator import query_shensha

# 构造八字数组
bazi = [
    eight_char.getYearGan(), eight_char.getYearZhi(),
    eight_char.getMonthGan(), eight_char.getMonthZhi(),
    eight_char.getDayGan(), eight_char.getDayZhi(),
    eight_char.getTimeGan(), eight_char.getTimeZhi()
]

# 获取年柱纳音
nian_nayin = eight_char.getYearNaYin()

# 性别转换 (1男0女 -> True男False女)
is_man = True  # 根据实际情况设置

# 分析各柱神煞
pillars = [
    ("年柱", eight_char.getYear(), 1),
    ("月柱", eight_char.getMonth(), 2),
    ("日柱", eight_char.getDay(), 3),
    ("时柱", eight_char.getTime(), 4)
]

for name, ganzhi, witch in pillars:
    try:
        shenshas = query_shensha(ganzhi, bazi, is_man, witch, nian_nayin)
        print(f"{name} {ganzhi}: {', '.join(shenshas) if shenshas else '无神煞'}")

        # 分类显示神煞
        if shenshas:
            categories = {
                "贵人类": ["天乙", "太极", "文昌", "国印", "金舆", "福星", "天厨", "德秀"],
                "德合类": ["天德", "月德", "天德合", "月德合", "天赦"],
                "星煞类": ["驿马", "华盖", "将星", "禄神", "红鸾", "天喜", "五鬼", "天医", "学堂", "词馆"],
                "刃煞类": ["羊刃", "飞刃", "血刃", "金神"],
                "凶煞类": ["空亡", "劫煞", "灾煞", "亡神", "孤辰", "寡宿", "天罗", "地网", "十恶大败",
                          "桃花", "孤鸾", "阴差阳错", "四废", "丧门", "吊客", "披麻", "童子", "流霞",
                          "红艳", "魁罡", "八专", "九丑", "十灵", "元辰"]
            }

            for category, shensha_list in categories.items():
                category_shenshas = [s for s in shenshas if s in shensha_list]
                if category_shenshas:
                    print(f"  {category}: {', '.join(category_shenshas)}")

    except Exception as e:
        print(f"{name} {ganzhi}: 查询出错 - {e}")
```

## 常量说明

### 长生十二神
- **十二神**: 长生、沐浴、冠带、临官、帝旺、衰、病、死、墓、绝、胎、养
- **含义**: 表示五行在十二地支中的旺衰状态
- **计算**: 以日干为主，看其在年月日时各支中的长生十二神状态
- **应用**: 用于判断命局强弱、分析人生各阶段运势

### 十神
- **十神类型**: 比肩、劫财、食神、伤官、偏财、正财、七杀、正官、偏印、正印
- **计算方法**: 以日干为中心，其他干支与日干的五行关系确定十神
- **天干十神**: 年干、月干、时干相对于日干的十神
- **地支十神**: 地支藏干相对于日干的十神
- **日干**: 固定为"日主"

### 五行
- 金、木、水、火、土

### 纳音
- 六十甲子纳音，如"海中金"、"炉中火"等

### 九星
- 一白水星（坎）、二黑土星（坤）、三碧木星（震）、四绿木星（巽）、五黄土星（中）
- 六白金星（乾）、七赤金星（兑）、八白土星（艮）、九紫火星（离）

### 九星体系
- **北斗九星**: 天枢、天璇、天玑、天权、玉衡、开阳、摇光、洞明、隐元
- **玄空九星**: 贪狼、巨门、禄存、文曲、廉贞、武曲、破军、左辅、右弼
- **奇门九星**: 天蓬、天芮、天冲、天辅、天禽、天心、天柱、天任、天英
- **太乙九星**: 太乙、摄提、轩辕、招摇、天符、青龙、咸池、太阴、天乙

## 注意事项

1. 八字计算基于精确的天文算法，考虑真太阳时
2. 支持两种流派的日柱计算方法，可通过`setSect()`方法设置
3. 十神计算以日干为中心，其他干支与日干的关系确定十神
4. 地支藏干可能包含1-3个天干，对应不同的十神
5. 运势分析需要指定性别，男女起运方向不同
6. 流年干支以立春为界，不是以正月初一为界
7. 九星计算支持不同流派，可通过`sect`参数设置：
   - sect=1: 农历年月
   - sect=2: 立春年/节气月（默认）
   - sect=3: 精确年月
8. 九星包含多个体系（北斗、玄空、奇门、太乙），每个体系有不同的名称和吉凶属性
9. 时九星的计算与冬至、夏至节气相关，会影响顺逆排布
10. 神煞计算需要完整的八字信息，包括年柱纳音
11. 神煞的吉凶含义需要结合具体命局分析，不可单独论断
12. 某些神煞（如元辰）的计算会受到性别影响
13. 学堂、词馆神煞需要提供年柱纳音才能准确计算

## 错误处理

所有方法都会返回相应的结果，如果输入参数有误，可能返回空字符串或空列表。建议在使用前验证输入的日期是否有效。

## 版本兼容性

本API基于lunar-python库，支持Python 3.x版本。
