# -*- coding: utf-8 -*-
"""
每日中医题目相关API - 异步版本 (性能优化版)
用于替代传统的Django View，解决连接池泄漏问题
主要优化：数据库层面随机查询、避免加载大量数据到内存、优化缓存策略
新增功能：支持难度和分类筛选，优化数据结构返回
"""

from ninja import Router, Query
from django.http import JsonResponse
from django.core.cache import cache
from django.db import models, connection
# from asgiref.sync import sync_to_async  # 已移除，使用 async_utils 替代
import logging
import json
import random
import functools
import time
from typing import Optional, Dict
from django.utils import timezone
import math

from api.ninja_apis.async_utils import (
    get_async, filter_async, save_async, create_async, count_async, select_related_async,
    filter_with_annotations_async, run_sync_function_async
)
from api.models import TCMQuestion, Category, UserInfo, DailyTCMQuizRecord
from api.utils.rate_limiter import rate_limit

logger = logging.getLogger(__name__)

def api_timer(func_name=None):
    """
    API耗时计算装饰器
    打印API执行时间，用于性能监控和优化
    
    Args:
        func_name: 自定义函数名，如果不提供则使用实际函数名
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            name = func_name or func.__name__
            print(f"[API_TIMER] 🚀 {name} 开始执行")
            
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ✅ {name} 执行完成，耗时: {duration:.3f}秒")
                return result
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ❌ {name} 执行失败，耗时: {duration:.3f}秒，错误: {str(e)}")
                raise
                
        return wrapper
    return decorator

def serialize_tcm_question(question):
    """
    优化的题目序列化函数，返回更友好的数据结构
    """
    return {
        'id': question.id,
        'content': question.content,
        'option_a': question.option_a,
        'option_b': question.option_b,
        'option_c': question.option_c,
        'option_d': question.option_d,
        'answer': question.answer,
        'explanation': question.explanation,
        'difficulty': question.difficulty,
        'difficulty_text': get_difficulty_text(question.difficulty),
        'category_id': question.category.id,
        'category_name': question.category.name,
        'question_type': getattr(question, 'question_type', 'MCQ'),
        'image': question.image.url if question.image else None
    }

def get_difficulty_text(difficulty):
    """将难度数字转换为文字描述"""
    difficulty_map = {
        1: "初级",
        2: "中级", 
        3: "高级"
    }
    return difficulty_map.get(difficulty, f"难度{difficulty}")

async def get_random_questions_optimized(filter_kwargs, limit=10):
    """
    性能优化的随机题目获取函数
    使用数据库层面的随机查询，避免加载大量数据到内存
    """
    # 构建基础查询
    queryset = TCMQuestion.objects.select_related('category')

    if filter_kwargs:
        queryset = queryset.filter(**filter_kwargs)

    # 获取总数，用于随机选择
    total_count = await count_async(queryset)
    if total_count == 0:
        return []

    if total_count <= limit:
        # 如果总数不超过需要的数量，直接返回所有
        return await select_related_async(queryset, 'category')

    # 性能优化：使用数据库随机排序，只取需要的数量
    # 对于MySQL，使用RAND()函数进行随机排序
    random_queryset = queryset.order_by('?')[:limit]
    return await select_related_async(random_queryset, 'category')

# 创建每日中医题目API路由器
daily_tcm_router = Router()

@daily_tcm_router.get("/daily-tcm-questions/")
@api_timer("获取每日中医题目_带筛选")
@rate_limit("daily_tcm_questions_api", normal_limit=40, member_limit=150)  # 题目查询相对频繁
async def get_daily_tcm_questions_async(
    request,
    difficulty: Optional[int] = Query(None, description="难度筛选: 1=初级, 2=中级, 3=高级"),
    category_id: Optional[int] = Query(None, description="分类筛选: 传入分类ID"),
    limit: Optional[int] = Query(10, description="题目数量，默认10道")
):
    """
    异步获取每日中医题目 - 支持筛选功能
    
    参数说明：
    - difficulty: 难度筛选 (1=初级, 2=中级, 3=高级)
    - category_id: 分类筛选 (传入分类ID)
    - limit: 返回题目数量，默认10道，最多20道
    """
    try:
        # 限制题目数量范围
        limit = min(max(limit, 1), 20)
        
        # 构建筛选条件
        filter_kwargs = {}
        if difficulty is not None:
            if difficulty not in [1, 2, 3]:
                return JsonResponse({'error': '难度参数无效，请传入1、2或3'}, status=400)
            filter_kwargs['difficulty'] = difficulty
            
        if category_id is not None:
            filter_kwargs['category_id'] = category_id
        
        print(f"[FILTER] 筛选条件: {filter_kwargs}, 题目数量: {limit}")
        
        # 构建缓存键
        cache_key_parts = ["daily_tcm_questions"]
        if difficulty is not None:
            cache_key_parts.append(f"diff_{difficulty}")
        if category_id is not None:
            cache_key_parts.append(f"cat_{category_id}")
        cache_key_parts.append(f"limit_{limit}")
        cache_key = ":".join(cache_key_parts)
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存数据")
                    return JsonResponse({
                        'questions': cached_data,
                        'total_count': len(cached_data),
                        'filters': {
                            'difficulty': difficulty,
                            'category_id': category_id,
                            'limit': limit
                        }
                    })
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 缓存数据解析失败")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        # 🚀 性能优化：直接获取随机题目，不加载所有数据
        selected_questions = await get_random_questions_optimized(filter_kwargs, limit=limit)

        if not selected_questions:
            return JsonResponse({
                'error': '暂无符合条件的题目',
                'filters': {
                    'difficulty': difficulty,
                    'category_id': category_id,
                    'limit': limit
                }
            }, status=404)

        print(f"[PERF] 实际获取题目数量: {len(selected_questions)}")

        # 使用优化的序列化函数
        serialized_data = []
        for question in selected_questions:
            serialized_data.append(serialize_tcm_question(question))

        # 🚀 优化缓存策略：缓存30分钟
        try:
            cache.set(cache_key, json.dumps(serialized_data), timeout=1800)  # 30分钟
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 数据已缓存30分钟")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse({
            'questions': serialized_data,
            'total_count': len(serialized_data),
            'filters': {
                'difficulty': difficulty,
                'difficulty_text': get_difficulty_text(difficulty) if difficulty else None,
                'category_id': category_id,
                'limit': limit
            }
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取每日中医题目失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.post("/daily-tcm-questions/")
@api_timer("快速获取题目_POST版")
@rate_limit("submit_tcm_questions_api", normal_limit=30, member_limit=120)  # 答题提交适中限制
async def post_daily_tcm_questions_async(
    request,
    difficulty: Optional[int] = Query(None, description="难度筛选: 1=初级, 2=中级, 3=高级"),
    category_id: Optional[int] = Query(None, description="分类筛选: 传入分类ID"),
    limit: Optional[int] = Query(5, description="题目数量，默认5道")
):
    """
    快速获取中医题目 - POST版本，默认返回5道题
    参数与GET版本相同，但默认数量为5道
    """
    try:
        # 限制题目数量范围
        limit = min(max(limit, 1), 10)  # POST版本最多10道
        
        # 构建筛选条件
        filter_kwargs = {}
        if difficulty is not None:
            if difficulty not in [1, 2, 3]:
                return JsonResponse({'error': '难度参数无效，请传入1、2或3'}, status=400)
            filter_kwargs['difficulty'] = difficulty
            
        if category_id is not None:
            filter_kwargs['category_id'] = category_id
        
        print(f"[FILTER] POST筛选条件: {filter_kwargs}, 题目数量: {limit}")
        
        # 构建缓存键
        cache_key_parts = ["daily_tcm_questions_post"]
        if difficulty is not None:
            cache_key_parts.append(f"diff_{difficulty}")
        if category_id is not None:
            cache_key_parts.append(f"cat_{category_id}")
        cache_key_parts.append(f"limit_{limit}")
        cache_key = ":".join(cache_key_parts)
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回POST缓存数据")
                    return JsonResponse({
                        'questions': cached_data,
                        'total_count': len(cached_data),
                        'filters': {
                            'difficulty': difficulty,
                            'category_id': category_id,
                            'limit': limit
                        }
                    })
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 缓存数据解析失败")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        # 获取随机题目
        selected_questions = await get_random_questions_optimized(filter_kwargs, limit=limit)

        if not selected_questions:
            return JsonResponse({
                'error': '暂无符合条件的题目',
                'filters': {
                    'difficulty': difficulty,
                    'category_id': category_id,
                    'limit': limit
                }
            }, status=404)

        print(f"[PERF] POST方法实际获取题目数量: {len(selected_questions)}")

        # 序列化数据
        serialized_data = []
        for question in selected_questions:
            serialized_data.append(serialize_tcm_question(question))

        # 缓存结果30分钟
        try:
            cache.set(cache_key, json.dumps(serialized_data), timeout=1800)
            logger.info(f"[CACHE_SET] 💾 {cache_key} - POST数据已缓存30分钟")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse({
            'questions': serialized_data,
            'total_count': len(serialized_data),
            'filters': {
                'difficulty': difficulty,
                'difficulty_text': get_difficulty_text(difficulty) if difficulty else None,
                'category_id': category_id,
                'limit': limit
            }
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"POST获取每日中医题目失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.post("/daily-quiz-score/")
@api_timer("提交每日答题得分")
@rate_limit("daily_quiz_score_api", normal_limit=25, member_limit=100)  # 分数提交适中限制
async def submit_daily_quiz_score_async(request):
    """
    异步提交每日答题得分 - 增强版本，支持详细记录和排名
    
    接收参数：
    - score: 答题得分 (兼容旧版本)
    - time_spent: 答题耗时(秒) (新增)
    - question_count: 题目总数 (新增)
    - correct_count: 正确答题数 (新增)
    - difficulty: 难度筛选 (可选)
    - category_id: 分类筛选 (可选)
    - question_details: 题目详情 (可选)
    """
    try:
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return JsonResponse({'error': '无法验证用户身份'}, status=401)

        # 解析请求数据
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            return JsonResponse({'error': '无效的 JSON 数据'}, status=400)

        # 获取基本参数（兼容旧版本）
        score = data.get('score')
        if score is None:
            return JsonResponse({'error': '缺少分数信息'}, status=400)

        # 获取新增参数
        time_spent = data.get('time_spent', 0)  # 答题耗时
        question_count = data.get('question_count', 0)  # 题目总数
        correct_count = data.get('correct_count', 0)  # 正确答题数
        difficulty = data.get('difficulty')  # 难度筛选
        category_id = data.get('category_id')  # 分类筛选
        question_details = data.get('question_details')  # 题目详情

        print(f"[QUIZ_SUBMIT] 用户{user_id} 提交答题记录: 得分={score}, 耗时={time_spent}秒, 题目数={question_count}, 正确数={correct_count}")

        # 异步获取用户信息
        user = await get_async(UserInfo, id=user_id)
        if not user:
            return JsonResponse({'error': '用户未找到'}, status=404)

        # 数据验证
        if question_count > 0 and correct_count > question_count:
            return JsonResponse({'error': '正确答题数不能超过题目总数'}, status=400)
        
        if time_spent < 0:
            return JsonResponse({'error': '答题耗时不能为负数'}, status=400)

        # 创建详细的答题记录 (新功能)
        quiz_record_data = {
            'user': user,
            'score': score,
            'total_questions': max(question_count, 1),  # 确保至少为1，避免除零错误
            'correct_answers': correct_count,
            'time_spent': max(time_spent, 1),  # 确保至少为1，避免除零错误
            'difficulty_filter': difficulty,
            'category_filter': category_id,
            'question_details': question_details
        }

        # 创建答题记录 - 使用异步方式
        quiz_record = await create_async(DailyTCMQuizRecord, **quiz_record_data)

        # 更新用户的每日答题得分 (保持向后兼容)
        user.daily_quiz_score = score
        await save_async(user)

        logger.info(f"[QUIZ_COMPLETE] 用户 {user_id} 答题记录已创建: ID={quiz_record.id}, 综合得分={quiz_record.rank_score}")

        # 返回详细的响应信息
        response_data = {
            'message': '答题记录提交成功',
            'record_id': quiz_record.id,
            'user_stats': {
                'score': score,
                'accuracy_rate': quiz_record.accuracy_rate,
                'time_spent': quiz_record.time_spent,
                'time_spent_display': quiz_record.time_spent_display,
                'rank_score': quiz_record.rank_score,
                'difficulty': quiz_record.difficulty_text
            }
        }

        return JsonResponse(response_data, status=200)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"异步提交每日答题得分失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/tcm-categories/")
@api_timer("获取中医题目分类")
@rate_limit("tcm_categories_api", normal_limit=50, member_limit=200)  # 分类查询相对频繁
async def get_tcm_categories_async(request):
    """异步获取中医题目分类列表，用于筛选器"""
    try:
        cache_key = "tcm_categories_with_count"
        
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的分类数据")
                    return JsonResponse({
                        'categories': cached_data,
                        'total_count': len(cached_data)
                    })
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        # 异步获取分类及题目数量统计
        categories_data = await filter_with_annotations_async(
            Category,
            annotations={'question_count': models.Count('tcmquestion')},
            values_fields=['id', 'name', 'question_count'],
            order_by=['name']
        )

        # 缓存2小时
        try:
            cache.set(cache_key, json.dumps(categories_data), timeout=7200)  # 2小时
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 分类数据已缓存2小时")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse({
            'categories': categories_data,
            'total_count': len(categories_data),
            'difficulty_options': [
                {'value': 1, 'text': '初级'},
                {'value': 2, 'text': '中级'},
                {'value': 3, 'text': '高级'}
            ]
        })

    except Exception as e:
        logger.error(f"异步获取中医题目分类失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/question-stats/")
@api_timer("获取题目统计信息")
@rate_limit("question_stats_api", normal_limit=30, member_limit=120)  # 统计查询适中限制
async def get_question_stats_async(request):
    """异步获取题目统计信息"""
    try:
        cache_key = "tcm_question_stats_enhanced"
        
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的统计数据")
                    return JsonResponse(cached_data)
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        # 异步统计题目信息
        total_questions = await count_async(TCMQuestion.objects.all())

        # 按难度统计
        difficulty_stats = await filter_with_annotations_async(
            TCMQuestion,
            annotations={'count': models.Count('id')},
            values_fields=['difficulty'],
            order_by=['difficulty']
        )

        # 为难度统计添加文字描述
        for stat in difficulty_stats:
            stat['difficulty_text'] = get_difficulty_text(stat['difficulty'])

        # 按分类统计
        category_stats = await filter_with_annotations_async(
            TCMQuestion,
            annotations={'count': models.Count('id')},
            values_fields=['category__id', 'category__name'],
            order_by=['category__name']
        )

        stats_data = {
            'total_questions': total_questions,
            'difficulty_distribution': difficulty_stats,
            'category_distribution': category_stats,
            'difficulty_options': [
                {'value': 1, 'text': '初级'},
                {'value': 2, 'text': '中级'},
                {'value': 3, 'text': '高级'}
            ]
        }

        # 缓存1小时
        try:
            cache.set(cache_key, json.dumps(stats_data), timeout=3600)  # 1小时
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 统计数据已缓存1小时")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse(stats_data)

    except Exception as e:
        logger.error(f"异步获取题目统计信息失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/quiz-ranking/")
@api_timer("获取新版答题排名")
@rate_limit("quiz_ranking_api", normal_limit=35, member_limit=140)  # 排行榜查询适中限制
async def get_quiz_ranking_new_async(
    request,
    period: Optional[str] = Query("today", description="排名周期: today=今日, week=本周, month=本月, all=全部"),
    difficulty: Optional[int] = Query(None, description="难度筛选: 1=初级, 2=中级, 3=高级"),
    limit: Optional[int] = Query(20, description="返回条数，默认20，最多50")
):
    """
    新版答题排名列表 - 基于DailyTCMQuizRecord模型
    
    参数说明：
    - period: 排名周期 (today=今日, week=本周, month=本月, all=全部)
    - difficulty: 难度筛选 (可选)
    - limit: 返回条数，默认20，最多50
    """
    try:
        # 限制返回条数
        limit = min(max(limit, 1), 50)
        
        # 构建缓存键
        cache_key_parts = ["quiz_ranking_new", period]
        if difficulty is not None:
            cache_key_parts.append(f"diff_{difficulty}")
        cache_key_parts.append(f"limit_{limit}")
        cache_key = ":".join(cache_key_parts)
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的新版排名数据")
                    return JsonResponse(cached_data)
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        def _get_ranking_data():
            from datetime import datetime, timedelta

            # 构建基础查询
            queryset = DailyTCMQuizRecord.objects.select_related('user')

            # 时间筛选
            if period == "today":
                today = datetime.now().date()
                queryset = queryset.filter(quiz_date=today)
            elif period == "week":
                week_ago = datetime.now().date() - timedelta(days=7)
                queryset = queryset.filter(quiz_date__gte=week_ago)
            elif period == "month":
                month_ago = datetime.now().date() - timedelta(days=30)
                queryset = queryset.filter(quiz_date__gte=month_ago)
            # period == "all" 不需要时间筛选

            # 难度筛选
            if difficulty is not None:
                queryset = queryset.filter(difficulty_filter=difficulty)

            # 排序和限制条数
            # 按综合得分排序：得分 > 正确率 > 耗时 (用负数让耗时越少排名越高)
            queryset = queryset.order_by('-score', '-accuracy_rate', 'time_spent')[:limit]

            ranking_data = []
            for rank, record in enumerate(queryset, 1):
                user_data = {
                    'rank': rank,
                    'user_id': record.user.id,
                    'user_nickname': record.user.nickname or f"用户{record.user.id}",
                    'score': record.score,
                    'accuracy_rate': record.accuracy_rate,
                    'time_spent': record.time_spent,
                    'time_spent_display': record.time_spent_display,
                    'total_questions': record.total_questions,
                    'correct_answers': record.correct_answers,
                    'rank_score': record.rank_score,
                    'difficulty_text': record.difficulty_text,
                    'quiz_date': record.quiz_date.strftime('%Y-%m-%d'),
                    'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
                }
                ranking_data.append(user_data)

            return ranking_data

        ranking_data = await run_sync_function_async(_get_ranking_data)
        
        # 构建响应数据
        response_data = {
            'status': 'success',
            'ranking': ranking_data,
            'total_count': len(ranking_data),
            'period': period,
            'period_text': {
                'today': '今日',
                'week': '本周',
                'month': '本月',
                'all': '全部'
            }.get(period, period),
            'difficulty': difficulty,
            'difficulty_text': get_difficulty_text(difficulty) if difficulty else '全部难度',
            'limit': limit
        }
        
        # 缓存10分钟
        try:
            cache.set(cache_key, json.dumps(response_data), timeout=600)  # 10分钟
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 新版排名数据已缓存10分钟")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取新版答题排名失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/user-quiz-history/")
@api_timer("获取用户答题历史")
@rate_limit("user_quiz_history_api", normal_limit=30, member_limit=120)  # 历史查询适中限制
async def get_user_quiz_history_async(
    request,
    limit: Optional[int] = Query(10, description="返回条数，默认10，最多20"),
    difficulty: Optional[int] = Query(None, description="难度筛选: 1=初级, 2=中级, 3=高级")
):
    """
    获取当前用户的答题历史记录
    
    参数说明：
    - limit: 返回条数，默认10，最多20
    - difficulty: 难度筛选 (可选)
    """
    try:
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return JsonResponse({'error': '无法验证用户身份'}, status=401)

        # 限制返回条数
        limit = min(max(limit, 1), 20)
        
        # 使用异步工具函数查询数据
        from api.ninja_apis.async_utils import filter_async

        # 构建查询条件
        query_filters = {'user_id': user_id}
        if difficulty is not None:
            query_filters['difficulty_filter'] = difficulty

        # 异步查询用户答题历史记录
        records = await filter_async(
            DailyTCMQuizRecord,
            **query_filters,
            order_by=['-created_at'],
            limit=limit
        )

        # 构建历史数据
        history_data = []
        for record in records:
            record_data = {
                'id': record.id,
                'score': record.score,
                'accuracy_rate': record.accuracy_rate,
                'time_spent': record.time_spent,
                'time_spent_display': record.time_spent_display,
                'total_questions': record.total_questions,
                'correct_answers': record.correct_answers,
                'rank_score': record.rank_score,
                'difficulty_text': record.difficulty_text,
                'quiz_date': record.quiz_date.strftime('%Y-%m-%d'),
                'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
            history_data.append(record_data)
        
        # 计算用户统计信息
        def _get_user_stats():
            if not history_data:
                return {
                    'total_attempts': 0,
                    'average_score': 0,
                    'average_accuracy': 0,
                    'best_score': 0,
                    'best_accuracy': 0
                }
            
            total_attempts = len(history_data)
            total_score = sum(record['score'] for record in history_data)
            total_accuracy = sum(record['accuracy_rate'] for record in history_data)
            
            return {
                'total_attempts': total_attempts,
                'average_score': round(total_score / total_attempts, 2),
                'average_accuracy': round(total_accuracy / total_attempts, 2),
                'best_score': max(record['score'] for record in history_data),
                'best_accuracy': max(record['accuracy_rate'] for record in history_data)
            }
        
        user_stats = _get_user_stats()
        
        response_data = {
            'status': 'success',
            'history': history_data,
            'user_stats': user_stats,
            'total_count': len(history_data),
            'difficulty': difficulty,
            'difficulty_text': get_difficulty_text(difficulty) if difficulty else '全部难度',
            'limit': limit
        }

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取用户答题历史失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

# ============================================================================
# 闯关系统API - 基于现有题库实现分级闯关
# ============================================================================

class ChallengeSystemConfig:
    """闯关系统配置类"""
    QUESTIONS_PER_LEVEL = 20  # 每关题目数量
    CACHE_TIMEOUT = 3600      # 缓存1小时
    MAX_LEVELS_PER_REQUEST = 50  # 单次请求最大关卡数
    
    # 分类映射
    CATEGORY_MAP = {
        '中药基础知识': {'id': 1, 'code': 'TCM_HERB', 'name': '中药基础知识'},
        '中医基础知识': {'id': 2, 'code': 'TCM_BASIC', 'name': '中医基础知识'},
        '中医食疗知识': {'id': 3, 'code': 'TCM_DIET', 'name': '中医食疗知识'}
    }
    
    # 难度映射
    DIFFICULTY_MAP = {
        1: {'name': '初级', 'code': 'EASY'},
        2: {'name': '中级', 'code': 'MEDIUM'},
        3: {'name': '高级', 'code': 'HARD'}
    }

async def get_challenge_system_stats():
    """获取闯关系统统计信息"""
    def _get_stats():
        stats = {}
        
        # 获取各分类各难度的题目数量
        cross_stats = TCMQuestion.objects.values(
            'category__name', 'difficulty'
        ).annotate(
            count=models.Count('id')
        ).order_by('category__name', 'difficulty')
        
        for stat in cross_stats:
            category_name = stat['category__name']
            difficulty = stat['difficulty']
            count = stat['count']
            
            if category_name not in stats:
                stats[category_name] = {}
            
            stats[category_name][difficulty] = {
                'question_count': count,
                'level_count': math.ceil(count / ChallengeSystemConfig.QUESTIONS_PER_LEVEL),
                'last_level_questions': count % ChallengeSystemConfig.QUESTIONS_PER_LEVEL or ChallengeSystemConfig.QUESTIONS_PER_LEVEL
            }
        
        return stats
    
    return await run_sync_function_async(_get_stats)

def generate_level_id(category_name: str, difficulty: int, level_number: int) -> str:
    """生成关卡ID"""
    category_code = ChallengeSystemConfig.CATEGORY_MAP.get(category_name, {}).get('code', 'UNKNOWN')
    difficulty_code = ChallengeSystemConfig.DIFFICULTY_MAP.get(difficulty, {}).get('code', 'UNKNOWN')
    return f"{category_code}_{difficulty_code}_{level_number:03d}"

def parse_level_id(level_id: str) -> Dict:
    """解析关卡ID"""
    try:
        parts = level_id.split('_')
        
        # 处理不同的ID格式
        if len(parts) == 4:
            # 新格式：TCM_BASIC_EASY_001
            category_part1, category_part2, difficulty_code, level_number = parts
            category_code = f"{category_part1}_{category_part2}"
        elif len(parts) == 3:
            # 旧格式：CATEGORY_DIFFICULTY_LEVEL
            category_code, difficulty_code, level_number = parts
        else:
            raise ValueError("Invalid level_id format")
        
        # 反向查找分类
        category_name = None
        for name, info in ChallengeSystemConfig.CATEGORY_MAP.items():
            if info['code'] == category_code:
                category_name = name
                break
        
        # 反向查找难度
        difficulty = None
        for diff, info in ChallengeSystemConfig.DIFFICULTY_MAP.items():
            if info['code'] == difficulty_code:
                difficulty = diff
                break
        
        return {
            'category_name': category_name,
            'difficulty': difficulty,
            'level_number': int(level_number),
            'valid': category_name is not None and difficulty is not None
        }
        
    except Exception as e:
        logger.error(f"解析关卡ID失败: {level_id}, 错误: {str(e)}")
        return {'valid': False}

async def get_level_questions(category_name: str, difficulty: int, level_number: int, questions_per_level: int = 20):
    """获取指定关卡的题目"""
    def _get_questions():
        # 计算跳过的题目数量
        skip_count = (level_number - 1) * questions_per_level
        
        # 获取题目，使用固定排序确保每次获取相同的题目
        queryset = TCMQuestion.objects.select_related('category').filter(
            category__name=category_name,
            difficulty=difficulty
        ).order_by('id')[skip_count:skip_count + questions_per_level]
        
        return list(queryset)
    
    return await run_sync_function_async(_get_questions)

def serialize_challenge_question(question):
    """闯关题目序列化"""
    return {
        'id': question.id,
        'content': question.content,
        'option_a': question.option_a,
        'option_b': question.option_b,
        'option_c': question.option_c,
        'option_d': question.option_d,
        'answer': question.answer,
        'explanation': question.explanation,
        'difficulty': question.difficulty,
        'category_name': question.category.name,
        'image': question.image.url if question.image else None
    }

@daily_tcm_router.get("/challenge-overview/")
@api_timer("获取闯关系统总览")
@rate_limit("challenge_overview_api", normal_limit=40, member_limit=150)  # 概览查询相对频繁
async def get_challenge_overview_async(request):
    """
    获取闯关系统总览信息
    返回所有分类、难度和关卡数量统计
    """
    try:
        cache_key = "challenge_system_overview"
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                cached_data = json.loads(cached_data_str)
                logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的总览数据")
                return JsonResponse(cached_data)
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        # 获取统计数据
        stats = await get_challenge_system_stats()
        
        # 构建总览数据
        overview_data = {
            'system_info': {
                'total_questions': 4279,
                'questions_per_level': ChallengeSystemConfig.QUESTIONS_PER_LEVEL,
                'total_categories': len(ChallengeSystemConfig.CATEGORY_MAP),
                'total_difficulties': len(ChallengeSystemConfig.DIFFICULTY_MAP)
            },
            'categories': [],
            'total_levels': 0
        }
        
        total_levels = 0
        
        for category_name, difficulties in stats.items():
            category_info = ChallengeSystemConfig.CATEGORY_MAP.get(category_name, {})
            category_data = {
                'name': category_name,
                'code': category_info.get('code', 'UNKNOWN'),
                'difficulties': []
            }
            
            category_total_levels = 0
            
            for difficulty, diff_stats in difficulties.items():
                difficulty_info = ChallengeSystemConfig.DIFFICULTY_MAP.get(difficulty, {})
                difficulty_data = {
                    'difficulty': difficulty,
                    'difficulty_name': difficulty_info.get('name', f'难度{difficulty}'),
                    'difficulty_code': difficulty_info.get('code', 'UNKNOWN'),
                    'question_count': diff_stats['question_count'],
                    'level_count': diff_stats['level_count'],
                    'last_level_questions': diff_stats['last_level_questions']
                }
                
                category_data['difficulties'].append(difficulty_data)
                category_total_levels += diff_stats['level_count']
            
            category_data['total_levels'] = category_total_levels
            overview_data['categories'].append(category_data)
            total_levels += category_total_levels
        
        overview_data['total_levels'] = total_levels
        
        # 缓存结果
        try:
            cache.set(cache_key, json.dumps(overview_data), timeout=ChallengeSystemConfig.CACHE_TIMEOUT)
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 总览数据已缓存")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse({
            'status': 'success',
            'data': overview_data
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取闯关系统总览失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/challenge-levels/")
@api_timer("获取关卡列表")
@rate_limit("challenge_levels_api", normal_limit=35, member_limit=140)  # 关卡列表查询
async def get_challenge_levels_async(
    request,
    category: Optional[str] = Query(None, description="分类筛选：中药基础知识、中医基础知识、中医食疗知识"),
    difficulty: Optional[int] = Query(None, description="难度筛选：1=初级, 2=中级, 3=高级"),
    page: Optional[int] = Query(1, description="页码，从1开始"),
    limit: Optional[int] = Query(20, description="每页关卡数，最多50")
):
    """
    获取关卡列表，支持分页和筛选
    """
    try:
        # 参数验证
        page = max(page, 1)
        limit = min(max(limit, 1), ChallengeSystemConfig.MAX_LEVELS_PER_REQUEST)
        
        if difficulty is not None and difficulty not in [1, 2, 3]:
            return JsonResponse({'error': '难度参数无效，请传入1、2或3'}, status=400)
        
        if category and category not in ChallengeSystemConfig.CATEGORY_MAP:
            return JsonResponse({'error': '分类参数无效'}, status=400)

        # 构建缓存键
        cache_key_parts = ["challenge_levels"]
        if category:
            cache_key_parts.append(f"cat_{category}")
        if difficulty:
            cache_key_parts.append(f"diff_{difficulty}")
        cache_key_parts.extend([f"page_{page}", f"limit_{limit}"])
        cache_key = ":".join(cache_key_parts)
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                cached_data = json.loads(cached_data_str)
                logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的关卡列表")
                return JsonResponse(cached_data)
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        # 获取统计数据
        stats = await get_challenge_system_stats()
        
        # 生成关卡列表
        all_levels = []
        
        for category_name, difficulties in stats.items():
            # 分类筛选
            if category and category_name != category:
                continue
                
            for diff, diff_stats in difficulties.items():
                # 难度筛选
                if difficulty is not None and diff != difficulty:
                    continue
                
                level_count = diff_stats['level_count']
                
                for level_num in range(1, level_count + 1):
                    # 计算这一关的题目数量
                    if level_num == level_count:
                        # 最后一关可能题目不足20道
                        questions_in_level = diff_stats['last_level_questions']
                    else:
                        questions_in_level = ChallengeSystemConfig.QUESTIONS_PER_LEVEL
                    
                    level_id = generate_level_id(category_name, diff, level_num)
                    
                    level_data = {
                        'level_id': level_id,
                        'category_name': category_name,
                        'category_code': ChallengeSystemConfig.CATEGORY_MAP[category_name]['code'],
                        'difficulty': diff,
                        'difficulty_name': ChallengeSystemConfig.DIFFICULTY_MAP[diff]['name'],
                        'difficulty_code': ChallengeSystemConfig.DIFFICULTY_MAP[diff]['code'],
                        'level_number': level_num,
                        'questions_count': questions_in_level,
                        'total_levels_in_difficulty': level_count,
                        'is_last_level': level_num == level_count
                    }
                    
                    all_levels.append(level_data)
        
        # 分页处理
        total_levels = len(all_levels)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        page_levels = all_levels[start_index:end_index]
        
        # 构建响应数据
        response_data = {
            'status': 'success',
            'levels': page_levels,
            'pagination': {
                'current_page': page,
                'total_pages': math.ceil(total_levels / limit),
                'total_levels': total_levels,
                'per_page': limit,
                'has_next': end_index < total_levels,
                'has_prev': page > 1
            },
            'filters': {
                'category': category,
                'difficulty': difficulty
            }
        }
        
        # 缓存结果
        try:
            cache.set(cache_key, json.dumps(response_data), timeout=ChallengeSystemConfig.CACHE_TIMEOUT)
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 关卡列表已缓存")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取关卡列表失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/challenge-level/{level_id}/")
@api_timer("获取关卡题目")
@rate_limit("challenge_level_questions_api", normal_limit=30, member_limit=120)  # 关卡题目查询
async def get_challenge_level_questions_async(request, level_id: str):
    """
    获取指定关卡的题目
    """
    try:
        # 解析关卡ID
        level_info = parse_level_id(level_id)
        if not level_info['valid']:
            return JsonResponse({'error': '无效的关卡ID'}, status=400)
        
        category_name = level_info['category_name']
        difficulty = level_info['difficulty']
        level_number = level_info['level_number']
        
        # 构建缓存键
        cache_key = f"challenge_level_questions:{level_id}"
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                cached_data = json.loads(cached_data_str)
                logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的关卡题目")
                return JsonResponse(cached_data)
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        # 获取关卡题目
        questions = await get_level_questions(
            category_name, difficulty, level_number, 
            ChallengeSystemConfig.QUESTIONS_PER_LEVEL
        )
        
        if not questions:
            return JsonResponse({'error': '关卡不存在或暂无题目'}, status=404)

        # 序列化题目
        serialized_questions = []
        for question in questions:
            serialized_questions.append(serialize_challenge_question(question))

        # 构建响应数据
        response_data = {
            'status': 'success',
            'level_info': {
                'level_id': level_id,
                'category_name': category_name,
                'difficulty': difficulty,
                'difficulty_name': ChallengeSystemConfig.DIFFICULTY_MAP[difficulty]['name'],
                'level_number': level_number,
                'questions_count': len(serialized_questions)
            },
            'questions': serialized_questions
        }
        
        # 缓存结果（缓存2小时，因为题目相对稳定）
        try:
            cache.set(cache_key, json.dumps(response_data), timeout=7200)
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 关卡题目已缓存2小时")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取关卡题目失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.post("/challenge-level/{level_id}/submit/")
@api_timer("提交关卡成绩")
@rate_limit("submit_challenge_level_api", normal_limit=20, member_limit=80)  # 挑战提交限制适中
async def submit_challenge_level_async(request, level_id: str):
    """
    提交关卡答题成绩
    """
    try:
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return JsonResponse({'error': '无法验证用户身份'}, status=401)

        # 解析关卡ID
        level_info = parse_level_id(level_id)
        if not level_info['valid']:
            return JsonResponse({'error': '无效的关卡ID'}, status=400)
        
        # 解析请求数据
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            return JsonResponse({'error': '无效的 JSON 数据'}, status=400)

        # 获取提交数据
        score = data.get('score')
        time_spent = data.get('time_spent', 0)
        correct_count = data.get('correct_count', 0)
        total_questions = data.get('total_questions', ChallengeSystemConfig.QUESTIONS_PER_LEVEL)
        answers = data.get('answers', [])  # 用户答案详情

        if score is None:
            return JsonResponse({'error': '缺少分数信息'}, status=400)

        print(f"[CHALLENGE_SUBMIT] 用户{user_id} 提交关卡{level_id}: 得分={score}, 正确={correct_count}/{total_questions}")

        # 验证用户
        user = await get_async(UserInfo, id=user_id)
        if not user:
            return JsonResponse({'error': '用户未找到'}, status=404)

        # 计算通关状态（假设60分及格）
        passing_score = 60
        is_passed = score >= passing_score
        accuracy_rate = (correct_count / max(total_questions, 1)) * 100

        # 🎯 保存闯关记录到数据库
        from api.models.questionnaire_models import ChallengeRecord
        
        def _create_challenge_record():
            return ChallengeRecord.objects.create(
                user=user,
                level_id=level_id,
                category_name=level_info['category_name'],
                difficulty=level_info['difficulty'],
                level_number=level_info['level_number'],
                score=score,
                total_questions=total_questions,
                correct_answers=correct_count,
                time_spent=time_spent,
                passing_score=passing_score,
                answers=answers,
                completion_data={
                    'submitted_at': timezone.now().isoformat(),
                    'ip_address': request.META.get('REMOTE_ADDR'),
                    'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200]
                }
            )
        
        challenge_record = await run_sync_function_async(_create_challenge_record)
        
        logger.info(f"[CHALLENGE_RECORD] 用户 {user_id} 闯关记录已保存: ID={challenge_record.id}, 关卡={level_id}, 状态={'通关' if is_passed else '未通关'}")
        
        response_data = {
            'status': 'success',
            'message': '恭喜通关！' if is_passed else '继续努力！',
            'record_id': challenge_record.id,
            'level_result': {
                'level_id': level_id,
                'score': score,
                'accuracy_rate': round(accuracy_rate, 2),
                'correct_count': correct_count,
                'total_questions': total_questions,
                'time_spent': time_spent,
                'is_passed': is_passed,
                'passing_score': passing_score,
                'rank_score': challenge_record.rank_score
            },
            'level_info': {
                'category_name': level_info['category_name'],
                'difficulty': level_info['difficulty'],
                'difficulty_name': ChallengeSystemConfig.DIFFICULTY_MAP[level_info['difficulty']]['name'],
                'level_number': level_info['level_number']
            }
        }

        # 如果通关，可以解锁下一关（这里只是示例逻辑）
        if is_passed:
            response_data['next_level'] = {
                'available': True,
                'message': '已解锁下一关！'
            }

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"提交关卡成绩失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

# ============================================================================
# 闯关排行榜API
# ============================================================================

@daily_tcm_router.get("/challenge-ranking/")
@api_timer("获取闯关排行榜")
@rate_limit("challenge_ranking_api", normal_limit=35, member_limit=140)  # 挑战排行榜查询
async def get_challenge_ranking_async(
    request,
    period: Optional[str] = Query("all", description="排名周期: today=今日, week=本周, month=本月, all=全部"),
    category: Optional[str] = Query(None, description="分类筛选：中药基础知识、中医基础知识、中医食疗知识"),
    difficulty: Optional[int] = Query(None, description="难度筛选: 1=初级, 2=中级, 3=高级"),
    ranking_type: Optional[str] = Query("score", description="排名类型: score=得分排名, pass_rate=通关率排名, efficiency=效率排名"),
    limit: Optional[int] = Query(20, description="返回条数，默认20，最多50")
):
    """
    获取闯关排行榜
    
    参数说明：
    - period: 排名周期
    - category: 分类筛选
    - difficulty: 难度筛选
    - ranking_type: 排名类型 (score=得分排名, pass_rate=通关率排名, efficiency=效率排名)
    - limit: 返回条数
    """
    try:
        from api.models.questionnaire_models import ChallengeRecord
        
        # 限制返回条数
        limit = min(max(limit, 1), 50)
        
        # 参数验证
        if difficulty is not None and difficulty not in [1, 2, 3]:
            return JsonResponse({'error': '难度参数无效，请传入1、2或3'}, status=400)
        
        if category and category not in ChallengeSystemConfig.CATEGORY_MAP:
            return JsonResponse({'error': '分类参数无效'}, status=400)
            
        if ranking_type not in ['score', 'pass_rate', 'efficiency']:
            return JsonResponse({'error': '排名类型无效'}, status=400)
        
        # 构建缓存键
        cache_key_parts = ["challenge_ranking", period, ranking_type]
        if category:
            cache_key_parts.append(f"cat_{category}")
        if difficulty:
            cache_key_parts.append(f"diff_{difficulty}")
        cache_key_parts.append(f"limit_{limit}")
        cache_key = ":".join(cache_key_parts)
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的闯关排名数据")
                    return JsonResponse(cached_data)
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        def _get_challenge_ranking():
            from datetime import datetime, timedelta
            
            # 构建基础查询
            queryset = ChallengeRecord.objects.select_related('user')
            
            # 时间筛选
            if period == "today":
                today = datetime.now().date()
                queryset = queryset.filter(challenge_date=today)
            elif period == "week":
                week_ago = datetime.now().date() - timedelta(days=7)
                queryset = queryset.filter(challenge_date__gte=week_ago)
            elif period == "month":
                month_ago = datetime.now().date() - timedelta(days=30)
                queryset = queryset.filter(challenge_date__gte=month_ago)
            # period == "all" 不需要时间筛选
            
            # 分类筛选
            if category:
                queryset = queryset.filter(category_name=category)
            
            # 难度筛选
            if difficulty is not None:
                queryset = queryset.filter(difficulty=difficulty)
            
            # 根据排名类型进行排序
            if ranking_type == "score":
                # 按得分排名：综合得分 > 原始得分 > 正确率 > 耗时
                queryset = queryset.order_by('-score', '-accuracy_rate', 'time_spent')
            elif ranking_type == "pass_rate":
                # 按通关率排名：是否通关 > 得分 > 正确率
                queryset = queryset.order_by('-is_passed', '-score', '-accuracy_rate')
            elif ranking_type == "efficiency":
                # 按效率排名：每秒得分 > 得分 > 正确率
                queryset = queryset.order_by('-score_per_second', '-score', '-accuracy_rate')
            
            # 限制条数
            queryset = queryset[:limit]
            
            ranking_data = []
            for rank, record in enumerate(queryset, 1):
                user_data = {
                    'rank': rank,
                    'user_id': record.user.id,
                    'user_nickname': record.user.nickname or f"用户{record.user.id}",
                    'level_id': record.level_id,
                    'level_display_name': record.level_display_name,
                    'category_name': record.category_name,
                    'difficulty': record.difficulty,
                    'difficulty_text': record.difficulty_text,
                    'level_number': record.level_number,
                    'score': record.score,
                    'accuracy_rate': record.accuracy_rate,
                    'time_spent': record.time_spent,
                    'time_spent_display': record.time_spent_display,
                    'total_questions': record.total_questions,
                    'correct_answers': record.correct_answers,
                    'is_passed': record.is_passed,
                    'rank_score': record.rank_score,
                    'score_per_second': record.score_per_second,
                    'challenge_date': record.challenge_date.strftime('%Y-%m-%d'),
                    'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
                }
                ranking_data.append(user_data)
            
            return ranking_data
        
        ranking_data = await run_sync_function_async(_get_challenge_ranking)
        
        # 构建响应数据
        response_data = {
            'status': 'success',
            'ranking': ranking_data,
            'total_count': len(ranking_data),
            'period': period,
            'period_text': {
                'today': '今日',
                'week': '本周',
                'month': '本月',
                'all': '全部'
            }.get(period, period),
            'category': category,
            'difficulty': difficulty,
            'difficulty_text': get_difficulty_text(difficulty) if difficulty else '全部难度',
            'ranking_type': ranking_type,
            'ranking_type_text': {
                'score': '得分排名',
                'pass_rate': '通关率排名',
                'efficiency': '效率排名'
            }.get(ranking_type, ranking_type),
            'limit': limit
        }
        
        # 缓存15分钟
        try:
            cache.set(cache_key, json.dumps(response_data), timeout=900)  # 15分钟
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 闯关排名数据已缓存15分钟")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取闯关排行榜失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/challenge-user-history/")
@api_timer("获取用户闯关历史")
@rate_limit("challenge_user_history_api", normal_limit=25, member_limit=100)  # 用户挑战历史查询
async def get_user_challenge_history_async(
    request,
    limit: Optional[int] = Query(10, description="返回条数，默认10，最多20"),
    category: Optional[str] = Query(None, description="分类筛选：中药基础知识、中医基础知识、中医食疗知识"),
    difficulty: Optional[int] = Query(None, description="难度筛选: 1=初级, 2=中级, 3=高级"),
    passed_only: Optional[bool] = Query(False, description="只显示通关记录")
):
    """
    获取当前用户的闯关历史记录
    
    参数说明：
    - limit: 返回条数，默认10，最多20
    - category: 分类筛选 (可选)
    - difficulty: 难度筛选 (可选)
    - passed_only: 只显示通关记录
    """
    try:
        from api.models.questionnaire_models import ChallengeRecord
        
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return JsonResponse({'error': '无法验证用户身份'}, status=401)

        # 限制返回条数
        limit = min(max(limit, 1), 20)
        
        # 使用异步工具函数查询数据
        from api.ninja_apis.async_utils import filter_async

        # 构建查询条件
        query_filters = {'user_id': user_id}
        if category:
            query_filters['category_name'] = category
        if difficulty is not None:
            query_filters['difficulty'] = difficulty
        if passed_only:
            query_filters['is_passed'] = True

        # 异步查询用户闯关历史记录
        records = await filter_async(
            ChallengeRecord,
            **query_filters,
            order_by=['-created_at'],
            limit=limit
        )

        # 构建历史数据
        history_data = []
        for record in records:
            record_data = {
                'id': record.id,
                'level_id': record.level_id,
                'level_display_name': record.level_display_name,
                'category_name': record.category_name,
                'difficulty': record.difficulty,
                'difficulty_text': record.difficulty_text,
                'level_number': record.level_number,
                'score': record.score,
                'accuracy_rate': record.accuracy_rate,
                'time_spent': record.time_spent,
                'time_spent_display': record.time_spent_display,
                'total_questions': record.total_questions,
                'correct_answers': record.correct_answers,
                'is_passed': record.is_passed,
                'rank_score': record.rank_score,
                'challenge_date': record.challenge_date.strftime('%Y-%m-%d'),
                'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
            history_data.append(record_data)
        
        # 计算用户统计信息
        def _get_user_challenge_stats():
            if not history_data:
                return {
                    'total_attempts': 0,
                    'total_passed': 0,
                    'pass_rate': 0,
                    'average_score': 0,
                    'average_accuracy': 0,
                    'best_score': 0,
                    'best_accuracy': 0,
                    'total_time_spent': 0
                }
            
            total_attempts = len(history_data)
            total_passed = sum(1 for record in history_data if record['is_passed'])
            total_score = sum(record['score'] for record in history_data)
            total_accuracy = sum(record['accuracy_rate'] for record in history_data)
            total_time = sum(record['time_spent'] for record in history_data)
            
            return {
                'total_attempts': total_attempts,
                'total_passed': total_passed,
                'pass_rate': round((total_passed / total_attempts) * 100, 2),
                'average_score': round(total_score / total_attempts, 2),
                'average_accuracy': round(total_accuracy / total_attempts, 2),
                'best_score': max(record['score'] for record in history_data),
                'best_accuracy': max(record['accuracy_rate'] for record in history_data),
                'total_time_spent': total_time,
                'total_time_display': f"{total_time // 60}分{total_time % 60}秒" if total_time >= 60 else f"{total_time}秒"
            }
        
        user_stats = _get_user_challenge_stats()
        
        response_data = {
            'status': 'success',
            'history': history_data,
            'user_stats': user_stats,
            'total_count': len(history_data),
            'filters': {
                'category': category,
                'difficulty': difficulty,
                'difficulty_text': get_difficulty_text(difficulty) if difficulty else '全部难度',
                'passed_only': passed_only
            },
            'limit': limit
        }

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取用户闯关历史失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@daily_tcm_router.get("/challenge-stats/")
@api_timer("获取闯关系统统计")
@rate_limit("challenge_stats_api", normal_limit=30, member_limit=120)  # 挑战统计查询
async def get_challenge_system_stats_async(request):
    """
    获取闯关系统的全局统计信息
    包括总体数据、各分类数据等
    """
    try:
        from api.models.questionnaire_models import ChallengeRecord
        
        cache_key = "challenge_system_global_stats"
        
        # 尝试从缓存获取
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                cached_data = json.loads(cached_data_str)
                logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回缓存的闯关统计数据")
                return JsonResponse(cached_data)
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存失败: {str(e)}")

        def _get_challenge_system_stats():
            from django.db.models import Count, Avg, Max
            
            # 总体统计
            total_records = ChallengeRecord.objects.count()
            total_users = ChallengeRecord.objects.values('user').distinct().count()
            total_passed = ChallengeRecord.objects.filter(is_passed=True).count()
            
            # 各分类统计
            category_stats = list(
                ChallengeRecord.objects.values('category_name').annotate(
                    total_attempts=Count('id'),
                    total_passed=Count('id', filter=models.Q(is_passed=True)),
                    avg_score=Avg('score'),
                    max_score=Max('score'),
                    unique_users=Count('user', distinct=True)
                ).order_by('category_name')
            )
            
            # 各难度统计
            difficulty_stats = list(
                ChallengeRecord.objects.values('difficulty').annotate(
                    total_attempts=Count('id'),
                    total_passed=Count('id', filter=models.Q(is_passed=True)),
                    avg_score=Avg('score'),
                    max_score=Max('score'),
                    unique_users=Count('user', distinct=True)
                ).order_by('difficulty')
            )
            
            # 为难度统计添加文字描述
            for stat in difficulty_stats:
                stat['difficulty_text'] = get_difficulty_text(stat['difficulty'])
            
            # 计算通关率
            for stats_list in [category_stats, difficulty_stats]:
                for stat in stats_list:
                    if stat['total_attempts'] > 0:
                        stat['pass_rate'] = round((stat['total_passed'] / stat['total_attempts']) * 100, 2)
                    else:
                        stat['pass_rate'] = 0.0
            
            return {
                'total_records': total_records,
                'total_users': total_users,
                'total_passed': total_passed,
                'global_pass_rate': round((total_passed / max(total_records, 1)) * 100, 2),
                'category_stats': category_stats,
                'difficulty_stats': difficulty_stats
            }
        
        stats_data = await run_sync_function_async(_get_challenge_system_stats)
        
        response_data = {
            'status': 'success',
            'stats': stats_data
        }
        
        # 缓存30分钟
        try:
            cache.set(cache_key, json.dumps(response_data), timeout=1800)
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 闯关统计数据已缓存30分钟")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存失败: {str(e)}")

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"获取闯关系统统计失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500) 