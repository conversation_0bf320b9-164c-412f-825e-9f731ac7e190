from django.urls import path
from ninja import NinjaAPI
from . import views
from .api import router
from .modular_bazi_api import modular_bazi_router

api = NinjaAPI(title="八字分析API", description="基于您的出生时间，分析您的八字五行情况")

api.add_router("", router)
api.add_router("/modular", modular_bazi_router)

urlpatterns = [
    path("", api.urls),
    path("bazi/", views.index, name="index"),  # HTML页面路由
    path("bazi/stream/", views.stream_bazi, name="stream_bazi"),  # 流式八字分析页面路由
] 