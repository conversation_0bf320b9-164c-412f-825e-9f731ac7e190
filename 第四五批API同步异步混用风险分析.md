# 第四批和第五批API同步异步混用风险分析报告

## 📊 分析概述
基于代码审查，对第四批和第五批测试API进行同步异步混用风险评估，识别可能导致连接池溢出的API。

## 🚨 高风险API（需要跳过测试）

### 第四批高风险API
1. **自定义活动** (`CustomActivityView`)
   - **风险等级**: 🔴 高风险
   - **问题**: 使用同步ORM操作 `Activity.objects.filter()`, `Activity.objects.create()`
   - **位置**: `api/views/bank.py:1266`
   - **建议**: 跳过测试

2. **用户卡片** (`UserCardView`)
   - **风险等级**: 🔴 高风险
   - **问题**: 大量同步数据库操作，包括事务处理
   - **位置**: `api/views/bank.py:963`
   - **建议**: 跳过测试

3. **创建自定义活动** (`CreateCustomActivityView`)
   - **风险等级**: 🔴 高风险
   - **问题**: 使用同步ORM `UserInfo.objects.get()`, `Activity.objects.filter()`
   - **位置**: `api/views/bank.py:1266`
   - **建议**: 跳过测试

4. **添加症状** (`add_symptom`)
   - **风险等级**: 🔴 高风险
   - **问题**: 同步数据库操作，无异步处理
   - **位置**: `api/views/tcmchat_refactored/user_management/views.py`
   - **建议**: 跳过测试

5. **更新症状** (`update_symptom`)
   - **风险等级**: 🔴 高风险
   - **问题**: 同步数据库操作
   - **建议**: 跳过测试

6. **添加自定义症状** (`add_custom_symptom`)
   - **风险等级**: 🔴 高风险
   - **问题**: 使用同步ORM `UserInfo.objects.get()`, `UserCustomSymptom.objects.create()`
   - **位置**: `api/views/tcmchat_refactored/user_management/views.py:38`
   - **建议**: 跳过测试

### 第五批高风险API
7. **检查支付状态** (`CheckPaymentStatusView`)
   - **风险等级**: 🔴 高风险
   - **问题**: 使用同步ORM `Order.objects.get()`
   - **位置**: `api/views/tcm_nlp/payment.py:387`
   - **建议**: 跳过测试

8. **创建自定义活动** (`CreateCustomActivityView`)
   - **风险等级**: 🔴 高风险
   - **问题**: 同第四批，重复API
   - **建议**: 跳过测试

9. **银行更新症状** (`update_symptoms`)
   - **风险等级**: 🔴 高风险
   - **问题**: 银行系统版本，可能使用同步操作
   - **建议**: 跳过测试

10. **删除症状** (`delete_symptom`)
    - **风险等级**: 🔴 高风险
    - **问题**: DELETE操作，通常使用同步ORM
    - **建议**: 跳过测试

## ⚠️ 中风险API（需要谨慎测试）

### 第四批中风险API
1. **提交游戏记录** (`medical_case_game_record`)
   - **风险等级**: 🟡 中风险
   - **问题**: 写入操作，可能涉及同步数据库操作
   - **建议**: 低并发测试（≤5并发）

2. **增加经验值** (`medical_case_add_exp`)
   - **风险等级**: 🟡 中风险
   - **问题**: 写入操作，可能涉及同步数据库操作
   - **建议**: 低并发测试（≤5并发）

### 第五批中风险API
3. **医案评分** (`chat_YiAnPingfen`)
   - **风险等级**: 🟡 中风险
   - **问题**: 写入操作，可能使用同步ORM
   - **建议**: 低并发测试（≤5并发）

4. **添加健康记录** (`add_health_record`)
   - **风险等级**: 🟡 中风险
   - **问题**: 写入操作，可能使用同步ORM
   - **建议**: 低并发测试（≤5并发）

## ✅ 安全API（可以正常测试）

### 第四批安全API
1. **日活动** (`DayActivitiesView`)
   - **风险等级**: 🟢 低风险
   - **原因**: 有对应的异步版本 `day_activities_async`
   - **建议**: 正常测试

2. **用户问答历史** (`user-quiz-history`)
   - **风险等级**: 🟢 低风险
   - **原因**: 异步API，使用async_bank_router
   - **建议**: 正常测试

3. **搜索帖子** (`search_posts`)
   - **风险等级**: 🟢 低风险
   - **原因**: 读取操作，论坛系统相对独立
   - **建议**: 正常测试

4. **帖子评论** (`post_comments`)
   - **风险等级**: 🟢 低风险
   - **原因**: 读取操作
   - **建议**: 正常测试

5. **时间效率排行** (`medical_case_time_efficiency_ranking`)
   - **风险等级**: 🟢 低风险
   - **原因**: 读取操作，排行榜类API
   - **建议**: 正常测试

6. **经验值历史** (`medical_case_exp_history`)
   - **风险等级**: 🟢 低风险
   - **原因**: 读取操作
   - **建议**: 正常测试

7. **游戏历史记录** (`medical_case_game_history`)
   - **风险等级**: 🟢 低风险
   - **原因**: 读取操作
   - **建议**: 正常测试

8. **案例排行榜** (`medical_case_cases_ranking`)
   - **风险等级**: 🟢 低风险
   - **原因**: 读取操作，排行榜类API
   - **建议**: 正常测试

9. **平均得分排行** (`medical_case_avg_score_ranking`)
   - **风险等级**: 🟢 低风险
   - **原因**: 读取操作，排行榜类API
   - **建议**: 正常测试

10. **执行搜索** (`prognosis/search`)
    - **风险等级**: 🟢 低风险
    - **原因**: 搜索操作，预后系统有异步优化
    - **建议**: 正常测试

11. **我的邀请码** (`my-code`)
    - **风险等级**: 🟢 低风险
    - **原因**: 读取操作，邀请系统相对简单
    - **建议**: 正常测试

12. **邀请记录** (`records`)
    - **风险等级**: 🟢 低风险
    - **原因**: 读取操作
    - **建议**: 正常测试

13. **系统心跳** (`ping`)
    - **风险等级**: 🟢 低风险
    - **原因**: 系统级API，无数据库操作
    - **建议**: 正常测试

14. **干支查询** (`ganzhi`)
    - **风险等级**: 🟢 低风险
    - **原因**: 计算类API，无复杂数据库操作
    - **建议**: 正常测试

15. **获取当前季节** (`get_current_season`)
    - **风险等级**: 🟢 低风险
    - **原因**: 计算类API，无复杂数据库操作
    - **建议**: 正常测试

### 第五批安全API
16. **获取特定帖子** (`posts/{id}`)
    - **风险等级**: 🟢 低风险
    - **原因**: 读取操作，有异步优化
    - **建议**: 正常测试

17. **用户排名** (`medical_case_user_ranking/{id}`)
    - **风险等级**: 🟢 低风险
    - **原因**: 读取操作，排行榜类API
    - **建议**: 正常测试

18. **分类下的疗法** (`therapy-classifications/{id}/therapies`)
    - **风险等级**: 🟢 低风险
    - **原因**: 读取操作，预后系统
    - **建议**: 正常测试

19. **疗法详情** (`therapies/{id}/`)
    - **风险等级**: 🟢 低风险
    - **原因**: 读取操作，预后系统
    - **建议**: 正常测试

## 📋 建议的测试配置更新

### 需要跳过的API（10个）
```python
# 第四批跳过
"自定义活动",      # 同步异步混用
"用户卡片",        # 同步异步混用
"添加症状",        # 同步异步混用
"更新症状",        # 同步异步混用
"添加自定义症状",   # 同步异步混用

# 第五批跳过
"检查支付状态",     # 同步异步混用
"创建自定义活动",   # 同步异步混用
"银行更新症状",     # 同步异步混用
"删除症状",        # 同步异步混用
```

### 需要低并发测试的API（4个）
```python
# 中风险API，限制并发≤5
"提交游戏记录",     # 写入操作
"增加经验值",       # 写入操作
"医案评分",        # 写入操作
"添加健康记录",     # 写入操作
```

### 可以正常测试的API（约40个）
其余API可以按照原计划进行正常并发测试。

## 🎯 总结建议
1. **立即跳过10个高风险API**，避免连接池溢出
2. **4个中风险API使用低并发测试**（≤5并发）
3. **其余API可以正常测试**
4. **优先测试异步API和读取操作API**
5. **建立API风险等级标记系统**，便于后续维护
