# api/ninja_apis/routertest1/bazi_analysis_api.py
# 🔮 八字分析API模块 - 遵循routertest1格式规范
#
# 【重要说明】本模块遵循routertest1最佳实践：
# 1. 路由定义与注册分离
# 2. 使用Router而非直接的NinjaAPI实例
# 3. 在主__init__.py中统一管理注册
# 4. 模块化设计，专注八字分析功能

from ninja import Router
from ninja.errors import HttpError
from ninja.schema import Schema
from typing import List, Dict, Any, Optional
import asyncio
import json
import traceback
from datetime import datetime
from django.core.cache import cache
from asgiref.sync import sync_to_async

# 导入八字分析核心模块
import sys
import os
# 添加bazi_module路径
bazi_module_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils/bazi_module')
sys.path.insert(0, bazi_module_path)

from lunar_python import Solar, Lunar

# 导入神煞计算器
try:
    from shensha_calculator import query_shensha
    SHENSHA_AVAILABLE = True
except ImportError:
    SHENSHA_AVAILABLE = False

# 导入装饰器和工具
from api.ninja_apis.routertest1.cache_decorators import prognosis_cache, CacheTimeout

# 注意：AI解读功能通过WebSocket Consumer实现流式传输，不在HTTP API中实现

# 创建路由实例 - 注意：这里使用routertest1_router，不创建新的router
from api.ninja_apis.routertest1.test_api import routertest1_router

# ==================== Schema定义 ====================

class BaziInputSchema(Schema):
    """八字输入参数Schema"""
    year: int
    month: int  
    day: int
    hour: Optional[int] = 0
    minute: Optional[int] = 0
    second: Optional[int] = 0
    gender: Optional[int] = 1  # 1男0女
    sect: Optional[int] = 2    # 流派

class BaziBasicInfoSchema(Schema):
    """八字基本信息Schema"""
    solar_date: str
    lunar_date: str
    eight_char: str
    gender: str
    sect: int
    year_pillar: str
    month_pillar: str
    day_pillar: str
    time_pillar: str

class BaziResponseSchema(Schema):
    """八字分析响应Schema"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    analysis_type: Optional[str] = None
    timestamp: str

# ==================== 核心分析类 ====================

class BaziAnalysisCore:
    """八字分析核心类 - 简化版本"""
    
    def __init__(self, year, month, day, hour=0, minute=0, second=0, gender=1, sect=2):
        self.year = year
        self.month = month
        self.day = day
        self.hour = hour
        self.minute = minute
        self.second = second
        self.gender = gender
        self.sect = sect
        
        # 创建阳历和农历对象
        self.solar = Solar.fromYmdHms(year, month, day, hour, minute, second)
        self.lunar = self.solar.getLunar()
        self.eight_char = self.lunar.getEightChar()
        self.eight_char.setSect(sect)
    
    def get_basic_info(self):
        """获取基本信息"""
        return {
            "solar_date": self.solar.toFullString(),
            "lunar_date": self.lunar.toFullString(),
            "eight_char": str(self.eight_char),
            "gender": "男" if self.gender == 1 else "女",
            "sect": self.sect,
            "year_pillar": self.eight_char.getYear(),
            "month_pillar": self.eight_char.getMonth(),
            "day_pillar": self.eight_char.getDay(),
            "time_pillar": self.eight_char.getTime()
        }
    
    def get_shishen_analysis(self):
        """获取十神分析"""
        return {
            "gan_shishen": {
                "year": self.eight_char.getYearShiShenGan(),
                "month": self.eight_char.getMonthShiShenGan(),
                "day": self.eight_char.getDayShiShenGan(),
                "time": self.eight_char.getTimeShiShenGan()
            },
            "zhi_shishen": {
                "year": self.eight_char.getYearShiShenZhi(),
                "month": self.eight_char.getMonthShiShenZhi(),
                "day": self.eight_char.getDayShiShenZhi(),
                "time": self.eight_char.getTimeShiShenZhi()
            }
        }
    
    def get_shensha_analysis(self):
        """获取神煞分析"""
        if not SHENSHA_AVAILABLE:
            return {"error": "神煞计算器模块未安装"}
        
        try:
            # 构造八字数组
            bazi = [
                self.eight_char.getYearGan(), self.eight_char.getYearZhi(),
                self.eight_char.getMonthGan(), self.eight_char.getMonthZhi(),
                self.eight_char.getDayGan(), self.eight_char.getDayZhi(),
                self.eight_char.getTimeGan(), self.eight_char.getTimeZhi()
            ]
            
            # 获取年柱纳音
            nian_nayin = self.eight_char.getYearNaYin()
            is_man = self.gender == 1
            
            # 构造四柱信息
            pillars = [
                ("年柱", self.eight_char.getYear(), 1),
                ("月柱", self.eight_char.getMonth(), 2),
                ("日柱", self.eight_char.getDay(), 3),
                ("时柱", self.eight_char.getTime(), 4)
            ]
            
            shensha_results = {}
            all_shenshas = []
            
            # 分析每一柱的神煞
            for name, ganzhi, witch in pillars:
                try:
                    shenshas = query_shensha(ganzhi, bazi, is_man, witch, nian_nayin)
                    shensha_results[name] = {
                        "ganzhi": ganzhi,
                        "shenshas": shenshas,
                        "count": len(shenshas)
                    }
                    all_shenshas.extend(shenshas)
                except Exception as e:
                    shensha_results[name] = {
                        "ganzhi": ganzhi,
                        "error": str(e)
                    }
            
            # 统计信息
            unique_shenshas = list(set(all_shenshas))
            
            return {
                "pillars": shensha_results,
                "total_count": len(all_shenshas),
                "unique_count": len(unique_shenshas),
                "unique_shenshas": sorted(unique_shenshas)
            }
            
        except Exception as e:
            return {"error": f"神煞分析出错: {e}"}

    def get_dayun_analysis(self):
        """获取大运分析"""
        try:
            # 获取运势
            yun = self.eight_char.getYun(self.gender, self.sect)

            # 基本起运信息
            start_info = {
                "start_year": yun.getStartYear(),
                "start_month": yun.getStartMonth(),
                "start_day": yun.getStartDay(),
                "start_solar": yun.getStartSolar().toYmd(),
                "description": f"{yun.getStartYear()}年{yun.getStartMonth()}个月{yun.getStartDay()}天后起运"
            }

            # 获取大运信息（8步大运）
            da_yun_list = yun.getDaYun(8)
            dayun_info = []

            for i, da_yun in enumerate(da_yun_list):
                if i == 0 and da_yun.getIndex() < 1:
                    # 起运前
                    dayun_info.append({
                        "index": i,
                        "period": "起运前",
                        "start_year": da_yun.getStartYear(),
                        "end_year": da_yun.getEndYear(),
                        "start_age": da_yun.getStartAge(),
                        "end_age": da_yun.getEndAge(),
                        "ganzhi": "",
                        "description": f"{da_yun.getStartYear()}年-{da_yun.getEndYear()}年 {da_yun.getStartAge()}岁-{da_yun.getEndAge()}岁 (起运前)"
                    })
                else:
                    # 正常大运
                    dayun_info.append({
                        "index": i,
                        "period": f"第{i}步大运" if i > 0 else "起运前",
                        "start_year": da_yun.getStartYear(),
                        "end_year": da_yun.getEndYear(),
                        "start_age": da_yun.getStartAge(),
                        "end_age": da_yun.getEndAge(),
                        "ganzhi": da_yun.getGanZhi(),
                        "description": f"{da_yun.getStartYear()}年-{da_yun.getEndYear()}年 {da_yun.getStartAge()}岁-{da_yun.getEndAge()}岁 {da_yun.getGanZhi()}"
                    })

            # 获取当前大运（如果有）
            current_year = datetime.now().year
            current_dayun = None
            for dayun in dayun_info:
                if dayun["start_year"] <= current_year <= dayun["end_year"]:
                    current_dayun = dayun
                    break

            # 获取第一步大运的流年（如果有大运干支）
            liu_nian_info = []
            if len(da_yun_list) > 1:
                first_da_yun = da_yun_list[1] if da_yun_list[0].getIndex() < 1 else da_yun_list[0]
                if first_da_yun.getGanZhi():
                    liu_nian_list = first_da_yun.getLiuNian(5)  # 获取前5年流年
                    for i, liu_nian in enumerate(liu_nian_list):
                        liu_nian_info.append({
                            "index": i,
                            "year": liu_nian.getYear(),
                            "age": liu_nian.getAge(),
                            "ganzhi": liu_nian.getGanZhi(),
                            "description": f"{liu_nian.getYear()}年 {liu_nian.getAge()}岁 {liu_nian.getGanZhi()}"
                        })

            return {
                "start_info": start_info,
                "dayun_list": dayun_info,
                "current_dayun": current_dayun,
                "liu_nian_sample": {
                    "dayun_ganzhi": first_da_yun.getGanZhi() if len(da_yun_list) > 1 and first_da_yun.getGanZhi() else "",
                    "liu_nian_list": liu_nian_info
                },
                "total_dayun_count": len(dayun_info)
            }

        except Exception as e:
            return {"error": f"大运分析出错: {e}"}

# ==================== 说明 ====================
# AI解读功能通过WebSocket Consumer实现流式传输
# 位置：api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/consumers.py

#
# HTTP API只提供八字计算和数据分析，不包含AI解读
# 这样设计的优势：
# 1. 数据分析可以快速缓存和返回
# 2. AI解读通过WebSocket实现真正的流式传输
# 3. 前端可以先显示数据，再流式显示AI解读

# ==================== API接口定义 ====================

@routertest1_router.post("/bazi/basic",
                        response=BaziResponseSchema,
                        summary="获取八字基本信息",
                        tags=["八字分析"])
@prognosis_cache(timeout=CacheTimeout.LONG, prefix="bazi_basic")  # 缓存30分钟
async def get_bazi_basic_info(request, data: BaziInputSchema):
    """
    获取八字基本信息
    
    包括：阳历、农历、八字、性别、流派、四柱等基础信息
    """
    try:
        # 获取用户ID（遵循项目规范）
        user_id = request.user_id
        
        # 创建分析器
        analyzer = BaziAnalysisCore(
            data.year, data.month, data.day, 
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        # 获取基本信息
        basic_info = analyzer.get_basic_info()
        
        return {
            "success": True,
            "data": basic_info,
            "analysis_type": "基本信息",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@routertest1_router.post("/bazi/shishen",
                        response=BaziResponseSchema,
                        summary="获取十神分析",
                        tags=["八字分析"])
@prognosis_cache(timeout=CacheTimeout.LONG, prefix="bazi_shishen")
async def get_bazi_shishen_analysis(request, data: BaziInputSchema):
    """
    获取十神分析
    
    包括：天干十神、地支十神等详细分析
    """
    try:
        user_id = request.user_id
        
        analyzer = BaziAnalysisCore(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        shishen_analysis = analyzer.get_shishen_analysis()
        
        return {
            "success": True,
            "data": shishen_analysis,
            "analysis_type": "十神分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@routertest1_router.post("/bazi/shensha",
                        response=BaziResponseSchema,
                        summary="获取神煞分析",
                        tags=["八字分析"])
@prognosis_cache(timeout=CacheTimeout.LONG, prefix="bazi_shensha")
async def get_bazi_shensha_analysis(request, data: BaziInputSchema):
    """
    获取神煞分析
    
    包括：各柱神煞、神煞分类、统计信息等
    """
    try:
        user_id = request.user_id
        
        analyzer = BaziAnalysisCore(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        shensha_analysis = analyzer.get_shensha_analysis()
        
        return {
            "success": True,
            "data": shensha_analysis,
            "analysis_type": "神煞分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@routertest1_router.post("/bazi/dayun",
                        response=BaziResponseSchema,
                        summary="获取当前大运",
                        tags=["八字分析"])
@prognosis_cache(timeout=CacheTimeout.LONG, prefix="bazi_dayun")
async def get_bazi_dayun_analysis(request, data: BaziInputSchema):
    """
    获取当前大运分析

    包括：起运时间、大运信息、流年等详细分析
    """
    try:
        user_id = request.user_id

        analyzer = BaziAnalysisCore(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )

        dayun_analysis = analyzer.get_dayun_analysis()

        return {
            "success": True,
            "data": dayun_analysis,
            "analysis_type": "大运分析",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@routertest1_router.get("/wuyun-liuqi/current",
                       response=BaziResponseSchema,
                       summary="获取当前五运六气",
                       tags=["八字分析"])
@prognosis_cache(timeout=CacheTimeout.SHORT, prefix="current_wuyun_liuqi")  # 缓存5分钟
async def get_current_wuyun_liuqi(request):
    """
    获取当前五运六气信息（根据后端时间）

    基于当前时间（东八区）获取五运六气信息
    """
    try:
        user_id = request.user_id

        # 导入五运六气服务
        from api.services.wuyun_liuqi_service import WuyunLiuqiService

        # 检查五运六气系统是否可用
        if not WuyunLiuqiService.is_available():
            return {
                "success": False,
                "error": "五运六气系统不可用，请检查系统配置",
                "timestamp": datetime.now().isoformat()
            }

        # 获取当前五运六气信息
        current_wuyun_data = WuyunLiuqiService.get_current_wuyun_liuqi()

        if not current_wuyun_data.get("success", False):
            return {
                "success": False,
                "error": current_wuyun_data.get("error", "获取五运六气信息失败"),
                "timestamp": datetime.now().isoformat()
            }

        return {
            "success": True,
            "data": current_wuyun_data,
            "analysis_type": "当前五运六气",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@routertest1_router.post("/bazi/complete",
                        response=BaziResponseSchema,
                        summary="获取完整八字分析",
                        tags=["八字分析"])
@prognosis_cache(timeout=CacheTimeout.LONG, prefix="bazi_complete")  # 缓存30分钟
async def get_bazi_complete_analysis(request, data: BaziInputSchema):
    """
    获取完整的八字分析

    整合基本信息、十神分析、神煞分析等所有模块
    """
    try:
        user_id = request.user_id

        analyzer = BaziAnalysisCore(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )

        # 整合所有分析结果
        complete_analysis = {
            "basic_info": analyzer.get_basic_info(),
            "shishen_analysis": analyzer.get_shishen_analysis(),
            "shensha_analysis": analyzer.get_shensha_analysis(),
            "dayun_analysis": analyzer.get_dayun_analysis()
        }

        return {
            "success": True,
            "data": complete_analysis,
            "analysis_type": "完整八字分析",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
