"""
八字分析WebSocket消费者模块
处理八字分析相关WebSocket请求
"""
import json
import traceback
from datetime import datetime
from api.utils.logging import safe_print
from api.utils.constants import SYSTEM_PROMPTS, CONSULT_TEMPLATES
from api.services.bazi_service import BaziService
from api.services.llm_service import LLMService
from api.websockets.base_consumer import BaseConsumer

class BaziAdviceConsumer(BaseConsumer):
    """八字分析和各类咨询建议的WebSocket消费者类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 默认使用健康咨询的系统提示
        print('初始化BaziAdviceConsumer，这是组件里的初始化')
        self.conversation_history = [
            {"role": "system", "content": SYSTEM_PROMPTS["health"]}
        ]
        
        # 当前咨询类型
        self.current_consult_type = "health"
    
    async def handle_message(self, data):
        """
        处理收到的消息
        
        Args:
            data (dict): 收到的消息数据
        """
        # 如果是八字分析请求
        if 'birth_info' in data:
            safe_print(f"处理八字分析请求: {data['birth_info']}")
            await self.handle_bazi_request(data['birth_info'])
        # 如果是普通聊天请求
        elif 'message' in data:
            message = data['message']
            safe_print(f"处理聊天消息: {message[:50]}...")
            # 确保消息是UTF-8编码的字符串
            if isinstance(message, bytes):
                message = message.decode('utf-8')
            await self.handle_chat_message(message)
        else:
            safe_print(f"无法识别的请求格式: {data}")
            await self.send_error('无法识别的请求格式')
    
    async def handle_bazi_request(self, birth_info):
        """
        处理八字分析请求
        
        Args:
            birth_info (dict): 出生信息
        """
        try:
            # 首先发送当前时间的八字信息和节气信息
            try:
                current_time = datetime.now()
                current_bazi = BaziService.get_current_bazi()
                # 获取节气信息
                today_solar_term, next_solar_term, phase_status = BaziService.get_solar_terms_info()
                
                await self.send(text_data=json.dumps({
                    'type': 'current_time_bazi',
                    'current_time': current_time.strftime("%Y-%m-%d %H:%M:%S"),
                    'current_bazi': current_bazi,
                    'today_solar_term': today_solar_term,
                    'next_solar_term': next_solar_term,
                    'phase_status': phase_status
                }))
                safe_print(f"已发送当前时间八字信息: {current_bazi}, 节气信息: {today_solar_term}, {next_solar_term}")
            except Exception as e:
                safe_print(f"获取当前时间八字或节气时出错: {str(e)}")
                safe_print(traceback.format_exc())

            # 提取出生信息
            birth_year = birth_info.get('birth_year')
            birth_month = birth_info.get('birth_month')
            birth_day = birth_info.get('birth_day')
            birth_time = birth_info.get('birth_time')
            # 提取是否需要五运六气信息
            include_wuyun_liuqi = birth_info.get('include_wuyun_liuqi', False)
            # 提取咨询类型
            consult_type = birth_info.get('consult_type', 'health')
            
            # 设置当前咨询类型
            self.current_consult_type = consult_type if consult_type in SYSTEM_PROMPTS else "health"
            
            # 更新系统提示
            self.conversation_history = [
                {"role": "system", "content": SYSTEM_PROMPTS[self.current_consult_type]}
            ]
            
            # 验证数据
            if not all([isinstance(x, int) for x in [birth_year, birth_month, birth_day, birth_time]]):
                safe_print(f"出生信息格式错误: {birth_info}")
                await self.send_error('出生信息格式错误，请确保年、月、日、时均为整数')
                return
            
            # 获取八字
            safe_print(f"计算八字: {birth_year}/{birth_month}/{birth_day} {birth_time}时")
            shenchenbazi = BaziService.get_birth_bazi(birth_year, birth_month, birth_day, birth_time)
            safe_print(f"八字结果: {shenchenbazi}")
            
            # 获取生肖
            shengxiao = BaziService.get_shengxiao(birth_year)
            
            # 计算五行
            wuxing_dict = BaziService.get_wuxing_analysis(shenchenbazi)
            safe_print(f"五行分析结果: {wuxing_dict}")
            
            # 获取当前五运六气（如果需要）
            wuyun_liuqi_data = BaziService.get_wuyun_liuqi_data(include_wuyun_liuqi, birth_year, birth_month, birth_day)
            
            # 先发送八字和五行分析结果
            bazi_result = {
                'type': 'bazi_result',
                'shenchenbazi': shenchenbazi,
                'wuxing': wuxing_dict,
                'shengxiao': shengxiao,
                'wuyun_liuqi': wuyun_liuqi_data,
                'today_solar_term': today_solar_term,
                'next_solar_term': next_solar_term,
                'phase_status': phase_status,
                'done': False
            }
            safe_print("发送八字分析结果")
            await self.send(text_data=json.dumps(bazi_result))
            
            # 构建提示信息
            prompt = BaziService.generate_prompt(
                self.current_consult_type, 
                shenchenbazi, 
                wuyun_liuqi_data, 
                CONSULT_TEMPLATES
            )
            
            # 记录提示信息的编码信息
            safe_print(f"提示信息类型: {type(prompt)}, 长度: {len(prompt)}")
            safe_print(f"提示信息编码: 尝试对提示信息的前10个字符进行编码测试:")
            for encoding in ['utf-8', 'ascii', 'gbk']:
                try:
                    encoded = prompt[:10].encode(encoding)
                    safe_print(f"  - {encoding}编码成功: {encoded}")
                except UnicodeEncodeError as e:
                    safe_print(f"  - {encoding}编码失败: {e}")
            
            # 添加用户消息到历史记录
            system_message = {"role": "system", "content": "作为中医大师，你擅长养生，根据八字和五行分析，提供养生建议。"}
            user_message = {"role": "user", "content": prompt}
            
            messages = [system_message, user_message]
            
            try:
                # 根据咨询类型设置消息类型
                advice_type = f"{self.current_consult_type}_advice"
                
                # 生成回复
                assistant_message = await LLMService.generate_response(
                    messages, 
                    self.send, 
                    advice_type
                )
                
                # 将完整回复添加到历史记录
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                safe_print(f"完整回复长度: {len(assistant_message)}")
            except Exception as api_error:
                error_detail = str(api_error)
                safe_print(f"调用LLM服务出错: {error_detail}")
                safe_print(f"错误类型: {type(api_error)}")
                safe_print(f"详细错误信息: {traceback.format_exc()}")
                
                await self.send_error("生成分析结果时出错，请稍后再试")
            
        except Exception as e:
            error_message = f"生成八字分析时出错: {str(e)}\n{traceback.format_exc()}"
            safe_print(error_message)
            await self.send_error(error_message)
    
    async def handle_chat_message(self, user_message):
        """
        处理普通聊天消息
        
        Args:
            user_message (str): 用户消息内容
        """
        try:
            safe_print(f"收到聊天消息: {user_message[:50]}...")
            
            # 确保消息是UTF-8编码的字符串
            if isinstance(user_message, bytes):
                user_message = user_message.decode('utf-8')
                
            # 记录消息编码信息
            safe_print(f"聊天消息类型: {type(user_message)}, 长度: {len(user_message)}")
            safe_print(f"聊天消息编码测试:")
            for encoding in ['utf-8', 'ascii', 'gbk']:
                try:
                    encoded = user_message[:10].encode(encoding)
                    safe_print(f"  - {encoding}编码成功: {encoded}")
                except UnicodeEncodeError as e:
                    safe_print(f"  - {encoding}编码失败: {e}")
            
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})
            
            try:
                # 使用简单消息格式
                messages = [
                    {"role": "system", "content": SYSTEM_PROMPTS[self.current_consult_type]},
                    {"role": "user", "content": user_message}
                ]
                
                # 生成回复
                assistant_message = await LLMService.generate_response(
                    messages, 
                    self.send, 
                    'chat_response',
                    max_tokens=4000
                )
                
                # 将完整回复添加到历史记录
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                safe_print(f"完整回复长度: {len(assistant_message)}")
            except Exception as api_error:
                error_detail = str(api_error)
                safe_print(f"调用LLM服务出错: {error_detail}")
                safe_print(f"错误类型: {type(api_error)}")
                safe_print(f"详细错误信息: {traceback.format_exc()}")
                
                await self.send_error("生成回复时出错，请稍后再试")
                
        except Exception as e:
            error_message = f"生成回复时出错: {str(e)}\n{traceback.format_exc()}"
            safe_print(error_message)
            await self.send_error(error_message) 