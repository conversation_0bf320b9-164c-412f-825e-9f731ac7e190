#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模拟前端频繁刷新token的场景测试
测试缓存机制在实际使用中的效果
"""

import os
import sys
import asyncio
import aiohttp
import time
import json
import jwt
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')

import django
django.setup()

from django.conf import settings
from api.models.user_models import UserInfo

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
REFRESH_TOKEN_URL = f"{BASE_URL}/api/auth/refresh-token"

def generate_test_refresh_token():
    """生成一个测试用的有效refresh token"""
    try:
        from django.db import connection

        # 使用原生SQL查询避免异步问题
        with connection.cursor() as cursor:
            cursor.execute("SELECT id FROM user_info LIMIT 1")
            row = cursor.fetchone()

        if not row:
            print("❌ 数据库中没有用户，无法生成测试token")
            return None

        user_id = row[0]
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + timedelta(days=90)
        }

        refresh_token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
        return refresh_token, user_id

    except Exception as e:
        print(f"❌ 生成测试token失败: {str(e)}")
        return None

async def single_refresh_request(session, refresh_token, request_id):
    """单次刷新请求"""
    headers = {
        "Refresh-Token": refresh_token,
        "Content-Type": "application/json"
    }
    
    start_time = time.time()
    
    try:
        async with session.post(REFRESH_TOKEN_URL, headers=headers) as response:
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status == 200:
                response_data = await response.json()
                access_token = response_data.get('access_token', '')
                return {
                    'id': request_id,
                    'success': True,
                    'duration': duration,
                    'access_token': access_token,
                    'status_code': response.status
                }
            else:
                response_data = await response.json()
                return {
                    'id': request_id,
                    'success': False,
                    'duration': duration,
                    'error': response_data,
                    'status_code': response.status
                }
                
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        return {
            'id': request_id,
            'success': False,
            'duration': duration,
            'error': str(e),
            'status_code': 0
        }

async def simulate_frontend_burst(refresh_token, burst_size=10, burst_interval=0.05):
    """模拟前端突发请求（如页面快速刷新）"""
    print(f"\n🚀 模拟前端突发请求：{burst_size}个请求，间隔{burst_interval}秒")
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        
        # 创建突发请求
        for i in range(burst_size):
            if i > 0:
                await asyncio.sleep(burst_interval)
            
            task = single_refresh_request(session, refresh_token, f"突发{i+1}")
            tasks.append(task)
        
        # 等待所有请求完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 分析结果
        success_count = 0
        total_duration = 0
        access_tokens = set()
        durations = []
        
        print(f"\n📊 突发请求结果分析:")
        for result in results:
            if isinstance(result, Exception):
                print(f"❌ 异常: {result}")
                continue
                
            if result['success']:
                success_count += 1
                total_duration += result['duration']
                durations.append(result['duration'])
                if result['access_token']:
                    access_tokens.add(result['access_token'])
                
                # 判断是否为缓存命中（通过响应时间）
                cache_hit = result['duration'] < 0.015  # 小于15ms认为是缓存命中
                cache_status = "🎯缓存命中" if cache_hit else "💾数据库查询"
                print(f"[{result['id']}] ✅ {result['duration']:.3f}秒 {cache_status}")
            else:
                print(f"[{result['id']}] ❌ 失败: {result['error']}")
        
        # 统计信息
        if durations:
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            print(f"\n📈 性能统计:")
            print(f"✅ 成功率: {success_count}/{burst_size} ({success_count/burst_size*100:.1f}%)")
            print(f"⏱️ 平均响应时间: {avg_duration:.3f}秒")
            print(f"⚡ 最快响应: {min_duration:.3f}秒")
            print(f"🐌 最慢响应: {max_duration:.3f}秒")
            print(f"🔑 返回的access_token数量: {len(access_tokens)}")
            
            # 缓存效果分析
            cache_hits = sum(1 for d in durations if d < 0.015)
            cache_hit_rate = cache_hits / len(durations) * 100
            print(f"🎯 缓存命中率: {cache_hits}/{len(durations)} ({cache_hit_rate:.1f}%)")
            
            if len(access_tokens) == 1:
                print("✅ 缓存一致性: 所有请求返回相同的access_token")
            else:
                print("⚠️ 缓存一致性问题: 返回了不同的access_token")
        
        return success_count, len(access_tokens), durations

async def simulate_user_behavior(refresh_token, session_duration=30):
    """模拟真实用户行为（随机间隔的刷新请求）"""
    print(f"\n👤 模拟真实用户行为：{session_duration}秒内随机刷新")
    
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        request_count = 0
        results = []
        
        while time.time() - start_time < session_duration:
            # 随机等待1-5秒
            wait_time = 1 + (time.time() % 4)  # 1-5秒随机间隔
            await asyncio.sleep(wait_time)
            
            request_count += 1
            result = await single_refresh_request(session, refresh_token, f"用户{request_count}")
            results.append(result)
            
            if result['success']:
                cache_hit = result['duration'] < 0.015
                cache_status = "🎯缓存" if cache_hit else "💾数据库"
                print(f"[用户{request_count}] ✅ {result['duration']:.3f}秒 {cache_status}")
            else:
                print(f"[用户{request_count}] ❌ 失败")
        
        # 分析用户行为结果
        success_results = [r for r in results if r['success']]
        if success_results:
            durations = [r['duration'] for r in success_results]
            cache_hits = sum(1 for d in durations if d < 0.015)
            
            print(f"\n📊 用户行为分析:")
            print(f"📝 总请求数: {len(results)}")
            print(f"✅ 成功请求: {len(success_results)}")
            print(f"⏱️ 平均响应时间: {sum(durations)/len(durations):.3f}秒")
            print(f"🎯 缓存命中: {cache_hits}/{len(durations)} ({cache_hits/len(durations)*100:.1f}%)")
        
        return results

async def main():
    """主测试函数"""
    print("🧪 前端Token刷新缓存机制测试")
    print("=" * 60)
    
    # 生成测试token
    result = generate_test_refresh_token()
    if not result:
        print("❌ 无法生成测试token")
        return
    
    refresh_token, user_id = result
    print(f"🔑 测试用户ID: {user_id}")
    print(f"📝 Refresh Token: {refresh_token[:30]}...")
    
    try:
        # 测试1: 模拟前端突发请求（页面快速刷新）
        print("\n" + "="*60)
        print("🔥 测试场景1: 前端页面快速刷新（10个请求，50ms间隔）")
        burst_success, burst_tokens, burst_durations = await simulate_frontend_burst(
            refresh_token, burst_size=10, burst_interval=0.05
        )
        
        # 等待缓存过期测试
        print("\n⏳ 等待3秒，测试缓存持续性...")
        await asyncio.sleep(3)
        
        # 测试2: 验证缓存仍然有效
        print("\n" + "="*60)
        print("🔄 测试场景2: 验证缓存持续性（3个请求，1秒间隔）")
        persistence_success, persistence_tokens, _ = await simulate_frontend_burst(
            refresh_token, burst_size=3, burst_interval=1.0
        )
        
        # 测试3: 模拟真实用户行为
        print("\n" + "="*60)
        print("👤 测试场景3: 真实用户行为模拟（15秒随机间隔）")
        user_results = await simulate_user_behavior(refresh_token, session_duration=15)
        
        # 总结报告
        print("\n" + "="*60)
        print("📋 测试总结报告:")
        print(f"🔥 突发请求: {burst_success}/10 成功, {burst_tokens} 个不同token")
        print(f"🔄 缓存持续性: {persistence_success}/3 成功, {persistence_tokens} 个不同token")
        print(f"👤 用户行为: {len([r for r in user_results if r['success']])}/{len(user_results)} 成功")
        
        # 性能改进评估
        if burst_durations:
            cache_hits = sum(1 for d in burst_durations if d < 0.015)
            performance_improvement = cache_hits / len(burst_durations) * 100
            print(f"⚡ 性能改进: 缓存命中率 {performance_improvement:.1f}%")
            
            if performance_improvement > 80:
                print("🎉 缓存机制工作优秀！显著减少了重复刷新")
            elif performance_improvement > 50:
                print("✅ 缓存机制工作良好，有效减少了重复刷新")
            else:
                print("⚠️ 缓存机制需要优化")
        
        print("\n💡 建议:")
        print("- 前端可以放心使用此缓存机制")
        print("- 短时间内的重复刷新会被自动优化")
        print("- 缓存时间为5小时，与access_token过期时间匹配")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
