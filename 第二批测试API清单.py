#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二批测试API清单
专门用于第二批扩展功能API的性能测试
包括：论坛系统、医案系统、症状管理、问答系统
"""

# 第二批测试API列表
SECOND_BATCH_APIS = [
    # 论坛系统API (3个)
    "论坛帖子列表",
    "论坛版块列表",
    "用户帖子",

    # 医案系统API (4个)
    "医案经验排行",
    "医案总分排行",
    "医案用户统计",
    "健康记录",

    # 症状管理API (2个)
    "所有症状",
    "自定义症状",

    # 问答系统API (2个) - 学习系统分类
    "提交答题得分",
    "答题排名"
]

# 按业务模块分组
SECOND_BATCH_BY_MODULE = {
    "论坛系统": [
        "论坛帖子列表",
        "论坛版块列表", 
        "用户帖子"
    ],
    "医案系统": [
        "医案经验排行",
        "医案总分排行",
        "医案用户统计",
        "健康记录"
    ],
    "症状管理": [
        "所有症状",
        "自定义症状"
    ],
    "问答系统": [
        "提交答题得分",
        "答题排名"
    ]
}

print("第二批测试API清单:")
print("=" * 50)
for module, apis in SECOND_BATCH_BY_MODULE.items():
    print(f"\n📋 {module} ({len(apis)}个API):")
    for i, api in enumerate(apis, 1):
        print(f"   {i}. {api}")

print(f"\n📊 总计: {len(SECOND_BATCH_APIS)} 个API")
