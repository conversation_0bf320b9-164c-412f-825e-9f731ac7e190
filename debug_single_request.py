#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试单个请求 - 验证测试脚本逻辑
"""

import requests
import time
import json

# 读取测试token
with open('test_token_user_2.txt', 'r') as f:
    TEST_TOKEN = f.read().strip()

def debug_request(url, headers=None):
    """调试单个请求"""
    print(f"🧪 调试请求: {url}")
    print(f"🔑 Headers: {headers}")
    
    start_time = time.time()
    try:
        response = requests.get(url, headers=headers or {}, timeout=10)
        response_time = time.time() - start_time
        
        print(f"✅ 状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time*1000:.1f}ms")
        print(f"📦 响应大小: {len(response.content)} bytes")
        print(f"📋 响应头: {dict(response.headers)}")
        
        # 检查成功状态
        success = response.status_code in [200, 201, 202]
        print(f"🎯 成功判断: {success}")
        
        # 检查缓存
        cache_hit = (
            'Cache-Control' in response.headers or 
            'ETag' in response.headers or
            'X-Cache' in response.headers or
            'cache' in response.headers.get('X-Cache-Status', '').lower()
        )
        
        # 检查JSON中的缓存标识
        if success and response.headers.get('content-type', '').startswith('application/json'):
            try:
                data = response.json()
                if isinstance(data, dict):
                    json_cache = data.get('cached', False) or data.get('from_cache', False)
                    cache_hit = cache_hit or json_cache
                    print(f"📄 JSON缓存标识: {json_cache}")
            except Exception as e:
                print(f"⚠️  JSON解析失败: {e}")
        
        print(f"💾 缓存命中: {cache_hit}")
        
        # 显示响应内容预览
        if response.content:
            content_preview = response.text[:200].replace('\n', ' ')
            print(f"📄 内容预览: {content_preview}...")
        
        return {
            'response_time': response_time,
            'success': success,
            'status_code': response.status_code,
            'cache_hit': cache_hit,
            'error': None
        }
        
    except Exception as e:
        response_time = time.time() - start_time
        print(f"💥 异常: {str(e)}")
        return {
            'response_time': response_time,
            'success': False,
            'status_code': 0,
            'cache_hit': False,
            'error': str(e)
        }

def main():
    """主函数"""
    print("🚀 开始调试单个请求")
    print("=" * 60)
    
    # 测试不需要认证的API
    print("\n1️⃣ 测试首页API（无需认证）")
    result1 = debug_request("http://127.0.0.1:8000/api/HomePage/")
    
    print("\n" + "="*60)
    
    # 测试需要认证的API
    print("\n2️⃣ 测试疗法分类API（需要认证）")
    headers = {"Authorization": f"Bearer {TEST_TOKEN}"}
    result2 = debug_request("http://127.0.0.1:8000/api/routertest1/prognosis/therapy-classifications", headers)
    
    print("\n" + "="*60)
    print("📊 调试结果总结:")
    print(f"首页API - 成功: {result1['success']}, 缓存: {result1['cache_hit']}")
    print(f"疗法API - 成功: {result2['success']}, 缓存: {result2['cache_hit']}")

if __name__ == "__main__":
    main()
