"""
映射关系数据

包含各种映射关系，如地支五行对应、天干阴阳对应等。
"""

from typing import Dict, List

# 地支对应五行
DIZHI_WUXING_MAP: Dict[str, str] = {
    "寅": "木", "卯": "木",
    "巳": "火", "午": "火", 
    "丑": "土", "辰": "土", "未": "土", "戌": "土",
    "申": "金", "酉": "金",
    "亥": "水", "子": "水"
}

# 天干阴阳对应（阳干为True，阴干为False）
TIANGAN_YINYANG_MAP: Dict[str, bool] = {
    "甲": True,  "乙": False,
    "丙": True,  "丁": False,
    "戊": True,  "己": False,
    "庚": True,  "辛": False,
    "壬": True,  "癸": False
}

# 年支数组（用于丧门、吊客、披麻计算）
NIANZHI_ARRAYS: Dict[str, List[str]] = {
    "nianzhi": ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"],
    "shangmen": ["寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥", "子", "丑"],
    "diaoke": ["戌", "亥", "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉"],
    "pima": ["酉", "戌", "亥", "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申"]
}

# 纳音五行对应表（部分，用于学堂、词馆计算）
NAYIN_WUXING_MAP: Dict[str, str] = {
    "海中金": "金", "炉中火": "火", "大林木": "木", "路旁土": "土", "剑锋金": "金",
    "山头火": "火", "涧下水": "水", "城头土": "土", "白蜡金": "金", "杨柳木": "木",
    "泉中水": "水", "屋上土": "土", "霹雳火": "火", "松柏木": "木", "长流水": "水",
    "沙中金": "金", "山下火": "火", "平地木": "木", "壁上土": "土", "金箔金": "金",
    "覆灯火": "火", "天河水": "水", "大驿土": "土", "钗钏金": "金", "桑柘木": "木",
    "大溪水": "水", "沙中土": "土", "天上火": "火", "石榴木": "木", "大海水": "水"
}

# 季节对应月支
SEASON_MONTHS: Dict[str, List[str]] = {
    "春": ["寅", "卯", "辰"],
    "夏": ["巳", "午", "未"], 
    "秋": ["申", "酉", "戌"],
    "冬": ["亥", "子", "丑"]
}

# 三合局对应
SANHE_GROUPS: Dict[str, List[str]] = {
    "申子辰": ["申", "子", "辰"],  # 水局
    "寅午戌": ["寅", "午", "戌"],  # 火局
    "巳酉丑": ["巳", "酉", "丑"],  # 金局
    "亥卯未": ["亥", "卯", "未"]   # 木局
}
