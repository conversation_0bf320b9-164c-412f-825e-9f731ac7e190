"""
测试数据

包含用于测试的八字数据和预期结果。
"""

# 测试用八字数据
TEST_BAZI_CASES = [
    {
        "name": "甲子年丙寅月戊午日癸亥时",
        "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"],
        "nian_nayin": "海中金",
        "is_man": True,
        "expected_results": {
            1: ["太极", "德秀", "福星"],  # 年柱预期神煞
            2: ["月德", "德秀", "福星"],  # 月柱预期神煞
            3: ["太极", "德秀", "羊刃"],  # 日柱预期神煞
            4: ["德秀"]  # 时柱预期神煞
        }
    },
    {
        "name": "乙丑年戊寅月庚申日丁丑时",
        "bazi": ["乙", "丑", "戊", "寅", "庚", "申", "丁", "丑"],
        "nian_nayin": "海中金",
        "is_man": False,
        "expected_results": {
            1: ["福星"],
            2: ["天乙", "太极"],
            3: ["天乙", "禄神"],
            4: ["天德", "福星"]
        }
    }
]

# 单个神煞测试用例
SHENSHA_TEST_CASES = {
    "tianyiguiren": [
        {"tiangan": "甲", "dizhi": "丑", "expected": 1},
        {"tiangan": "甲", "dizhi": "未", "expected": 1},
        {"tiangan": "甲", "dizhi": "子", "expected": 0},
        {"tiangan": "乙", "dizhi": "申", "expected": 1},
        {"tiangan": "乙", "dizhi": "子", "expected": 1},
        {"tiangan": "丙", "dizhi": "亥", "expected": 1},
        {"tiangan": "丙", "dizhi": "酉", "expected": 1}
    ],
    "taijiguiren": [
        {"tiangan": "甲", "dizhi": "子", "expected": 1},
        {"tiangan": "甲", "dizhi": "午", "expected": 1},
        {"tiangan": "乙", "dizhi": "子", "expected": 1},
        {"tiangan": "丙", "dizhi": "酉", "expected": 1},
        {"tiangan": "戊", "dizhi": "辰", "expected": 1},  # 土支
        {"tiangan": "己", "dizhi": "未", "expected": 1}   # 土支
    ],
    "wenchang": [
        {"tiangan": "甲", "dizhi": "巳", "expected": 1},
        {"tiangan": "乙", "dizhi": "午", "expected": 1},
        {"tiangan": "丙", "dizhi": "申", "expected": 1},
        {"tiangan": "丁", "dizhi": "酉", "expected": 1},
        {"tiangan": "戊", "dizhi": "申", "expected": 1},
        {"tiangan": "己", "dizhi": "酉", "expected": 1}
    ],
    "yangren": [
        {"day_gan": "甲", "dizhi": "卯", "expected": 1},
        {"day_gan": "乙", "dizhi": "寅", "expected": 1},
        {"day_gan": "丙", "dizhi": "午", "expected": 1},
        {"day_gan": "丁", "dizhi": "巳", "expected": 1},
        {"day_gan": "戊", "dizhi": "午", "expected": 1},
        {"day_gan": "己", "dizhi": "巳", "expected": 1}
    ],
    "yima": [
        {"base_zhi": "申", "target_zhi": "寅", "expected": 1},
        {"base_zhi": "子", "target_zhi": "寅", "expected": 1},
        {"base_zhi": "辰", "target_zhi": "寅", "expected": 1},
        {"base_zhi": "寅", "target_zhi": "申", "expected": 1},
        {"base_zhi": "午", "target_zhi": "申", "expected": 1},
        {"base_zhi": "戌", "target_zhi": "申", "expected": 1}
    ],
    "kongwang": [
        {"ganzhi": "甲子", "dizhi": "戌", "expected": 1},
        {"ganzhi": "甲子", "dizhi": "亥", "expected": 1},
        {"ganzhi": "甲戌", "dizhi": "申", "expected": 1},
        {"ganzhi": "甲戌", "dizhi": "酉", "expected": 1},
        {"ganzhi": "甲申", "dizhi": "午", "expected": 1},
        {"ganzhi": "甲申", "dizhi": "未", "expected": 1}
    ]
}

# 工具函数测试用例
HELPER_TEST_CASES = {
    "get_jiazi_order": [
        {"ganzhi": "甲子", "expected": 1},
        {"ganzhi": "乙丑", "expected": 2},
        {"ganzhi": "癸亥", "expected": 60},
        {"ganzhi": "无效", "expected": 0}
    ],
    "get_dizhi_wuxing": [
        {"dizhi": "子", "expected": "水"},
        {"dizhi": "丑", "expected": "土"},
        {"dizhi": "寅", "expected": "木"},
        {"dizhi": "卯", "expected": "木"},
        {"dizhi": "辰", "expected": "土"},
        {"dizhi": "巳", "expected": "火"}
    ],
    "get_tiangan_yinyang": [
        {"tiangan": "甲", "expected": True},
        {"tiangan": "乙", "expected": False},
        {"tiangan": "丙", "expected": True},
        {"tiangan": "丁", "expected": False},
        {"tiangan": "戊", "expected": True},
        {"tiangan": "己", "expected": False}
    ]
}

# 验证函数测试用例
VALIDATOR_TEST_CASES = {
    "validate_ganzhi": [
        {"ganzhi": "甲子", "expected": True},
        {"ganzhi": "癸亥", "expected": True},
        {"ganzhi": "甲", "expected": False},
        {"ganzhi": "甲子丑", "expected": False},
        {"ganzhi": "无效", "expected": False}
    ],
    "validate_bazi": [
        {
            "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"],
            "expected": True
        },
        {
            "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸"],  # 缺少一个
            "expected": False
        },
        {
            "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸", "无"],  # 无效地支
            "expected": False
        }
    ],
    "validate_witch": [
        {"witch": 1, "expected": True},
        {"witch": 4, "expected": True},
        {"witch": 8, "expected": True},
        {"witch": 0, "expected": False},
        {"witch": 9, "expected": False}
    ]
}
