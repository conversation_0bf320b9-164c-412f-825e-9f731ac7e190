"""
预后系统相关模型的Admin配置
包括疗法分类、疗法管理、用户疗法等功能的admin设置
"""

from django.contrib import admin
from import_export import resources, fields
from import_export.admin import ImportExportModelAdmin
from import_export.widgets import ForeignKeyWidget
from django.utils.html import format_html
from django.db.models import Count, Avg
from django.contrib import messages

from ..models import (
    PrognosisTherapyClassification,
    PrognosisTherapyCategory, 
    PrognosisUserTherapy,
    PrognosisTherapyLike,
    PrognosisTherapyRating,
    PrognosisTherapyComment,
    PrognosisSymptomTherapyMapping,
    PrognosisTherapyUsageRecord,
    PrognosisSymptom,
    PrognosisUserSymptom
)


# ==================== 疗法分类管理 ====================

class PrognosisTherapyClassificationResource(resources.ModelResource):
    class Meta:
        model = PrognosisTherapyClassification
        fields = ('id', 'name', 'code', 'description', 'icon', 'color', 'sort_order', 'is_active')
        export_order = fields


@admin.register(PrognosisTherapyClassification)
class PrognosisTherapyClassificationAdmin(ImportExportModelAdmin):
    resource_class = PrognosisTherapyClassificationResource
    list_display = ('name', 'code', 'description_short', 'color_display', 'therapy_count_display', 'sort_order', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code', 'description')
    ordering = ('sort_order', 'name')
    list_editable = ('sort_order', 'is_active')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'description')
        }),
        ('外观设置', {
            'fields': ('icon', 'color')
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def description_short(self, obj):
        return obj.description[:50] + '...' if len(obj.description) > 50 else obj.description
    description_short.short_description = '描述摘要'
    
    def color_display(self, obj):
        return format_html(
            '<span style="display: inline-block; width: 20px; height: 20px; background-color: {}; border-radius: 3px; border: 1px solid #ccc;"></span> {}',
            obj.color, obj.color
        )
    color_display.short_description = '主题色'
    
    def therapy_count_display(self, obj):
        # 统计PrognosisUserTherapy中对应此分类code的疗法数量
        from ..models import PrognosisUserTherapy
        user_therapy_count = PrognosisUserTherapy.objects.filter(category=obj.code).count()
        
        # 统计PrognosisTherapyCategory中关联到此分类的疗法数量
        system_therapy_count = obj.therapies.filter(is_active=True).count()
        
        # 总数量
        total_count = user_therapy_count + system_therapy_count
        
        if total_count > 0:
            return format_html(
                '<strong style="color: #52c41a;">{}</strong><br/><small style="color: #8c8c8c;">用户疗法: {} | 系统疗法: {}</small>', 
                total_count, user_therapy_count, system_therapy_count
            )
        else:
            return format_html('<span style="color: #ccc;">0</span>')
    therapy_count_display.short_description = '疗法数量'


# ==================== 疗法管理（支持批量导入） ====================

class PrognosisTherapyCategoryResource(resources.ModelResource):
    # 分类关联字段，支持通过分类名称或编码导入
    classification = fields.Field(
        column_name='分类名称',
        attribute='classification',
        widget=ForeignKeyWidget(PrognosisTherapyClassification, 'name')
    )
    
    classification_code = fields.Field(
        column_name='分类编码', 
        attribute='classification',
        widget=ForeignKeyWidget(PrognosisTherapyClassification, 'code')
    )
    
    # 中文字段名，方便Excel导入
    name = fields.Field(column_name='疗法名称', attribute='name')
    description = fields.Field(column_name='疗法描述', attribute='description')
    instructions = fields.Field(column_name='操作指南', attribute='instructions')
    duration = fields.Field(column_name='持续时间', attribute='duration')
    difficulty_level = fields.Field(column_name='难度等级', attribute='difficulty_level')
    contraindications = fields.Field(column_name='禁忌症', attribute='contraindications')
    is_recommended = fields.Field(column_name='是否推荐', attribute='is_recommended')
    is_active = fields.Field(column_name='是否启用', attribute='is_active')
    
    class Meta:
        model = PrognosisTherapyCategory
        fields = ('id', 'classification', 'classification_code', 'name', 'description', 'instructions', 
                 'duration', 'difficulty_level', 'contraindications', 'is_recommended', 'is_active')
        export_order = fields
        import_id_fields = ('name', 'classification')  # 防重复导入的字段组合
        skip_unchanged = True  # 跳过未更改的记录
        report_skipped = True  # 报告跳过的记录
    
    def before_import_row(self, row, **kwargs):
        """导入前的数据预处理"""
        # 处理难度等级映射
        difficulty_map = {'简单': 1, '中等': 2, '困难': 3, '1': 1, '2': 2, '3': 3}
        if row.get('难度等级') in difficulty_map:
            row['难度等级'] = difficulty_map[row['难度等级']]
        elif row.get('难度等级'):
            row['难度等级'] = 1  # 默认值
            
        # 处理布尔值映射
        bool_map = {'是': True, '否': False, 'TRUE': True, 'FALSE': False, '1': True, '0': False, True: True, False: False}
        for field in ['是否推荐', '是否启用']:
            if row.get(field) in bool_map:
                row[field] = bool_map[row[field]]
            elif field == '是否推荐':
                row[field] = False  # 默认不推荐
            elif field == '是否启用':  
                row[field] = True   # 默认启用
    
    def after_save_instance(self, instance, using_transactions, dry_run):
        """保存后设置默认值"""
        if not dry_run:
            if not instance.image:
                instance.image = 'https://example.com/default-therapy.jpg'
            if not instance.color:
                instance.color = '#1890ff'
            instance.save()


@admin.register(PrognosisTherapyCategory)
class PrognosisTherapyCategoryAdmin(ImportExportModelAdmin):
    resource_class = PrognosisTherapyCategoryResource
    list_display = ('name', 'classification', 'difficulty_level_display', 'duration', 
                   'likes_count_display', 'rating_display', 'user_count_display', 
                   'is_recommended', 'is_active', 'created_at')
    list_filter = ('classification', 'difficulty_level', 'is_recommended', 'is_active', 'created_at')
    search_fields = ('name', 'description', 'instructions')
    ordering = ('classification', 'name')
    list_editable = ('is_recommended', 'is_active')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('classification', 'name', 'description')
        }),
        ('详细信息', {
            'fields': ('instructions', 'duration', 'difficulty_level', 'contraindications')
        }),
        ('外观设置', {
            'fields': ('image', 'color')
        }),
        ('状态设置', {
            'fields': ('is_recommended', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def difficulty_level_display(self, obj):
        levels = {1: '简单', 2: '中等', 3: '困难'}
        colors = {1: 'green', 2: 'orange', 3: 'red'}
        level_text = levels.get(obj.difficulty_level, '未知')
        color = colors.get(obj.difficulty_level, 'gray')
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, level_text)
    difficulty_level_display.short_description = '难度等级'
    
    def likes_count_display(self, obj):
        count = obj.likes_count
        return format_html('<span style="color: red;">❤️ {}</span>', count)
    likes_count_display.short_description = '点赞数'
    
    def rating_display(self, obj):
        rating = obj.rating
        if rating > 0:
            stars = '⭐' * int(rating)
            rating_value = float(rating) if rating else 0.0
            return format_html('{} ({:.1f})', stars, rating_value)
        return '暂无评分'
    rating_display.short_description = '评分'
    
    def user_count_display(self, obj):
        count = obj.user_count
        return format_html('<strong>{}</strong>', count)
    user_count_display.short_description = '使用用户数'


# ==================== 用户创建疗法管理（支持批量导入） ====================

class PrognosisUserTherapyResource(resources.ModelResource):
    # 创建者字段，支持通过用户名导入，也支持"系统"
    creator = fields.Field(
        column_name='创建者',
        attribute='creator',
        widget=ForeignKeyWidget(model=None)  # 后面会在before_import_row中处理
    )
    
    # 疗法类别，关联到现有分类
    category = fields.Field(
        column_name='疗法类别',
        attribute='category'
    )
    
    # 中文字段名，方便Excel导入
    name = fields.Field(column_name='疗法名称', attribute='name')
    description = fields.Field(column_name='疗法描述', attribute='description')
    instructions = fields.Field(column_name='操作指南', attribute='instructions')
    duration = fields.Field(column_name='持续时间', attribute='duration')
    
    # 新增：适宜体质字段
    constitution_1 = fields.Field(column_name='主要体质', attribute='constitution_1')
    constitution_2 = fields.Field(column_name='次要体质', attribute='constitution_2')
    constitution_3 = fields.Field(column_name='第三体质', attribute='constitution_3')
    constitution_4 = fields.Field(column_name='第四体质', attribute='constitution_4')
    
    # 新增：适宜脏腑字段
    organ_1 = fields.Field(column_name='主要脏腑', attribute='organ_1')
    organ_2 = fields.Field(column_name='次要脏腑', attribute='organ_2')
    
    # 新增：不适宜体质字段
    unsuitable_constitution_1 = fields.Field(column_name='不适宜体质1', attribute='unsuitable_constitution_1')
    unsuitable_constitution_2 = fields.Field(column_name='不适宜体质2', attribute='unsuitable_constitution_2')
    unsuitable_constitution_3 = fields.Field(column_name='不适宜体质3', attribute='unsuitable_constitution_3')
    unsuitable_constitution_4 = fields.Field(column_name='不适宜体质4', attribute='unsuitable_constitution_4')
    
    # 新增：不适宜脏腑字段
    unsuitable_organ_1 = fields.Field(column_name='不适宜脏腑1', attribute='unsuitable_organ_1')
    unsuitable_organ_2 = fields.Field(column_name='不适宜脏腑2', attribute='unsuitable_organ_2')
    
    # 新增：相关症状字段
    related_symptoms = fields.Field(column_name='相关症状', attribute='related_symptoms')
    
    is_public = fields.Field(column_name='是否公开', attribute='is_public')
    is_verified = fields.Field(column_name='是否验证', attribute='is_verified')
    
    class Meta:
        model = PrognosisUserTherapy
        fields = ('id', 'creator', 'name', 'category', 'description', 'instructions', 
                 'duration', 'constitution_1', 'constitution_2', 'constitution_3', 'constitution_4',
                 'organ_1', 'organ_2', 'unsuitable_constitution_1', 'unsuitable_constitution_2',
                 'unsuitable_constitution_3', 'unsuitable_constitution_4', 'unsuitable_organ_1',
                 'unsuitable_organ_2', 'related_symptoms', 'is_public', 'is_verified')
        export_order = fields
        import_id_fields = ('name', 'creator')  # 防重复导入的字段组合
        skip_unchanged = True
        report_skipped = True
    
    def before_import_row(self, row, **kwargs):
        """导入前的数据预处理"""
        from api.models import UserInfo, PrognosisTherapyClassification
        
        # 处理创建者字段
        creator_name = row.get('创建者', '').strip()
        if creator_name in ['系统', 'system', 'System', '']:
            # 创建或获取系统用户
            system_user, created = UserInfo.objects.get_or_create(
                nickname='系统',
                defaults={
                    'gender': 1,
                    'age': 0,
                    'profession': '系统管理员',
                    'location': '系统',
                    'height': 0.0,
                    'weight': 0.0,
                    'health_score': 100.0,
                    'is_member': True,
                    'member_type': '系统',
                    'score': 0
                }
            )
            row['创建者'] = system_user.id
        else:
            # 尝试通过昵称查找用户
            try:
                user = UserInfo.objects.get(nickname=creator_name)
                row['创建者'] = user.id
            except UserInfo.DoesNotExist:
                # 如果找不到用户，默认使用系统用户
                system_user, created = UserInfo.objects.get_or_create(
                    nickname='系统',
                    defaults={
                        'gender': 1,
                        'age': 0,
                        'profession': '系统管理员',
                        'location': '系统',
                        'height': 0.0,
                        'weight': 0.0,
                        'health_score': 100.0,
                        'is_member': True,
                        'member_type': '系统',
                        'score': 0
                    }
                )
                row['创建者'] = system_user.id
        
        # 处理疗法类别 - 通过code精确匹配
        category_input = row.get('疗法类别', '').strip()
        
        # 首先尝试通过code查找
        classification = None
        if category_input:
            try:
                # 先尝试作为code查找
                classification = PrognosisTherapyClassification.objects.get(code=category_input, is_active=True)
            except PrognosisTherapyClassification.DoesNotExist:
                # 如果code找不到，再尝试作为名称查找
                try:
                    classification = PrognosisTherapyClassification.objects.get(name=category_input, is_active=True)
                except PrognosisTherapyClassification.DoesNotExist:
                    pass
        
        # 如果找到了分类，使用分类名称
        if classification:
            row['疗法类别'] = classification.name
        else:
            # 如果都找不到，使用默认分类
            try:
                default_classification = PrognosisTherapyClassification.objects.get(code='others_therapy', is_active=True)
                row['疗法类别'] = default_classification.name
            except PrognosisTherapyClassification.DoesNotExist:
                row['疗法类别'] = '其他疗法'  # 最后的备用方案
            
        # 处理布尔值映射
        bool_map = {'是': True, '否': False, 'TRUE': True, 'FALSE': False, '1': True, '0': False, True: True, False: False}
        for field in ['是否公开', '是否验证']:
            if row.get(field) in bool_map:
                row[field] = bool_map[row[field]]
            elif field == '是否公开':
                row[field] = True   # 默认公开
            elif field == '是否验证':  
                row[field] = False  # 默认未验证，需要人工审核
    
    def after_save_instance(self, instance, using_transactions, dry_run):
        """保存后设置默认值"""
        if not dry_run:
            if not hasattr(instance, 'usage_count'):
                instance.usage_count = 0
                instance.save()


@admin.register(PrognosisUserTherapy)
class PrognosisUserTherapyAdmin(ImportExportModelAdmin):
    resource_class = PrognosisUserTherapyResource
    list_display = ('name', 'creator_display', 'category', 'symptoms_display',
                   'constitution_display', 'organ_display', 'duration', 'usage_count', 
                   'is_public', 'is_verified', 'created_at')
    list_filter = ('category', 'constitution_1', 'organ_1', 'is_public', 'is_verified', 'created_at', 'creator__nickname')
    search_fields = ('name', 'description', 'creator__nickname', 'category', 'related_symptoms',
                    'constitution_1', 'constitution_2', 'organ_1', 'organ_2')
    ordering = ('-created_at',)
    list_editable = ('is_public', 'is_verified')
    actions = ['mark_as_verified', 'mark_as_public', 'set_creator_as_system', 'custom_delete_selected']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('creator', 'name', 'category')
        }),
        ('详细信息', {
            'fields': ('description', 'instructions', 'duration')
        }),
        ('相关症状', {
            'fields': ('related_symptoms',),
            'description': '多个症状用逗号分隔，如：头痛,失眠,焦虑'
        }),
        ('适宜体质', {
            'fields': ('constitution_1', 'constitution_2', 'constitution_3', 'constitution_4'),
            'classes': ('collapse',)
        }),
        ('适宜脏腑', {
            'fields': ('organ_1', 'organ_2'),
            'classes': ('collapse',)
        }),
        ('不适宜体质', {
            'fields': ('unsuitable_constitution_1', 'unsuitable_constitution_2', 
                      'unsuitable_constitution_3', 'unsuitable_constitution_4'),
            'classes': ('collapse',)
        }),
        ('不适宜脏腑', {
            'fields': ('unsuitable_organ_1', 'unsuitable_organ_2'),
            'classes': ('collapse',)
        }),
        ('状态设置', {
            'fields': ('is_public', 'is_verified', 'usage_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at', 'usage_count')
    
    def _get_deletable_objects_summary(self, queryset):
        """
        高效地计算并返回待删除对象的摘要信息，以兼容Admin模板。
        临时简化版本，跳过有问题的模型查询。
        """
        # 按照Django Admin模板预期的格式构建摘要列表
        summary = [f" {queryset.count()} 个 {self.model._meta.verbose_name_plural}。"]
        
        # 暂时跳过相关对象统计，直到数据库表结构修复
        # related_objects_summary = []
        # summary.append(related_objects_summary)
            
        return summary
    
    def get_actions(self, request):
        """
        移除默认的删除操作，使用自定义的
        """
        actions = super().get_actions(request)
        if 'delete_selected' in actions:
            del actions['delete_selected']
        return actions

    def custom_delete_selected(self, request, queryset):
        """
        自定义的批量删除操作，用于替代默认的delete_selected
        - 通过计算关联对象数量而非加载实例来提升性能
        - 重用Django Admin的确认页面模板
        """
        from django.contrib.admin import helpers
        from django.template.response import TemplateResponse

        if request.POST.get('post'):
            n = queryset.count()
            queryset.delete()
            self.message_user(request, f"成功删除了 {n} 个疗法。", messages.SUCCESS)
            return None

        opts = self.model._meta
        deletable_objects_summary = self._get_deletable_objects_summary(queryset)

        context = {
            **self.admin_site.each_context(request),
            "title": "你确定吗?",
            "opts": opts,
            "deletable_objects": [deletable_objects_summary],
            "queryset": queryset,
            "action_checkbox_name": helpers.ACTION_CHECKBOX_NAME,
        }
        return TemplateResponse(request, "admin/delete_selected_confirmation.html", context)
    custom_delete_selected.short_description = "删除所选的用户创建疗法"
    
    def delete_view(self, request, object_id, extra_context=None):
        """
        重写单个对象的删除视图，同样为了性能
        """
        from django.contrib.admin.utils import unquote
        from django.template.response import TemplateResponse
        from django.shortcuts import get_object_or_404

        opts = self.model._meta
        obj = get_object_or_404(self.get_queryset(request), pk=unquote(object_id))
        queryset = self.get_queryset(request).filter(pk=obj.pk)

        if request.method == 'POST':
            obj.delete()
            self.message_user(request, f"成功删除了疗法: {obj}", messages.SUCCESS)
            return self.response_delete(request, obj_display=str(obj), obj_id=obj.pk)

        deletable_objects_summary = self._get_deletable_objects_summary(queryset)

        context = {
            **self.admin_site.each_context(request),
            'object_name': str(opts.verbose_name),
            'object': obj,
            'opts': opts,
            'deletable_objects': [deletable_objects_summary],
            'title': f'确认删除 {opts.verbose_name}',
            'is_popup': "_popup" in request.POST,
            'media': self.media,
            'preserved_filters': self.get_preserved_filters(request),
            **(extra_context or {}),
        }

        return TemplateResponse(request, "admin/delete_confirmation.html", context)

    def mark_as_verified(self, request, queryset):
        """批量标记为已验证"""
        updated = queryset.update(is_verified=True)
        self.message_user(request, f'已将 {updated} 个疗法标记为已验证', messages.SUCCESS)
    mark_as_verified.short_description = "标记为已验证"
    
    def mark_as_public(self, request, queryset):
        """批量标记为公开"""
        updated = queryset.update(is_public=True)
        self.message_user(request, f'已将 {updated} 个疗法标记为公开', messages.SUCCESS)
    mark_as_public.short_description = "标记为公开"
    
    def set_creator_as_system(self, request, queryset):
        """批量设置创建者为系统"""
        from api.models import UserInfo
        system_user, created = UserInfo.objects.get_or_create(
            nickname='系统',
            defaults={
                'gender': 1,
                'age': 0,
                'profession': '系统管理员',
                'location': '系统',
                'height': 0.0,
                'weight': 0.0,
                'health_score': 100.0,
                'is_member': True,
                'member_type': '系统',
                'score': 0
            }
        )
        updated = queryset.update(creator=system_user)
        self.message_user(request, f'已将 {updated} 个疗法的创建者设置为系统', messages.SUCCESS)
    set_creator_as_system.short_description = "设置创建者为系统"
    
    def creator_display(self, obj):
        if obj.creator and obj.creator.nickname:
            creator_name = obj.creator.nickname
            if creator_name == '系统':
                return format_html('<span style="color: #1890ff; font-weight: bold;">🔧 系统</span>')
            else:
                return creator_name
        return f"用户ID: {obj.creator_id}"
    creator_display.short_description = '创建者'

    def symptoms_display(self, obj):
        """显示相关症状信息"""
        if obj.related_symptoms:
            # 将逗号分隔的症状拆分并清理空白字符
            symptoms = [s.strip() for s in obj.related_symptoms.split(',') if s.strip()]
            if symptoms:
                return format_html('<span style="color: #ff7875;">🩺 {}</span>', ' | '.join(symptoms))
        return format_html('<span style="color: #ccc;">未设置</span>')
    symptoms_display.short_description = '相关症状'

    def constitution_display(self, obj):
        """显示适宜体质信息"""
        constitutions = [c for c in [obj.constitution_1, obj.constitution_2, obj.constitution_3, obj.constitution_4] if c]
        if constitutions:
            return format_html('<span style="color: #52c41a;">✓ {}</span>', ' | '.join(constitutions))
        return format_html('<span style="color: #ccc;">未设置</span>')
    constitution_display.short_description = '适宜体质'

    def organ_display(self, obj):
        """显示适宜脏腑信息"""
        organs = [o for o in [obj.organ_1, obj.organ_2] if o]
        if organs:
            return format_html('<span style="color: #1890ff;">⚡ {}</span>', ' | '.join(organs))
        return format_html('<span style="color: #ccc;">未设置</span>')
    organ_display.short_description = '适宜脏腑'


# ==================== 症状-疗法映射管理 ====================

@admin.register(PrognosisSymptomTherapyMapping)
class PrognosisSymptomTherapyMappingAdmin(admin.ModelAdmin):
    list_display = ('symptom_display', 'therapy_display', 'effectiveness_score', 
                   'confidence_level', 'created_by_display', 'created_at')
    list_filter = ('effectiveness_score', 'confidence_level', 'created_at')
    search_fields = ('symptom__name', 'user_symptom__name', 'therapy__name', 'user_therapy__name')
    ordering = ('-effectiveness_score', '-confidence_level')
    
    fieldsets = (
        ('症状信息', {
            'fields': ('symptom', 'user_symptom')
        }),
        ('疗法信息', {
            'fields': ('therapy', 'user_therapy')
        }),
        ('匹配信息', {
            'fields': ('constitution_match', 'organ_match')
        }),
        ('评分信息', {
            'fields': ('effectiveness_score', 'confidence_level')
        }),
        ('创建信息', {
            'fields': ('created_by', 'created_at')
        }),
    )
    
    readonly_fields = ('created_at',)
    
    def symptom_display(self, obj):
        return obj.get_symptom_name()
    symptom_display.short_description = '症状'
    
    def therapy_display(self, obj):
        return obj.get_therapy_name()
    therapy_display.short_description = '疗法'
    
    def created_by_display(self, obj):
        if obj.created_by:
            return obj.created_by.nickname if obj.created_by.nickname else f"用户ID: {obj.created_by_id}"
        return "系统创建"
    created_by_display.short_description = '创建者'


# ==================== 疗法使用记录管理 ====================

@admin.register(PrognosisTherapyUsageRecord)
class PrognosisTherapyUsageRecordAdmin(admin.ModelAdmin):
    list_display = ('user_display', 'therapy_display', 'usage_duration_days', 
                   'effectiveness_score', 'compliance_rating', 'created_at')
    list_filter = ('effectiveness_score', 'compliance_rating', 'usage_duration_days', 'created_at')
    search_fields = ('user__nickname', 'therapy__name', 'user_therapy__name', 'effectiveness_description')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('用户信息', {
            'fields': ('user',)
        }),
        ('疗法信息', {
            'fields': ('therapy', 'user_therapy')
        }),
        ('症状信息', {
            'fields': ('symptoms', 'user_symptoms')
        }),
        ('使用记录', {
            'fields': ('constitution_snapshot', 'usage_duration_days', 'compliance_rating')
        }),
        ('疗效评价', {
            'fields': ('effectiveness_score', 'effectiveness_description', 'side_effects')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    filter_horizontal = ('symptoms', 'user_symptoms')
    
    def user_display(self, obj):
        return obj.user.nickname if obj.user and obj.user.nickname else f"用户ID: {obj.user_id}"
    user_display.short_description = '用户'
    
    def therapy_display(self, obj):
        return obj.get_therapy_name()
    therapy_display.short_description = '疗法'


# ==================== 疗法互动数据管理 ====================

@admin.register(PrognosisTherapyLike)
class PrognosisTherapyLikeAdmin(admin.ModelAdmin):
    list_display = ('user_display', 'therapy', 'liked', 'created_at')
    list_filter = ('liked', 'created_at')
    search_fields = ('user__nickname', 'therapy__name')
    ordering = ('-created_at',)
    
    def user_display(self, obj):
        return obj.user.nickname if obj.user and obj.user.nickname else f"用户ID: {obj.user_id}"
    user_display.short_description = '用户'


@admin.register(PrognosisTherapyRating)
class PrognosisTherapyRatingAdmin(admin.ModelAdmin):
    list_display = ('user_display', 'therapy', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('user__nickname', 'therapy__name')
    ordering = ('-created_at',)
    
    def user_display(self, obj):
        return obj.user.nickname if obj.user and obj.user.nickname else f"用户ID: {obj.user_id}"
    user_display.short_description = '用户'


@admin.register(PrognosisTherapyComment)
class PrognosisTherapyCommentAdmin(admin.ModelAdmin):
    list_display = ('user_display', 'therapy', 'content_short', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__nickname', 'therapy__name', 'content')
    ordering = ('-created_at',)
    
    def user_display(self, obj):
        return obj.user.nickname if obj.user and obj.user.nickname else f"用户ID: {obj.user_id}"
    user_display.short_description = '用户'
    
    def content_short(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_short.short_description = '评论内容' 