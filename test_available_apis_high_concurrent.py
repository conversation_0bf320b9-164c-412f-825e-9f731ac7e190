#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可用API高并发性能测试脚本
专门测试已知可用的API，支持最高100并发
基于实际测试结果，只测试确实可用的API端点
"""

import asyncio
import aiohttp
import time
import json
import statistics
from datetime import datetime
from typing import List, Dict, Any
from dataclasses import dataclass

# 基于实际测试结果的可用API配置
AVAILABLE_APIS = {
    # 基础页面API - 确认可用
    "HomePage": {
        "url": "http://127.0.0.1:8000/api/HomePage/",
        "method": "GET",
        "description": "首页静态内容",
        "category": "基础页面",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    # 会员系统API - 部分可用
    "日活动": {
        "url": "http://127.0.0.1:8000/api/bank/DayActivitiesView/",
        "method": "POST",
        "data": {"date": "2025-08-04"},
        "description": "获取指定日期活动",
        "category": "会员系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    # 医案系统API - 部分可用
    "时间效率排行": {
        "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_time_efficiency_ranking",
        "method": "GET",
        "description": "医案时间效率排行榜",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    "案例排行榜": {
        "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_cases_ranking",
        "method": "GET",
        "description": "医案案例排行榜",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    "平均得分排行": {
        "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_avg_score_ranking",
        "method": "GET",
        "description": "医案平均得分排行榜",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    # 学习系统API - 确认可用
    "每日中医题目": {
        "url": "http://127.0.0.1:8000/api/async-bank/daily-tcm-questions/",
        "method": "GET",
        "description": "获取每日中医题目",
        "category": "学习系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    # 银行系统API - 确认可用
    "用户症状": {
        "url": "http://127.0.0.1:8000/api/async-bank/user-symptoms/",
        "method": "GET",
        "description": "获取用户症状列表",
        "category": "银行系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    "症状历史": {
        "url": "http://127.0.0.1:8000/api/async-bank/symptom-history/",
        "method": "GET",
        "description": "获取症状历史记录",
        "category": "银行系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    # 预后系统API - 部分可用
    "热门关键词": {
        "url": "http://127.0.0.1:8000/api/routertest1/prognosis/popular-keywords",
        "method": "GET",
        "description": "获取搜索热门关键词",
        "category": "预后系统",
        "risk_level": "低",
        "max_concurrent": 100
    },
    
    "搜索建议": {
        "url": "http://127.0.0.1:8000/api/routertest1/prognosis/search-suggestions",
        "method": "GET",
        "description": "获取搜索建议列表",
        "category": "预后系统",
        "risk_level": "低",
        "max_concurrent": 100
    }
}

@dataclass
class TestResult:
    """测试结果数据类"""
    api_name: str
    success_count: int
    error_count: int
    total_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p95_response_time: float
    qps: float
    error_rate: float
    cache_hit_rate: float = 0.0

class HighConcurrentTester:
    """高并发API测试器"""
    
    def __init__(self):
        self.session = None
        self.results: List[TestResult] = []
        self.token = self._load_token()
        
    def _load_token(self) -> str:
        """加载测试token"""
        try:
            with open('test_token_user_2.txt', 'r') as f:
                return f.read().strip()
        except FileNotFoundError:
            print("⚠️ 未找到test_token_user_2.txt文件")
            return ""
    
    async def create_session(self):
        """创建HTTP会话"""
        connector = aiohttp.TCPConnector(
            limit=200,
            limit_per_host=100,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json",
                "User-Agent": "HighConcurrentTester/1.0"
            }
        )
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, api_config: Dict[str, Any]) -> Dict[str, Any]:
        """发送单个请求"""
        start_time = time.time()
        
        try:
            url = api_config['url']
            method = api_config['method'].upper()
            
            # 准备请求参数
            kwargs = {}
            if method == "POST" and "data" in api_config:
                kwargs["json"] = api_config["data"]
            elif method == "GET" and "params" in api_config:
                kwargs["params"] = api_config["params"]
            
            # 发送请求
            async with self.session.request(method, url, **kwargs) as response:
                response_time = time.time() - start_time
                
                # 读取响应内容
                try:
                    content = await response.text()
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = json.loads(content)
                    else:
                        data = {"content": content[:200] + "..." if len(content) > 200 else content}
                except:
                    data = {"content": "无法解析响应"}
                
                # 检查缓存命中
                cache_hit = response.headers.get('X-Cache-Hit', 'false').lower() == 'true'
                
                return {
                    "success": response.status < 400,
                    "status_code": response.status,
                    "response_time": response_time,
                    "cache_hit": cache_hit,
                    "data": data,
                    "error": None
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "cache_hit": False,
                "data": None,
                "error": str(e)
            }
    
    async def test_api_concurrent(self, api_name: str, concurrent_users: int, requests_per_user: int = 10):
        """测试单个API的并发性能"""
        api_config = AVAILABLE_APIS[api_name]
        total_requests = concurrent_users * requests_per_user
        
        print(f"\n🎯 测试API: {api_name}")
        print(f"📊 并发配置: {concurrent_users}用户 × {requests_per_user}请求 = {total_requests}总请求")
        print(f"📍 URL: {api_config['url']}")
        print(f"🔧 方法: {api_config['method']}")
        print(f"⚠️ 风险等级: {api_config['risk_level']}")
        
        # 创建并发任务
        tasks = []
        for user_id in range(concurrent_users):
            for req_id in range(requests_per_user):
                task = asyncio.create_task(self.make_request(api_config))
                tasks.append(task)
        
        # 执行并发测试
        start_time = time.time()
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 统计结果
        success_count = 0
        error_count = 0
        response_times = []
        cache_hits = 0
        
        for result in results:
            if isinstance(result, Exception):
                error_count += 1
                continue
                
            if result["success"]:
                success_count += 1
            else:
                error_count += 1
                
            response_times.append(result["response_time"])
            
            if result["cache_hit"]:
                cache_hits += 1
        
        # 计算统计数据
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 1 else avg_response_time
        else:
            avg_response_time = min_response_time = max_response_time = p95_response_time = 0
        
        qps = total_requests / total_time if total_time > 0 else 0
        error_rate = (error_count / total_requests) * 100
        cache_hit_rate = (cache_hits / total_requests) * 100
        
        # 创建测试结果
        test_result = TestResult(
            api_name=api_name,
            success_count=success_count,
            error_count=error_count,
            total_requests=total_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p95_response_time=p95_response_time,
            qps=qps,
            error_rate=error_rate,
            cache_hit_rate=cache_hit_rate
        )
        
        self.results.append(test_result)
        
        # 输出测试结果
        print(f"✅ 完成时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏱️ 总耗时: {total_time:.2f}秒")
        print(f"📈 成功率: {success_count}/{total_requests} ({100-error_rate:.1f}%)")
        print(f"🚀 QPS: {qps:.1f}")
        print(f"⚡ 平均响应时间: {avg_response_time*1000:.1f}ms")
        print(f"📊 P95响应时间: {p95_response_time*1000:.1f}ms")
        
        # 性能评估
        if error_rate < 1:
            status = "🟢 优秀"
        elif error_rate < 5:
            status = "🟡 良好"
        else:
            status = "🔴 需要优化"
        print(f"📋 性能评估: {status}")
        
        return test_result

    def print_summary_report(self):
        """打印汇总报告"""
        if not self.results:
            print("❌ 没有测试结果")
            return
        
        print("\n" + "=" * 100)
        print("📊 可用API高并发性能测试汇总报告")
        print("=" * 100)
        
        # 总体统计
        total_requests = sum(r.total_requests for r in self.results)
        total_success = sum(r.success_count for r in self.results)
        overall_success_rate = (total_success / total_requests) * 100 if total_requests > 0 else 0
        avg_qps = statistics.mean([r.qps for r in self.results])
        avg_response_time = statistics.mean([r.avg_response_time for r in self.results])
        
        print(f"🎯 测试API数量: {len(self.results)}")
        print(f"📊 总请求数: {total_requests:,}")
        print(f"✅ 总成功数: {total_success:,}")
        print(f"📈 总体成功率: {overall_success_rate:.1f}%")
        print(f"🚀 平均QPS: {avg_qps:.1f}")
        print(f"⚡ 平均响应时间: {avg_response_time*1000:.1f}ms")
        
        # 按类别统计
        categories = {}
        for result in self.results:
            api_config = AVAILABLE_APIS[result.api_name]
            category = api_config['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(result)
        
        print(f"\n📋 按业务模块统计:")
        for category, results in categories.items():
            cat_success_rate = statistics.mean([100-r.error_rate for r in results])
            cat_avg_qps = statistics.mean([r.qps for r in results])
            print(f"  🔹 {category}: {len(results)}个API, 平均成功率{cat_success_rate:.1f}%, 平均QPS{cat_avg_qps:.1f}")
        
        # 详细结果表格
        print(f"\n📊 详细测试结果:")
        print(f"{'API名称':<20} {'成功率':<8} {'QPS':<8} {'平均响应':<10} {'P95响应':<10} {'状态':<8}")
        print("-" * 80)
        
        for result in self.results:
            success_rate = 100 - result.error_rate
            if success_rate >= 99:
                status = "🟢优秀"
            elif success_rate >= 95:
                status = "🟡良好"
            else:
                status = "🔴待优化"
                
            print(f"{result.api_name:<20} {success_rate:>6.1f}% {result.qps:>6.1f} {result.avg_response_time*1000:>8.1f}ms {result.p95_response_time*1000:>8.1f}ms {status}")

async def main():
    """主测试函数"""
    print("🚀 可用API高并发性能测试启动")
    print("=" * 80)
    
    # 测试配置
    CONCURRENT_LEVELS = [1, 10, 20, 50, 100]  # 并发级别
    REQUESTS_PER_USER = 10  # 每用户请求数
    
    tester = HighConcurrentTester()
    await tester.create_session()
    
    try:
        # 获取要测试的API列表
        api_names = list(AVAILABLE_APIS.keys())
        print(f"📋 待测试API数量: {len(api_names)}")
        
        # 为每个API测试不同并发级别
        for api_name in api_names:
            api_config = AVAILABLE_APIS[api_name]
            max_concurrent = api_config.get('max_concurrent', 100)
            
            print(f"\n{'='*60}")
            print(f"🎯 开始测试: {api_name}")
            print(f"📂 类别: {api_config['category']}")
            print(f"⚠️ 风险等级: {api_config['risk_level']}")
            print(f"🔧 最大并发: {max_concurrent}")
            
            # 选择适合的并发级别
            test_levels = [level for level in CONCURRENT_LEVELS if level <= max_concurrent]
            
            for concurrent in test_levels:
                await tester.test_api_concurrent(api_name, concurrent, REQUESTS_PER_USER)
                
                # 短暂休息，避免过载
                await asyncio.sleep(2)
        
        # 生成汇总报告
        tester.print_summary_report()
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"available_apis_high_concurrent_test_{timestamp}.json"
        
        results_data = {
            "test_time": datetime.now().isoformat(),
            "test_config": {
                "concurrent_levels": CONCURRENT_LEVELS,
                "requests_per_user": REQUESTS_PER_USER,
                "total_apis": len(api_names)
            },
            "results": [
                {
                    "api_name": r.api_name,
                    "success_count": r.success_count,
                    "error_count": r.error_count,
                    "total_requests": r.total_requests,
                    "avg_response_time": r.avg_response_time,
                    "qps": r.qps,
                    "error_rate": r.error_rate,
                    "cache_hit_rate": r.cache_hit_rate
                }
                for r in tester.results
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {filename}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        await tester.close_session()
        print("\n🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
