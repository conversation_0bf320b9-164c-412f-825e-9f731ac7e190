import traceback
import sys
import json
import datetime as dt
from typing import List
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.http import Http404
from ninja import Router
from ninja.errors import HttpError
from django.db.models import F
from django.core.cache import cache
from asgiref.sync import sync_to_async
from pydantic import BaseModel
from api.models import TCMForumPost, TCMForumInteraction, TCMComment
from api.views.schemas import TCMPostResponse
from api.views.modules.forum.post_utils import format_post
from api.views.utils import get_user_by_id
from api.ninja_apis.questionnaire.utils import api_timer

router = Router()

class JSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime等无法直接序列化的对象"""
    def default(self, obj):
        if isinstance(obj, dt.datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        return super().default(obj)

class InteractionResponse(BaseModel):
    is_liked: bool
    is_collected: bool
    message: str

@router.post("/posts/{post_id}/like", response=InteractionResponse)
@api_timer("点赞帖子")
async def like_post(request, post_id: int):
    """点赞帖子 - 异步版本"""
    try:
        print(f'开始处理点赞请求: post_id={post_id}')

        # 使用异步事务处理
        from api.ninja_apis.async_utils import get_async, get_or_create_async, save_async
        from asgiref.sync import sync_to_async

        # 异步获取用户信息
        try:
            print(f'获取用户信息: user_id={request.user_id}')
            user = await get_async(UserInfo, id=request.user_id)
            if not user:
                print(f'用户认证失败: user_id={request.user_id}')
                raise HttpError(401, {"error": "用户未认证"})
            print(f'获取用户成功: user_id={user.id}, nickname={user.nickname}')
        except Exception as e:
            print(f'获取用户信息失败: {str(e)}')
            print(f'详细错误: {traceback.format_exc()}')
            raise

        # 异步验证帖子存在且未删除
        try:
            print(f'验证帖子: post_id={post_id}')
            post = await get_async(
                TCMForumPost,
                id=post_id,
                status='published'
            )
            if not post:
                raise HttpError(404, {"error": "帖子不存在或已删除"})
            print(f'帖子验证成功: post_id={post.id}, title={post.title}')
        except Exception as e:
            print(f'验证帖子失败: {str(e)}')
            print(f'详细错误: {traceback.format_exc()}')
            raise

        # 异步更新或创建互动记录
        try:
            print(f'更新互动记录: post_id={post.id}, user_id={user.id}')

            # 检查是否已存在互动记录
            existing_interaction = await get_async(
                TCMForumInteraction,
                post=post,
                user=user
            )

            if existing_interaction:
                if existing_interaction.is_liked:
                    print('已经点赞过了')
                    return InteractionResponse(
                        is_liked=True,
                        is_collected=existing_interaction.is_collected,
                        message="已经点赞过了"
                    )
                else:
                    # 更新点赞状态
                    existing_interaction.is_liked = True
                    await save_async(existing_interaction)
                    print('更新点赞状态成功')
            else:
                # 创建新的互动记录
                from api.ninja_apis.async_utils import create_async
                await create_async(
                    TCMForumInteraction,
                    post=post,
                    user=user,
                    is_liked=True
                )
                print('创建点赞记录成功')

            # 异步更新帖子点赞数和活跃时间
            def update_post_stats():
                from django.db.models import F
                post.like_count = F('like_count') + 1
                post.last_active = dt.datetime.now()
                post.save()
                return post

            # 在线程池中执行更新
            from api.ninja_apis.async_utils import db
            await db._run_in_thread(update_post_stats)

            # 清除相关缓存
            cache_key = f"forum_post:{post_id}:{request.user_id}"
            cache.delete(cache_key)
            cache_key_liked = f"forum_user_liked_posts:{request.user_id}"
            cache.delete(cache_key_liked)
            print(f"[CACHE_DELETE] 🗑️ - 点赞后清除相关缓存")

        except Exception as e:
            print(f'更新互动记录失败: {str(e)}')
            print(f'详细错误: {traceback.format_exc()}')
            raise

        # 获取最终的互动状态
        final_interaction = await get_async(
            TCMForumInteraction,
            post=post,
            user=user
        )

        print('点赞操作完成')
        return InteractionResponse(
            is_liked=True,
            is_collected=final_interaction.is_collected if final_interaction else False,
            message="点赞成功"
        )

    except HttpError as e:
        print(f'HTTP错误: {str(e)}')
        raise e
    except Exception as e:
        print(f'点赞失败(未预期的错误): {str(e)}')
        print(f'详细错误信息: {traceback.format_exc()}')
        raise HttpError(500, {"error": "点赞失败", "detail": str(e)})

@router.delete("/posts/{post_id}/like", response=InteractionResponse)
@api_timer("取消点赞帖子")
def unlike_post(request, post_id: int):
    """取消点赞帖子"""
    try:
        print(f'开始处理取消点赞请求: post_id={post_id}')
        
        with transaction.atomic():
            # 获取用户信息
            try:
                print(f'获取用户信息: user_id={request.user_id}')
                user = get_user_by_id(request.user_id)
                if not user:
                    print(f'用户认证失败: user_id={request.user_id}')
                    raise HttpError(401, {"error": "用户未认证"})
                print(f'获取用户成功: user_id={user.id}, nickname={user.nickname}')
            except Exception as e:
                print(f'获取用户信息失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise

            # 验证帖子存在
            try:
                print(f'验证帖子: post_id={post_id}')
                post = get_object_or_404(TCMForumPost, id=post_id)
                print(f'帖子验证成功: post_id={post.id}, title={post.title}')
            except Exception as e:
                print(f'验证帖子失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise
            
            # 更新互动记录
            try:
                print(f'查找互动记录: post_id={post.id}, user_id={user.id}')
                interaction = TCMForumInteraction.objects.filter(
                    post=post,
                    user=user,
                    is_liked=True
                ).first()
                
                if not interaction:
                    print('未找到点赞记录')
                    return InteractionResponse(
                        is_liked=False,
                        is_collected=False,
                        message="未找到点赞记录"
                    )
                
                print('更新互动记录')
                interaction.is_liked = False
                interaction.save()
                
                # 减少帖子点赞数
                post.like_count = F('like_count') - 1
                post.save()
                print('取消点赞成功')
                
                # 清除相关缓存
                cache_key = f"forum_post:{post_id}:{request.user_id}"
                cache.delete(cache_key)
                cache_key_liked = f"forum_user_liked_posts:{request.user_id}"
                cache.delete(cache_key_liked)
                print(f"[CACHE_DELETE] 🗑️ - 取消点赞后清除相关缓存")
                
            except Exception as e:
                print(f'更新互动记录失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise
            
            print('取消点赞操作完成')
            return InteractionResponse(
                is_liked=False,
                is_collected=interaction.is_collected,
                message="取消点赞成功"
            )

    except HttpError as e:
        print(f'HTTP错误: {str(e)}')
        raise e
    except Exception as e:
        print(f'取消点赞失败(未预期的错误): {str(e)}')
        print(f'详细错误信息: {traceback.format_exc()}')
        raise HttpError(500, {"error": "取消点赞失败", "detail": str(e)})

@router.post("/posts/{post_id}/collect", response=InteractionResponse)
@api_timer("收藏帖子")
def collect_post(request, post_id: int):
    """收藏帖子"""
    try:
        print(f'开始处理收藏请求: post_id={post_id}')
        
        with transaction.atomic():
            # 获取用户信息
            try:
                print(f'获取用户信息: user_id={request.user_id}')
                user = get_user_by_id(request.user_id)
                if not user:
                    print(f'用户认证失败: user_id={request.user_id}')
                    raise HttpError(401, {"error": "用户未认证"})
                print(f'获取用户成功: user_id={user.id}, nickname={user.nickname}')
            except Exception as e:
                print(f'获取用户信息失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise

            # 验证帖子存在且未删除
            try:
                print(f'验证帖子: post_id={post_id}')
                post = get_object_or_404(
                    TCMForumPost, 
                    id=post_id,
                    status='published'
                )
                print(f'帖子验证成功: post_id={post.id}, title={post.title}')
            except Exception as e:
                print(f'验证帖子失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise
            
            # 更新或创建互动记录
            try:
                print(f'更新互动记录: post_id={post.id}, user_id={user.id}')
                interaction, created = TCMForumInteraction.objects.get_or_create(
                    post=post,
                    user=user,
                    defaults={'is_collected': True}
                )
                
                should_update_count = False
                
                if not created and not interaction.is_collected:
                    interaction.is_collected = True
                    interaction.save()
                    should_update_count = True
                    print('更新收藏状态成功')
                elif created:
                    should_update_count = True
                    print('创建收藏记录成功')
                else:
                    print('已经收藏过了')
                    return InteractionResponse(
                        is_liked=interaction.is_liked,
                        is_collected=True,
                        message="已经收藏过了"
                    )
                
                # 更新帖子收藏计数
                if should_update_count:
                    post.collect_count = F('collect_count') + 1
                    post.last_active = dt.datetime.now()
                    post.save()
                    
                # 清除相关缓存
                cache_key = f"forum_post:{post_id}:{request.user_id}"
                cache.delete(cache_key)
                cache_key_collected = f"forum_user_collected_posts:{request.user_id}"
                cache.delete(cache_key_collected)
                print(f"[CACHE_DELETE] 🗑️ - 收藏后清除相关缓存")
                
            except Exception as e:
                print(f'更新互动记录失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise
            
            # 重要：在返回前刷新post对象，确保前端能通过loadPostDetail获取到最新计数
            post.refresh_from_db()
            print('收藏操作完成')
            return InteractionResponse(
                is_liked=interaction.is_liked,
                is_collected=True,
                message="收藏成功"
            )

    except HttpError as e:
        print(f'HTTP错误: {str(e)}')
        raise e
    except Exception as e:
        print(f'收藏失败(未预期的错误): {str(e)}')
        print(f'详细错误信息: {traceback.format_exc()}')
        raise HttpError(500, {"error": "收藏失败", "detail": str(e)})

@router.delete("/posts/{post_id}/collect", response=InteractionResponse)
@api_timer("取消收藏帖子")
def uncollect_post(request, post_id: int):
    """取消收藏帖子"""
    try:
        print(f'开始处理取消收藏请求: post_id={post_id}')
        
        with transaction.atomic():
            # 获取用户信息
            try:
                print(f'获取用户信息: user_id={request.user_id}')
                user = get_user_by_id(request.user_id)
                if not user:
                    print(f'用户认证失败: user_id={request.user_id}')
                    raise HttpError(401, {"error": "用户未认证"})
                print(f'获取用户成功: user_id={user.id}, nickname={user.nickname}')
            except Exception as e:
                print(f'获取用户信息失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise

            # 验证帖子存在
            try:
                print(f'验证帖子: post_id={post_id}')
                post = get_object_or_404(TCMForumPost, id=post_id)
                print(f'帖子验证成功: post_id={post.id}, title={post.title}')
            except Exception as e:
                print(f'验证帖子失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise
            
            # 更新互动记录
            try:
                print(f'查找互动记录: post_id={post.id}, user_id={user.id}')
                interaction = TCMForumInteraction.objects.filter(
                    post=post,
                    user=user,
                    is_collected=True
                ).first()
                
                if not interaction:
                    print('未找到收藏记录')
                    return InteractionResponse(
                        is_liked=False,
                        is_collected=False,
                        message="未找到收藏记录"
                    )
                
                print('更新互动记录')
                interaction.is_collected = False
                interaction.save()
                
                # 更新帖子收藏计数
                post.collect_count = F('collect_count') - 1
                post.save()
                print(f'更新帖子收藏数成功')
                
                # 清除相关缓存
                cache_key = f"forum_post:{post_id}:{request.user_id}"
                cache.delete(cache_key)
                cache_key_collected = f"forum_user_collected_posts:{request.user_id}"
                cache.delete(cache_key_collected)
                print(f"[CACHE_DELETE] 🗑️ - 取消收藏后清除相关缓存")
                
            except Exception as e:
                print(f'更新互动记录失败: {str(e)}')
                print(f'详细错误: {traceback.format_exc()}')
                raise
            
            print('取消收藏操作完成')
            return InteractionResponse(
                is_liked=interaction.is_liked,
                is_collected=False,
                message="取消收藏成功"
            )

    except HttpError as e:
        print(f'HTTP错误: {str(e)}')
        raise e
    except Exception as e:
        print(f'取消收藏失败(未预期的错误): {str(e)}')
        print(f'详细错误信息: {traceback.format_exc()}')
        raise HttpError(500, {"error": "取消收藏失败", "detail": str(e)})


@router.get("/user/collected-posts", response=List[TCMPostResponse])
@api_timer("获取用户收藏帖子")
async def get_user_collected_posts(request, page: int = 1, size: int = 20):
    """
    获取用户收藏帖子列表 - 支持分页
    
    参数说明:
    - page: 页码，从1开始 (默认: 1)
    - size: 每页数量 (默认: 20, 最大: 50)
    """
    try:
        print(f"[DEBUG] 获取用户收藏帖子 - user_id: {request.user_id}, page: {page}, size: {size}")
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 50:
            size = 20
            
        # 计算偏移量
        offset = (page - 1) * size
        
        # 检查缓存中是否有数据
        cache_key = f"forum_user_collected_posts:{request.user_id}:p{page}:s{size}"
        cached_posts = await sync_to_async(cache.get)(cache_key)
        
        if cached_posts:
            print(f"[CACHE_HIT] ✅ {cache_key} - 返回用户收藏帖子缓存数据")
            return json.loads(cached_posts)
        
        # 缓存未命中，从数据库获取 - 支持分页
        # 通过互动记录表找到用户收藏的所有帖子
        interactions_query = TCMForumInteraction.objects.filter(
            user_id=request.user_id,
            is_collected=True
        ).select_related('post').order_by('-id')[offset:offset+size]
        
        interactions = await sync_to_async(list)(interactions_query)
        
        posts = [interaction.post for interaction in interactions]
        
        # 格式化帖子
        formatted_posts = []
        for post in posts:
            if hasattr(format_post, '__awaitable__'):
                formatted_post = await format_post(post, request.user_id)
            else:
                formatted_post = await sync_to_async(format_post)(post, request.user_id)
                
            # 处理datetime序列化
            if isinstance(formatted_post, dict):
                if 'created_at' in formatted_post and formatted_post['created_at']:
                    if isinstance(formatted_post['created_at'], dt.datetime):
                        formatted_post['created_at'] = formatted_post['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
                if 'updated_at' in formatted_post and formatted_post['updated_at']:
                    if isinstance(formatted_post['updated_at'], dt.datetime):
                        formatted_post['updated_at'] = formatted_post['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
            formatted_posts.append(formatted_post)
        
        # 设置缓存，缓存3分钟，使用自定义JSON编码器处理datetime
        await sync_to_async(cache.set)(
            cache_key, 
            json.dumps(formatted_posts, cls=JSONEncoder), 
            timeout=180  # 分页数据缓存时间较短
        )
        print(f"[CACHE_SET] 💾 {cache_key} - 用户收藏帖子第{page}页数据已缓存")
        
        print(f"[DEBUG] 用户收藏帖子第{page}页查询完成，返回 {len(formatted_posts)} 个帖子")
        return formatted_posts
        
    except Exception as e:
        print('获取用户收藏帖子失败:', str(e))
        print('详细错误信息:', traceback.format_exc())
        raise HttpError(500, {"error": "获取收藏帖子失败", "detail": str(e)})

@router.get("/user/liked-posts", response=List[TCMPostResponse])
@api_timer("获取用户点赞帖子")
async def get_user_liked_posts(request, page: int = 1, size: int = 20):
    """
    获取用户点赞帖子列表 - 支持分页
    
    参数说明:
    - page: 页码，从1开始 (默认: 1)
    - size: 每页数量 (默认: 20, 最大: 50)
    """
    try:
        print(f"[DEBUG] 获取用户点赞帖子 - user_id: {request.user_id}, page: {page}, size: {size}")
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 50:
            size = 20
            
        # 计算偏移量
        offset = (page - 1) * size
        
        # 检查缓存中是否有数据
        cache_key = f"forum_user_liked_posts:{request.user_id}:p{page}:s{size}"
        cached_posts = await sync_to_async(cache.get)(cache_key)
        
        if cached_posts:
            print(f"[CACHE_HIT] ✅ {cache_key} - 返回用户点赞帖子缓存数据")
            return json.loads(cached_posts)
        
        # 缓存未命中，从数据库获取 - 支持分页
        # 通过互动记录表找到用户点赞的所有帖子
        interactions_query = TCMForumInteraction.objects.filter(
            user_id=request.user_id,
            is_liked=True
        ).select_related('post').order_by('-id')[offset:offset+size]
        
        interactions = await sync_to_async(list)(interactions_query)
        
        posts = [interaction.post for interaction in interactions]
        
        # 格式化帖子
        formatted_posts = []
        for post in posts:
            if hasattr(format_post, '__awaitable__'):
                formatted_post = await format_post(post, request.user_id)
            else:
                formatted_post = await sync_to_async(format_post)(post, request.user_id)
                
            # 处理datetime序列化
            if isinstance(formatted_post, dict):
                if 'created_at' in formatted_post and formatted_post['created_at']:
                    if isinstance(formatted_post['created_at'], dt.datetime):
                        formatted_post['created_at'] = formatted_post['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
                if 'updated_at' in formatted_post and formatted_post['updated_at']:
                    if isinstance(formatted_post['updated_at'], dt.datetime):
                        formatted_post['updated_at'] = formatted_post['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                        
            formatted_posts.append(formatted_post)
        
        # 设置缓存，缓存3分钟，使用自定义JSON编码器处理datetime
        await sync_to_async(cache.set)(
            cache_key, 
            json.dumps(formatted_posts, cls=JSONEncoder), 
            timeout=180  # 分页数据缓存时间较短
        )
        print(f"[CACHE_SET] 💾 {cache_key} - 用户点赞帖子第{page}页数据已缓存")
        
        print(f"[DEBUG] 用户点赞帖子第{page}页查询完成，返回 {len(formatted_posts)} 个帖子")
        return formatted_posts
        
    except Exception as e:
        print('获取用户点赞帖子失败:', str(e))
        print('详细错误信息:', traceback.format_exc())
        raise HttpError(500, {"error": "获取点赞帖子失败", "detail": str(e)})


@router.post("/comments/{comment_id}/like", response=InteractionResponse)
@api_timer("点赞评论")
def like_comment(request, comment_id: int):
    """点赞评论"""
    try:
        with transaction.atomic():
            user = get_user_by_id(request.user_id)
            if not user:
                raise HttpError(401, "用户未认证")
            
            try:
                comment = get_object_or_404(
                    TCMComment,
                    id=comment_id,
                    is_deleted=False
                )
            except Http404:
                raise HttpError(404, "评论不存在或已删除")
                
            interaction = TCMForumInteraction.objects.filter(
                post=comment.post,
                user=user,
                comment=comment
            ).first()
            
            if interaction and interaction.is_liked:
                return InteractionResponse(
                    is_liked=True, 
                    is_collected=interaction.is_collected,
                    message="已经点赞过了"  
                )
            
            if not interaction:
                interaction = TCMForumInteraction.objects.create(
                    post=comment.post,
                    user=user,
                    comment=comment,
                    is_liked=True
                )
            else:
                interaction.is_liked = True
                interaction.save()
            
            comment.like_count = F('like_count') + 1
            comment.save()
            
            # 清除相关缓存
            cache_key = f"forum_comments:{comment.post_id}"
            cache.delete(cache_key)
            print(f"[CACHE_DELETE] 🗑️ {cache_key} - 点赞评论后清除评论列表缓存")
            
            return InteractionResponse(
                is_liked=True,
                is_collected=interaction.is_collected,
                message="点赞成功"
            )

    except HttpError as e:
        raise e
    except Exception as e:
        print(f"点赞评论失败: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        raise HttpError(500, "点赞失败")

@router.delete("/comments/{comment_id}/like", response=InteractionResponse)
@api_timer("取消点赞评论")
def unlike_comment(request, comment_id: int):
    """取消点赞评论"""
    try:
        with transaction.atomic():
            user = get_user_by_id(request.user_id)
            if not user:
                raise HttpError(401, "用户未认证")

            try:
                comment = get_object_or_404(
                    TCMComment,
                    id=comment_id,
                    is_deleted=False
                )
            except Http404:
                raise HttpError(404, "评论不存在或已删除")

            try:
                interaction = TCMForumInteraction.objects.get(
                    post=comment.post,
                    user=user,
                    comment=comment,
                    is_liked=True
                )
            except TCMForumInteraction.DoesNotExist:
                return InteractionResponse(
                    is_liked=False,
                    is_collected=False,
                    message="尚未点赞"
                )

            interaction.is_liked = False
            interaction.save()
            
            comment.like_count = F('like_count') - 1
            comment.save()
            
            # 清除相关缓存
            cache_key = f"forum_comments:{comment.post_id}"
            cache.delete(cache_key)
            print(f"[CACHE_DELETE] 🗑️ {cache_key} - 取消点赞评论后清除评论列表缓存")

            return InteractionResponse(
                is_liked=False,
                is_collected=interaction.is_collected,
                message="取消点赞成功"
            )

    except HttpError as e:
        raise e
    except Exception as e:
        print(f"取消点赞评论失败: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        raise HttpError(500, "取消点赞失败") 