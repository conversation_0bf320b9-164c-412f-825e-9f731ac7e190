# API性能标准与基准制定文档

## 📊 基准数据来源
基于三批性能测试结果制定：
- **第一批**: 11个核心业务API（高并发测试）
- **第二批**: 11个扩展功能API（标准并发测试）  
- **第三批**: 6个谨慎API（低并发测试）
- **测试时间**: 2025年8月3日
- **测试环境**: Django + MySQL + Redis + 连接池优化

## 🎯 API分类与性能标准

### 📈 按业务重要性分类

#### 🥇 核心业务API（第一批）
**适用范围**: 首页、认证、核心问卷、预后系统
**并发级别**: 高并发（150用户）

| 性能等级 | QPS范围 | 响应时间 | 成功率 | 代表API |
|----------|---------|----------|--------|---------|
| 🟢 优秀 | > 300 | < 300ms | 100% | HomePage (351.3) |
| 🟡 良好 | 100-300 | 300-1000ms | 100% | 预后系统APIs |
| 🟠 需优化 | 50-100 | 1000-2000ms | 100% | 问卷计算APIs |
| 🔴 不合格 | < 50 | > 2000ms | < 100% | 需要修复 |

#### 🥈 扩展功能API（第二批）
**适用范围**: 论坛、医案、症状管理、问答系统
**并发级别**: 标准并发（150用户）

| 性能等级 | QPS范围 | 响应时间 | 成功率 | 代表API |
|----------|---------|----------|--------|---------|
| 🟢 优秀 | > 110 | < 900ms | 100% | 自定义症状 (122.1) |
| 🟡 良好 | 80-110 | 900-1200ms | 100% | 医案系统APIs |
| 🟠 需优化 | 50-80 | 1200-1500ms | 100% | 所有症状 (72.2) |
| 🔴 不合格 | < 50 | > 1500ms | < 100% | 需要修复 |

#### 🥉 谨慎API（第三批）
**适用范围**: 写入操作、认证系统、计算密集型
**并发级别**: 低并发（10用户）

| 性能等级 | QPS范围 | 响应时间 | 成功率 | 代表API |
|----------|---------|----------|--------|---------|
| 🟢 优秀 | > 80 | < 100ms | 100% | 空请求 (122.9) |
| 🟡 良好 | 50-80 | 100-200ms | 100% | 问卷系统APIs |
| 🟠 需优化 | 30-50 | 200-500ms | 100% | 写入操作APIs |
| 🔴 不合格 | < 30 | > 500ms | < 100% | Token刷新 |

### 🔧 按API类型分类

#### 🚀 缓存型API
**特征**: 静态内容，长期缓存，高频访问
**标准**: 
- **优秀**: QPS > 300, 响应时间 < 300ms, 缓存命中率 > 90%
- **良好**: QPS > 200, 响应时间 < 500ms, 缓存命中率 > 70%
- **基准**: HomePage (QPS 351.3, 224ms, 缓存100%)

#### 📊 标准业务API
**特征**: 常规查询，适度缓存，中频访问
**标准**:
- **优秀**: QPS > 150, 响应时间 < 600ms, 成功率 100%
- **良好**: QPS > 100, 响应时间 < 1000ms, 成功率 100%
- **基准**: 预后系统APIs (QPS 104-125, 600-900ms)

#### 🧮 计算密集型API
**特征**: 复杂计算，无缓存，低频访问
**高并发标准**:
- **优秀**: QPS > 80, 响应时间 < 1500ms, 成功率 100%
- **良好**: QPS > 50, 响应时间 < 2000ms, 成功率 100%
**低并发标准**:
- **优秀**: QPS > 70, 响应时间 < 100ms, 成功率 100%
- **良好**: QPS > 50, 响应时间 < 200ms, 成功率 100%

#### ✍️ 写入操作API
**特征**: 数据写入，事务处理，安全敏感
**标准**:
- **优秀**: QPS > 50, 响应时间 < 200ms, 成功率 100%
- **良好**: QPS > 30, 响应时间 < 500ms, 成功率 100%
- **基准**: 提交答题得分 (低并发: QPS 70.5, 133ms)

#### 🔐 认证系统API
**特征**: 安全关键，会话管理，高可用要求
**标准**:
- **优秀**: QPS > 200, 响应时间 < 100ms, 成功率 100%
- **良好**: QPS > 100, 响应时间 < 200ms, 成功率 100%
- **问题**: Token刷新 (成功率 0% - 需紧急修复)

## ⚠️ 性能预警阈值

### 🟡 黄色警告阈值
**触发条件** (满足任一):
- QPS 下降超过基准值的30%
- 响应时间超过基准值的150%
- 成功率低于95%
- 缓存命中率低于预期的50%

**示例**:
- HomePage: QPS < 245, 响应时间 > 336ms
- 预后系统: QPS < 70, 响应时间 > 1350ms
- 写入操作: QPS < 35, 响应时间 > 300ms

### 🔴 红色警告阈值
**触发条件** (满足任一):
- QPS 下降超过基准值的50%
- 响应时间超过基准值的200%
- 成功率低于90%
- 连续5分钟超过黄色阈值

**示例**:
- HomePage: QPS < 175, 响应时间 > 448ms
- 预后系统: QPS < 50, 响应时间 > 1800ms
- 任何API: 成功率 < 90%

## 📊 性能监控指标

### 核心指标
1. **QPS (每秒查询数)**: 衡量吞吐量
2. **响应时间**: 衡量用户体验
3. **成功率**: 衡量系统稳定性
4. **缓存命中率**: 衡量缓存效果

### 扩展指标
1. **并发用户数**: 当前活跃用户
2. **错误率分布**: 不同错误类型统计
3. **数据库连接池**: 连接使用情况
4. **内存使用率**: 系统资源监控

## 🎯 性能优化建议

### 按性能等级优化策略

#### 🟢 优秀级API
- **维护策略**: 保持现有性能，定期监控
- **优化方向**: 进一步提升缓存效率
- **监控频率**: 每日检查

#### 🟡 良好级API  
- **维护策略**: 稳定现有性能，适度优化
- **优化方向**: 数据库查询优化，增加缓存
- **监控频率**: 每日检查

#### 🟠 需优化级API
- **优化策略**: 重点优化对象
- **优化方向**: 算法优化，异步处理，缓存策略
- **监控频率**: 实时监控

#### 🔴 不合格级API
- **紧急处理**: 立即修复，暂停使用
- **优化方向**: 全面重构，根本性修复
- **监控频率**: 持续监控

## 📋 性能测试建议

### 定期测试计划
1. **每日**: 核心API健康检查
2. **每周**: 扩展功能API性能测试
3. **每月**: 全面性能回归测试
4. **季度**: 压力测试和容量规划

### 测试场景配置
1. **高并发测试**: 核心业务API (150并发)
2. **标准测试**: 扩展功能API (80并发)
3. **低并发测试**: 写入操作API (10并发)
4. **压力测试**: 极限并发测试 (300+并发)

## 🎉 性能基准总结

### 🏆 最佳实践API
1. **HomePage**: 缓存型API的标杆 (QPS 351.3)
2. **医案系统**: 标准业务API的典型 (QPS 104-119)
3. **空请求**: 轻量级API的代表 (QPS 122.9)

### ⚠️ 需要关注的API
1. **Token刷新**: 成功率0%，紧急修复
2. **计算密集型**: 高并发下性能下降明显
3. **写入操作**: 需要合理的并发控制

### 📈 性能改善证据
1. **连接池优化**: 第二批测试性能显著提升
2. **低并发优势**: 计算密集型API在低并发下性能提升96%
3. **缓存效果**: HomePage缓存命中率100%，性能优异

这套性能标准将作为系统监控、性能优化和容量规划的重要依据。
