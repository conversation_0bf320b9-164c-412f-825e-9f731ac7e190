#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三批谨慎API低并发测试脚本
专门针对写入操作和敏感API进行低并发测试
避免对生产环境造成影响
"""

import requests
import time
import statistics
import threading
import os
import json
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# 导入API配置
from performance_test_api_config import (
    PERFORMANCE_TEST_APIS,
    BASE_URL,
    AUTH_HEADERS
)

# 第三批测试配置 - 低并发安全测试
LOW_CONCURRENT_USERS = [1, 3, 5, 10]  # 低并发用户数
TEST_REQUESTS = 50  # 减少测试请求数
RESULTS_DIR = "performance_test_results"

# 第三批测试API - 需要谨慎测试的API
THIRD_BATCH_APIS = [
    "Token刷新",           # 认证系统 - POST
    "空请求",             # 银行系统 - POST  
    "检查问卷填写",        # 问卷系统 - POST
    "计算证型指标",        # 问卷系统 - POST（计算密集）
    "分析问卷结果",        # 问卷系统 - POST（计算密集）
    "提交答题得分",        # 学习系统 - POST（中风险）
]

def test_api_performance(api_name, api_config, concurrent_users, total_requests):
    """测试单个API的性能"""
    print(f"\n🚀 开始测试 {api_name} - {concurrent_users} 并发用户，总请求数 {total_requests}")
    print(f"   📝 {api_config['description']}")
    
    url = api_config['url']
    method = api_config.get('method', 'GET')
    headers = api_config.get('headers', {})
    data = api_config.get('data', {})
    
    results = []
    success_count = 0
    cache_hits = 0
    
    def make_request():
        nonlocal success_count, cache_hits
        start_time = time.time()
        try:
            if method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=30)
            else:
                response = requests.get(url, headers=headers, timeout=30)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                success_count += 1
                # 检查缓存命中
                if 'X-Cache' in response.headers and response.headers['X-Cache'] == 'HIT':
                    cache_hits += 1
            
            results.append(response_time)
            return response_time
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            results.append(response_time)
            print(f"   ❌ 请求失败: {str(e)}")
            return response_time
    
    # 执行并发测试
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
        futures = [executor.submit(make_request) for _ in range(total_requests)]
        for future in as_completed(futures):
            future.result()
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 计算统计数据
    if results:
        avg_response_time = statistics.mean(results)
        median_response_time = statistics.median(results)
        min_response_time = min(results)
        max_response_time = max(results)
        qps = total_requests / total_duration if total_duration > 0 else 0
        success_rate = (success_count / total_requests) * 100
        cache_hit_rate = (cache_hits / success_count) * 100 if success_count > 0 else 0
    else:
        avg_response_time = median_response_time = min_response_time = max_response_time = 0
        qps = success_rate = cache_hit_rate = 0
    
    # 输出结果
    print(f"\n📊 {api_name} 测试结果 ({concurrent_users} 并发用户):")
    print(f"   ✅ 总请求数: {total_requests}")
    print(f"   ✅ 成功率: {success_rate:.1f}%")
    print(f"   ⚡ 缓存命中率: {cache_hit_rate:.1f}%")
    print(f"   ⏱️  平均响应时间: {avg_response_time*1000:.1f}ms")
    print(f"   ⏱️  中位数响应时间: {median_response_time*1000:.1f}ms")
    print(f"   ⏱️  最大响应时间: {max_response_time*1000:.1f}ms")
    print(f"   🔥 QPS: {qps:.1f}")
    print(f"   ⏰ 总耗时: {total_duration:.2f}秒")
    
    return {
        'concurrent_users': concurrent_users,
        'total_requests': total_requests,
        'success_count': success_count,
        'success_rate': success_rate,
        'cache_hit_rate': cache_hit_rate,
        'avg_response_time': avg_response_time,
        'median_response_time': median_response_time,
        'min_response_time': min_response_time,
        'max_response_time': max_response_time,
        'qps': qps,
        'total_duration': total_duration
    }

def run_third_batch_test():
    """运行第三批低并发测试"""
    print("🎯 第三批谨慎API低并发测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 测试地址: {BASE_URL}")
    print(f"👥 并发用户数: {LOW_CONCURRENT_USERS}")
    print(f"📊 每轮测试请求数: {TEST_REQUESTS}")
    print(f"🎯 测试API数量: {len(THIRD_BATCH_APIS)}")
    
    all_results = {}
    
    for i, api_name in enumerate(THIRD_BATCH_APIS, 1):
        if api_name not in PERFORMANCE_TEST_APIS:
            print(f"⚠️ API {api_name} 未在配置中找到，跳过测试")
            continue
            
        api_config = PERFORMANCE_TEST_APIS[api_name]
        print(f"\n" + "=" * 80)
        print(f"🎯 测试进度: {i}/{len(THIRD_BATCH_APIS)} - {api_name}")
        print(f"📂 类别: {api_config.get('category', '未分类')}")
        print(f"📍 URL: {api_config['url']}")
        print(f"📝 描述: {api_config['description']}")
        
        api_results = {}
        
        for concurrent_users in LOW_CONCURRENT_USERS:
            result = test_api_performance(api_name, api_config, concurrent_users, TEST_REQUESTS)
            api_results[str(concurrent_users)] = result
            
            # 安全间隔，避免对服务器造成压力
            time.sleep(2)
        
        all_results[api_name] = api_results
    
    # 保存测试结果
    save_test_results(all_results)
    
    # 输出汇总报告
    print_summary_report(all_results)

def save_test_results(all_results):
    """保存测试结果"""
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存详细结果
    detailed_file = f"{RESULTS_DIR}/third_batch_detailed_{timestamp}.json"
    with open(detailed_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 第三批测试结果已保存:")
    print(f"   详细结果: {detailed_file}")

def print_summary_report(all_results):
    """输出汇总报告"""
    print(f"\n🏆 第三批测试汇总报告:")
    print("=" * 80)
    print("API名称                    | 最大QPS | 平均响应时间 | 成功率")
    print("-" * 80)
    
    for api_name, results in all_results.items():
        # 找到最高QPS
        max_qps = 0
        best_response_time = 0
        best_success_rate = 0
        
        for concurrent, result in results.items():
            if result['qps'] > max_qps:
                max_qps = result['qps']
                best_response_time = result['avg_response_time'] * 1000
                best_success_rate = result['success_rate']
        
        print(f"{api_name:<25} | {max_qps:7.1f} | {best_response_time:8.1f}ms | {best_success_rate:6.1f}%")

if __name__ == "__main__":
    run_third_batch_test()
