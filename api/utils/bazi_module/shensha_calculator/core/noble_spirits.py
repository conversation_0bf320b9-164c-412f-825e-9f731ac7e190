"""
贵人类神煞计算

包含天乙贵人、太极贵人、文昌贵人等贵人类神煞的计算函数。
"""

from typing import List
from ..utils.helpers import get_dizhi_wuxing, has_tiangan_in_list, has_tiangan_combination


def tianyiguiren(tiangan: str, dizhi: str) -> int:
    """
    天乙贵人
    甲戊并牛羊, 乙己鼠猴乡, 丙丁猪鸡位, 壬癸兔蛇藏, 庚辛逢虎马, 此是贵人方.
    查法: 以日干年干起贵人, 地支见者为是
    
    Args:
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是天乙贵人，0表示不是
    """
    conditions = {
        "甲": ["丑", "未"],
        "戊": ["丑", "未"],
        "乙": ["申", "子"],
        "己": ["申", "子"],
        "丙": ["亥", "酉"],
        "丁": ["亥", "酉"],
        "壬": ["卯", "巳"],
        "癸": ["卯", "巳"],
        "庚": ["午", "寅"],
        "辛": ["午", "寅"]
    }
    
    return 1 if conditions.get(tiangan, []) and dizhi in conditions[tiangan] else 0


def taijiguiren(tiangan: str, dizhi: str) -> int:
    """
    太极贵人
    甲乙生人子午中,丙丁鸡兔定亨通,
    戊己两干临四季,庚辛寅亥禄丰隆,
    壬癸巳申偏喜美,值此应当福气钟,
    更须贵格来相扶,候封万户到三公.
    查法：以日/年干查四地支
    
    Args:
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是太极贵人，0表示不是
    """
    conditions = {
        "甲": ["子", "午"],
        "乙": ["子", "午"],
        "丙": ["酉", "卯"],
        "丁": ["酉", "卯"],
        "庚": ["寅", "亥"],
        "辛": ["寅", "亥"],
        "壬": ["申", "巳"],
        "癸": ["申", "巳"]
    }
    
    # 戊己两干临四季（土支）
    if tiangan in ["戊", "己"] and get_dizhi_wuxing(dizhi) == "土":
        return 1
    
    return 1 if conditions.get(tiangan, []) and dizhi in conditions[tiangan] else 0


def wenchang(tiangan: str, dizhi: str) -> int:
    """
    文昌贵人
    甲乙巳午报君知, 丙戊申宫丁己鸡.
    庚猪辛鼠壬逢虎, 癸人见卯入云梯.
    查法: 以年干或日干为主, 凡四柱中地支所见者为是
    
    Args:
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是文昌贵人，0表示不是
    """
    conditions = {
        "甲": "巳",
        "乙": "午",
        "丙": "申",
        "丁": "酉",
        "戊": "申",
        "己": "酉",
        "庚": "亥",
        "辛": "子",
        "壬": "寅",
        "癸": "卯"
    }
    
    return 1 if conditions.get(tiangan) == dizhi else 0


def guoying(tiangan: str, dizhi: str) -> int:
    """
    国印贵人
    甲见戌, 乙见亥, 丙见丑, 丁见寅, 戊见丑,
    己见寅, 庚见辰, 辛见巳. 壬见未, 癸见申
    查法：以年、日干查四支
    
    Args:
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是国印贵人，0表示不是
    """
    conditions = {
        "甲": "戌",
        "乙": "亥",
        "丙": "丑",
        "丁": "寅",
        "戊": "丑",
        "己": "寅",
        "庚": "辰",
        "辛": "巳",
        "壬": "未",
        "癸": "申"
    }
    
    return 1 if conditions.get(tiangan) == dizhi else 0


def jingyu(tiangan: str, dizhi: str) -> int:
    """
    金舆
    甲龙乙蛇丙戊羊, 丁己猴歌庚犬方,
    辛猪壬牛癸逢虎, 凡人遇此福气昌.
    查法：以日/年干查四地支
    
    Args:
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是金舆，0表示不是
    """
    conditions = {
        "甲": "辰",
        "乙": "巳",
        "丁": "申",
        "己": "申",
        "丙": "未",
        "戊": "未",
        "庚": "戌",
        "辛": "亥",
        "壬": "丑",
        "癸": "寅"
    }
    
    return 1 if conditions.get(tiangan) == dizhi else 0


def fuxing(tiangan: str, dizhi: str) -> int:
    """
    福星
    凡甲、丙两干见寅或子，乙、癸两干见卯或丑，戊干见申，己干见未，
    丁干见亥，庚干见午，辛干见巳，壬干见辰是也。
    查法: 以年/日干查地支
    
    Args:
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是福星，0表示不是
    """
    conditions = [
        {"gan": ["甲", "丙"], "zhi": ["寅", "子"]},
        {"gan": ["乙", "癸"], "zhi": ["卯", "丑"]},
        {"gan": ["戊"], "zhi": ["申"]},
        {"gan": ["己"], "zhi": ["未"]},
        {"gan": ["丁"], "zhi": ["亥"]},
        {"gan": ["庚"], "zhi": ["午"]},
        {"gan": ["辛"], "zhi": ["巳"]},
        {"gan": ["壬"], "zhi": ["辰"]}
    ]
    
    for condition in conditions:
        if tiangan in condition["gan"] and dizhi in condition["zhi"]:
            return 1
    
    return 0


def tianchu(year_gan: str, day_gan: str, dizhi: str) -> int:
    """
    天厨贵人
    查法：以年干、日干查余四支。
    丙干见巳，丁干见午
    戊干见申，己干见酉
    庚干见亥，辛干见子
    壬干见寅，癸干见卯
    
    Args:
        year_gan: 年干
        day_gan: 日干
        dizhi: 地支
        
    Returns:
        1表示是天厨贵人，0表示不是
    """
    conditions = {
        "丙": "巳",
        "丁": "午",
        "戊": "申",
        "己": "酉",
        "庚": "亥",
        "辛": "子",
        "壬": "寅",
        "癸": "卯"
    }
    
    return 1 if (conditions.get(year_gan) == dizhi or conditions.get(day_gan) == dizhi) else 0


def dexiuguiren(month_zhi: str, tiangans: List[str]) -> int:
    """
    德秀贵人
    《三命通会》中云："德者，本月生旺之气；秀者，合天地中和之气，五行变化而成者也。"
    寅午戌月，丙丁为德，戊癸为秀；
    申子辰月，壬癸戊己为德，丙辛甲己为秀；
    巳酉丑月，庚辛为德，乙庚为秀；
    亥卯未月，甲乙为德，丁壬为秀。
    
    Args:
        month_zhi: 月支
        tiangans: 天干数组，包含年干、月干、日干、时干
        
    Returns:
        1表示是德秀贵人，0表示不是
    """
    # 火局：寅午戌
    if month_zhi in ["寅", "午", "戌"]:
        # 德：丙丁，秀：戊癸
        return 1 if (has_tiangan_in_list(["丙", "丁"], tiangans) and 
                    has_tiangan_combination("戊", "癸", tiangans)) else 0
    
    # 水局：申子辰
    elif month_zhi in ["申", "子", "辰"]:
        # 德：壬癸戊己，秀：丙辛或甲己
        return 1 if (has_tiangan_in_list(["壬", "癸", "戊", "己"], tiangans) and 
                    (has_tiangan_combination("丙", "辛", tiangans) or 
                     has_tiangan_combination("甲", "己", tiangans))) else 0
    
    # 金局：巳酉丑
    elif month_zhi in ["巳", "酉", "丑"]:
        # 德：庚辛，秀：乙庚
        return 1 if (has_tiangan_in_list(["庚", "辛"], tiangans) and 
                    has_tiangan_combination("乙", "庚", tiangans)) else 0
    
    # 木局：亥卯未
    elif month_zhi in ["亥", "卯", "未"]:
        # 德：甲乙，秀：丁壬
        return 1 if (has_tiangan_in_list(["甲", "乙"], tiangans) and 
                    has_tiangan_combination("丁", "壬", tiangans)) else 0
    
    return 0
