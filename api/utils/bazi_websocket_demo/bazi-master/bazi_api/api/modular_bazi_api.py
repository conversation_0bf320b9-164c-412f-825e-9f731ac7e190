"""
模块化八字分析API
提供组件化的八字分析接口，低耦合高内聚
"""

from ninja import Router
from ninja.schema import Schema
from typing import List, Dict, Optional, Any
from datetime import datetime
import json
import traceback

# 导入八字分析核心服务
from api.services.bazi_analysis_core import create_bazi_analyzer
from api.services.bazi_ai_interpreter import bazi_ai_interpreter

# 创建路由器
modular_bazi_router = Router()

# 输入Schema定义
class BaziInputSchema(Schema):
    year: int
    month: int  
    day: int
    hour: Optional[int] = 0
    minute: Optional[int] = 0
    second: Optional[int] = 0
    gender: Optional[int] = 1  # 1男0女
    sect: Optional[int] = 2    # 流派


@modular_bazi_router.post("/basic_info", summary="获取八字基本信息")
def get_bazi_basic_info(request, data: BaziInputSchema):
    """
    获取八字基本信息
    包括：阳历、农历、八字、性别、流派、四柱等基础信息
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day, 
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        basic_info = analyzer.get_basic_info()
        
        return {
            "success": True,
            "data": basic_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/four_pillars", summary="获取四柱详细信息")
def get_four_pillars_detail(request, data: BaziInputSchema):
    """
    获取四柱详细信息
    包括：年月日时四柱的干支、五行、纳音、藏干、地势、旬空等
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        four_pillars = analyzer.get_four_pillars_detail()
        
        return {
            "success": True,
            "data": four_pillars,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/shishen_analysis", summary="获取十神分析")
def get_shishen_analysis(request, data: BaziInputSchema):
    """
    专门的十神分析接口
    包括：天干十神、地支十神、藏干十神等详细分析
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        shishen_analysis = analyzer.get_shishen_analysis()
        
        return {
            "success": True,
            "data": shishen_analysis,
            "analysis_type": "十神分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/shensha_analysis", summary="获取神煞分析")
def get_shensha_analysis(request, data: BaziInputSchema):
    """
    专门的神煞分析接口
    包括：各柱神煞、神煞分类、统计信息等
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        shensha_analysis = analyzer.get_shensha_analysis()
        
        return {
            "success": True,
            "data": shensha_analysis,
            "analysis_type": "神煞分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/dishi_analysis", summary="获取长生十二神分析")
def get_dishi_analysis(request, data: BaziInputSchema):
    """
    专门的长生十二神分析接口
    包括：年月日时四柱的地势状态
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        dishi_analysis = analyzer.get_dishi_analysis()
        
        return {
            "success": True,
            "data": dishi_analysis,
            "analysis_type": "长生十二神分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/special_positions", summary="获取特殊宫位")
def get_special_positions(request, data: BaziInputSchema):
    """
    获取特殊宫位信息
    包括：胎元、胎息、命宫、身宫等
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        special_positions = analyzer.get_special_positions()
        
        return {
            "success": True,
            "data": special_positions,
            "analysis_type": "特殊宫位",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/nine_star_analysis", summary="获取九星分析")
def get_nine_star_analysis(request, data: BaziInputSchema):
    """
    获取九星分析
    包括：年月日时九星的详细信息
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        nine_star_analysis = analyzer.get_nine_star_analysis()
        
        return {
            "success": True,
            "data": nine_star_analysis,
            "analysis_type": "九星分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/fortune_analysis", summary="获取运势分析")
def get_fortune_analysis(request, data: BaziInputSchema):
    """
    获取运势分析
    包括：起运时间、大运、流年等信息
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        fortune_analysis = analyzer.get_fortune_analysis()
        
        return {
            "success": True,
            "data": fortune_analysis,
            "analysis_type": "运势分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/complete_analysis", summary="获取完整八字分析")
def get_complete_analysis(request, data: BaziInputSchema):
    """
    获取完整的八字分析
    整合所有模块的分析结果
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        # 整合所有分析结果
        complete_analysis = {
            "basic_info": analyzer.get_basic_info(),
            "four_pillars": analyzer.get_four_pillars_detail(),
            "shishen_analysis": analyzer.get_shishen_analysis(),
            "shensha_analysis": analyzer.get_shensha_analysis(),
            "dishi_analysis": analyzer.get_dishi_analysis(),
            "special_positions": analyzer.get_special_positions(),
            "nine_star_analysis": analyzer.get_nine_star_analysis(),
            "fortune_analysis": analyzer.get_fortune_analysis()
        }
        
        return {
            "success": True,
            "data": complete_analysis,
            "analysis_type": "完整八字分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


# ==================== 带AI解读的接口 ====================

@modular_bazi_router.post("/shishen_with_ai", summary="十神分析+AI解读")
def get_shishen_with_ai_interpretation(request, data: BaziInputSchema):
    """
    十神分析 + AI专业解读
    提供十神数据和AI智能解读
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )

        basic_info = analyzer.get_basic_info()
        shishen_analysis = analyzer.get_shishen_analysis()

        # AI解读
        ai_interpretation = bazi_ai_interpreter.interpret_shishen(shishen_analysis, basic_info)

        return {
            "success": True,
            "data": {
                "shishen_data": shishen_analysis,
                "ai_interpretation": ai_interpretation
            },
            "analysis_type": "十神分析+AI解读",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/shensha_with_ai", summary="神煞分析+AI解读")
def get_shensha_with_ai_interpretation(request, data: BaziInputSchema):
    """
    神煞分析 + AI专业解读
    提供神煞数据和AI智能解读
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )

        basic_info = analyzer.get_basic_info()
        shensha_analysis = analyzer.get_shensha_analysis()

        # AI解读
        ai_interpretation = bazi_ai_interpreter.interpret_shensha(shensha_analysis, basic_info)

        return {
            "success": True,
            "data": {
                "shensha_data": shensha_analysis,
                "ai_interpretation": ai_interpretation
            },
            "analysis_type": "神煞分析+AI解读",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/fortune_with_ai", summary="运势分析+AI解读")
def get_fortune_with_ai_interpretation(request, data: BaziInputSchema):
    """
    运势分析 + AI专业解读
    提供运势数据和AI智能解读
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )

        basic_info = analyzer.get_basic_info()
        fortune_analysis = analyzer.get_fortune_analysis()

        # AI解读
        ai_interpretation = bazi_ai_interpreter.interpret_fortune(fortune_analysis, basic_info)

        return {
            "success": True,
            "data": {
                "fortune_data": fortune_analysis,
                "ai_interpretation": ai_interpretation
            },
            "analysis_type": "运势分析+AI解读",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/dishi_with_ai", summary="长生十二神+AI解读")
def get_dishi_with_ai_interpretation(request, data: BaziInputSchema):
    """
    长生十二神分析 + AI专业解读
    提供地势数据和AI智能解读
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )

        basic_info = analyzer.get_basic_info()
        dishi_analysis = analyzer.get_dishi_analysis()

        # AI解读
        ai_interpretation = bazi_ai_interpreter.interpret_dishi(dishi_analysis, basic_info)

        return {
            "success": True,
            "data": {
                "dishi_data": dishi_analysis,
                "ai_interpretation": ai_interpretation
            },
            "analysis_type": "长生十二神+AI解读",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }


@modular_bazi_router.post("/nine_star_with_ai", summary="九星分析+AI解读")
def get_nine_star_with_ai_interpretation(request, data: BaziInputSchema):
    """
    九星分析 + AI专业解读
    提供九星数据和AI智能解读
    """
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )

        basic_info = analyzer.get_basic_info()
        nine_star_analysis = analyzer.get_nine_star_analysis()

        # AI解读
        ai_interpretation = bazi_ai_interpreter.interpret_nine_star(nine_star_analysis, basic_info)

        return {
            "success": True,
            "data": {
                "nine_star_data": nine_star_analysis,
                "ai_interpretation": ai_interpretation
            },
            "analysis_type": "九星分析+AI解读",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }
