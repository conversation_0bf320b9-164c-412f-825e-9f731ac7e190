#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第四批补充API性能测试脚本
专门针对之前遗漏的API进行补充测试
包括：会员系统、论坛系统、医案系统、症状管理、预后系统、邀请系统、其他功能API
"""

import requests
import time
import statistics
import threading
import os
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入API配置
from performance_test_api_config import (
    PERFORMANCE_TEST_APIS,
    BASE_URL,
    AUTH_HEADERS
)

# 导入第四批API清单
from 第四批测试API清单 import FOURTH_BATCH_APIS, FOURTH_BATCH_BY_RISK

# 第四批测试配置
NORMAL_CONCURRENT_USERS = [1, 20, 40, 60, 100]  # 正常并发测试
SAFE_CONCURRENT_USERS = [1, 20, 40, 60, 100]     # 安全并发测试
TEST_REQUESTS = 100  # 测试请求数
RESULTS_DIR = "performance_test_results"

def ensure_results_dir():
    """确保结果目录存在"""
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)

def test_api_performance(api_name, api_config, concurrent_users, total_requests):
    """测试单个API的性能"""
    print(f"  🔄 测试 {concurrent_users} 并发用户...")
    
    results = []
    success_count = 0
    cache_hits = 0
    
    def make_request():
        nonlocal success_count, cache_hits
        try:
            start_time = time.time()
            
            # 根据方法类型发送请求
            if api_config['method'] == 'GET':
                response = requests.get(
                    api_config['url'], 
                    headers=api_config.get('headers', {}),
                    timeout=30
                )
            elif api_config['method'] == 'POST':
                response = requests.post(
                    api_config['url'],
                    headers=api_config.get('headers', {}),
                    json=api_config.get('data', {}),
                    timeout=30
                )
            elif api_config['method'] == 'PUT':
                response = requests.put(
                    api_config['url'],
                    headers=api_config.get('headers', {}),
                    json=api_config.get('data', {}),
                    timeout=30
                )
            elif api_config['method'] == 'DELETE':
                response = requests.delete(
                    api_config['url'],
                    headers=api_config.get('headers', {}),
                    timeout=30
                )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # 检查响应状态
            if response.status_code in [200, 201, 204]:
                success_count += 1
                
                # 检查缓存命中
                if 'X-Cache-Hit' in response.headers or 'cache' in response.headers.get('X-Cache-Status', '').lower():
                    cache_hits += 1
            
            results.append(response_time)
            
        except Exception as e:
            print(f"    ❌ 请求失败: {str(e)}")
            results.append(30.0)  # 超时时间作为失败响应时间
    
    # 执行并发测试
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
        futures = [executor.submit(make_request) for _ in range(total_requests)]
        for future in as_completed(futures):
            future.result()
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 计算统计数据
    if results:
        avg_response_time = statistics.mean(results)
        median_response_time = statistics.median(results)
        min_response_time = min(results)
        max_response_time = max(results)
        qps = total_requests / total_duration if total_duration > 0 else 0
        success_rate = (success_count / total_requests) * 100
        cache_hit_rate = (cache_hits / success_count) * 100 if success_count > 0 else 0
    else:
        avg_response_time = median_response_time = min_response_time = max_response_time = 0
        qps = success_rate = cache_hit_rate = 0
    
    print(f"    📊 QPS: {qps:.1f}, 响应时间: {avg_response_time*1000:.1f}ms, 成功率: {success_rate:.1f}%")
    
    return {
        'concurrent_users': concurrent_users,
        'total_requests': total_requests,
        'success_count': success_count,
        'success_rate': success_rate,
        'cache_hit_rate': cache_hit_rate,
        'avg_response_time': avg_response_time,
        'median_response_time': median_response_time,
        'min_response_time': min_response_time,
        'max_response_time': max_response_time,
        'qps': qps,
        'total_duration': total_duration
    }

def save_test_results(all_results):
    """保存测试结果"""
    ensure_results_dir()
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    detailed_file = f"{RESULTS_DIR}/fourth_batch_detailed_{timestamp}.json"
    with open(detailed_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    # 保存汇总报告
    summary_file = f"{RESULTS_DIR}/fourth_batch_summary_{timestamp}.json"
    summary_data = {
        "test_time": datetime.now().isoformat(),
        "base_url": BASE_URL,
        "test_requests": TEST_REQUESTS,
        "selected_apis": list(all_results.keys()),
        "total_apis_tested": len(all_results)
    }
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 测试结果已保存:")
    print(f"   详细结果: {detailed_file}")
    print(f"   汇总报告: {summary_file}")

def run_fourth_batch_test():
    """运行第四批补充测试"""
    print("🎯 第四批补充API性能测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 测试地址: {BASE_URL}")
    print(f"📊 每轮测试请求数: {TEST_REQUESTS}")
    print(f"🎯 测试API数量: {len(FOURTH_BATCH_APIS)}")
    
    all_results = {}
    
    for i, api_name in enumerate(FOURTH_BATCH_APIS, 1):
        if api_name not in PERFORMANCE_TEST_APIS:
            print(f"⚠️ API {api_name} 未在配置中找到，跳过测试")
            continue
            
        api_config = PERFORMANCE_TEST_APIS[api_name]
        print(f"\n" + "=" * 80)
        print(f"🎯 测试进度: {i}/{len(FOURTH_BATCH_APIS)} - {api_name}")
        print(f"📂 类别: {api_config.get('category', '未分类')}")
        print(f"📍 URL: {api_config['url']}")
        print(f"📝 描述: {api_config['description']}")
        
        # 根据风险等级选择并发配置
        if api_name in FOURTH_BATCH_BY_RISK['中风险']:
            concurrent_users = SAFE_CONCURRENT_USERS
            print(f"⚠️ 中风险API，使用安全并发配置: {concurrent_users}")
        else:
            concurrent_users = NORMAL_CONCURRENT_USERS
            print(f"✅ 低风险API，使用正常并发配置: {concurrent_users}")
        
        api_results = {}
        
        for concurrent in concurrent_users:
            result = test_api_performance(api_name, api_config, concurrent, TEST_REQUESTS)
            api_results[str(concurrent)] = result
            
            # 中风险API增加安全间隔
            if api_name in FOURTH_BATCH_BY_RISK['中风险']:
                time.sleep(1)
        
        all_results[api_name] = api_results
    
    # 保存测试结果
    save_test_results(all_results)
    
    # 输出汇总报告
    print_summary_report(all_results)

def print_summary_report(all_results):
    """输出汇总报告"""
    print(f"\n🏆 第四批测试汇总报告:")
    print("=" * 80)
    print("API名称                    | 最大QPS | 平均响应时间 | 成功率 | 风险等级")
    print("-" * 80)
    
    for api_name, results in all_results.items():
        # 找到最高QPS
        max_qps = 0
        best_response_time = 0
        best_success_rate = 0
        
        for concurrent, result in results.items():
            if result['qps'] > max_qps:
                max_qps = result['qps']
                best_response_time = result['avg_response_time'] * 1000
                best_success_rate = result['success_rate']
        
        risk_level = "中风险" if api_name in FOURTH_BATCH_BY_RISK['中风险'] else "低风险"
        print(f"{api_name:<25} | {max_qps:7.1f} | {best_response_time:8.1f}ms | {best_success_rate:6.1f}% | {risk_level}")

if __name__ == "__main__":
    run_fourth_batch_test()
