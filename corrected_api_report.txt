============================================================
🚀 修正版API统计详细报告
============================================================
扫描时间: 2025-08-02 21:37:01

📊 总体统计:
  总API数量: 481
  使用API_TIMER的API: 269
  使用缓存的API: 33
  API_TIMER覆盖率: 55.9%
  缓存覆盖率: 6.9%

📁 详细API列表:

🔹 admin 模块 (1个API, Timer:0.0%, Cache:0.0%):
  ❌ Class ActiveAnnouncementView (class_view)
     📍 api/admin/common_admin.py:88
     🔧 Timer:❌ | Cache:❌


🔹 ninja_apis 模块 (156个API, Timer:55.8%, Cache:7.1%):
  ❌ GET /categories (ninja_router)
     📍 api/ninja_apis/bazi_analysis_api.py:21
     🔧 Timer:❌ | Cache:❌

  ❌ GET /prompts/available (ninja_router)
     📍 api/ninja_apis/bazi_analysis_api.py:94
     🔧 Timer:❌ | Cache:❌

  ❌ GET /prompts/content/{category}/{version} (ninja_router)
     📍 api/ninja_apis/bazi_analysis_api.py:135
     🔧 Timer:❌ | Cache:❌

  ❌ POST /prompts/reload (ninja_router)
     📍 api/ninja_apis/bazi_analysis_api.py:183
     🔧 Timer:❌ | Cache:❌

  ❌ GET /manager (ninja_router)
     📍 api/ninja_apis/bazi_analysis_api.py:208
     🔧 Timer:❌ | Cache:❌

  ⏱️ POST /checkmembership/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:145
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /empty-request/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:214
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-goal/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:221
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /activity-list/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:253
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /badhabit-list/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:336
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-stats/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:409
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /abstract-goal/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:576
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /abstract-goal/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:621
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-card-month/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:769
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /day-activities/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:885
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /check-register-user/ (ninja_router)
     📍 api/ninja_apis/async_bank_apis.py:968
     🔧 Timer:api_timer | Cache:❌

  ❌ GET /pool-stats (ninja_router)
     📍 api/ninja_apis/db_health_api.py:31
     🔧 Timer:❌ | Cache:❌

  ❌ POST /reset-pool (ninja_router)
     📍 api/ninja_apis/db_health_api.py:50
     🔧 Timer:❌ | Cache:❌

  ❌ GET /health-check (ninja_router)
     📍 api/ninja_apis/db_health_api.py:77
     🔧 Timer:❌ | Cache:❌

  ❌ POST /handle-timeout (ninja_router)
     📍 api/ninja_apis/db_health_api.py:93
     🔧 Timer:❌ | Cache:❌

  ❌ GET /async-health-check (ninja_router)
     📍 api/ninja_apis/db_health_api.py:113
     🔧 Timer:❌ | Cache:❌

  ❌ POST /async-reset-pool (ninja_router)
     📍 api/ninja_apis/db_health_api.py:133
     🔧 Timer:❌ | Cache:❌

  ❌ POST /async-handle-timeout (ninja_router)
     📍 api/ninja_apis/db_health_api.py:160
     🔧 Timer:❌ | Cache:❌

  ❌ POST /start-monitor (ninja_router)
     📍 api/ninja_apis/db_health_api.py:180
     🔧 Timer:❌ | Cache:❌

  ❌ POST /stop-monitor (ninja_router)
     📍 api/ninja_apis/db_health_api.py:208
     🔧 Timer:❌ | Cache:❌

  ⏱️ POST /refresh-token (ninja_router)
     📍 api/ninja_apis/auth_api.py:142
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /wechat-login (ninja_router)
     📍 api/ninja_apis/auth_api.py:287
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /phone-login (ninja_router)
     📍 api/ninja_apis/auth_api.py:427
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /daily-tcm-questions/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:121
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /daily-tcm-questions/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:228
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /daily-quiz-score/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:331
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /tcm-categories/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:431
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /question-stats/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:485
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /quiz-ranking/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:553
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-quiz-history/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:675
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /challenge-overview/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:906
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /challenge-levels/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:992
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /challenge-level/{level_id}/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:1117
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /challenge-level/{level_id}/submit/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:1190
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /challenge-ranking/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:1304
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /challenge-user-history/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:1472
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /challenge-stats/ (ninja_router)
     📍 api/ninja_apis/daily_tcm_question_apis.py:1603
     🔧 Timer:api_timer | Cache:❌

  ❌ GET /test (ninja_router)
     📍 api/ninja_apis/__init__.py:165
     🔧 Timer:❌ | Cache:❌

  ❌ GET /endpoint (ninja_router)
     📍 api/ninja_apis/__init__.py:302
     🔧 Timer:❌ | Cache:❌

  ❌ GET /health-score (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:22
     🔧 Timer:❌ | Cache:❌

  ❌ GET /online-time (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:80
     🔧 Timer:❌ | Cache:❌

  ❌ GET /cultivation-days (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:138
     🔧 Timer:❌ | Cache:❌

  ❌ GET /level (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:196
     🔧 Timer:❌ | Cache:❌

  ❌ POST /generate (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:254
     🔧 Timer:❌ | Cache:❌

  ❌ GET /all-types (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:271
     🔧 Timer:❌ | Cache:❌

  ❌ GET /user-positions/{user_id} (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:309
     🔧 Timer:❌ | Cache:❌

  ❌ GET /top-performers (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:374
     🔧 Timer:❌ | Cache:❌

  ❌ GET /history/{ranking_type} (ninja_router)
     📍 api/ninja_apis/cultivation/views/rankings.py:430
     🔧 Timer:❌ | Cache:❌

  ❌ GET /list (ninja_router)
     📍 api/ninja_apis/cultivation/views/achievements.py:25
     🔧 Timer:❌ | Cache:❌

  ❌ GET /user-achievements (ninja_router)
     📍 api/ninja_apis/cultivation/views/achievements.py:69
     🔧 Timer:❌ | Cache:❌

  ❌ GET /achievement-detail/{achievement_id} (ninja_router)
     📍 api/ninja_apis/cultivation/views/achievements.py:174
     🔧 Timer:❌ | Cache:❌

  ❌ POST /check-achievements (ninja_router)
     📍 api/ninja_apis/cultivation/views/achievements.py:247
     🔧 Timer:❌ | Cache:❌

  ❌ GET /leaderboard (ninja_router)
     📍 api/ninja_apis/cultivation/views/achievements.py:286
     🔧 Timer:❌ | Cache:❌

  ❌ GET /types (ninja_router)
     📍 api/ninja_apis/cultivation/views/achievements.py:339
     🔧 Timer:❌ | Cache:❌

  ❌ GET /recent (ninja_router)
     📍 api/ninja_apis/cultivation/views/achievements.py:377
     🔧 Timer:❌ | Cache:❌

  ❌ POST /start-session (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:24
     🔧 Timer:❌ | Cache:❌

  ❌ POST /heartbeat (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:60
     🔧 Timer:❌ | Cache:❌

  ❌ POST /end-session/{session_id} (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:78
     🔧 Timer:❌ | Cache:❌

  ❌ GET /active-sessions (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:105
     🔧 Timer:❌ | Cache:❌

  ❌ GET /recent-sessions (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:137
     🔧 Timer:❌ | Cache:❌

  ❌ GET /stats (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:169
     🔧 Timer:❌ | Cache:❌

  ❌ GET /daily-stats (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:228
     🔧 Timer:❌ | Cache:❌

  ❌ POST /batch-end-sessions (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:289
     🔧 Timer:❌ | Cache:❌

  ❌ GET /session-detail/{session_id} (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:316
     🔧 Timer:❌ | Cache:❌

  ❌ GET /platform-stats (ninja_router)
     📍 api/ninja_apis/cultivation/views/online_tracking.py:348
     🔧 Timer:❌ | Cache:❌

  ❌ GET /stats (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:31
     🔧 Timer:❌ | Cache:❌

  ❌ GET /profile (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:53
     🔧 Timer:❌ | Cache:❌

  ❌ POST /daily-checkin (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:90
     🔧 Timer:❌ | Cache:❌

  ❌ POST /record-login-time (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:119
     🔧 Timer:❌ | Cache:❌

  ❌ POST /record-module-time (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:147
     🔧 Timer:❌ | Cache:❌

  ❌ POST /start-focus-session (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:176
     🔧 Timer:❌ | Cache:❌

  ❌ POST /complete-focus-session (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:199
     🔧 Timer:❌ | Cache:❌

  ❌ GET /realms (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:228
     🔧 Timer:❌ | Cache:❌

  ❌ GET /achievements (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:259
     🔧 Timer:❌ | Cache:❌

  ❌ GET /rankings/exp (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:292
     🔧 Timer:❌ | Cache:❌

  ❌ GET /rankings/focus (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:312
     🔧 Timer:❌ | Cache:❌

  ❌ GET /rankings/health (ninja_router)
     📍 api/ninja_apis/cultivation/views/cultivation.py:332
     🔧 Timer:❌ | Cache:❌

  🗄️ POST /bazi/basic (ninja_router)
     📍 api/ninja_apis/routertest1/bazi_analysis_api.py:283
     🔧 Timer:❌ | Cache:prognosis_cache

  🗄️ POST /bazi/shishen (ninja_router)
     📍 api/ninja_apis/routertest1/bazi_analysis_api.py:323
     🔧 Timer:❌ | Cache:prognosis_cache

  🗄️ POST /bazi/shensha (ninja_router)
     📍 api/ninja_apis/routertest1/bazi_analysis_api.py:360
     🔧 Timer:❌ | Cache:prognosis_cache

  🗄️ POST /bazi/dayun (ninja_router)
     📍 api/ninja_apis/routertest1/bazi_analysis_api.py:397
     🔧 Timer:❌ | Cache:prognosis_cache

  🗄️ GET /wuyun-liuqi/current (ninja_router)
     📍 api/ninja_apis/routertest1/bazi_analysis_api.py:434
     🔧 Timer:❌ | Cache:prognosis_cache

  🗄️ POST /bazi/complete (ninja_router)
     📍 api/ninja_apis/routertest1/bazi_analysis_api.py:484
     🔧 Timer:❌ | Cache:prognosis_cache

  ⏱️ POST /therapy-users-intervention-records-similarity (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:421
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /constitution-similarity-analysis/{therapy_id} (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:621
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /symptom-search (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:2746
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /search-suggestions (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:3069
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /search (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:3159
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /popular-keywords (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:3205
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-categories-stats (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:3382
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /personalized-symptom-search (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py:3763
     🔧 Timer:api_timer | Cache:❌

  ❌ POST /wuyun-liuqi-health/analyze (ninja_router)
     📍 api/ninja_apis/routertest1/wuyun_liuqi_health_api.py:49
     🔧 Timer:❌ | Cache:❌

  ❌ GET /wuyun-liuqi-health/current (ninja_router)
     📍 api/ninja_apis/routertest1/wuyun_liuqi_health_api.py:137
     🔧 Timer:❌ | Cache:❌

  ❌ GET /wuyun-liuqi-health/status (ninja_router)
     📍 api/ninja_apis/routertest1/wuyun_liuqi_health_api.py:163
     🔧 Timer:❌ | Cache:❌

  ❌ GET /wuyun-liuqi-health/template (ninja_router)
     📍 api/ninja_apis/routertest1/wuyun_liuqi_health_api.py:183
     🔧 Timer:❌ | Cache:❌

  ❌ POST /wuyun-liuqi-health/test (ninja_router)
     📍 api/ninja_apis/routertest1/wuyun_liuqi_health_api.py:207
     🔧 Timer:❌ | Cache:❌

  ❌ POST /wuyun-liuqi-health/debug (ninja_router)
     📍 api/ninja_apis/routertest1/wuyun_liuqi_health_api.py:237
     🔧 Timer:❌ | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/comments (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:315
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/comments (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:410
     🔧 Timer:api_timer | Cache:❌

  ⏱️ DELETE /therapies/{therapy_id}/comments/{comment_id} (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:479
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/like (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:516
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/like-status (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:599
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/rating (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:666
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/rating-stats (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:748
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage-records (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:836
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-therapy-usage-records (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:1046
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /intervention-records (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:1302
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-intervention-records (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:1389
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /users/{target_user_id}/intervention-records (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:1487
     🔧 Timer:api_timer | Cache:❌

  ⏱️ PUT /intervention-records/{record_id} (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:1618
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage-records-debug (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:1695
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-users-intervention-records (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_interaction_api.py:1851
     🔧 Timer:api_timer | Cache:❌

  🎯 GET /therapy-classifications (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:66
     🔧 Timer:api_timer | Cache:classification_cache

  ⏱️ POST /therapy-classifications (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:204
     🔧 Timer:api_timer | Cache:❌

  🎯 GET /therapy-classifications/{classification_id}/therapies (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:247
     🔧 Timer:api_timer | Cache:therapy_list_cache

  🎯 GET /therapies (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:389
     🔧 Timer:api_timer | Cache:therapy_list_cache

  🎯 GET /therapies/{therapy_id}/ (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:603
     🔧 Timer:api_timer | Cache:therapy_detail_cache

  ⏱️ POST /therapies (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:727
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-therapies (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:803
     🔧 Timer:api_timer | Cache:❌

  🎯 GET /user-therapies (ninja_router)
     📍 api/ninja_apis/routertest1/prognosis_api.py:851
     🔧 Timer:api_timer | Cache:user_therapy_cache

  ⏱️ GET /health/ (ninja_router)
     📍 api/ninja_apis/questionnaire/db_health_api.py:21
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /stats/ (ninja_router)
     📍 api/ninja_apis/questionnaire/db_health_api.py:63
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /reset/ (ninja_router)
     📍 api/ninja_apis/questionnaire/db_health_api.py:92
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/questionnaire/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/basic.py:27
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /questionnaire/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/basic.py:28
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /get_questionnaire/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/basic.py:29
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /check_questionnaire_filled (ninja_router)
     📍 api/ninja_apis/questionnaire/views/basic.py:98
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /check_questionnaire_filled/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/basic.py:99
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/update_user_info/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/user_info.py:29
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /update_user_info/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/user_info.py:30
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/update_symptoms/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/user_info.py:130
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /update_symptoms/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/user_info.py:131
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /v1/user_info/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/user_info.py:195
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user_info/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/user_info.py:196
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/calculate_scores/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/questionnaires.py:59
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /calculate_scores/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/questionnaires.py:60
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/get_user_questionnaires/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/questionnaires.py:193
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /get_user_questionnaires/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/questionnaires.py:194
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/calculation_histories/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation_histories.py:36
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /calculation_histories/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation_histories.py:37
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /v1/calculation_history/{history_id}/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation_histories.py:216
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /calculation_history/{history_id}/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation_histories.py:217
     🔧 Timer:api_timer | Cache:❌

  ⏱️ DELETE /v1/calculation_history/{history_id}/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation_histories.py:453
     🔧 Timer:api_timer | Cache:❌

  ⏱️ DELETE /calculation_history/{history_id}/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation_histories.py:454
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /calculate_constitution_indicators/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation.py:38
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/analyze_questionnaire/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation.py:293
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /analyze_questionnaire/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation.py:294
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /v1/get_latest_calculation/ (ninja_router)
     📍 api/ninja_apis/questionnaire/views/calculation.py:502
     🔧 Timer:api_timer | Cache:❌

  ❌ GET /users/profile (ninja_router)
     📍 api/ninja_apis/user_management/user_api.py:24
     🔧 Timer:❌ | Cache:❌

  ❌ GET /users/list (ninja_router)
     📍 api/ninja_apis/user_management/user_api.py:48
     🔧 Timer:❌ | Cache:❌

  ❌ POST /users/settings (ninja_router)
     📍 api/ninja_apis/user_management/user_api.py:69
     🔧 Timer:❌ | Cache:❌


🔹 ssh-remote%2B7b22686f73744e616d65223a22446a616e676fe4b8bbe69c8de58aa1e599a8227dapi 模块 (1个API, Timer:0.0%, Cache:0.0%):
  ❌ Class AbstractGoalView (class_view)
     📍 vscode-remote:/ssh-remote%2B7b22686f73744e616d65223a22446a616e676fe4b8bbe69c8de58aa1e599a8227dapi/views/bank.py:2
     🔧 Timer:❌ | Cache:❌


🔹 views 模块 (323个API, Timer:56.3%, Cache:6.8%):
  ❌ Class NLPdiet (class_view)
     📍 newapi/views/tcmNLP.py:152
     🔧 Timer:❌ | Cache:❌

  ❌ Class BankView (class_view)
     📍 newapi/views/bank.py:71
     🔧 Timer:❌ | Cache:❌

  ❌ Class FaceView (class_view)
     📍 newapi/views/bank.py:88
     🔧 Timer:❌ | Cache:❌

  ❌ Class VoiceView (class_view)
     📍 newapi/views/bank.py:105
     🔧 Timer:❌ | Cache:❌

  ❌ Class ExchangeView (class_view)
     📍 newapi/views/bank.py:171
     🔧 Timer:❌ | Cache:❌

  ❌ Class ActivateMembershipView (class_view)
     📍 newapi/views/bank.py:196
     🔧 Timer:❌ | Cache:❌

  ❌ Class WeChatLoginView (class_view)
     📍 newapi/views/bank.py:243
     🔧 Timer:❌ | Cache:❌

  ❌ Class checkmembershipView (class_view)
     📍 newapi/views/bank.py:263
     🔧 Timer:❌ | Cache:❌

  ❌ Class CheckOrRegisterUserView (class_view)
     📍 newapi/views/bank.py:295
     🔧 Timer:❌ | Cache:❌

  ❌ Class AddSharePointsView (class_view)
     📍 newapi/views/bank.py:342
     🔧 Timer:❌ | Cache:❌

  ❌ Class AcupointListView (class_view)
     📍 newapi/views/bank.py:408
     🔧 Timer:❌ | Cache:❌

  ❌ Class AcupointDetailView (class_view)
     📍 newapi/views/bank.py:428
     🔧 Timer:❌ | Cache:❌

  ❌ Class QuestionnaireDetailView (class_view)
     📍 newapi/views/bank.py:442
     🔧 Timer:❌ | Cache:❌

  ❌ Class CalculateScoresView (class_view)
     📍 newapi/views/bank.py:472
     🔧 Timer:❌ | Cache:❌

  ❌ Class SaveQuestionnaireView (class_view)
     📍 newapi/views/bank.py:573
     🔧 Timer:❌ | Cache:❌

  ❌ Class CheckQuestionnaireFilledView (class_view)
     📍 newapi/views/bank.py:598
     🔧 Timer:❌ | Cache:❌

  ❌ Class GetUserQuestionnairesView (class_view)
     📍 newapi/views/bank.py:613
     🔧 Timer:❌ | Cache:❌

  ❌ Class GetUserInfoView (class_view)
     📍 newapi/views/bank.py:643
     🔧 Timer:❌ | Cache:❌

  ❌ Class UserCardView (class_view)
     📍 newapi/views/bank.py:667
     🔧 Timer:❌ | Cache:❌

  ❌ Class UserCardMonthView (class_view)
     📍 newapi/views/bank.py:722
     🔧 Timer:❌ | Cache:❌

  ❌ Class check_points (class_view)
     📍 newapi/views/bank.py:755
     🔧 Timer:❌ | Cache:❌

  🎯 Class HealthExpRankingView (class_view)
     📍 newapi/views/bank.py:803
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class MemberExpRankingView (class_view)
     📍 newapi/views/bank.py:812
     🔧 Timer:❌ | Cache:❌

  ❌ Class DailyTCMQuestionView (class_view)
     📍 newapi/views/bank.py:821
     🔧 Timer:❌ | Cache:❌

  🎯 Class DailyTCMQuestionView1 (class_view)
     📍 newapi/views/bank.py:850
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class DailyQuizScoreView (class_view)
     📍 newapi/views/bank.py:855
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class DailyQuizRankingView (class_view)
     📍 newapi/views/bank.py:888
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class UserCreateView (class_view)
     📍 newapi/views/bank.py:906
     🔧 Timer:❌ | Cache:❌

  ❌ Class UIDTokenObtainPairView (class_view)
     📍 newapi/views/bank.py:934
     🔧 Timer:❌ | Cache:❌

  ❌ Class CustomTokenRefreshView (class_view)
     📍 newapi/views/bank.py:959
     🔧 Timer:❌ | Cache:❌

  ❌ Class HomeView (class_view)
     📍 newapi/views/bank.py:971
     🔧 Timer:❌ | Cache:❌

  ❌ Class LoginByWeixinView_new1 (class_view)
     📍 newapi/views/bank.py:980
     🔧 Timer:❌ | Cache:❌

  ❌ Class RegisterOrLoginByPhoneView (class_view)
     📍 newapi/views/bank.py:1046
     🔧 Timer:❌ | Cache:❌

  ❌ Class LogoutView (class_view)
     📍 newapi/views/bank.py:1109
     🔧 Timer:❌ | Cache:❌

  ❌ Class HelloView11 (class_view)
     📍 newapi/views/bank.py:1124
     🔧 Timer:❌ | Cache:❌

  ❌ Class AnalyzeQuestionnaireView (class_view)
     📍 newapi/views/bank.py:1143
     🔧 Timer:❌ | Cache:❌

  ❌ Class AnalyzeQuestionnaireViewcomplex (class_view)
     📍 newapi/views/bank.py:1264
     🔧 Timer:❌ | Cache:❌

  ❌ Class AbstractGoalView (class_view)
     📍 api/views/abstractDAKA.py:19
     🔧 Timer:❌ | Cache:❌

  ❌ Class AbstractGoalDetailView (class_view)
     📍 api/views/abstractDAKA.py:222
     🔧 Timer:❌ | Cache:❌

  ❌ Class EnhancedQuestionnaireDetailView (class_view)
     📍 api/views/questionnaire_enhanced.py:30
     🔧 Timer:❌ | Cache:❌

  ❌ Class EnhancedCalculateScoresView (class_view)
     📍 api/views/questionnaire_enhanced.py:71
     🔧 Timer:❌ | Cache:❌

  ❌ Class EnhancedUpdateSymptomsView (class_view)
     📍 api/views/questionnaire_enhanced.py:167
     🔧 Timer:❌ | Cache:❌

  ❌ Class EnhancedAnalyzeQuestionnaireView (class_view)
     📍 api/views/questionnaire_enhanced.py:219
     🔧 Timer:❌ | Cache:❌

  ❌ Class EnhancedGetUserQuestionnairesView (class_view)
     📍 api/views/questionnaire_enhanced.py:317
     🔧 Timer:❌ | Cache:❌

  ❌ Class EnhancedCheckQuestionnaireFilledView (class_view)
     📍 api/views/questionnaire_enhanced.py:374
     🔧 Timer:❌ | Cache:❌

  ❌ Class OptimizedBookListView (class_view)
     📍 api/views/tcm_books_api.py:39
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookDetailView (class_view)
     📍 api/views/tcm_books_api.py:130
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookChaptersView (class_view)
     📍 api/views/tcm_books_api.py:170
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookSearchView (class_view)
     📍 api/views/tcm_books_api.py:234
     🔧 Timer:❌ | Cache:❌

  ❌ Class ChapterContentView (class_view)
     📍 api/views/tcm_books_api.py:290
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookCacheManagementView (class_view)
     📍 api/views/tcm_books_api.py:374
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookDownloadInfoView (class_view)
     📍 api/views/tcm_books_api.py:413
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookDownloadView (class_view)
     📍 api/views/tcm_books_api.py:452
     🔧 Timer:❌ | Cache:❌

  ❌ Class BatchBookDownloadView (class_view)
     📍 api/views/tcm_books_api.py:543
     🔧 Timer:❌ | Cache:❌

  ❌ GET /test (ninja_router)
     📍 api/views/ninja_chat.py:17
     🔧 Timer:❌ | Cache:❌

  ❌ Class HighPerformanceHomeView (class_view)
     📍 api/views/bank.py:180
     🔧 Timer:❌ | Cache:❌

  ❌ Class BankView (class_view)
     📍 api/views/bank.py:245
     🔧 Timer:❌ | Cache:❌

  ❌ Class FaceView (class_view)
     📍 api/views/bank.py:262
     🔧 Timer:❌ | Cache:❌

  ❌ Class VoiceView (class_view)
     📍 api/views/bank.py:279
     🔧 Timer:❌ | Cache:❌

  ❌ Class ExchangeView (class_view)
     📍 api/views/bank.py:345
     🔧 Timer:❌ | Cache:❌

  ❌ Class ActivateMembershipView (class_view)
     📍 api/views/bank.py:370
     🔧 Timer:❌ | Cache:❌

  ❌ Class WeChatLoginView (class_view)
     📍 api/views/bank.py:417
     🔧 Timer:❌ | Cache:❌

  ❌ Class checkmembershipView (class_view)
     📍 api/views/bank.py:437
     🔧 Timer:❌ | Cache:❌

  ❌ Class CheckOrRegisterUserView (class_view)
     📍 api/views/bank.py:485
     🔧 Timer:❌ | Cache:❌

  ❌ Class CheckOrRegisterUserView (class_view)
     📍 api/views/bank.py:531
     🔧 Timer:❌ | Cache:❌

  ❌ Class AddSharePointsView (class_view)
     📍 api/views/bank.py:606
     🔧 Timer:❌ | Cache:❌

  ❌ Class AcupointListView (class_view)
     📍 api/views/bank.py:673
     🔧 Timer:❌ | Cache:❌

  ❌ Class AcupointDetailView (class_view)
     📍 api/views/bank.py:695
     🔧 Timer:❌ | Cache:❌

  ❌ Class QuestionnaireDetailView (class_view)
     📍 api/views/bank.py:712
     🔧 Timer:❌ | Cache:❌

  ❌ Class CalculateScoresView (class_view)
     📍 api/views/bank.py:745
     🔧 Timer:❌ | Cache:❌

  ❌ Class SaveQuestionnaireView (class_view)
     📍 api/views/bank.py:854
     🔧 Timer:❌ | Cache:❌

  ❌ Class CheckQuestionnaireFilledView (class_view)
     📍 api/views/bank.py:879
     🔧 Timer:❌ | Cache:❌

  ❌ Class GetUserQuestionnairesView (class_view)
     📍 api/views/bank.py:894
     🔧 Timer:❌ | Cache:❌

  ❌ Class GetUserInfoView (class_view)
     📍 api/views/bank.py:924
     🔧 Timer:❌ | Cache:❌

  ❌ Class UserCardView (class_view)
     📍 api/views/bank.py:963
     🔧 Timer:❌ | Cache:❌

  ❌ Class ActivityListView (class_view)
     📍 api/views/bank.py:1175
     🔧 Timer:❌ | Cache:❌

  ❌ Class BadHabitListView (class_view)
     📍 api/views/bank.py:1224
     🔧 Timer:❌ | Cache:❌

  ❌ Class CreateCustomActivityView (class_view)
     📍 api/views/bank.py:1266
     🔧 Timer:❌ | Cache:❌

  ❌ Class UserGoalView (class_view)
     📍 api/views/bank.py:1332
     🔧 Timer:❌ | Cache:❌

  ❌ Class UserCardMonthView (class_view)
     📍 api/views/bank.py:1445
     🔧 Timer:❌ | Cache:❌

  ❌ Class DayActivitiesView (class_view)
     📍 api/views/bank.py:1535
     🔧 Timer:❌ | Cache:❌

  ❌ Class UserStatsView (class_view)
     📍 api/views/bank.py:1607
     🔧 Timer:❌ | Cache:❌

  ❌ Class AbstractGoalRecordView (class_view)
     📍 api/views/bank.py:1775
     🔧 Timer:❌ | Cache:❌

  ❌ Class DeleteUserActivityView (class_view)
     📍 api/views/bank.py:1870
     🔧 Timer:❌ | Cache:❌

  ❌ Class UserCardView (class_view)
     📍 api/views/bank.py:1982
     🔧 Timer:❌ | Cache:❌

  ❌ Class check_points (class_view)
     📍 api/views/bank.py:2074
     🔧 Timer:❌ | Cache:❌

  🎯 Class HealthExpRankingView (class_view)
     📍 api/views/bank.py:2122
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class MemberExpRankingView (class_view)
     📍 api/views/bank.py:2131
     🔧 Timer:❌ | Cache:❌

  ❌ Class DailyTCMQuestionView (class_view)
     📍 api/views/bank.py:2140
     🔧 Timer:❌ | Cache:❌

  🎯 Class DailyQuizScoreView (class_view)
     📍 api/views/bank.py:2206
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class DailyQuizRankingView (class_view)
     📍 api/views/bank.py:2239
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class UserCreateView (class_view)
     📍 api/views/bank.py:2257
     🔧 Timer:❌ | Cache:❌

  ❌ Class UIDTokenObtainPairView (class_view)
     📍 api/views/bank.py:2285
     🔧 Timer:❌ | Cache:❌

  ❌ Class CustomTokenRefreshView (class_view)
     📍 api/views/bank.py:2310
     🔧 Timer:❌ | Cache:❌

  ❌ Class HomeView (class_view)
     📍 api/views/bank.py:2322
     🔧 Timer:❌ | Cache:❌

  ❌ Class LoginByWeixinView_new1 (class_view)
     📍 api/views/bank.py:2334
     🔧 Timer:❌ | Cache:❌

  ❌ Class RegisterOrLoginByPhoneView (class_view)
     📍 api/views/bank.py:2442
     🔧 Timer:❌ | Cache:❌

  ❌ Class RefreshTokenView (class_view)
     📍 api/views/bank.py:2506
     🔧 Timer:❌ | Cache:❌

  ❌ Class LogoutView (class_view)
     📍 api/views/bank.py:2571
     🔧 Timer:❌ | Cache:❌

  ❌ Class HelloView11 (class_view)
     📍 api/views/bank.py:2611
     🔧 Timer:❌ | Cache:❌

  🎯 Class AnalyzeQuestionnaireView (class_view)
     📍 api/views/bank.py:2630
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class AnalyzeQuestionnaireView (class_view)
     📍 api/views/bank.py:2779
     🔧 Timer:❌ | Cache:❌

  ❌ Class AnalyzeQuestionnaireViewcomplexnew (class_view)
     📍 api/views/bank.py:2931
     🔧 Timer:❌ | Cache:❌

  ❌ Class AnalyzeQuestionnaireViewcomplex (class_view)
     📍 api/views/bank.py:3728
     🔧 Timer:❌ | Cache:❌

  ❌ Class ActiveAnnouncementView (class_view)
     📍 api/views/bank.py:4505
     🔧 Timer:❌ | Cache:❌

  ❌ POST /EmptyRequestView (ninja_router)
     📍 api/views/bank.py:4533
     🔧 Timer:❌ | Cache:❌

  ❌ Class EmptyRequestView (class_view)
     📍 api/views/bank.py:4539
     🔧 Timer:❌ | Cache:❌

  ❌ Class chat_with_deepseek (class_view)
     📍 api/views/tcmchat_bak.py:139
     🔧 Timer:❌ | Cache:❌

  ❌ Class chat_with_deepseek (class_view)
     📍 api/views/tcmchat_bak.py:195
     🔧 Timer:❌ | Cache:❌

  ❌ Class chat_with_douban (class_view)
     📍 api/views/tcmchat_bak.py:249
     🔧 Timer:❌ | Cache:❌

  ❌ Class analysis_with_deepseek (class_view)
     📍 api/views/tcmchat_bak.py:373
     🔧 Timer:❌ | Cache:❌

  ❌ Class analysis_with_deepseek_symptoms (class_view)
     📍 api/views/tcmchat_bak.py:434
     🔧 Timer:❌ | Cache:❌

  ❌ Class Only_analysis_with_deepseek_symptoms (class_view)
     📍 api/views/tcmchat_bak.py:499
     🔧 Timer:❌ | Cache:❌

  ❌ Class analysis_with_deepseek_For_Now_advice (class_view)
     📍 api/views/tcmchat_bak.py:552
     🔧 Timer:❌ | Cache:❌

  ❌ Class analysis_with_deepseek (class_view)
     📍 api/views/tcmchat_bak.py:610
     🔧 Timer:❌ | Cache:❌

  ❌ Class analysis_with_deepseek_symptoms (class_view)
     📍 api/views/tcmchat_bak.py:620
     🔧 Timer:❌ | Cache:❌

  ❌ Class Only_analysis_with_deepseek_symptoms (class_view)
     📍 api/views/tcmchat_bak.py:631
     🔧 Timer:❌ | Cache:❌

  ❌ Class analysis_with_deepseek_For_Now_advice (class_view)
     📍 api/views/tcmchat_bak.py:642
     🔧 Timer:❌ | Cache:❌

  ❌ Class analysis_with_douban_symptoms (class_view)
     📍 api/views/tcmchat_bak.py:653
     🔧 Timer:❌ | Cache:❌

  ❌ Class WeChatPayView (class_view)
     📍 api/views/tcmchat_bak.py:730
     🔧 Timer:❌ | Cache:❌

  ❌ Class WeChatPayView_new (class_view)
     📍 api/views/tcmchat_bak.py:849
     🔧 Timer:❌ | Cache:❌

  🎯 Class WeChatPayNotifyView (class_view)
     📍 api/views/tcmchat_bak.py:936
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class StreamingDeepseekAnalysisView (class_view)
     📍 api/views/tcmchat_bak.py:1544
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class AsyncTestView (class_view)
     📍 api/views/tcmchat_bak.py:1561
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class DailyTCMQuestionView (class_view)
     📍 api/views/daily_tcm_questions.py:16
     🔧 Timer:❌ | Cache:❌

  ❌ Class DailyQuizScoreView (class_view)
     📍 api/views/daily_tcm_questions.py:107
     🔧 Timer:❌ | Cache:❌

  ❌ Class DailyQuizRankingView (class_view)
     📍 api/views/daily_tcm_questions.py:139
     🔧 Timer:❌ | Cache:❌

  ❌ POST /invitation/generate (ninja_router)
     📍 api/views/invite_views.py:69
     🔧 Timer:❌ | Cache:❌

  ❌ GET /my-code (ninja_router)
     📍 api/views/invite_views.py:94
     🔧 Timer:❌ | Cache:❌

  ❌ GET /scan/{code} (ninja_router)
     📍 api/views/invite_views.py:125
     🔧 Timer:❌ | Cache:❌

  ❌ GET /records (ninja_router)
     📍 api/views/invite_views.py:171
     🔧 Timer:❌ | Cache:❌

  ❌ Class NLPdiet (class_view)
     📍 api/views/tcmNLP_bak.py:146
     🔧 Timer:❌ | Cache:❌

  ❌ Class TotalWeightsView (class_view)
     📍 api/views/tcmNLP_bak.py:912
     🔧 Timer:❌ | Cache:❌

  ❌ Class AlipayView (class_view)
     📍 api/views/tcmNLP_bak.py:1209
     🔧 Timer:❌ | Cache:❌

  ❌ Class AlipayAuthCallbackView (class_view)
     📍 api/views/tcmNLP_bak.py:1302
     🔧 Timer:❌ | Cache:❌

  ❌ Class CheckPaymentStatusView (class_view)
     📍 api/views/tcmNLP_bak.py:1421
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookListView (class_view)
     📍 api/views/tcmNLP_bak.py:1613
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookContentView (class_view)
     📍 api/views/tcmNLP_bak.py:1639
     🔧 Timer:❌ | Cache:❌

  ❌ Class ChapterContentView (class_view)
     📍 api/views/tcmNLP_bak.py:1653
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookSearchView (class_view)
     📍 api/views/tcmNLP_bak.py:1672
     🔧 Timer:❌ | Cache:❌

  ❌ Class OwnAIdiet (class_view)
     📍 api/views/tcmNLP_bak.py:1747
     🔧 Timer:❌ | Cache:❌

  ⏱️ GET /symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:44
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:51
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:61
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:71
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /constitution (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:83
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /constitution (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:90
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-classifications (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:99
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-classifications (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:126
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-classifications/{classification_id}/therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:141
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:168
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:333
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:353
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/like (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:372
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/rate (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:382
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:392
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:402
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:414
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:424
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /intervention-records (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:436
     🔧 Timer:api_timer | Cache:❌

  ⏱️ PUT /intervention-records/{record_id} (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:479
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-intervention-records (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:519
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /intervention-records/{record_id}/complete (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:555
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /recommend-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:605
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:632
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-therapy-usage (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:647
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /effectiveness-analysis/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:659
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-statistics (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:682
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-effectiveness-analysis (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_refactored.py:741
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:39
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:46
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:56
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:66
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /constitution (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:78
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /constitution (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:85
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:94
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:101
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/like (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:111
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/rate (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:121
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:131
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:141
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:153
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:163
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /recommend-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:175
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:202
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-therapy-usage (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:217
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /effectiveness-analysis/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views.py:229
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:55
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:89
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:111
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:153
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:183
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:303
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/like (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:371
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/rate (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:416
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:461
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:517
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /constitution (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:556
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /constitution (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:599
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:634
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:669
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /recommend-therapies (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:703
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:914
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /effectiveness-analysis/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:1007
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-therapy-usage (ninja_router)
     📍 api/views/modules/prognosis/prognosis_views_backup.py:1140
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /posts (ninja_router)
     📍 api/views/modules/forum/post_views.py:29
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /posts/{post_id} (ninja_router)
     📍 api/views/modules/forum/post_views.py:99
     🔧 Timer:api_timer | Cache:❌

  ⏱️ PUT /posts/{post_id} (ninja_router)
     📍 api/views/modules/forum/post_views.py:149
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /posts/{post_id}/verify (ninja_router)
     📍 api/views/modules/forum/post_views.py:181
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /posts (ninja_router)
     📍 api/views/modules/forum/post_views.py:204
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /sections (ninja_router)
     📍 api/views/modules/forum/section_views.py:27
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /sections/{section_id} (ninja_router)
     📍 api/views/modules/forum/section_views.py:63
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /sections/{section_id}/posts (ninja_router)
     📍 api/views/modules/forum/section_views.py:98
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /posts/{post_id}/comments (ninja_router)
     📍 api/views/modules/forum/comment_views.py:49
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /posts/{post_id}/comments (ninja_router)
     📍 api/views/modules/forum/comment_views.py:115
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /posts/{post_id}/like (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:35
     🔧 Timer:api_timer | Cache:❌

  ⏱️ DELETE /posts/{post_id}/like (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:128
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /posts/{post_id}/collect (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:212
     🔧 Timer:api_timer | Cache:❌

  ⏱️ DELETE /posts/{post_id}/collect (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:309
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user/collected-posts (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:394
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user/liked-posts (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:471
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /comments/{comment_id}/like (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:549
     🔧 Timer:api_timer | Cache:❌

  ⏱️ DELETE /comments/{comment_id}/like (ninja_router)
     📍 api/views/modules/forum/interaction_views.py:613
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /chat (ninja_router)
     📍 api/views/modules/chat/chat_views.py:39
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /chat_YiAnStudy (ninja_router)
     📍 api/views/modules/chat/chat_views.py:148
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /chat_YiAnPingfen/{case_id} (ninja_router)
     📍 api/views/modules/chat/chat_views.py:275
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /medical_case_exp_ranking (ninja_router)
     📍 api/views/modules/chat/chat_views.py:460
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /medical_case_cases_ranking (ninja_router)
     📍 api/views/modules/chat/chat_views.py:501
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /medical_case_total_score_ranking (ninja_router)
     📍 api/views/modules/chat/chat_views.py:542
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /medical_case_avg_score_ranking (ninja_router)
     📍 api/views/modules/chat/chat_views.py:583
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /medical_case_time_efficiency_ranking (ninja_router)
     📍 api/views/modules/chat/chat_views.py:624
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /medical_case_user_ranking/{ranking_type} (ninja_router)
     📍 api/views/modules/chat/chat_views.py:665
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /medical_case_user_stats (ninja_router)
     📍 api/views/modules/chat/chat_views.py:708
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /rate_limit_status (ninja_router)
     📍 api/views/modules/chat/chat_views.py:823
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:44
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:51
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:61
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:71
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /constitution (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:83
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /constitution (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:90
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-classifications (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:99
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-classifications (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:173
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-classifications/{classification_id}/therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:202
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:253
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:465
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:503
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/like (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:547
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/rate (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:557
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:567
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:577
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:589
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:599
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /intervention-records (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:611
     🔧 Timer:api_timer | Cache:❌

  ⏱️ PUT /intervention-records/{record_id} (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:654
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-intervention-records (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:694
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /intervention-records/{record_id}/complete (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:730
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /recommend-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:780
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:807
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-therapy-usage (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:822
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /effectiveness-analysis/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:834
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapy-statistics (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:857
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-effectiveness-analysis (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_refactored.py:917
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:39
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:46
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:56
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:66
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /constitution (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:78
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /constitution (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:85
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:94
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:101
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/like (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:111
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/rate (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:121
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:131
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:141
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:153
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:163
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /recommend-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:175
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:202
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-therapy-usage (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:217
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /effectiveness-analysis/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views.py:229
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:55
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:89
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:111
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-symptoms (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:153
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:183
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:303
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/like (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:371
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/rate (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:416
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:461
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapies/{therapy_id}/comments (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:517
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /constitution (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:556
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /constitution (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:599
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /user-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:634
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /user-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:669
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /recommend-therapies (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:703
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /therapy-usage (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:914
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /effectiveness-analysis/{therapy_id} (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:1007
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /my-therapy-usage (ninja_router)
     📍 api/views/modules/prognosis重构！/prognosis_views_backup.py:1140
     🔧 Timer:api_timer | Cache:❌

  ⏱️ POST /add_health_record (ninja_router)
     📍 api/views/modules/health/health_record_views.py:17
     🔧 Timer:api_timer | Cache:❌

  ⏱️ GET /get_health_records (ninja_router)
     📍 api/views/modules/health/health_record_views.py:102
     🔧 Timer:api_timer | Cache:❌

  ⏱️ DELETE /delete_health_record/{record_id} (ninja_router)
     📍 api/views/modules/health/health_record_views.py:309
     🔧 Timer:api_timer | Cache:❌

  ❌ Class TotalWeightsView (class_view)
     📍 api/views/tcm_nlp/weight_calculation.py:28
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookListView (class_view)
     📍 api/views/tcm_nlp/books.py:175
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookContentView (class_view)
     📍 api/views/tcm_nlp/books.py:209
     🔧 Timer:❌ | Cache:❌

  ❌ Class ChapterContentView (class_view)
     📍 api/views/tcm_nlp/books.py:231
     🔧 Timer:❌ | Cache:❌

  ❌ Class BookSearchView (class_view)
     📍 api/views/tcm_nlp/books.py:255
     🔧 Timer:❌ | Cache:❌

  ❌ Class NLPdiet (class_view)
     📍 api/views/tcm_nlp/diet_recommendation.py:29
     🔧 Timer:❌ | Cache:❌

  ❌ Class OwnAIdiet (class_view)
     📍 api/views/tcm_nlp/diet_recommendation.py:219
     🔧 Timer:❌ | Cache:❌

  ❌ Class AlipayView (class_view)
     📍 api/views/tcm_nlp/payment.py:42
     🔧 Timer:❌ | Cache:❌

  🎯 Class AlipayAuthCallbackView (class_view)
     📍 api/views/tcm_nlp/payment.py:135
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class CheckPaymentStatusView (class_view)
     📍 api/views/tcm_nlp/payment.py:387
     🔧 Timer:❌ | Cache:❌

  ❌ POST /chat (ninja_router)
     📍 api/views/chat/chat_views.py:27
     🔧 Timer:❌ | Cache:❌

  🎯 Class AsyncTestView (class_view)
     📍 api/views/tcmchat_refactored/utilities/views.py:99
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class chat_with_deepseek (class_view)
     📍 api/views/tcmchat_refactored/ai_chat/views.py:6
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class chat_with_douban (class_view)
     📍 api/views/tcmchat_refactored/ai_chat/views.py:107
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class analysis_with_deepseek (class_view)
     📍 api/views/tcmchat_refactored/ai_analysis/views.py:6
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class analysis_with_deepseek_symptoms (class_view)
     📍 api/views/tcmchat_refactored/ai_analysis/views.py:85
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class Only_analysis_with_deepseek_symptoms (class_view)
     📍 api/views/tcmchat_refactored/ai_analysis/views.py:164
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class analysis_with_deepseek_For_Now_advice (class_view)
     📍 api/views/tcmchat_refactored/ai_analysis/views.py:233
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class analysis_with_douban_symptoms (class_view)
     📍 api/views/tcmchat_refactored/ai_analysis/views.py:313
     🔧 Timer:class_timer | Cache:class_cache

  🎯 Class StreamingDeepseekAnalysisView (class_view)
     📍 api/views/tcmchat_refactored/ai_analysis/views.py:397
     🔧 Timer:class_timer | Cache:class_cache

  ❌ Class WeChatPayView_new (class_view)
     📍 api/views/tcmchat_refactored/payment/views.py:9
     🔧 Timer:❌ | Cache:❌

  🎯 Class WeChatPayNotifyView (class_view)
     📍 api/views/tcmchat_refactored/payment/views.py:127
     🔧 Timer:class_timer | Cache:class_cache

