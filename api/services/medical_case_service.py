# -*- coding: utf-8 -*-
"""
医案经验值服务
提供医案评分、经验值计算、统计更新等核心业务逻辑
"""
import logging
import re
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Sum, Avg, Max, Count, Q, F
from django.core.cache import cache
from django.db import transaction

from api.models.medical_case_models import (
    MedicalCaseScore, MedicalCaseExpRecord, MedicalCaseUserProfile
)
from api.models.user_models import UserInfo

logger = logging.getLogger(__name__)


class MedicalCaseExpService:
    """医案经验值核心服务"""
    
    @staticmethod
    def extract_score_from_ai_response(ai_response: str) -> Optional[int]:
        """
        从AI响应中提取评分
        支持多种格式：评分：85分、得分85、85分等
        """
        if not ai_response:
            return None
        
        # 定义正则表达式模式
        patterns = [
            r'评分[：:]\s*(\d+)\s*分',
            r'得分[：:]\s*(\d+)',
            r'分数[：:]\s*(\d+)',
            r'(\d+)\s*分',
            r'评分[：:]\s*(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, ai_response)
            if match:
                score = int(match.group(1))
                if 0 <= score <= 100:
                    return score
        
        return None
    
    @staticmethod
    def calculate_exp_from_score(score: int) -> int:
        """
        根据评分计算经验值
        算法：<60分→5分，60-70分→10分，70-100分→10-50分线性增加
        """
        return MedicalCaseScore.calculate_exp_from_score(score)
    
    @staticmethod
    async def record_medical_case_score(
        user_id: int,
        case_id: str,
        ai_response: str,
        time_spent: int = 0
    ) -> Dict:
        """
        记录医案评分和经验值
        支持覆盖已有记录
        """
        from api.ninja_apis.async_utils import (
            get_async, get_or_create_async, save_async, update_or_create_async
        )
        
        try:
            # 提取评分
            score = MedicalCaseExpService.extract_score_from_ai_response(ai_response)
            if score is None:
                return {
                    'success': False,
                    'message': '无法从AI响应中提取有效评分',
                    'ai_response': ai_response
                }
            
            # 计算经验值
            exp_gained = MedicalCaseExpService.calculate_exp_from_score(score)
            
            # 获取用户
            user = await get_async(UserInfo, id=user_id)
            if not user:
                return {
                    'success': False,
                    'message': f'用户不存在: {user_id}'
                }
            
            # 检查是否已有记录
            existing_score = await get_async(MedicalCaseScore, user=user, case_id=case_id)
            
            operation_type = 'first_score'
            exp_before = 0
            
            if existing_score:
                operation_type = 'rescore'
                exp_before = existing_score.exp_gained
                
                # 更新现有记录
                existing_score.score = score
                existing_score.evaluation_content = ai_response
                existing_score.time_spent = time_spent
                existing_score.exp_gained = exp_gained
                case_score = await save_async(existing_score)
            else:
                # 创建新记录
                case_score = await update_or_create_async(
                    MedicalCaseScore,
                    defaults={
                        'score': score,
                        'evaluation_content': ai_response,
                        'time_spent': time_spent,
                        'exp_gained': exp_gained,
                    },
                    user=user,
                    case_id=case_id
                )
                case_score = case_score[0] if isinstance(case_score, tuple) else case_score
            
            # 记录经验值变化
            exp_record = await update_or_create_async(
                MedicalCaseExpRecord,
                defaults={
                    'exp_before': exp_before,
                    'exp_gained': exp_gained,
                    'exp_after': exp_before + exp_gained,
                    'operation_type': operation_type,
                },
                user=user,
                case_score=case_score
            )
            
            # 异步更新用户统计
            await MedicalCaseExpService.update_user_profile_async(user_id)
            
            return {
                'success': True,
                'message': '医案评分记录成功',
                'data': {
                    'case_id': case_id,
                    'score': score,
                    'exp_gained': exp_gained,
                    'operation_type': operation_type,
                    'total_exp': exp_before + exp_gained,
                }
            }
            
        except Exception as e:
            logger.error(f"记录医案评分失败: {e}", exc_info=True)
            return {
                'success': False,
                'message': f'记录评分失败: {str(e)}'
            }
    
    @staticmethod
    async def update_user_profile_async(user_id: int):
        """异步更新用户医案统计档案"""
        from api.ninja_apis.async_utils import get_async, get_or_create_async, save_async
        
        try:
            user = await get_async(UserInfo, id=user_id)
            if not user:
                return
            
            # 获取或创建用户档案
            profile, created = await get_or_create_async(
                MedicalCaseUserProfile,
                user=user
            )
            
            # 使用同步方法更新统计（因为涉及复杂的聚合查询）
            def update_stats():
                profile.update_statistics()
                return profile
            
            # 在线程池中执行统计更新
            from api.ninja_apis.async_utils import db
            await db._run_in_thread(update_stats)
            
        except Exception as e:
            logger.error(f"更新用户档案失败: {e}", exc_info=True)


class MedicalCaseRankingService:
    """医案排行榜服务"""
    
    CACHE_TIMEOUT = 300  # 缓存5分钟
    
    @staticmethod
    def get_cache_key(ranking_type: str, period: str = 'all') -> str:
        """生成缓存键"""
        return f"medical_case_ranking:{ranking_type}:{period}"
    
    @staticmethod
    async def get_exp_ranking(period: str = 'all', limit: int = 50) -> List[Dict]:
        """获取经验值排行榜"""
        cache_key = MedicalCaseRankingService.get_cache_key('exp', period)
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        from api.ninja_apis.async_utils import filter_async

        # 构建查询条件
        query_filters = {}
        if period == 'week':
            week_ago = timezone.now() - timedelta(days=7)
            query_filters['last_case_date__gte'] = week_ago

        # 异步获取排行榜数据
        profiles = await filter_async(
            MedicalCaseUserProfile,
            **query_filters,
            select_related=['user'],
            order_by=['-total_exp', '-total_cases_completed']
        )

        # 应用limit限制
        if limit and limit > 0:
            profiles = profiles[:limit]

        # 构建排行榜数据
        ranking = []
        for rank, profile in enumerate(profiles, 1):
            ranking.append({
                'rank': rank,
                'user_id': profile.user.id,
                'nickname': profile.user.nickname,
                'avatar': profile.user.avatar,
                'total_exp': profile.total_exp,
                'total_cases': profile.total_cases_completed,
                'average_score': profile.average_score,
                'highest_score': profile.highest_score,
            })

        result = ranking
        cache.set(cache_key, result, MedicalCaseRankingService.CACHE_TIMEOUT)
        return result
    
    @staticmethod
    async def get_cases_completed_ranking(period: str = 'all', limit: int = 50) -> List[Dict]:
        """获取完成医案数量排行榜"""
        cache_key = MedicalCaseRankingService.get_cache_key('cases', period)
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        from api.ninja_apis.async_utils import db
        
        def _get_ranking():
            queryset = MedicalCaseUserProfile.objects.select_related('user')
            
            # 时间筛选
            if period == 'week':
                week_ago = timezone.now() - timedelta(days=7)
                queryset = queryset.filter(last_case_date__gte=week_ago)
            
            # 按完成数量排序
            queryset = queryset.order_by('-total_cases_completed', '-total_exp')[:limit]
            
            ranking = []
            for rank, profile in enumerate(queryset, 1):
                ranking.append({
                    'rank': rank,
                    'user_id': profile.user.id,
                    'nickname': profile.user.nickname,
                    'avatar': profile.user.avatar,
                    'total_cases': profile.total_cases_completed,
                    'total_exp': profile.total_exp,
                    'average_score': profile.average_score,
                    'completion_rate': round(profile.total_cases_completed / max(profile.total_cases_completed, 1) * 100, 2),
                })
            
            return ranking
        
        result = await db._run_in_thread(_get_ranking)
        cache.set(cache_key, result, MedicalCaseRankingService.CACHE_TIMEOUT)
        return result
    
    @staticmethod
    async def get_total_score_ranking(period: str = 'all', limit: int = 50) -> List[Dict]:
        """获取总得分排行榜"""
        cache_key = MedicalCaseRankingService.get_cache_key('total_score', period)
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        from api.ninja_apis.async_utils import db
        
        def _get_ranking():
            queryset = MedicalCaseUserProfile.objects.select_related('user')
            
            # 时间筛选
            if period == 'week':
                week_ago = timezone.now() - timedelta(days=7)
                queryset = queryset.filter(last_case_date__gte=week_ago)
            
            # 按总得分排序
            queryset = queryset.order_by('-total_score_sum', '-total_cases_completed')[:limit]
            
            ranking = []
            for rank, profile in enumerate(queryset, 1):
                ranking.append({
                    'rank': rank,
                    'user_id': profile.user.id,
                    'nickname': profile.user.nickname,
                    'avatar': profile.user.avatar,
                    'total_score': profile.total_score_sum,
                    'total_cases': profile.total_cases_completed,
                    'average_score': profile.average_score,
                    'highest_score': profile.highest_score,
                })
            
            return ranking
        
        result = await db._run_in_thread(_get_ranking)
        cache.set(cache_key, result, MedicalCaseRankingService.CACHE_TIMEOUT)
        return result
    
    @staticmethod
    async def get_average_score_ranking(period: str = 'all', limit: int = 50) -> List[Dict]:
        """获取平均得分排行榜"""
        cache_key = MedicalCaseRankingService.get_cache_key('avg_score', period)
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        from api.ninja_apis.async_utils import db
        
        def _get_ranking():
            queryset = MedicalCaseUserProfile.objects.select_related('user')
            
            # 时间筛选
            if period == 'week':
                week_ago = timezone.now() - timedelta(days=7)
                queryset = queryset.filter(last_case_date__gte=week_ago)
            
            # 过滤掉没有完成医案的用户
            queryset = queryset.filter(total_cases_completed__gt=0)
            
            # 按平均得分排序
            queryset = queryset.order_by('-average_score', '-total_cases_completed')[:limit]
            
            ranking = []
            for rank, profile in enumerate(queryset, 1):
                ranking.append({
                    'rank': rank,
                    'user_id': profile.user.id,
                    'nickname': profile.user.nickname,
                    'avatar': profile.user.avatar,
                    'average_score': profile.average_score,
                    'total_cases': profile.total_cases_completed,
                    'total_score': profile.total_score_sum,
                    'highest_score': profile.highest_score,
                })
            
            return ranking
        
        result = await db._run_in_thread(_get_ranking)
        cache.set(cache_key, result, MedicalCaseRankingService.CACHE_TIMEOUT)
        return result
    
    @staticmethod
    async def get_time_efficiency_ranking(period: str = 'all', limit: int = 50) -> List[Dict]:
        """获取总用时排行榜（效率排行）"""
        cache_key = MedicalCaseRankingService.get_cache_key('time_efficiency', period)
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        from api.ninja_apis.async_utils import filter_async

        # 构建查询条件
        query_filters = {
            'total_cases_completed__gt': 0,
            'total_time_spent__gt': 0
        }
        if period == 'week':
            week_ago = timezone.now() - timedelta(days=7)
            query_filters['last_case_date__gte'] = week_ago

        # 异步获取排行榜数据
        profiles = await filter_async(
            MedicalCaseUserProfile,
            **query_filters,
            select_related=['user'],
            order_by=['average_time_per_case', '-average_score']
        )

        # 应用limit限制
        if limit and limit > 0:
            profiles = profiles[:limit]

        # 构建排行榜数据
        ranking = []
        for rank, profile in enumerate(profiles, 1):
            total_hours = round(profile.total_time_spent / 3600, 2)
            avg_minutes = round(profile.average_time_per_case / 60, 2)

            ranking.append({
                'rank': rank,
                'user_id': profile.user.id,
                'nickname': profile.user.nickname,
                'avatar': profile.user.avatar,
                'total_time_hours': total_hours,
                'average_time_minutes': avg_minutes,
                'total_cases': profile.total_cases_completed,
                'average_score': profile.average_score,
                'efficiency_score': round(profile.average_score / max(avg_minutes, 1), 2),  # 效率指标
            })

        result = ranking
        cache.set(cache_key, result, MedicalCaseRankingService.CACHE_TIMEOUT)
        return result
    
    @staticmethod
    async def get_user_ranking_position(user_id: int, ranking_type: str, period: str = 'all') -> Dict:
        """获取用户在指定排行榜中的位置"""
        try:
            # 根据排行榜类型获取完整排行榜
            if ranking_type == 'exp':
                ranking = await MedicalCaseRankingService.get_exp_ranking(period, limit=1000)
            elif ranking_type == 'cases':
                ranking = await MedicalCaseRankingService.get_cases_completed_ranking(period, limit=1000)
            elif ranking_type == 'total_score':
                ranking = await MedicalCaseRankingService.get_total_score_ranking(period, limit=1000)
            elif ranking_type == 'avg_score':
                ranking = await MedicalCaseRankingService.get_average_score_ranking(period, limit=1000)
            elif ranking_type == 'time_efficiency':
                ranking = await MedicalCaseRankingService.get_time_efficiency_ranking(period, limit=1000)
            else:
                return {'success': False, 'message': '无效的排行榜类型'}
            
            # 查找用户位置
            for item in ranking:
                if item['user_id'] == user_id:
                    return {
                        'success': True,
                        'rank': item['rank'],
                        'total_participants': len(ranking),
                        'data': item
                    }
            
            return {
                'success': False,
                'message': '用户未在排行榜中',
                'rank': None,
                'total_participants': len(ranking)
            }
            
        except Exception as e:
            logger.error(f"获取用户排名失败: {e}", exc_info=True)
            return {'success': False, 'message': f'获取排名失败: {str(e)}'}
    
    @staticmethod
    def clear_ranking_cache():
        """清除所有排行榜缓存"""
        cache_patterns = [
            'medical_case_ranking:exp:all',
            'medical_case_ranking:exp:week',
            'medical_case_ranking:cases:all',
            'medical_case_ranking:cases:week',
            'medical_case_ranking:total_score:all',
            'medical_case_ranking:total_score:week',
            'medical_case_ranking:avg_score:all',
            'medical_case_ranking:avg_score:week',
            'medical_case_ranking:time_efficiency:all',
            'medical_case_ranking:time_efficiency:week',
        ]
        
        for pattern in cache_patterns:
            cache.delete(pattern)
        
        logger.info("医案排行榜缓存已清除") 