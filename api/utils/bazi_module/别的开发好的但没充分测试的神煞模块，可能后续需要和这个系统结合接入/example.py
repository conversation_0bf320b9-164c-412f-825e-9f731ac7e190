#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神煞计算器使用示例

展示如何使用Python神煞计算模块进行八字神煞分析。
"""

from shensha_calculator import query_shensha


def analyze_single_pillar(ganzhi, bazi, is_man, witch, nian_nayin):
    """分析单个柱的神煞"""
    try:
        shenshas = query_shensha(ganzhi, bazi, is_man, witch, nian_nayin)
        return {
            "success": True,
            "shenshas": shenshas,
            "count": len(shenshas)
        }
    except ValueError as e:
        return {
            "success": False,
            "error": str(e)
        }


def analyze_complete_bazi(bazi, nian_nayin, is_man=True):
    """完整分析八字的所有神煞"""
    
    print("=" * 60)
    print("八字神煞分析")
    print("=" * 60)
    
    # 显示基本信息
    print(f"八字: {' '.join(bazi)}")
    print(f"年柱纳音: {nian_nayin}")
    print(f"性别: {'男' if is_man else '女'}")
    print()
    
    # 构造四柱信息
    pillars = [
        ("年柱", bazi[0] + bazi[1], 1),
        ("月柱", bazi[2] + bazi[3], 2),
        ("日柱", bazi[4] + bazi[5], 3),
        ("时柱", bazi[6] + bazi[7], 4)
    ]
    
    total_shenshas = []
    
    # 分析每一柱
    for name, ganzhi, witch in pillars:
        print(f"{name}: {ganzhi}")
        
        result = analyze_single_pillar(ganzhi, bazi, is_man, witch, nian_nayin)
        
        if result["success"]:
            shenshas = result["shenshas"]
            total_shenshas.extend(shenshas)
            
            if shenshas:
                print(f"  神煞({result['count']}个): {', '.join(shenshas)}")
                
                # 分类显示神煞
                noble_spirits = [s for s in shenshas if s in ["天乙", "太极", "文昌", "国印", "金舆", "福星", "天厨", "德秀"]]
                virtue_harmony = [s for s in shenshas if s in ["天德", "月德", "天德合", "月德合", "天赦"]]
                star_spirits = [s for s in shenshas if s in ["驿马", "华盖", "将星", "禄神", "红鸾", "天喜", "五鬼", "天医", "学堂", "词馆"]]
                blade_spirits = [s for s in shenshas if s in ["羊刃", "飞刃", "血刃", "金神"]]
                malefic_spirits = [s for s in shenshas if s not in noble_spirits + virtue_harmony + star_spirits + blade_spirits]
                
                if noble_spirits:
                    print(f"    贵人类: {', '.join(noble_spirits)}")
                if virtue_harmony:
                    print(f"    德合类: {', '.join(virtue_harmony)}")
                if star_spirits:
                    print(f"    星煞类: {', '.join(star_spirits)}")
                if blade_spirits:
                    print(f"    刃煞类: {', '.join(blade_spirits)}")
                if malefic_spirits:
                    print(f"    凶煞类: {', '.join(malefic_spirits)}")
            else:
                print("  无神煞")
        else:
            print(f"  错误: {result['error']}")
        
        print()
    
    # 统计总结
    print("-" * 60)
    print("总结:")
    print(f"总神煞数量: {len(total_shenshas)}")
    
    if total_shenshas:
        # 去重统计
        unique_shenshas = list(set(total_shenshas))
        print(f"不重复神煞: {len(unique_shenshas)}个")
        print(f"所有神煞: {', '.join(sorted(unique_shenshas))}")
        
        # 统计各类神煞数量
        categories = {
            "贵人类": ["天乙", "太极", "文昌", "国印", "金舆", "福星", "天厨", "德秀"],
            "德合类": ["天德", "月德", "天德合", "月德合", "天赦"],
            "星煞类": ["驿马", "华盖", "将星", "禄神", "红鸾", "天喜", "五鬼", "天医", "学堂", "词馆"],
            "刃煞类": ["羊刃", "飞刃", "血刃", "金神"],
            "凶煞类": ["空亡", "劫煞", "灾煞", "亡神", "孤辰", "寡宿", "天罗", "地网", "十恶大败", 
                      "桃花", "孤鸾", "阴差阳错", "四废", "丧门", "吊客", "披麻", "童子", "流霞", 
                      "红艳", "魁罡", "八专", "九丑", "十灵", "元辰"]
        }
        
        print("\n各类神煞统计:")
        for category, shensha_list in categories.items():
            count = len([s for s in unique_shenshas if s in shensha_list])
            if count > 0:
                category_shenshas = [s for s in unique_shenshas if s in shensha_list]
                print(f"  {category}: {count}个 ({', '.join(category_shenshas)})")
    
    print("=" * 60)


def demo_specific_cases():
    """演示特定神煞的案例"""
    
    print("\n特定神煞案例演示:")
    print("-" * 40)
    
    # 魁罡日案例
    print("1. 魁罡日案例:")
    bazi_kuigang = ["甲", "子", "丙", "寅", "壬", "辰", "癸", "亥"]
    result = query_shensha("壬辰", bazi_kuigang, True, 3, "海中金")
    print(f"   壬辰日: {result}")
    
    # 金神日案例
    print("2. 金神日案例:")
    bazi_jinshen = ["甲", "子", "丙", "寅", "乙", "丑", "癸", "亥"]
    result = query_shensha("乙丑", bazi_jinshen, True, 3, "海中金")
    print(f"   乙丑日: {result}")
    
    # 十恶大败日案例
    print("3. 十恶大败日案例:")
    bazi_shiedabai = ["甲", "子", "丙", "寅", "甲", "辰", "癸", "亥"]
    result = query_shensha("甲辰", bazi_shiedabai, True, 3, "海中金")
    print(f"   甲辰日: {result}")
    
    # 孤鸾日案例
    print("4. 孤鸾日案例:")
    bazi_guluan = ["甲", "子", "丙", "寅", "乙", "巳", "癸", "亥"]
    result = query_shensha("乙巳", bazi_guluan, True, 3, "海中金")
    print(f"   乙巳日: {result}")


def main():
    """主函数"""
    
    # 示例1：经典八字
    print("示例1：经典八字分析")
    bazi1 = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]
    analyze_complete_bazi(bazi1, "海中金", True)
    
    # 示例2：另一个八字
    print("\n示例2：另一个八字分析")
    bazi2 = ["乙", "丑", "戊", "寅", "庚", "申", "丁", "丑"]
    analyze_complete_bazi(bazi2, "海中金", False)
    
    # 特定案例演示
    demo_specific_cases()
    
    # 交互式查询示例
    print("\n交互式查询示例:")
    print("-" * 40)
    
    # 用户可以修改这里的参数进行测试
    test_ganzhi = "戊午"
    test_bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]
    test_is_man = True
    test_witch = 3
    test_nayin = "海中金"
    
    print(f"查询参数:")
    print(f"  干支: {test_ganzhi}")
    print(f"  八字: {' '.join(test_bazi)}")
    print(f"  性别: {'男' if test_is_man else '女'}")
    print(f"  柱位: {test_witch} ({'年柱' if test_witch==1 else '月柱' if test_witch==2 else '日柱' if test_witch==3 else '时柱'})")
    print(f"  纳音: {test_nayin}")
    
    try:
        result = query_shensha(test_ganzhi, test_bazi, test_is_man, test_witch, test_nayin)
        print(f"\n查询结果: {result}")
        print(f"神煞数量: {len(result)}")
    except ValueError as e:
        print(f"\n查询错误: {e}")


if __name__ == "__main__":
    main()
