#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试获取自定义症状API
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VhDgOlaTZFjxZaGUdPhe7r658Vqfq48TOc6jTH-6zSA"

headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json",
    "User-Agent": "CustomSymptomsAPITester/1.0"
}

def test_custom_symptoms_api():
    """测试获取自定义症状API"""
    print("🔍 测试获取自定义症状API")
    print("=" * 50)
    
    url = f"{BASE_URL}/api/tcmchat/get_custom_symptoms/"
    print(f"📍 URL: {url}")
    
    try:
        print("🚀 发送GET请求...")
        start_time = time.time()
        
        response = requests.get(url, headers=headers, timeout=10)
        
        response_time = time.time() - start_time
        
        print(f"📊 状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 请求成功!")
            try:
                json_data = response.json()
                print(f"📦 响应数据: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError:
                print(f"📄 响应内容 (非JSON): {response.text}")
        else:
            print(f"❌ 请求失败 - HTTP {response.status_code}")
            print(f"🚨 错误内容: {response.text}")
            
            # 如果是500错误，提供更详细的信息
            if response.status_code == 500:
                print("🔥 这是一个服务器内部错误，检查服务器日志获取详细信息")
        
        return response.status_code == 200
        
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误 - 确保服务器正在运行")
        return False
    except Exception as e:
        print(f"💥 异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 获取自定义症状API专项测试")
    print("=" * 50)
    
    # 测试多次以确保稳定性
    success_count = 0
    total_tests = 3
    
    for i in range(total_tests):
        print(f"\n🔄 第 {i+1}/{total_tests} 次测试")
        if test_custom_symptoms_api():
            success_count += 1
        
        if i < total_tests - 1:
            print("⏳ 等待1秒后进行下次测试...")
            time.sleep(1)
    
    print(f"\n📊 测试结果汇总:")
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"❌ 失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试都成功！API修复完成！")
    elif success_count > 0:
        print("⚠️  部分测试成功，API可能存在间歇性问题")
    else:
        print("💥 所有测试都失败，API仍需修复")

if __name__ == "__main__":
    main()
