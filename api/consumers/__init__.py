# api/consumers/__init__.py
"""
WebSocket消费者模块
提供各种WebSocket功能的消费者类
"""

# 导入所有消费者类
from .test_consumer import TestStreamConsumer
from .ark_chat_consumer import ArkChatConsumer, ArkChatVIPConsumer
from .deepseek_chat_consumer import DeepseekChatConsumer, DeepseekChatVIPConsumer
from .douban_chat_consumer import DoubanChatConsumer
from .deepseek_symptom_consumer import Deepseek2SingleSymptom
from .bazi_advice_consumer import BaziAdviceConsumer
from .bazi_router import BaziAnalysisRouter
from .schemas import MessageSchema, ChatRequestSchema, ChatResponse

# 公开的API
__all__ = [
    # 测试消费者
    'TestStreamConsumer',

    # Ark模型消费者（推荐使用）
    'ArkChatConsumer',
    'ArkChatVIPConsumer',

    # Deepseek模型消费者（已弃用）
    'DeepseekChatConsumer',
    'DeepseekChatVIPConsumer',

    # 其他消费者
    'DoubanChatConsumer',
    'Deepseek2SingleSymptom',
    'BaziAdviceConsumer',
    'BaziAnalysisRouter',

    # 数据模式
    'MessageSchema',
    'ChatRequestSchema',
    'ChatResponse',
]