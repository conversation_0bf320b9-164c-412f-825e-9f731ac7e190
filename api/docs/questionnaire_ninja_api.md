# 问卷系统 Ninja API 文档

## 概述

我们使用Django Ninja框架重构了问卷系统的API，支持异步处理，并提供了更完善的错误处理机制。新系统具有以下特点：

1. 完全兼容现有数据格式，前端代码改动最小
2. 拆分为三个独立接口：问卷提交、分数计算、用户信息更新
3. 支持增量更新
4. 采用异步处理，提高性能
5. 完善的错误处理和日志记录

## API 接口说明

所有API接口的基础URL为：`/api/questionnaire/`

### 1. 获取问卷详情

- **接口**: `/api/questionnaire/questionnaire`
- **方法**: POST
- **描述**: 获取问卷详情，包括问题和选项
- **请求参数**:
  ```json
  {
    "id": 1  // 问卷ID
  }
  ```
- **响应示例**:
  ```json
  {
    "questionnaire_id": 1,
    "questionnaire_name": "体质评估问卷",
    "questions": [
      {
        "id": 1,
        "text": "问题文本",
        "options": [
          {
            "id": 1,
            "text": "选项文本",
            "probability": 10,
            "weight": 5,
            "type": "气虚"
          }
        ]
      }
    ]
  }
  ```

### 2. 提交问卷答案

- **接口**: `/api/questionnaire/submit_answers`
- **方法**: POST
- **描述**: 提交问卷答案，不进行计算
- **请求参数**:
  ```json
  {
    "id": 1,  // 问卷ID
    "answers": {
      "1": 2,  // 问题ID: 选项ID
      "2": 3
    }
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "问卷提交成功，等待计算分数",
    "temp_data": {
      "user_id": 123,
      "questionnaire_id": 1,
      "answers": {"1": 2, "2": 3},
      "version": 2
    }
  }
  ```

### 3. 计算问卷得分

- **接口**: `/api/questionnaire/calculate_scores`
- **方法**: POST
- **描述**: 计算问卷得分，保存到数据库
- **请求参数**:
  ```json
  {
    "id": 1,  // 问卷ID
    "answers": {
      "1": 2,  // 问题ID: 选项ID
      "2": 3
    }
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "成功完成问卷评分",
    "result": {
      "气虚": {
        "probability": 20.5,
        "weight": 15.75,
        "type": "气虚"
      },
      "阳虚": {
        "probability": 10.2,
        "weight": 8.4,
        "type": "阳虚"
      }
    }
  }
  ```

### 4. 更新用户信息

- **接口**: `/api/questionnaire/update_user_info`
- **方法**: POST
- **描述**: 更新用户附加信息（坐标、性别、年龄）
- **请求参数**:
  ```json
  {
    "questionnaire_id": 1,
    "location": "北京市海淀区",
    "gender": "男",
    "age": 28
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "用户信息更新成功"
  }
  ```

### 5. 更新症状信息

- **接口**: `/api/questionnaire/update_symptoms`
- **方法**: POST
- **描述**: 更新用户症状信息
- **请求参数**:
  ```json
  {
    "symptoms": [
      {
        "text": "头痛",
        "level": 2
      },
      {
        "text": "疲劳",
        "level": 3
      }
    ],
    "questionnaireId": 1
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "症状更新成功",
    "deleted_count": 5,
    "created_count": 2
  }
  ```

### 6. 分析问卷结果

- **接口**: `/api/questionnaire/analyze_questionnaire`
- **方法**: POST
- **描述**: 分析用户问卷结果
- **请求参数**: 无需特定参数，基于token识别用户
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "问卷分析完成",
    "results": [
      {
        "type": "气虚",
        "percent": 45.8,
        "totalWeight": 73.6,
        "totalProbability": 29.2
      },
      {
        "type": "肝",
        "percent": 32.1,
        "totalWeight": 69.05,
        "totalProbability": 25.7
      }
    ],
    "history_id": 123
  }
  ```

### 7. 获取用户问卷

- **接口**: `/api/questionnaire/get_user_questionnaires`
- **方法**: POST
- **描述**: 获取用户问卷列表
- **请求参数**:
  ```json
  {
    "questionnaireId": 1,  // 可选，特定问卷ID
    "includeAllVersions": false  // 可选，是否包含所有版本
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "count": 2,
    "data": [
      {
        "user_id": 123,
        "questionnaire_id": 1,
        "answers": {"1": 2, "2": 3},
        "total_scores": {...},
        "summary": {...},
        "location": "北京市海淀区",
        "gender": "男",
        "age": 28,
        "version": 2,
        "latest": true,
        "created_at": "2023-08-15T14:30:00.000Z",
        "updated_at": "2023-08-15T14:30:00.000Z"
      }
    ]
  }
  ```

### 8. 检查问卷填写状态

- **接口**: `/api/questionnaire/check_questionnaire_filled`
- **方法**: POST
- **描述**: 检查用户是否已填写特定问卷
- **请求参数**:
  ```json
  {
    "questionnaire_id": 1
  }
  ```
- **响应示例**:
  ```json
  {
    "filled": true,
    "questionnaire_id": 1
  }
  ```

## 认证

所有接口都使用现有的中间件进行认证。中间件会自动处理认证并将用户ID设置到请求中，无需在API层面额外处理认证。

API接口通过以下方式获取用户信息：
```python
user_id = request.user_id  # 由中间件设置
```

前端调用API时，应当按照现有系统的认证方式进行认证，无需更改。

## 错误处理

所有接口在发生错误时都会返回包含错误信息的JSON响应：

```json
{
  "status": "error",
  "message": "错误信息"
}
```

常见错误包括：

- 400 Bad Request：请求参数错误
- 401 Unauthorized：认证失败
- 404 Not Found：资源不存在
- 500 Internal Server Error：服务器内部错误

## 前端调用示例

```javascript
// 获取问卷详情
async function getQuestionnaire(id) {
  const response = await fetch('/api/questionnaire/questionnaire', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ id })
  });
  return await response.json();
}

// 问卷提交和计算流程
async function submitAndCalculate(id, answers) {
  // 1. 提交问卷答案
  await fetch('/api/questionnaire/submit_answers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ id, answers })
  });
  
  // 2. 计算问卷得分
  const calcResponse = await fetch('/api/questionnaire/calculate_scores', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ id, answers })
  });
  
  const result = await calcResponse.json();
  
  // 3. 更新用户信息（可选）
  await fetch('/api/questionnaire/update_user_info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      questionnaire_id: id,
      location: '北京市海淀区',
      gender: '男',
      age: 28
    })
  });
  
  return result;
}
```

**注意**：以上示例中没有包含认证相关的代码，因为认证是由现有的中间件处理的。如果您的系统使用cookie、session或其他认证方式，应当保持原样，无需修改。 