#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第四批和第五批API并发测试脚本
测试已修复的同步异步混用问题的API
"""

import asyncio
import aiohttp
import time
import json
import sys
from typing import List, Dict, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VhDgOlaTZFjxZaGUdPhe7r658Vqfq48TOc6jTH-6zSA"

# 并发测试参数
CONCURRENT_USERS = 20  # 并发用户数
REQUESTS_PER_USER = 5  # 每个用户的请求次数
TIMEOUT = 30  # 请求超时时间

@dataclass
class TestResult:
    """测试结果数据类"""
    api_name: str
    method: str
    url: str
    success_count: int
    error_count: int
    avg_response_time: float
    max_response_time: float
    min_response_time: float
    errors: List[str]
    status_codes: Dict[int, int]

class ConcurrentAPITester:
    """并发API测试器"""
    
    def __init__(self):
        self.results: List[TestResult] = []
        self.session = None
        
    async def create_session(self):
        """创建HTTP会话"""
        connector = aiohttp.TCPConnector(
            limit=100,  # 连接池大小
            limit_per_host=50,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        timeout = aiohttp.ClientTimeout(total=TIMEOUT)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {TOKEN}",
                "Content-Type": "application/json",
                "User-Agent": "ConcurrentAPITester/1.0"
            }
        )
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, url: str, data: Dict = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        start_time = time.time()
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    response_time = time.time() - start_time
                    content = await response.text()
                    # 检查是否为错误状态码
                    is_success = 200 <= response.status < 300
                    return {
                        "success": is_success,
                        "status_code": response.status,
                        "response_time": response_time,
                        "content": content[:500] if is_success else content[:1000],  # 错误时保留更多内容
                        "error": None if is_success else f"HTTP {response.status}: {content[:200]}"
                    }
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    response_time = time.time() - start_time
                    content = await response.text()
                    # 检查是否为错误状态码
                    is_success = 200 <= response.status < 300
                    return {
                        "success": is_success,
                        "status_code": response.status,
                        "response_time": response_time,
                        "content": content[:500] if is_success else content[:1000],  # 错误时保留更多内容
                        "error": None if is_success else f"HTTP {response.status}: {content[:200]}"
                    }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "content": "",
                "error": f"Request Exception: {str(e)}"
            }
    
    async def test_api_concurrent(self, api_name: str, method: str, url: str, data: Dict = None) -> TestResult:
        """并发测试单个API"""
        print(f"\n🚀 开始测试 {api_name} - {method} {url}")
        print(f"📊 并发用户: {CONCURRENT_USERS}, 每用户请求: {REQUESTS_PER_USER}")
        
        # 创建任务列表
        tasks = []
        for user_id in range(CONCURRENT_USERS):
            for req_id in range(REQUESTS_PER_USER):
                task = self.make_request(method, url, data)
                tasks.append(task)
        
        # 执行并发请求
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 统计结果
        success_count = 0
        error_count = 0
        response_times = []
        errors = []
        status_codes = {}
        
        for result in results:
            if isinstance(result, Exception):
                error_count += 1
                errors.append(str(result))
            elif result["success"]:
                success_count += 1
                response_times.append(result["response_time"])
                status_code = result["status_code"]
                status_codes[status_code] = status_codes.get(status_code, 0) + 1
            else:
                error_count += 1
                errors.append(result["error"])
        
        # 计算统计数据
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        min_response_time = min(response_times) if response_times else 0
        
        # 创建测试结果
        test_result = TestResult(
            api_name=api_name,
            method=method,
            url=url,
            success_count=success_count,
            error_count=error_count,
            avg_response_time=avg_response_time,
            max_response_time=max_response_time,
            min_response_time=min_response_time,
            errors=errors[:5],  # 只保留前5个错误
            status_codes=status_codes
        )
        
        # 打印结果
        total_requests = CONCURRENT_USERS * REQUESTS_PER_USER
        success_rate = (success_count / total_requests) * 100
        
        print(f"✅ 成功: {success_count}/{total_requests} ({success_rate:.1f}%)")
        print(f"❌ 失败: {error_count}")
        print(f"⏱️  平均响应时间: {avg_response_time:.3f}s")
        print(f"📈 最大响应时间: {max_response_time:.3f}s")
        print(f"📉 最小响应时间: {min_response_time:.3f}s")
        print(f"🔢 状态码分布: {status_codes}")

        if errors:
            print(f"🚨 错误示例:")
            for i, error in enumerate(errors[:3], 1):
                print(f"   {i}. {error}")

        # 特别标记有错误的API
        if error_count > 0:
            print(f"⚠️  警告: 该API存在 {error_count} 个错误请求!")

        self.results.append(test_result)
        return test_result

# 第四批API测试清单
BATCH_4_APIS = [
    # 会员系统API
    {
        "name": "日活动API",
        "method": "GET",
        "url": f"{BASE_URL}/api/async-bank/daily-activity/",
        "data": None
    },
    {
        "name": "用户问答历史API",
        "method": "GET", 
        "url": f"{BASE_URL}/api/daily_tcm_question/user_quiz_history/",
        "data": None
    },
    
    # 论坛系统API
    {
        "name": "获取帖子列表API",
        "method": "GET",
        "url": f"{BASE_URL}/api/forum/posts/",
        "data": None
    },
    {
        "name": "获取帖子评论API",
        "method": "GET",
        "url": f"{BASE_URL}/api/forum/posts/1/comments/",
        "data": None
    },
    
    # 医案系统API
    {
        "name": "经验值排行API",
        "method": "GET",
        "url": f"{BASE_URL}/api/medical-case/ranking/exp/",
        "data": None
    },
    {
        "name": "时间效率排行API",
        "method": "GET",
        "url": f"{BASE_URL}/api/medical-case/ranking/efficiency/",
        "data": None
    },
    
    # 症状管理API
    {
        "name": "获取自定义症状API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmchat/get_custom_symptoms/",
        "data": None
    },
    
    # 预后系统API
    {
        "name": "执行搜索API",
        "method": "POST",
        "url": f"{BASE_URL}/api/routertest1/search_symptoms_and_recommend_therapies/",
        "data": {
            "symptoms": ["头痛", "失眠"],
            "limit": 10
        }
    },
    
    # 邀请系统API
    {
        "name": "我的邀请码API",
        "method": "GET",
        "url": f"{BASE_URL}/api/invite_api/my-code/",
        "data": None
    },
    {
        "name": "邀请记录API",
        "method": "GET",
        "url": f"{BASE_URL}/api/invite_api/records/",
        "data": None
    },
    
    # 其他功能API
    {
        "name": "干支查询API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmNLP/get_ganzhi/",
        "data": None
    },
    {
        "name": "获取当前季节API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmNLP/get_current_season/",
        "data": None
    }
]

async def main():
    """主测试函数"""
    print("🎯 第四批API并发测试开始")
    print(f"📊 测试配置: {CONCURRENT_USERS}并发用户 × {REQUESTS_PER_USER}请求/用户 = {CONCURRENT_USERS * REQUESTS_PER_USER}总请求")
    print("=" * 80)
    
    tester = ConcurrentAPITester()
    await tester.create_session()
    
    try:
        # 测试第四批API
        for api_config in BATCH_4_APIS:
            await tester.test_api_concurrent(
                api_name=api_config["name"],
                method=api_config["method"],
                url=api_config["url"],
                data=api_config["data"]
            )
            # 短暂休息避免过载
            await asyncio.sleep(1)
        
        # 生成测试报告
        print("\n" + "=" * 80)
        print("📊 第四批API并发测试报告")
        print("=" * 80)
        
        total_success = sum(r.success_count for r in tester.results)
        total_requests = len(BATCH_4_APIS) * CONCURRENT_USERS * REQUESTS_PER_USER
        overall_success_rate = (total_success / total_requests) * 100
        
        print(f"🎯 总体成功率: {total_success}/{total_requests} ({overall_success_rate:.1f}%)")
        print(f"📈 平均响应时间: {sum(r.avg_response_time for r in tester.results) / len(tester.results):.3f}s")
        
        # 详细结果
        for result in tester.results:
            success_rate = (result.success_count / (CONCURRENT_USERS * REQUESTS_PER_USER)) * 100
            status = "✅" if success_rate >= 95 else "⚠️" if success_rate >= 80 else "❌"
            print(f"{status} {result.api_name}: {success_rate:.1f}% ({result.avg_response_time:.3f}s)")
        
    finally:
        await tester.close_session()

if __name__ == "__main__":
    asyncio.run(main())
