{"scan_time": "2025-08-02T21:13:57.526421", "summary": {"total_apis": 1209, "apis_with_timer": 293, "apis_with_cache": 42, "timer_coverage": "24.2%", "cache_coverage": "3.5%"}, "details": [{"name": "concurrent_test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/test_api_stress.py", "line": 48, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "sync_request_test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/test_api_stress.py", "line": 90, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "single_refresh_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/test_frontend_refresh_scenario.py", "line": 58, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_sequential_refresh", "file": "/home/<USER>/riyue-llm/myproject/demo_api/test_token_refresh_cache.py", "line": 153, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /checkmembership/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/fix_connection_leak.py", "line": 106, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_membership_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/fix_connection_leak.py", "line": 107, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "make_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/stress_test_middleware.py", "line": 27, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "run_stress_test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/stress_test_middleware.py", "line": 69, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "make_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/simple_api_test.py", "line": 22, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_sequential_requests", "file": "/home/<USER>/riyue-llm/myproject/demo_api/simple_api_test.py", "line": 48, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_concurrent_requests", "file": "/home/<USER>/riyue-llm/myproject/demo_api/simple_api_test.py", "line": 72, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_stress_requests", "file": "/home/<USER>/riyue-llm/myproject/demo_api/simple_api_test.py", "line": 101, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_concurrent_requests", "file": "/home/<USER>/riyue-llm/myproject/demo_api/test_async_bank_apis.py", "line": 68, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "run_concurrent_test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/test_concurrent_performance.py", "line": 89, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "redirect_to_homepage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/demo_api/urls.py", "line": 21, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "__call__", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/middleware.py", "line": 14, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_cache", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/tcmNLP.py", "line": 65, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "NLPdiet", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/tcmNLP.py", "line": 152, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/tcmNLP.py", "line": 153, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_recommand", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/tcmNLP.py", "line": 228, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_ganzhi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/tcmNLP.py", "line": 310, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_static_settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 50, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "terms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 62, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "privacy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 65, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "contact", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 68, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BankView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 71, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 79, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 88, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 89, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "VoiceView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 105, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 106, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "filter_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 118, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "filter_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 128, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "filter_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 135, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_offset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 155, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ExchangeView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 171, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 173, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ActivateMembershipView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 196, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 197, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "WeChatLoginView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 243, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 244, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "checkmembership<PERSON>iew", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 263, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 264, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 290, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CheckOrRegisterUserView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 295, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 296, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 300, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AddSharePointsView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 342, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 343, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AcupointListView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 408, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 409, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AcupointDetailView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 428, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 429, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "QuestionnaireDetailView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 442, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 443, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CalculateScoresView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 472, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 473, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "SaveQuestionnaireView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 573, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 574, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CheckQuestionnaireFilledView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 598, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 600, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GetUserQuestionnairesView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 613, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 616, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GetUserInfoView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 643, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 644, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserCardView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 667, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 668, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserCardMonthView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 722, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 723, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_points", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 755, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 756, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "HealthExpRankingView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 803, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 804, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "MemberExpRankingView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 812, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 813, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyTCMQuestionView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 821, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 822, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 836, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyTCMQuestionView1", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 850, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 851, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyQuizScoreView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 855, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 857, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyQuizRankingView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 888, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 890, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserCreateView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 906, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 909, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 913, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UIDTokenObtainPairView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 934, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 938, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CustomTokenRefreshView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 959, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 960, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "HomeView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 971, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "LoginByWeixinView_new1", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 980, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 982, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "RegisterOrLoginByPhoneView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1046, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1047, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "LogoutView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1109, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1110, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "HelloView11", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1124, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1126, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": ".*_cache"}, {"name": "simple_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1132, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": ".*_cache"}, {"name": "AnalyzeQuestionnaireView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1143, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1165, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AnalyzeQuestionnaireViewcomplex", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1264, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/newapi/views/bank.py", "line": 1919, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AbstractGoalView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/vscode-remote:/ssh-remote%2B7b22686f73744e616d65223a22446a616e676fe4b8bbe69c8de58aa1e599a8227d/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/vscode-remote:/ssh-remote%2B7b22686f73744e616d65223a22446a616e676fe4b8bbe69c8de58aa1e599a8227d/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 3, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "placeholder", "file": "/home/<USER>/riyue-llm/myproject/demo_api/mini_build/elasticsearch_app/api_es.py", "line": 7, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_token_leak", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", "line": 254, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "__call__", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", "line": 439, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "_get_client_ip", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", "line": 704, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "__call__", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/middleware.py", "line": 753, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "db_pool_admin_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin.py", "line": 26, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/calculation_admin.py", "line": 64, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "changelist_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/calculation_admin.py", "line": 342, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_by_user", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/calculation_admin.py", "line": 425, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "export_calculation_results", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/calculation_admin.py", "line": 450, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_calculation_data", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/calculation_admin.py", "line": 460, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_selected_questionnaires", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/questionnaire_admin.py", "line": 53, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/questionnaire_admin.py", "line": 95, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_selected_calculations", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/questionnaire_admin.py", "line": 147, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_all_by_user", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/questionnaire_admin.py", "line": 169, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "changelist_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/questionnaire_admin.py", "line": 204, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "clear_all_calculations", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/questionnaire_admin.py", "line": 277, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_fieldsets", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/common_admin.py", "line": 39, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_list_display", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/common_admin.py", "line": 71, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ActiveAnnouncementView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/common_admin.py", "line": 88, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/common_admin.py", "line": 89, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "make_permanent_ban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/payment_admin.py", "line": 69, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "make_temporary_ban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/payment_admin.py", "line": 79, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "activate_ban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/payment_admin.py", "line": 90, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "deactivate_ban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/payment_admin.py", "line": 96, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "clear_expired_bans", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/payment_admin.py", "line": 102, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/payment_admin.py", "line": 114, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "has_delete_permission", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/payment_admin.py", "line": 118, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "unban_users", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 48, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "changelist_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 95, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "changelist_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 604, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "add_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 619, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "change_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 632, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "remove_ban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 693, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "make_permanent", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 709, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "extend_ban_30_days", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 715, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "changelist_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 749, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "add_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 764, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "change_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 777, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "remove_ban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 835, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "make_permanent", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 863, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "extend_ban_30_days", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/user_admin.py", "line": 869, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_actions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/prognosis_admin.py", "line": 439, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "custom_delete_selected", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/prognosis_admin.py", "line": 448, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/prognosis_admin.py", "line": 477, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "mark_as_verified", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/prognosis_admin.py", "line": 511, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "mark_as_public", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/prognosis_admin.py", "line": 517, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "set_creator_as_system", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/prognosis_admin.py", "line": 523, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "change_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/feedback_admin.py", "line": 111, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_model", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/feedback_admin.py", "line": 117, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_export_filename", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/feedback_admin.py", "line": 126, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_model", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/tcm_admin.py", "line": 121, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_model", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/admin/tcm_admin.py", "line": 208, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "my_api_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/rate_limiter.py", "line": 167, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wrapper", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/rate_limiter.py", "line": 172, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "login_api_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/rate_limiter.py", "line": 284, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wrapper", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/rate_limiter.py", "line": 289, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "your_api_function", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/rate_limiter.py", "line": 527, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /basic_info", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 32, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_bazi_basic_info", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 33, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /four_pillars", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 62, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_four_pillars_detail", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 63, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /shishen_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 92, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_shishen_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 93, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /shensha_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 123, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_shensha_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 124, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /dishi_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 154, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_dishi_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 155, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /special_positions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 185, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_special_positions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 186, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /nine_star_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 216, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_nine_star_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 217, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /fortune_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 247, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_fortune_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 248, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /complete_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 278, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_complete_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 279, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /shishen_with_ai", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 321, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_shishen_with_ai_interpretation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 322, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /shensha_with_ai", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 359, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_shensha_with_ai_interpretation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 360, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /fortune_with_ai", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 397, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_fortune_with_ai_interpretation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 398, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /dishi_with_ai", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 435, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_dishi_with_ai_interpretation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 436, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /nine_star_with_ai", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 473, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_nine_star_with_ai_interpretation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py", "line": 474, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "index", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/views.py", "line": 3, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "stream_bazi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/views.py", "line": 7, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /divination/interpret", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 155, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "interpret_divination", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 156, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /divination/interpret/{gua_number}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 227, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "interpret_gua_by_number", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 228, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /divination", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 281, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "perform_divination", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 282, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /divination/simple/{user_number}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 315, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_simple_divination", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 316, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /divination/gua/{gua_number}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 349, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_gua_info", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 350, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /divination/stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 371, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_divination_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 372, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /divination/stream", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 390, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /divination/stream", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 391, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "stream_divination", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 392, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /bazi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 545, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_bazi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 546, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /bazi/stream", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 634, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /bazi/stream", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 635, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "stream_bazi_advice", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 636, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /health", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 661, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "health_check", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 662, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /wuyun-liuqi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 667, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_current_wuyun_liuqi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 668, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /wuxing/current", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 694, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /test_wuxing", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 778, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /wuyun-liuqi/{date}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 825, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_date_wuyun_liuqi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/api.py", "line": 826, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "handle_divination_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/websockets/divination_consumer.py", "line": 55, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "handle_interpretation_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/websockets/divination_consumer.py", "line": 110, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AbstractGoalView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/abstractDAKA.py", "line": 19, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "dispatch", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/abstractDAKA.py", "line": 20, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/abstractDAKA.py", "line": 39, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/abstractDAKA.py", "line": 138, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "detail_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/abstractDAKA.py", "line": 179, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AbstractGoalDetailView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/abstractDAKA.py", "line": 222, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/abstractDAKA.py", "line": 223, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "EnhancedQuestionnaireDetailView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 30, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 33, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "EnhancedCalculateScoresView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 71, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 75, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "EnhancedUpdateSymptomsView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 167, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 171, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "EnhancedAnalyzeQuestionnaireView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 219, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 245, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "EnhancedGetUserQuestionnairesView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 317, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 321, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "EnhancedCheckQuestionnaireFilledView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 374, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/questionnaire_enhanced.py", "line": 378, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "OptimizedBookListView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 39, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 46, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookDetailView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 130, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 137, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookChaptersView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 170, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 177, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookSearchView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 234, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 241, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ChapterContentView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 290, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 297, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookCacheManagementView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 374, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 381, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookDownloadInfoView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 413, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 420, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookDownloadView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 452, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 459, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BatchBookDownloadView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 543, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_books_api.py", "line": 550, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/ninja_chat.py", "line": 17, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_endpoint", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/ninja_chat.py", "line": 18, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_static_settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 57, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "HighPerformanceHomeView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 180, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "dispatch", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 184, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 188, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "home_page_with_timer", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 208, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "terms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 234, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_page"}, {"name": "privacy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 238, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_page"}, {"name": "contact", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 242, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_page"}, {"name": "BankView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 245, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_page"}, {"name": "delete", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 253, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 262, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 263, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "VoiceView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 279, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 280, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "filter_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 292, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "filter_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 302, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "filter_queryset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 309, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_offset", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 329, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ExchangeView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 345, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 347, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ActivateMembershipView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 370, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 371, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "WeChatLoginView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 417, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 418, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "checkmembership<PERSON>iew", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 437, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 438, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 480, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CheckOrRegisterUserView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 485, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 486, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 490, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CheckOrRegisterUserView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 531, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 532, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 535, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AddSharePointsView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 606, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 607, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AcupointListView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 673, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 676, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AcupointDetailView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 695, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 699, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "QuestionnaireDetailView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 712, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 716, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CalculateScoresView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 745, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 746, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "SaveQuestionnaireView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 854, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 855, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CheckQuestionnaireFilledView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 879, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 881, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GetUserQuestionnairesView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 894, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 897, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GetUserInfoView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 924, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 925, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserCardView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 963, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 965, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ActivityListView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1175, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1176, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BadHabitListView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1224, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1225, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CreateCustomActivityView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1266, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1267, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserGoalView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1332, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1333, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1410, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserCardMonthView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1445, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1446, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DayActivitiesView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1535, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1536, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserStatsView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1607, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1608, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AbstractGoalRecordView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1775, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1776, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DeleteUserActivityView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1870, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1871, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserCardView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1982, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 1983, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_points", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2074, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2075, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "HealthExpRankingView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2122, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2123, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "MemberExpRankingView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2131, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2132, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyTCMQuestionView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2140, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2141, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2172, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyQuizScoreView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2206, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2208, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyQuizRankingView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2239, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2241, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UserCreateView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2257, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2260, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2264, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "UIDTokenObtainPairView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2285, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2289, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CustomTokenRefreshView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2310, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2311, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "HomeView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2322, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "LoginByWeixinView_new1", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2334, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2335, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "RegisterOrLoginByPhoneView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2442, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2443, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "RefreshTokenView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2506, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2507, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "LogoutView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2571, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2572, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "HelloView11", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2611, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2613, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": ".*_cache"}, {"name": "simple_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2619, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": ".*_cache"}, {"name": "AnalyzeQuestionnaireView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2630, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2652, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AnalyzeQuestionnaireView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2779, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2788, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AnalyzeQuestionnaireViewcomplexnew", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 2931, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 3586, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AnalyzeQuestionnaireViewcomplex", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 3728, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4383, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ActiveAnnouncementView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4505, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4506, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /EmptyRequestView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4533, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "empty_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4534, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "EmptyRequestView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4539, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4540, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4551, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_all_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4603, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_all_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py", "line": 4620, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "chat_with_deepseek", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 86, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "chat_with_deepseek", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 139, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 145, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 193, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "chat_with_deepseek", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 195, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 202, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 243, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "chat_with_douban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 249, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 256, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 299, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_logging", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 302, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_account", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 332, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_report", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 356, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analysis_with_deepseek", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 373, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 376, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 429, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analysis_with_deepseek_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 434, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 442, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 495, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "Only_analysis_with_deepseek_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 499, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 506, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 547, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analysis_with_deepseek_For_Now_advice", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 552, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 559, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 600, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analysis_with_deepseek", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 610, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 613, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 616, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analysis_with_deepseek_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 620, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 623, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 627, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "Only_analysis_with_deepseek_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 631, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 634, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 638, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analysis_with_deepseek_For_Now_advice", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 642, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 645, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 649, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analysis_with_douban_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 653, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 661, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 705, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "WeChatPayView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 730, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 731, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "WeChatPayView_new", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 849, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 850, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "WeChatPayNotifyView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 936, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 937, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wechatV3_pay", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 979, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wechatV3_pay_app", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 998, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wechatV3_pay_app", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1077, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wechatV3_notify", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1134, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wechatV3_notify", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1225, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "clear_member_status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1344, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "add_custom_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1371, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_custom_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1442, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_custom_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1483, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "StreamingDeepseekAnalysisView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1544, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1545, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AsyncTestView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1561, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_bak.py", "line": 1562, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyTCMQuestionView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 16, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 22, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 72, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "_get_questions_from_db", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 76, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyQuizScoreView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 107, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 110, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DailyQuizRankingView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 139, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/daily_tcm_questions.py", "line": 142, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /invitation/generate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 69, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "generate_invitation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 70, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /my-code", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 94, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_or_create_invitation_code", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 95, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /scan/{code}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 125, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "scan_invitation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 126, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 171, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_invitation_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/invite_views.py", "line": 172, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "db_pool_monitor_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/db_admin.py", "line": 13, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_cache", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 69, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "NLPdiet", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 146, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 219, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_recommand", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 360, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_ganzhi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 431, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_current_season", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 571, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_current_tcm_season", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 642, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_solar_term", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 833, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "TotalWeightsView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 912, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 922, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AlipayView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1209, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1250, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AlipayAuthCallbackView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1302, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1304, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1406, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CheckPaymentStatusView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1421, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1422, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1450, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookListView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1613, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1615, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookContentView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1639, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1641, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ChapterContentView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1653, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1655, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookSearchView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1672, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1673, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "OwnAIdiet", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 1747, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 2043, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_feedback", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 2160, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_feedbacks", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 2216, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_feedbacks", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmNLP_bak.py", "line": 2221, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 44, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 46, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 51, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 53, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 61, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 63, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 71, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_user_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 73, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 83, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 85, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 90, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 92, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 99, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapy_classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 101, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 126, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy_classification", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 128, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-classifications/{classification_id}/therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 141, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies_by_classification", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 143, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 168, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 170, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 333, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 335, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 353, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 355, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 372, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "like_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 374, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/rate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 382, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "rate_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 384, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 392, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapy_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 394, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 402, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 404, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 414, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 416, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 424, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 426, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 436, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 438, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "PUT /intervention-records/{record_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 479, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 481, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 519, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_user_intervention_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 521, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /intervention-records/{record_id}/complete", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 555, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "complete_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 557, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /recommend-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 605, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 607, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 632, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_therapy_usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 634, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 647, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_therapy_usage_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 649, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /effectiveness-analysis/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 659, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_therapy_effectiveness", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 661, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-statistics", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 682, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_statistics", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 684, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-effectiveness-analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 741, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_personal_effectiveness_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_refactored.py", "line": 743, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 39, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 41, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 46, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 48, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 56, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 58, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 66, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_user_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 68, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 78, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 80, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 85, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 87, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 94, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 96, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 101, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 103, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 111, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "like_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 113, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/rate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 121, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "rate_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 123, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 131, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapy_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 133, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 141, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 143, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 153, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 155, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 163, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 165, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /recommend-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 175, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 177, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 202, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_therapy_usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 204, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 217, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_therapy_usage_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 219, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /effectiveness-analysis/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 229, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_therapy_effectiveness", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views.py", "line": 231, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 55, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 57, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 89, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 91, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 111, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_user_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 113, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 153, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 155, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 183, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 185, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 303, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /therapies/{therapy_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 371, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "like_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 373, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/rate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 416, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "rate_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 418, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 461, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapy_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 463, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 517, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 519, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 556, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 558, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 599, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 601, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 634, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 636, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 669, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_user_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 671, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /recommend-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 703, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 705, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 914, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_therapy_usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 916, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /effectiveness-analysis/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 1007, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_therapy_effectiveness", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 1009, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 1140, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_therapy_usage_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis/prognosis_views_backup.py", "line": 1142, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 29, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 31, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /posts/{post_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 99, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 101, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "PUT /posts/{post_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 149, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 151, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /posts/{post_id}/verify", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 181, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "verify_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 183, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 204, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/post_views.py", "line": 206, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /sections", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/section_views.py", "line": 27, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_sections", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/section_views.py", "line": 29, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /sections/{section_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/section_views.py", "line": 63, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_section", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/section_views.py", "line": 65, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /sections/{section_id}/posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/section_views.py", "line": 98, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_section_posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/section_views.py", "line": 100, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /posts/{post_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/comment_views.py", "line": 49, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/comment_views.py", "line": 51, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /posts/{post_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/comment_views.py", "line": 115, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/comment_views.py", "line": 117, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /posts/{post_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 35, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "like_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 37, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "DELETE /posts/{post_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 128, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "unlike_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 130, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /posts/{post_id}/collect", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 212, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "collect_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 214, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "DELETE /posts/{post_id}/collect", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 309, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "uncollect_post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 311, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user/collected-posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 394, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_collected_posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 396, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user/liked-posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 471, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_liked_posts", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 473, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /comments/{comment_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 549, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "like_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 551, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "DELETE /comments/{comment_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 613, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "unlike_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/forum/interaction_views.py", "line": 615, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /chat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 39, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "chat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 42, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /chat_YiAnStudy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 148, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "medical_case_study_chat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 151, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /chat_YiAnPingfen/{case_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 275, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "medical_case_scoring_chat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 278, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /medical_case_exp_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 460, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /medical_case_cases_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 501, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /medical_case_total_score_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 542, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /medical_case_avg_score_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 583, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /medical_case_time_efficiency_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 624, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /medical_case_user_ranking/{ranking_type}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 665, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /medical_case_user_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 708, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_medical_case_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 710, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /rate_limit_status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 823, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_rate_limit_status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/chat/chat_views.py", "line": 825, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 44, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 46, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 51, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 53, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 61, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 63, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 71, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_user_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 73, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 83, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 85, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 90, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 92, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 99, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapy_classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 101, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 173, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy_classification", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 175, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-classifications/{classification_id}/therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 202, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies_by_classification", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 204, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 253, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 255, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 465, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 467, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 503, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 505, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 547, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "like_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 549, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/rate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 557, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "rate_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 559, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 567, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapy_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 569, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 577, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 579, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 589, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 591, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 599, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 601, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 611, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 613, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "PUT /intervention-records/{record_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 654, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 656, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 694, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_user_intervention_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 696, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /intervention-records/{record_id}/complete", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 730, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "complete_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 732, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /recommend-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 780, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 782, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 807, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_therapy_usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 809, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 822, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_therapy_usage_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 824, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /effectiveness-analysis/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 834, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_therapy_effectiveness", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 836, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-statistics", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 857, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_statistics", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 859, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-effectiveness-analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 917, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_personal_effectiveness_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_refactored.py", "line": 919, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 39, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 41, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 46, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 48, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 56, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 58, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 66, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_user_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 68, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 78, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 80, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 85, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 87, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 94, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 96, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 101, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 103, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 111, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "like_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 113, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/rate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 121, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "rate_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 123, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 131, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapy_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 133, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 141, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "create_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 143, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 153, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 155, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 163, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_user_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 165, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /recommend-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 175, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 177, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 202, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_therapy_usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 204, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 217, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_therapy_usage_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 219, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /effectiveness-analysis/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 229, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_therapy_effectiveness", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views.py", "line": 231, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 55, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 57, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 89, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 91, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 111, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_user_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 113, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 153, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 155, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 183, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 185, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 303, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /therapies/{therapy_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 371, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "like_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 373, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/rate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 416, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "rate_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 418, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 461, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapy_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 463, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 517, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 519, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 556, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "save_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 558, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 599, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_constitution", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 601, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 634, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 636, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 669, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_user_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 671, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /recommend-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 703, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 705, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 914, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_therapy_usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 916, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /effectiveness-analysis/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 1007, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_therapy_effectiveness", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 1009, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-therapy-usage", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 1140, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_therapy_usage_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/prognosis重构！/prognosis_views_backup.py", "line": 1142, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /add_health_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/health/health_record_views.py", "line": 17, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "add_user_health_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/health/health_record_views.py", "line": 19, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /get_health_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/health/health_record_views.py", "line": 102, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_health_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/health/health_record_views.py", "line": 104, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "DELETE /delete_health_record/{record_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/health/health_record_views.py", "line": 309, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_health_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/modules/health/health_record_views.py", "line": 311, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "TotalWeightsView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/weight_calculation.py", "line": 28, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/weight_calculation.py", "line": 33, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_cache", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/utils.py", "line": 10, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookListView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 175, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 179, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookContentView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 209, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 213, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "ChapterContentView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 231, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 235, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "BookSearchView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 255, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/books.py", "line": 258, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "NLPdiet", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/diet_recommendation.py", "line": 29, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/diet_recommendation.py", "line": 81, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "OwnAIdiet", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/diet_recommendation.py", "line": 219, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/diet_recommendation.py", "line": 467, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_recommand", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/diet_recommendation.py", "line": 480, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_client_ip", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 33, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AlipayView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 42, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 82, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "AlipayAuthCallbackView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 135, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 138, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 375, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "CheckPaymentStatusView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 387, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 390, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/payment.py", "line": 414, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_feedback", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/feedback.py", "line": 20, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_feedbacks", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/feedback.py", "line": 77, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_feedbacks", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/feedback.py", "line": 84, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_ganzhi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/calendar_system.py", "line": 71, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_current_season", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/calendar_system.py", "line": 159, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_current_tcm_season", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/calendar_system.py", "line": 231, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_solar_term", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcm_nlp/calendar_system.py", "line": 366, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /chat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/chat/chat_views.py", "line": 27, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "chat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/chat/chat_views.py", "line": 28, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_report", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/utilities/views.py", "line": 5, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "test_logging", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/utilities/views.py", "line": 45, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "AsyncTestView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/utilities/views.py", "line": 99, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/utilities/views.py", "line": 106, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/utilities/views.py", "line": 158, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "chat_with_deepseek", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_chat/views.py", "line": 6, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_get_requests"}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_chat/views.py", "line": 13, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "cache_get_requests"}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_chat/views.py", "line": 82, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "chat_with_douban", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_chat/views.py", "line": 107, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_get_requests"}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_chat/views.py", "line": 114, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "cache_get_requests"}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_chat/views.py", "line": 159, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "analysis_with_deepseek", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 6, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_get_requests"}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 13, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "cache_get_requests"}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 63, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "analysis_with_deepseek_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 85, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "cache_get_requests"}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 92, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "cache_get_requests"}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 143, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "Only_analysis_with_deepseek_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 164, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 171, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 200, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "analysis_with_deepseek_For_Now_advice", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 233, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 240, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 289, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "analysis_with_douban_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 313, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 320, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 372, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "StreamingDeepseekAnalysisView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 397, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 404, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/ai_analysis/views.py", "line": 459, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "delete_account", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/user_management/views.py", "line": 7, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "add_custom_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/user_management/views.py", "line": 38, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_custom_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/user_management/views.py", "line": 131, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "smart_cache"}, {"name": "delete_custom_symptom", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/user_management/views.py", "line": 255, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_all_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/user_management/views.py", "line": 296, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "cached_get", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/base/decorators.py", "line": 147, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "WeChatPayView_new", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 9, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 10, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "WeChatPayNotifyView", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 127, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "post", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 128, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wechatV3_pay", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 152, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "payment_config", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 268, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "order_query", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 334, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "smart_cache"}, {"name": "payment_callback", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 421, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "wechatV3_pay_app", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 488, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "wechatV3_notify", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 661, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_client_ip", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 671, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "clear_member_status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/views/tcmchat_refactored/payment/views.py", "line": 854, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /categories", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", "line": 21, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /prompts/available", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", "line": 94, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /prompts/content/{category}/{version}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", "line": 135, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /prompts/reload", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", "line": 183, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /manager", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", "line": 208, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "prompt_manager_page", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/bazi_analysis_api.py", "line": 209, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_id_from_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_utils_v3_enhanced.py", "line": 321, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "authenticate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 92, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /checkmembership/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 145, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_membership_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 148, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /empty-request/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 214, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "empty_request_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 216, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-goal/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 221, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "user_goal_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 223, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /activity-list/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 253, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "activity_list_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 255, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /badhabit-list/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 336, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "badhabit_list_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 338, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-stats/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 409, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "user_stats_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 411, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /abstract-goal/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 576, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "abstract_goal_list_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 578, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /abstract-goal/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 621, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "abstract_goal_create_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 623, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-card-month/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 769, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "user_card_month_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 771, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /day-activities/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 885, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "day_activities_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 887, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /check-register-user/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 968, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_or_register_user_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis.py", "line": 970, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /pool-stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 31, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_pool_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 33, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /reset-pool", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 50, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "reset_pool_sync", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 52, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /health-check", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 77, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "health_check_sync", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 79, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /handle-timeout", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 93, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "handle_timeout_sync", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 95, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /async-health-check", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 113, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "async_health_check", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 115, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /async-reset-pool", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 133, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "async_reset_pool", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 135, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /async-handle-timeout", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 160, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "async_handle_timeout", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 162, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /start-monitor", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 180, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "start_monitor_endpoint", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 182, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /stop-monitor", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 208, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "stop_monitor_endpoint", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/db_health_api.py", "line": 210, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_id_from_request", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_utils.py", "line": 331, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /refresh-token", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py", "line": 142, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "refresh_token_api", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py", "line": 145, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /wechat-login", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py", "line": 287, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "wechat_login_api", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py", "line": 291, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /phone-login", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py", "line": 427, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "phone_login_api", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py", "line": 430, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /daily-tcm-questions/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 121, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /daily-tcm-questions/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 228, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /daily-quiz-score/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 331, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "submit_daily_quiz_score_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 334, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /tcm-categories/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 431, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_tcm_categories_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 434, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /question-stats/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 485, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_question_stats_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 488, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /quiz-ranking/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 553, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /user-quiz-history/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 675, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /challenge-overview/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 906, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_challenge_overview_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 909, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /challenge-levels/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 992, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /challenge-level/{level_id}/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1117, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_challenge_level_questions_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1120, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /challenge-level/{level_id}/submit/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1190, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "submit_challenge_level_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1193, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /challenge-ranking/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1304, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /challenge-user-history/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1472, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /challenge-stats/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1603, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_challenge_system_stats_async", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/daily_tcm_question_apis.py", "line": 1606, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", "line": 165, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "placeholder_test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", "line": 166, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /endpoint", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", "line": 302, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "your_endpoint", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/__init__.py", "line": 303, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_membership_v3", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis_v3.py", "line": 54, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "activity_list_v3", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis_v3.py", "line": 120, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "user_stats_v3", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/async_bank_apis_v3.py", "line": 198, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /messages/list", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 26, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_messages_list", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 28, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /messages/send", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 67, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "send_message", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 69, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "PATCH /messages/{message_id}/read", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 91, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "mark_message_read", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 93, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /config/settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 113, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_config_settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 115, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "PUT /config/settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 146, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_config_settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 148, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /stats/summary", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 175, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_stats_summary", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest3/test_api.py", "line": 177, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /health-score", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 22, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_health_score_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 26, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /online-time", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 80, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_online_time_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 84, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /cultivation-days", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 138, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_cultivation_days_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 142, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /level", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 196, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_level_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 200, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /generate", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 254, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "generate_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 258, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /all-types", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 271, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_all_ranking_types", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 275, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /user-positions/{user_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 309, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_positions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 314, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /top-performers", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 374, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_top_performers", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 378, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /history/{ranking_type}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 430, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_ranking_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/rankings.py", "line": 434, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /list", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 25, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_achievements_list", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 29, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /user-achievements", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 69, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /achievement-detail/{achievement_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 174, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_achievement_detail", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 179, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /check-achievements", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 247, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_user_achievements", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 252, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /leaderboard", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 286, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_achievement_leaderboard", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 290, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /types", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 339, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_achievement_types", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 343, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /recent", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 377, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_recent_achievements", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/achievements.py", "line": 382, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /start-session", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 24, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "start_online_session", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 29, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /heartbeat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 60, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "send_heartbeat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 65, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /end-session/{session_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 78, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "end_online_session", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 83, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /active-sessions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 105, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_active_sessions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 110, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /recent-sessions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 137, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_recent_sessions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 142, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 169, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_online_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 174, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /daily-stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 228, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_daily_online_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 233, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /batch-end-sessions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 289, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "batch_end_sessions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 294, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /session-detail/{session_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 316, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_session_detail", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 321, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /platform-stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 348, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_platform_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/online_tracking.py", "line": 353, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 31, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_cultivation_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 32, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /profile", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 53, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_profile", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 54, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /daily-checkin", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 90, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "daily_checkin", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 91, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /record-login-time", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 119, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_login_time", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 120, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /record-module-time", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 147, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "record_module_time", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 148, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /start-focus-session", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 176, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "start_focus_session", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 177, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /complete-focus-session", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 199, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "complete_focus_session", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 200, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /realms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 228, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_cultivation_realms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 229, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /achievements", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 259, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_cultivation_achievements", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 260, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /rankings/exp", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 292, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_exp_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 293, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /rankings/focus", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 312, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_focus_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 313, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /rankings/health", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 332, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_health_score_ranking", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/cultivation/views/cultivation.py", "line": 333, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /data/summary", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 25, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_data_summary", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 27, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /data/values", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 51, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_data_values", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 53, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /data/record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 76, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "add_data_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 78, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /data/trends", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 99, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_data_trends", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest2/test_api.py", "line": 101, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /bazi/basic", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 283, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_bazi_basic_info", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 288, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "POST /bazi/shishen", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 323, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_bazi_shishen_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 328, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "POST /bazi/shensha", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 360, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_bazi_shensha_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 365, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "POST /bazi/dayun", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 397, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_bazi_dayun_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 402, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "GET /wuyun-liuqi/current", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 434, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_current_wuyun_liuqi", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 439, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "POST /bazi/complete", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 484, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_bazi_complete_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/bazi_analysis_api.py", "line": 489, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "get_therapy_users_intervention_records_optimized", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api_optimized.py", "line": 251, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "get_cache_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api_optimized.py", "line": 317, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "clear_cache", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api_optimized.py", "line": 336, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "invalidate_cache", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api_optimized.py", "line": 355, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-users-intervention-records-similarity", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 421, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_users_intervention_records_by_similarity", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 424, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /constitution-similarity-analysis/{therapy_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 621, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_constitution_similarity_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 624, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /symptom-search", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 2746, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "search_symptoms_and_recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 2749, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /search-suggestions", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3069, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /search", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3159, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "search_symptoms_and_recommend_therapies_compat", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3162, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /popular-keywords", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3205, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_popular_keywords", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3208, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-categories-stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3382, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_categories_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3385, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /personalized-symptom-search", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3763, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "personalized_search_symptoms_and_recommend_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py", "line": 3766, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "list_therapy_classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/cache_decorators.py", "line": 148, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "get_therapy_details", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/cache_decorators.py", "line": 156, "has_timer": false, "has_cache": true, "timer_type": null, "cache_type": "prognosis_cache"}, {"name": "POST /wuyun-liuqi-health/analyze", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 49, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_wuyun_liuqi_health", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 50, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /wuyun-liuqi-health/current", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 137, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_current_wuyun_liuqi_info", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 138, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /wuyun-liuqi-health/status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 163, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_wuyun_liuqi_system_status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 164, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /wuyun-liuqi-health/template", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 183, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_prompt_template", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 184, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /wuyun-liuqi-health/test", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 207, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "test_wuyun_liuqi_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 208, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /wuyun-liuqi-health/debug", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 237, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "debug_wuyun_liuqi_analysis", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/wuyun_liuqi_health_api.py", "line": 238, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 315, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 318, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/comments", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 410, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 413, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "DELETE /therapies/{therapy_id}/comments/{comment_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 479, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_therapy_comment", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 482, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 516, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "toggle_therapy_like", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 519, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/like-status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 599, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_like_status", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 602, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapies/{therapy_id}/rating", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 666, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "submit_therapy_rating", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 669, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapies/{therapy_id}/rating-stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 748, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_rating_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 751, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 836, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy_usage_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 839, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-therapy-usage-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1046, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_my_therapy_usage_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1049, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1302, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1305, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /my-intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1389, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_my_intervention_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1392, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /users/{target_user_id}/intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1487, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_intervention_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1490, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "PUT /intervention-records/{record_id}", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1618, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_intervention_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1621, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /therapy-usage-records-debug", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1695, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "debug_therapy_usage_record", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1698, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-users-intervention-records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1851, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy_users_intervention_records", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_interaction_api.py", "line": 1854, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 66, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapy_classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 70, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "classification_cache"}, {"name": "POST /therapy-classifications", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 204, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy_classification", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 207, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /therapy-classifications/{classification_id}/therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 247, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies_by_classification", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 251, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "therapy_list_cache"}, {"name": "GET /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 389, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 393, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "therapy_list_cache"}, {"name": "GET /therapies/{therapy_id}/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 603, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 607, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "therapy_detail_cache"}, {"name": "POST /therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 727, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 730, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 803, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "create_user_therapy", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 806, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /user-therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 851, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "list_user_therapies", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/routertest1/prognosis_api.py", "line": 855, "has_timer": true, "has_cache": true, "timer_type": "api_timer", "cache_type": "user_therapy_cache"}, {"name": "GET /health/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/db_health_api.py", "line": 21, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "database_health_check", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/db_health_api.py", "line": 23, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /stats/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/db_health_api.py", "line": 63, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "connection_stats", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/db_health_api.py", "line": 65, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /reset/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/db_health_api.py", "line": 92, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "reset_connection", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/db_health_api.py", "line": 94, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /v1/questionnaire/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/basic.py", "line": 27, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /questionnaire/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/basic.py", "line": 28, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /get_questionnaire/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/basic.py", "line": 29, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_questionnaire", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/basic.py", "line": 32, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /check_questionnaire_filled", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/basic.py", "line": 98, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /check_questionnaire_filled/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/basic.py", "line": 99, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "check_questionnaire_filled", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/basic.py", "line": 102, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /v1/update_user_info/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 29, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /update_user_info/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 30, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_user_info", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 33, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /v1/update_symptoms/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 130, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /update_symptoms/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 131, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_symptoms", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 134, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /v1/user_info/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 195, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /user_info/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 196, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_info", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/user_info.py", "line": 199, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /v1/calculate_scores/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/questionnaires.py", "line": 59, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /calculate_scores/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/questionnaires.py", "line": 60, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "calculate_scores", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/questionnaires.py", "line": 64, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /v1/get_user_questionnaires/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/questionnaires.py", "line": 193, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /get_user_questionnaires/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/questionnaires.py", "line": 194, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /v1/calculation_histories/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 36, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /calculation_histories/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 37, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_calculation_histories", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 40, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /v1/calculation_history/{history_id}/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 216, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /calculation_history/{history_id}/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 217, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_calculation_history_detail", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 220, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "DELETE /v1/calculation_history/{history_id}/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 453, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "DELETE /calculation_history/{history_id}/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 454, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "delete_calculation_history", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation_histories.py", "line": 457, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /calculate_constitution_indicators/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation.py", "line": 38, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "calculate_constitution_indicators", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation.py", "line": 41, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /v1/analyze_questionnaire/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation.py", "line": 293, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /analyze_questionnaire/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation.py", "line": 294, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "analyze_questionnaire", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation.py", "line": 297, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "POST /v1/get_latest_calculation/", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation.py", "line": 502, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_latest_calculation", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/questionnaire/views/calculation.py", "line": 505, "has_timer": true, "has_cache": false, "timer_type": "api_timer", "cache_type": null}, {"name": "GET /users/profile", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/user_management/user_api.py", "line": 24, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_user_profile", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/user_management/user_api.py", "line": 26, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "GET /users/list", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/user_management/user_api.py", "line": 48, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_users_list", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/user_management/user_api.py", "line": 50, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "POST /users/settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/user_management/user_api.py", "line": 69, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "update_user_settings", "file": "/home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/user_management/user_api.py", "line": 71, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "placeholder", "file": "/home/<USER>/riyue-llm/myproject/demo_api/elasticsearch_app_placeholder/api_es.py", "line": 7, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "_wrapped_view", "file": "/home/<USER>/riyue-llm/myproject/demo_api/elasticsearch_app/api_es.py", "line": 62, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "hello", "file": "/home/<USER>/riyue-llm/myproject/demo_api/elasticsearch_app/api_es.py", "line": 142, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}, {"name": "get_drug_detail", "file": "/home/<USER>/riyue-llm/myproject/demo_api/elasticsearch_app/api_es.py", "line": 609, "has_timer": false, "has_cache": false, "timer_type": null, "cache_type": null}]}