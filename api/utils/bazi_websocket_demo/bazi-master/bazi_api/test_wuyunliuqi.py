#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
五运六气系统测试脚本
用于测试complete_wuyunliuqi模块的导入和功能
"""

import os
import sys
import json
import traceback
import importlib.util
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 查找项目根目录
project_root = current_dir
# 尝试找到正确的项目根目录
while project_root and not os.path.exists(os.path.join(project_root, 'bazi_api', 'bazi_api', 'complete_wuyunliuqi')):
    parent = os.path.dirname(project_root)
    if parent == project_root:  # 已经到达文件系统根目录
        break
    project_root = parent

print(f"当前目录: {current_dir}")
print(f"项目根目录: {project_root}")

# 检查目录结构
module_path = os.path.join(project_root, 'bazi_api', 'bazi_api', 'complete_wuyunliuqi')
print(f"模块路径: {module_path}")
print(f"路径是否存在: {os.path.exists(module_path)}")

if os.path.exists(module_path):
    print("目录内容:")
    for item in os.listdir(module_path):
        print(f"  - {item}")

# 尝试找到完整五运六气模块的可能路径
wuyunliuqi_module_paths = [
    "bazi_api.bazi_api.complete_wuyunliuqi.great_contribution",
    "bazi_api.complete_wuyunliuqi.great_contribution",
    "complete_wuyunliuqi.great_contribution",
    os.path.join(project_root, "bazi_api", "bazi_api", "complete_wuyunliuqi", "great_contribution.py")
]

# 测试cnlunar库
try:
    import cnlunar
    print("cnlunar库导入成功")
    print(f"cnlunar版本: {getattr(cnlunar, '__version__', '未知')}")
    
    # 测试cnlunar基本功能
    try:
        today = datetime.now()
        lunar = cnlunar.Lunar(today)
        print(f"今天的农历: {lunar.lunarYearCn}{lunar.lunarMonthCn}{lunar.lunarDayCn}")
        print(f"今天的干支: {lunar.year8Char} {lunar.month8Char} {lunar.day8Char}")
        print(f"今天的节气: {lunar.todaySolarTerms}")
        print(f"下一个节气: {lunar.nextSolarTerm}")
    except Exception as e:
        print(f"cnlunar测试失败: {str(e)}")
        print(traceback.format_exc())
except ImportError:
    print("cnlunar库导入失败，请先安装: pip install cnlunar>=0.2.2")

# 尝试多种导入方式
for import_path in wuyunliuqi_module_paths:
    print("\n" + "="*60)
    print(f"测试导入路径: {import_path}")
    print("="*60)
    try:
        if import_path.endswith('.py'):
            # 直接从文件路径导入
            print(f"尝试从文件导入: {import_path}")
            if os.path.exists(import_path):
                spec = importlib.util.spec_from_file_location("great_contribution", import_path)
                wuyunliuqi_core = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(wuyunliuqi_core)
                print(f"成功从文件路径导入: {import_path}")
            else:
                print(f"文件不存在: {import_path}")
                continue
        else:
            # 从模块路径导入
            print(f"尝试导入模块: {import_path}")
            wuyunliuqi_core = importlib.import_module(import_path)
            print(f"成功导入模块: {import_path}")
        
        # 验证导入是否正确
        print(f"导入成功，模块: {wuyunliuqi_core}")
        print(f"模块位置: {getattr(wuyunliuqi_core, '__file__', '未知')}")
        print(f"模块属性: {dir(wuyunliuqi_core)}")
        
        # 需要确认模块中有getWuYunLiuQi函数
        if not hasattr(wuyunliuqi_core, 'getWuYunLiuQi'):
            print("错误: 模块中没有getWuYunLiuQi函数")
            continue
        
        # 测试函数可用性
        test_dates = [
            "2023-01-01", 
            "2023-05-01", 
            "2023-08-01", 
            "2023-12-01", 
            "2024-04-24", 
            "2025-07-15"
        ]
        for test_date in test_dates:
            print(f"\n测试日期: {test_date}")
            try:
                test_result = wuyunliuqi_core.getWuYunLiuQi(test_date)
                # 尝试解析JSON结果
                try:
                    result_data = json.loads(test_result)
                    print(f"五运六气系统测试成功，结果: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
                except json.JSONDecodeError:
                    print(f"五运六气结果不是有效JSON: {test_result[:100]}...")
                
                print(f"**** 成功测试日期 {test_date} ****")
            except Exception as test_e:
                print(f"测试日期 {test_date} 失败: {str(test_e)}")
                print(f"错误详情: {traceback.format_exc()}")
        
        print("\n导入和功能测试完成")
    except ImportError as e:
        print(f"导入路径 {import_path} 失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
    except Exception as e:
        print(f"尝试导入 {import_path} 时出现未知错误: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")

print("\n测试完成") 