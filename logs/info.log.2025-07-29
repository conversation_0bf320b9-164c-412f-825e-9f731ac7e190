INFO 2025-07-29 00:12:19,640 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 90
INFO 2025-07-29 00:12:19,811 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 91
WARNING 2025-07-29 00:12:20,020 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:12:20,067 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:12:36,506 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:12:39,553 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 92
INFO 2025-07-29 00:12:39,561 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 93
WARNING 2025-07-29 00:12:39,909 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:12:39,957 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:13:30,106 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:13:33,297 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 94
INFO 2025-07-29 00:13:33,696 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 95
WARNING 2025-07-29 00:13:34,207 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:13:34,255 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:24:41,818 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 00:28:27,803 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 96
INFO 2025-07-29 00:28:27,816 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 97
INFO 2025-07-29 00:28:28,286 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 98
INFO 2025-07-29 00:28:31,303 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 99
INFO 2025-07-29 00:34:52,130 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 100
INFO 2025-07-29 00:34:52,340 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 101
WARNING 2025-07-29 00:34:52,588 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:34:52,635 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:36:51,127 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 00:37:20,429 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 102
INFO 2025-07-29 00:37:20,450 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 103
INFO 2025-07-29 00:37:21,260 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 104
INFO 2025-07-29 00:37:23,744 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 105
INFO 2025-07-29 00:37:27,885 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 106
INFO 2025-07-29 00:37:27,933 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 107
WARNING 2025-07-29 00:37:28,316 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:37:28,364 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:39:39,063 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:39:42,303 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 108
INFO 2025-07-29 00:39:42,543 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 109
WARNING 2025-07-29 00:39:42,914 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:39:42,962 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:41:38,586 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:41:41,795 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 110
INFO 2025-07-29 00:41:41,977 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 111
WARNING 2025-07-29 00:41:42,114 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:41:42,162 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:43:20,546 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:43:23,699 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 112
INFO 2025-07-29 00:43:23,916 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 113
WARNING 2025-07-29 00:43:24,071 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:43:24,119 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:43:52,773 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:43:55,846 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 114
INFO 2025-07-29 00:43:55,939 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 115
WARNING 2025-07-29 00:43:56,207 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:43:56,255 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:44:14,173 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:44:17,281 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 116
INFO 2025-07-29 00:44:17,328 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 117
WARNING 2025-07-29 00:44:17,669 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:44:17,717 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:46:30,162 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:46:33,323 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 118
INFO 2025-07-29 00:46:33,503 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 119
WARNING 2025-07-29 00:46:33,648 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:46:33,696 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:47:37,411 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:48:01,084 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 120
INFO 2025-07-29 00:48:01,213 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 121
WARNING 2025-07-29 00:48:01,414 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:48:01,461 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:49:13,504 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:49:16,651 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 122
INFO 2025-07-29 00:49:16,832 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 123
WARNING 2025-07-29 00:49:17,382 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:49:17,430 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:49:58,961 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:50:01,979 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 124
INFO 2025-07-29 00:50:01,996 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 125
WARNING 2025-07-29 00:50:02,393 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:50:02,441 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:52:24,137 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:52:27,348 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 126
INFO 2025-07-29 00:52:27,525 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 127
WARNING 2025-07-29 00:52:27,762 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:52:27,809 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:52:38,781 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:52:41,978 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 128
INFO 2025-07-29 00:52:42,029 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 129
WARNING 2025-07-29 00:52:42,400 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:52:42,447 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:52:53,509 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:52:56,548 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 130
INFO 2025-07-29 00:52:56,580 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 131
WARNING 2025-07-29 00:52:56,944 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:52:56,991 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:53:10,880 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:53:14,058 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 132
INFO 2025-07-29 00:53:14,089 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 133
WARNING 2025-07-29 00:53:14,472 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:53:14,520 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 00:53:29,126 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:53:32,266 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 134
INFO 2025-07-29 00:53:32,324 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 135
WARNING 2025-07-29 00:53:32,610 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:53:32,658 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 00:53:47,394 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 00:53:50,503 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 136
INFO 2025-07-29 00:53:50,565 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 137
WARNING 2025-07-29 00:53:50,902 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 00:53:50,950 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:02:22,178 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:02:25,389 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 138
INFO 2025-07-29 01:02:25,574 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 139
WARNING 2025-07-29 01:02:25,713 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:02:25,761 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:02:52,639 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:02:55,674 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 140
INFO 2025-07-29 01:02:55,724 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 141
WARNING 2025-07-29 01:02:56,039 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:02:56,087 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:04:04,939 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 01:06:21,606 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 142
INFO 2025-07-29 01:06:21,770 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 143
WARNING 2025-07-29 01:06:22,072 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:06:22,120 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 01:15:11,395 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:15:14,635 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 144
WARNING 2025-07-29 01:15:25,092 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:15:25,140 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:15:31,900 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 145
INFO 2025-07-29 01:15:54,215 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:16:18,505 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 146
INFO 2025-07-29 01:16:18,696 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 147
WARNING 2025-07-29 01:16:19,067 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:16:19,115 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:16:41,483 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:16:44,417 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 148
INFO 2025-07-29 01:16:44,436 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 149
WARNING 2025-07-29 01:16:44,786 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:16:44,833 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 01:17:37,516 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:17:40,605 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 150
INFO 2025-07-29 01:17:40,649 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 151
WARNING 2025-07-29 01:17:40,958 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:17:41,005 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:18:26,016 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:18:29,243 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 152
INFO 2025-07-29 01:18:29,293 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 153
WARNING 2025-07-29 01:18:29,594 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:18:29,642 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:18:46,755 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:18:49,873 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 154
INFO 2025-07-29 01:18:49,929 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 155
WARNING 2025-07-29 01:18:50,246 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:18:50,294 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:19:07,974 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:19:11,000 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 156
INFO 2025-07-29 01:19:11,051 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 157
WARNING 2025-07-29 01:19:11,339 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:19:11,386 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 01:19:34,439 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:19:37,605 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 158
INFO 2025-07-29 01:19:37,663 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 159
WARNING 2025-07-29 01:19:37,979 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:19:38,027 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:25:05,116 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:25:08,325 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 160
INFO 2025-07-29 01:25:08,470 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 161
WARNING 2025-07-29 01:25:08,686 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:25:08,734 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:25:32,789 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:25:35,851 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 162
INFO 2025-07-29 01:25:35,877 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 163
WARNING 2025-07-29 01:25:36,141 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:25:36,189 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:25:40,111 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:25:43,083 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 164
INFO 2025-07-29 01:25:43,101 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 165
WARNING 2025-07-29 01:25:43,404 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:25:43,452 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:29:23,513 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:29:26,765 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 166
INFO 2025-07-29 01:29:27,037 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 167
WARNING 2025-07-29 01:29:27,122 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:29:27,170 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:40:52,761 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 01:44:02,735 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 168
INFO 2025-07-29 01:44:02,881 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 169
WARNING 2025-07-29 01:44:03,067 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:44:03,114 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:44:24,467 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:44:27,547 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 170
INFO 2025-07-29 01:44:27,585 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 171
WARNING 2025-07-29 01:44:27,917 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:44:27,964 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 01:45:00,354 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:45:03,457 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 172
INFO 2025-07-29 01:45:03,489 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 173
WARNING 2025-07-29 01:45:03,745 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:45:03,793 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:52:44,512 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:53:08,117 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 174
INFO 2025-07-29 01:53:08,305 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 175
WARNING 2025-07-29 01:53:08,444 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:53:08,491 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:53:54,118 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 01:53:57,199 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 176
INFO 2025-07-29 01:53:57,246 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 177
WARNING 2025-07-29 01:53:57,542 base 3363624 140070837526528 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 01:53:57,590 base 3363624 140070837526528 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 01:57:07,008 rate_limiter 3363624 140070837526528 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 178
INFO 2025-07-29 01:58:19,385 ark_chat_consumer 3363624 140070837526528 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 02:01:51,491 rate_limiter 3429063 140088416182272 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 179
INFO 2025-07-29 02:01:51,502 rate_limiter 3429063 140088416182272 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 180
INFO 2025-07-29 02:01:51,668 rate_limiter 3429063 140088416182272 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 181
ERROR 2025-07-29 05:04:41,742 exception 3429063 140087156942400 Invalid HTTP_HOST header: '0.0.0.0'. You may need to add '0.0.0.0' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '0.0.0.0'. You may need to add '0.0.0.0' to ALLOWED_HOSTS.
INFO 2025-07-29 14:00:38,960 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 1
INFO 2025-07-29 14:00:39,215 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 2
WARNING 2025-07-29 14:00:41,464 base 3572934 140718880907264 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 14:00:41,511 base 3572934 140718880907264 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 14:01:19,863 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户1(每周) API:chat_api 调用次数: 2
INFO 2025-07-29 14:01:26,273 base 3572934 140718880907264 ✅ [ARK_CHAT] 消息处理完成，用户ID: 1，耗时: 6.430秒
INFO 2025-07-29 14:07:03,961 ark_chat_consumer 3572934 140718880907264 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 14:13:17,908 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 3
INFO 2025-07-29 14:13:17,913 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 4
INFO 2025-07-29 14:13:18,237 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 5
INFO 2025-07-29 14:13:29,822 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 6
INFO 2025-07-29 14:13:29,843 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 7
INFO 2025-07-29 14:13:30,509 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 8
INFO 2025-07-29 14:14:00,334 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 9
INFO 2025-07-29 14:14:00,615 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 10
INFO 2025-07-29 14:14:00,836 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 11
INFO 2025-07-29 14:14:53,000 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 12
INFO 2025-07-29 14:14:53,034 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 13
INFO 2025-07-29 14:14:53,235 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 14
INFO 2025-07-29 14:16:26,032 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 15
INFO 2025-07-29 14:16:26,171 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 16
WARNING 2025-07-29 14:16:26,405 base 3572934 140718880907264 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 14:16:26,453 base 3572934 140718880907264 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 14:18:53,619 ark_chat_consumer 3572934 140718880907264 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 14:18:57,060 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 17
INFO 2025-07-29 14:18:57,331 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 18
WARNING 2025-07-29 14:18:57,517 base 3572934 140718880907264 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 14:18:57,565 base 3572934 140718880907264 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.049秒
INFO 2025-07-29 14:19:08,770 ark_chat_consumer 3572934 140718880907264 [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 14:20:00,603 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 19
INFO 2025-07-29 14:20:01,103 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 20
WARNING 2025-07-29 14:20:02,304 base 3572934 140718880907264 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 14:20:02,352 base 3572934 140718880907264 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 14:21:35,986 rate_limiter 3572934 140718880907264 [RATE_LIMIT] 用户1(每周) API:chat_api 调用次数: 3
INFO 2025-07-29 14:21:41,412 base 3572934 140718880907264 ✅ [ARK_CHAT] 消息处理完成，用户ID: 1，耗时: 5.447秒
INFO 2025-07-29 14:45:20,828 ark_chat_consumer 3572934 140718880907264 [ARK_CHAT] WebSocket断开连接: {'close_code': 1012, 'user_id': 1, 'disconnect_reason': '未知代码(1012)'}
INFO 2025-07-29 14:47:32,494 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 21
INFO 2025-07-29 14:47:32,653 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 22
WARNING 2025-07-29 14:47:32,831 base 3584425 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 14:47:32,880 base 3584425 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.049秒
INFO 2025-07-29 14:51:19,288 ark_chat_consumer 3584425 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 14:51:22,587 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 23
INFO 2025-07-29 14:51:22,791 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 24
WARNING 2025-07-29 14:51:23,221 base 3584425 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 14:51:23,268 base 3584425 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 14:51:39,013 ark_chat_consumer 3584425 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 14:51:42,207 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 25
INFO 2025-07-29 14:51:42,244 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 26
WARNING 2025-07-29 14:51:42,541 base 3584425 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 14:51:42,587 base 3584425 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 15:05:09,130 ark_chat_consumer 3584425 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 15:07:34,712 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 27
INFO 2025-07-29 15:07:34,882 rate_limiter 3584425 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 28
WARNING 2025-07-29 15:07:35,111 base 3584425 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 15:07:35,157 base 3584425 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 15:10:09,118 ark_chat_consumer 3584425 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 15:52:01,127 autoreload 3599461 *************** Watching for file changes with StatReloader
INFO 2025-07-29 15:52:05,563 basehttp 3599461 *************** "POST /api/auth/wechat-login HTTP/1.0" 200 403
INFO 2025-07-29 15:52:06,927 basehttp 3599461 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:52:07,025 rate_limiter 3599461 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 29
INFO 2025-07-29 15:52:07,035 basehttp 3599461 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:52:08,436 rate_limiter 3599461 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 30
INFO 2025-07-29 15:52:08,451 basehttp 3599461 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-29 15:52:08,451 basehttp 3599461 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:52:08,454 basehttp 3599461 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 15:52:08,472 basehttp 3599461 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-29 15:52:08,678 basehttp 3599461 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-29 15:52:12,572 autoreload 3599461 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py changed, reloading.
INFO 2025-07-29 15:52:13,897 autoreload 3599610 *************** Watching for file changes with StatReloader
INFO 2025-07-29 15:52:28,204 autoreload 3599610 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py changed, reloading.
INFO 2025-07-29 15:52:29,514 autoreload 3599726 *************** Watching for file changes with StatReloader
INFO 2025-07-29 15:52:43,827 autoreload 3599726 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/ninja_apis/auth_api.py changed, reloading.
INFO 2025-07-29 15:52:45,152 autoreload 3599842 *************** Watching for file changes with StatReloader
INFO 2025-07-29 15:53:02,620 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 31
INFO 2025-07-29 15:53:02,629 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 32
INFO 2025-07-29 15:53:02,632 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:02,639 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:02,743 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:03,032 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 33
INFO 2025-07-29 15:53:03,042 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:05,887 basehttp 3599842 *************** "POST /api/logout/ HTTP/1.0" 200 39
INFO 2025-07-29 15:53:11,516 rate_limiter 3599842 *************** [IP_RATE_LIMIT] IP:************** API:wechat_login_api 调用次数: 1/30
INFO 2025-07-29 15:53:11,518 basehttp 3599842 *************** "POST /api/auth/wechat-login HTTP/1.0" 200 403
INFO 2025-07-29 15:53:12,867 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:12,877 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:12,906 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 34
INFO 2025-07-29 15:53:12,916 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:13,094 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:13,124 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 35
INFO 2025-07-29 15:53:13,135 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:13,168 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:13,439 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 36
INFO 2025-07-29 15:53:13,449 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:14,908 basehttp 3599842 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 802
INFO 2025-07-29 15:53:14,910 basehttp 3599842 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 15:53:14,914 basehttp 3599842 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-29 15:53:14,950 basehttp 3599842 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 15:53:14,961 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 37
INFO 2025-07-29 15:53:14,971 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:15,092 basehttp 3599842 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-29 15:53:15,161 basehttp 3599842 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 1554
INFO 2025-07-29 15:53:15,194 basehttp 3599842 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-29 15:53:15,199 basehttp 3599842 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-29 15:53:16,399 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:16,416 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 38
INFO 2025-07-29 15:53:16,420 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:16,427 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:17,880 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 39
INFO 2025-07-29 15:53:17,888 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 40
INFO 2025-07-29 15:53:17,890 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:17,898 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:17,931 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:18,256 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 41
INFO 2025-07-29 15:53:18,266 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:53:20,310 basehttp 3599842 *************** "POST /api/logout/ HTTP/1.0" 200 39
INFO 2025-07-29 15:53:30,141 rate_limiter 3599842 *************** [IP_RATE_LIMIT] IP:************** API:phone_login_api 调用次数: 1/20
INFO 2025-07-29 15:53:30,142 basehttp 3599842 *************** "POST /api/auth/phone-login HTTP/1.0" 200 325
INFO 2025-07-29 15:53:32,860 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:32,864 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:32,893 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 1
INFO 2025-07-29 15:53:32,903 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:33,076 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:33,359 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 2
INFO 2025-07-29 15:53:33,369 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:33,382 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:33,390 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 3
INFO 2025-07-29 15:53:33,401 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:33,425 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 4
INFO 2025-07-29 15:53:33,436 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:33,446 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:34,766 basehttp 3599842 *************** "GET /api/async-bank/user-goal/ HTTP/1.0" 200 2
INFO 2025-07-29 15:53:34,823 basehttp 3599842 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 15:53:34,828 basehttp 3599842 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1147
INFO 2025-07-29 15:53:34,864 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 5
INFO 2025-07-29 15:53:34,874 basehttp 3599842 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 15:53:34,874 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:34,974 basehttp 3599842 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 182
INFO 2025-07-29 15:53:35,021 basehttp 3599842 *************** "GET /api/async-bank/abstract-goal/ HTTP/1.0" 200 2
INFO 2025-07-29 15:53:35,572 basehttp 3599842 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1147
INFO 2025-07-29 15:53:35,888 basehttp 3599842 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2256
INFO 2025-07-29 15:53:38,583 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 6
INFO 2025-07-29 15:53:38,593 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:38,632 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 7
INFO 2025-07-29 15:53:38,642 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:38,747 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:38,987 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 8
INFO 2025-07-29 15:53:38,997 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:39,803 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 9
INFO 2025-07-29 15:53:39,808 basehttp 3599842 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 15:53:39,813 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:39,857 basehttp 3599842 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 1147
INFO 2025-07-29 15:53:40,242 basehttp 3599842 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 182
INFO 2025-07-29 15:53:40,833 basehttp 3599842 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2256
INFO 2025-07-29 15:53:41,300 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:53:41,524 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 10
INFO 2025-07-29 15:53:41,535 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:51,636 rate_limiter 3599842 *************** [RATE_LIMIT] 用户32(每周) API:检查会员状态 调用次数: 11
INFO 2025-07-29 15:53:51,646 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 464
INFO 2025-07-29 15:53:57,947 basehttp 3599842 *************** "POST /api/logout/ HTTP/1.0" 200 39
INFO 2025-07-29 15:54:01,567 rate_limiter 3599842 *************** [IP_RATE_LIMIT] IP:************** API:wechat_login_api 调用次数: 2/30
INFO 2025-07-29 15:54:01,568 basehttp 3599842 *************** "POST /api/auth/wechat-login HTTP/1.0" 200 403
INFO 2025-07-29 15:54:02,952 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:54:02,979 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:54:03,035 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 42
INFO 2025-07-29 15:54:03,040 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:54:03,045 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:54:03,628 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 43
INFO 2025-07-29 15:54:03,638 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:54:24,501 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:54:24,543 basehttp 3599842 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 15:54:24,788 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 44
INFO 2025-07-29 15:54:24,800 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 15:54:24,831 basehttp 3599842 *************** "POST /api/bank/AnalyzeQuestionnaireView/ HTTP/1.0" 200 1486
INFO 2025-07-29 15:54:25,487 rate_limiter 3599842 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 45
INFO 2025-07-29 15:54:25,497 basehttp 3599842 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
WARNING 2025-07-29 15:54:29,642 basehttp 3599842 *************** "GET /api/ws/deepseek_chat/ HTTP/1.1" 404 179
INFO 2025-07-29 15:54:47,080 basehttp 3599842 *************** "POST /api/routertest1/bazi/complete HTTP/1.0" 200 5587
INFO 2025-07-29 16:10:44,406 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 46
INFO 2025-07-29 16:10:45,902 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 47
INFO 2025-07-29 16:12:32,106 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 48
INFO 2025-07-29 16:12:33,483 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 49
INFO 2025-07-29 16:12:34,392 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 50
INFO 2025-07-29 16:12:34,620 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 51
WARNING 2025-07-29 16:12:35,537 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 16:12:35,585 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 16:13:30,930 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 16:17:12,598 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 52
INFO 2025-07-29 16:17:13,082 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 53
INFO 2025-07-29 16:18:34,671 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 54
WARNING 2025-07-29 16:18:35,065 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 16:18:35,113 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 16:18:35,226 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 55
INFO 2025-07-29 16:23:07,439 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 16:23:26,689 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 56
INFO 2025-07-29 16:23:27,021 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 57
WARNING 2025-07-29 16:23:27,610 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 16:23:27,656 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 16:23:39,634 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 16:23:42,814 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 58
INFO 2025-07-29 16:23:43,256 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 59
WARNING 2025-07-29 16:23:48,731 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 16:23:48,777 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 16:24:18,210 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 16:24:21,360 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 60
INFO 2025-07-29 16:24:21,399 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 61
WARNING 2025-07-29 16:24:22,029 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 16:24:22,075 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 16:25:18,295 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 16:25:21,199 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 62
INFO 2025-07-29 16:25:21,665 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 63
WARNING 2025-07-29 16:25:23,303 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 16:25:23,350 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 16:25:36,426 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 16:25:39,647 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 64
INFO 2025-07-29 16:25:39,947 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 65
WARNING 2025-07-29 16:25:40,172 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 16:25:40,218 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 16:48:00,625 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 17:06:53,117 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 66
WARNING 2025-07-29 17:07:20,541 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:07:20,587 base 3601032 *************** ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 17:07:20,781 rate_limiter 3601032 *************** [RATE_LIMIT] 用户1(每周) API:bazi_advice_api 调用次数: 1
INFO 2025-07-29 17:07:48,123 base 3601032 *************** ✅ [BAZI_ADVICE] 消息处理完成，用户ID: 1，耗时: 27.362秒
WARNING 2025-07-29 17:09:23,396 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:09:23,444 base 3601032 *************** ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:09:23,673 rate_limiter 3601032 *************** [RATE_LIMIT] 用户1(每周) API:bazi_advice_api 调用次数: 2
INFO 2025-07-29 17:09:53,656 base 3601032 *************** ✅ [BAZI_ADVICE] 消息处理完成，用户ID: 1，耗时: 30.002秒
INFO 2025-07-29 17:09:53,677 bazi_advice_consumer 3601032 *************** [BAZI_ADVICE] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:09:53,677 bazi_advice_consumer 3601032 *************** [BAZI_ADVICE] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:09:53,745 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 67
INFO 2025-07-29 17:09:53,753 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 68
INFO 2025-07-29 17:09:54,017 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 69
INFO 2025-07-29 17:09:54,383 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 70
INFO 2025-07-29 17:09:54,514 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 71
INFO 2025-07-29 17:09:54,559 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 72
WARNING 2025-07-29 17:09:54,911 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:09:54,958 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
WARNING 2025-07-29 17:09:54,961 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:09:55,008 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
INFO 2025-07-29 17:15:00,593 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:15:00,610 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:15:04,094 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 73
INFO 2025-07-29 17:15:04,247 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 74
WARNING 2025-07-29 17:15:04,631 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:15:04,679 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:15:29,107 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:15:32,590 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 75
INFO 2025-07-29 17:15:32,663 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 76
WARNING 2025-07-29 17:15:33,059 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:15:33,107 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:15:48,321 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:15:51,790 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 77
INFO 2025-07-29 17:15:51,797 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 78
WARNING 2025-07-29 17:15:52,220 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:15:52,268 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:16:19,485 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:16:22,869 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 79
INFO 2025-07-29 17:16:22,924 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 80
WARNING 2025-07-29 17:16:23,292 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:16:23,340 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:16:41,425 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:16:44,815 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 81
INFO 2025-07-29 17:16:44,864 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 82
WARNING 2025-07-29 17:16:45,245 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:16:45,293 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:16:45,738 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:16:49,331 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 83
INFO 2025-07-29 17:16:49,402 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 84
WARNING 2025-07-29 17:16:50,109 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:16:50,157 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:17:22,529 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:17:25,942 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 85
INFO 2025-07-29 17:17:25,962 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 86
WARNING 2025-07-29 17:17:26,382 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:17:26,430 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:17:57,494 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:18:00,954 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 87
INFO 2025-07-29 17:18:01,012 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 88
WARNING 2025-07-29 17:18:01,330 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:18:01,378 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:18:16,430 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:18:19,877 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 89
INFO 2025-07-29 17:18:19,907 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 90
WARNING 2025-07-29 17:18:20,420 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:18:20,467 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:19:05,681 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:19:38,884 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 91
INFO 2025-07-29 17:19:39,089 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 92
WARNING 2025-07-29 17:19:39,230 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:19:39,278 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:19:50,625 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:20:07,951 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 93
INFO 2025-07-29 17:20:07,996 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 94
WARNING 2025-07-29 17:20:08,380 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:20:08,428 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:23:58,596 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:24:02,342 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 95
INFO 2025-07-29 17:24:02,490 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 96
WARNING 2025-07-29 17:24:02,693 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:24:02,740 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:24:12,859 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:24:16,438 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 97
INFO 2025-07-29 17:24:16,473 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 98
WARNING 2025-07-29 17:24:16,790 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:24:16,837 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:26:41,216 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:26:44,874 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 99
INFO 2025-07-29 17:26:45,179 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 100
WARNING 2025-07-29 17:26:45,190 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:26:45,238 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:30:34,464 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:30:38,178 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 101
INFO 2025-07-29 17:30:38,360 rate_limiter 3601032 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 102
WARNING 2025-07-29 17:30:38,569 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:30:38,618 base 3601032 *************** ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
WARNING 2025-07-29 17:32:38,299 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:32:38,347 base 3601032 *************** ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:32:38,487 rate_limiter 3601032 *************** [RATE_LIMIT] 用户1(每周) API:bazi_advice_api 调用次数: 3
INFO 2025-07-29 17:33:04,732 base 3601032 *************** ✅ [BAZI_ADVICE] 消息处理完成，用户ID: 1，耗时: 26.265秒
INFO 2025-07-29 17:43:22,369 prompt_engine 3624777 140460370317312 生成提示词成功: basic_analysis_health_yearly
INFO 2025-07-29 17:43:22,369 prompt_engine 3624777 140460370317312 生成提示词成功: contradiction_analysis_career_short_term
INFO 2025-07-29 17:43:22,369 prompt_engine 3624777 140460370317312 生成提示词成功: decision_support_marriage_monthly
INFO 2025-07-29 17:43:22,370 prompt_engine 3624777 140460370317312 生成提示词成功: pattern_analysis_wealth_long_term
WARNING 2025-07-29 17:43:22,382 base 3601032 *************** 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:43:22,431 base 3601032 *************** ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:43:22,454 rate_limiter 3601032 *************** [RATE_LIMIT] 用户1(每周) API:bazi_advice_api 调用次数: 4
INFO 2025-07-29 17:43:52,467 bazi_advice_consumer 3601032 *************** [BAZI_ADVICE] WebSocket断开连接: {'close_code': 1000, 'user_id': 1, 'disconnect_reason': '正常关闭'}
INFO 2025-07-29 17:45:36,166 bazi_advice_consumer 3601032 *************** [BAZI_ADVICE] WebSocket断开连接: {'close_code': 1012, 'user_id': 1, 'disconnect_reason': '未知代码(1012)'}
INFO 2025-07-29 17:45:36,166 ark_chat_consumer 3601032 *************** [ARK_CHAT] WebSocket断开连接: {'close_code': 1012, 'user_id': 1, 'disconnect_reason': '未知代码(1012)'}
WARNING 2025-07-29 17:45:47,487 base 3625381 140546076000256 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:45:47,535 base 3625381 140546076000256 ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:45:49,359 rate_limiter 3625381 140546076000256 [RATE_LIMIT] 用户1(每周) API:bazi_advice_api 调用次数: 5
INFO 2025-07-29 17:46:14,862 base 3625381 140546076000256 ✅ [BAZI_ADVICE] 消息处理完成，用户ID: 1，耗时: 26.771秒
INFO 2025-07-29 17:47:48,151 bazi_advice_consumer 3625381 140546076000256 [BAZI_ADVICE] WebSocket断开连接: {'close_code': 1001, 'user_id': 1, 'disconnect_reason': '端点离开'}
INFO 2025-07-29 17:47:53,207 rate_limiter 3625381 140546076000256 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 103
WARNING 2025-07-29 17:47:53,860 base 3625381 140546076000256 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:47:53,909 base 3625381 140546076000256 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.049秒
INFO 2025-07-29 17:47:54,574 rate_limiter 3625381 140546076000256 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 104
INFO 2025-07-29 17:52:48,525 prompt_engine 3627042 140353213583360 生成提示词成功: basic_analysis_health_yearly
INFO 2025-07-29 17:53:03,845 ark_chat_consumer 3625381 140546076000256 [ARK_CHAT] WebSocket断开连接: {'close_code': 1012, 'user_id': 1, 'disconnect_reason': '未知代码(1012)'}
INFO 2025-07-29 17:53:09,843 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 105
INFO 2025-07-29 17:53:10,577 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 106
INFO 2025-07-29 17:53:10,844 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 107
WARNING 2025-07-29 17:53:11,494 base 3627149 139979005755392 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:53:11,542 base 3627149 139979005755392 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 17:53:11,748 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 108
WARNING 2025-07-29 17:53:36,628 base 3627149 139979005755392 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:53:36,674 base 3627149 139979005755392 ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
ERROR 2025-07-29 17:53:36,801 base 3627149 139979005755392 ❌ [BAZI_ADVICE] 限流拦截，用户ID: 1，耗时: 0.010秒
INFO 2025-07-29 17:54:46,233 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 109
INFO 2025-07-29 17:54:47,066 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 110
INFO 2025-07-29 17:54:47,275 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 111
INFO 2025-07-29 17:54:49,231 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 112
INFO 2025-07-29 17:54:51,219 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 113
INFO 2025-07-29 17:54:51,271 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 114
INFO 2025-07-29 17:54:53,910 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 115
INFO 2025-07-29 17:54:54,154 rate_limiter 3627149 139979005755392 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 116
INFO 2025-07-29 17:54:55,081 ark_chat_consumer 3627149 139979005755392 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 17:54:55,100 bazi_advice_consumer 3627149 139979005755392 [BAZI_ADVICE] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
WARNING 2025-07-29 17:54:57,934 base 3627149 139979005755392 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:54:57,981 base 3627149 139979005755392 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
WARNING 2025-07-29 17:55:14,600 base 3627149 139979005755392 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 17:55:14,646 base 3627149 139979005755392 ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.047秒
ERROR 2025-07-29 17:55:14,865 base 3627149 139979005755392 ❌ [BAZI_ADVICE] 限流拦截，用户ID: 1，耗时: 0.011秒
INFO 2025-07-29 17:59:55,056 bazi_advice_consumer 3627149 139979005755392 [BAZI_ADVICE] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 17:59:55,063 ark_chat_consumer 3627149 139979005755392 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
WARNING 2025-07-29 18:01:08,367 base 3629348 140458771152896 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 18:01:08,415 base 3629348 140458771152896 ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
INFO 2025-07-29 18:01:09,804 rate_limiter 3629348 140458771152896 [RATE_LIMIT] 用户1(每周) API:bazi_advice_api 调用次数: 6
INFO 2025-07-29 18:01:35,295 base 3629348 140458771152896 ✅ [BAZI_ADVICE] 消息处理完成，用户ID: 1，耗时: 26.753秒
INFO 2025-07-29 18:01:35,296 bazi_advice_consumer 3629348 140458771152896 [BAZI_ADVICE] WebSocket断开连接: {'close_code': 1012, 'user_id': 1, 'disconnect_reason': '未知代码(1012)'}
INFO 2025-07-29 18:02:33,204 rate_limiter 3629722 139980249513984 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 117
INFO 2025-07-29 19:31:52,785 rate_limiter 3648245 140444527378432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 118
INFO 2025-07-29 19:31:52,786 rate_limiter 3648245 140444527378432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 118
INFO 2025-07-29 19:31:53,212 rate_limiter 3648245 140444527378432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 119
INFO 2025-07-29 19:31:53,264 rate_limiter 3648245 140444527378432 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 120
WARNING 2025-07-29 19:31:53,708 base 3648245 140444527378432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 19:31:53,756 base 3648245 140444527378432 ✅ [ARK_CHAT] WebSocket连接建立成功，用户ID: 1，耗时: 0.048秒
WARNING 2025-07-29 19:32:05,473 base 3648245 140444527378432 用户ID未获取到，允许匿名连接（仅供测试）。调试信息: user_obj_type: <class 'channels.auth.UserLazyObject'>; user_obj_hasattr_id: True; user_id_from_user_obj: None; query_string: ; fallback_to_default_user
INFO 2025-07-29 19:32:05,520 base 3648245 140444527378432 ✅ [BAZI_ADVICE] WebSocket连接建立成功，用户ID: 1，耗时: 0.046秒
INFO 2025-07-29 19:32:05,650 rate_limiter 3648245 140444527378432 [RATE_LIMIT] 用户1(每周) API:bazi_advice_api 调用次数: 7
INFO 2025-07-29 19:32:30,196 base 3648245 140444527378432 ✅ [BAZI_ADVICE] 消息处理完成，用户ID: 1，耗时: 24.567秒
INFO 2025-07-29 19:34:03,882 ark_chat_consumer 3648245 140444527378432 [ARK_CHAT] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 19:34:03,888 bazi_advice_consumer 3648245 140444527378432 [BAZI_ADVICE] WebSocket断开连接: {'close_code': <CloseCode.ABNORMAL_CLOSURE: 1006>, 'user_id': 1, 'disconnect_reason': '连接异常关闭'}
INFO 2025-07-29 21:24:24,421 autoreload 3671605 140237661614080 Watching for file changes with StatReloader
INFO 2025-07-29 21:29:14,950 basehttp 3671605 *************** "GET / HTTP/1.1" 302 0
INFO 2025-07-29 21:29:15,000 basehttp 3671605 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
WARNING 2025-07-29 21:29:45,181 basehttp 3671605 *************** "GET /favicon.ico HTTP/1.1" 409 49
INFO 2025-07-29 21:29:50,662 basehttp 3671605 140236453574208 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:30:03,436 basehttp 3671605 140236453574208 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:30:47,694 basehttp 3671605 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
WARNING 2025-07-29 21:32:29,883 basehttp 3671605 *************** "GET /favicon.ico HTTP/1.1" 409 49
INFO 2025-07-29 21:32:36,654 basehttp 3671605 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:32:45,367 basehttp 3671605 *************** "HEAD / HTTP/1.1" 302 0
INFO 2025-07-29 21:35:01,377 basehttp 3671605 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 21:36:22,564 basehttp 3671605 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
WARNING 2025-07-29 21:36:42,409 basehttp 3671605 *************** "GET /api/bank/ HTTP/1.1" 409 49
INFO 2025-07-29 21:37:44,794 autoreload 3675252 *************** Watching for file changes with StatReloader
INFO 2025-07-29 21:43:17,432 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,438 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,462 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,504 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,523 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,551 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,618 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,632 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,650 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,683 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,685 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,700 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,716 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,722 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,723 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,725 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,728 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,729 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,737 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,743 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,744 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,746 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,764 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,769 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,773 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,802 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,807 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,809 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,809 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,829 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,838 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,849 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,856 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,862 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,865 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,869 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,871 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,872 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,883 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,887 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,896 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,898 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,901 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,908 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,913 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,918 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,920 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,925 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,934 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,940 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,950 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,951 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,975 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,979 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,982 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:17,995 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,007 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,013 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,015 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,017 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,028 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,028 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,033 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,059 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,065 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,065 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,079 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,086 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,096 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,111 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,129 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,144 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,146 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,164 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,195 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,198 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,201 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,210 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,226 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,227 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,228 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,231 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,231 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,254 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,276 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,278 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,286 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,290 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,298 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,318 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,326 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,337 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,342 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,382 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,394 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,406 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,413 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,417 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,430 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,439 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,527 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,709 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,752 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,806 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,848 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,902 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,956 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,981 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:18,993 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,105 basehttp 3675252 140076349949504 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,107 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,210 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,231 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,233 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,350 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,409 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,577 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,664 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,685 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,706 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:19,941 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:20,323 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:20,523 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:20,541 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:20,735 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:21,003 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:21,142 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:21,323 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:23,747 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:43:25,790 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:47:13,759 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:47:26,057 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:50:37,767 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:52:09,047 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:52:10,662 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:53:18,803 basehttp 3675252 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:53:54,159 autoreload 3675252 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/urls.py changed, reloading.
INFO 2025-07-29 21:53:55,467 autoreload 3679087 140569651052544 Watching for file changes with StatReloader
INFO 2025-07-29 21:54:07,678 autoreload 3679087 140569651052544 /home/<USER>/riyue-llm/myproject/demo_api/api/urls.py changed, reloading.
INFO 2025-07-29 21:54:08,984 autoreload 3679199 140615698984960 Watching for file changes with StatReloader
INFO 2025-07-29 21:55:40,495 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:55:42,420 basehttp 3679199 140614715323968 "HEAD /api/HomePage/ HTTP/1.1" 200 0
INFO 2025-07-29 21:57:53,669 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 21:57:53,969 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
WARNING 2025-07-29 21:59:37,950 basehttp 3679199 140614715323968 "GET /favicon.ico HTTP/1.0" 409 49
WARNING 2025-07-29 21:59:40,425 basehttp 3679199 140614715323968 "GET /favicon.ico HTTP/1.0" 409 49
WARNING 2025-07-29 22:01:22,593 basehttp 3679199 140614715323968 "GET /.env HTTP/1.0" 409 49
INFO 2025-07-29 22:01:24,637 basehttp 3679199 140614715323968 "POST / HTTP/1.0" 302 0
INFO 2025-07-29 22:02:30,121 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:02:30,274 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
WARNING 2025-07-29 22:02:31,823 basehttp 3679199 140614715323968 "GET /favicon.ico HTTP/1.0" 409 49
INFO 2025-07-29 22:06:43,577 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
WARNING 2025-07-29 22:06:44,230 basehttp 3679199 140614715323968 "GET /favicon.ico HTTP/1.0" 409 49
INFO 2025-07-29 22:12:07,801 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,831 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,866 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,890 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:07,898 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,909 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,921 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,942 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:07,944 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,945 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,964 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,968 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:07,973 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:07,995 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,000 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,010 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,020 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,020 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,029 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,033 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,050 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,062 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,066 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,072 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,082 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,084 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,085 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,095 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,097 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,098 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,099 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,104 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,114 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,115 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,125 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,125 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,128 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,128 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,130 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,131 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,134 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,135 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,135 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,140 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,140 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,141 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,147 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,149 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,154 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,154 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,155 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,158 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,159 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,163 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,168 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,169 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,176 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,176 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,178 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,178 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,180 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,181 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,192 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,192 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,195 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,201 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,205 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,205 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,206 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,208 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,209 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,210 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,210 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,211 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,216 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,216 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,221 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,225 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,231 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,231 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,239 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,244 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,245 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,246 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,248 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,248 basehttp 3679199 140614703785536 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,250 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,259 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,259 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,260 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,260 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,265 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,265 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,272 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,276 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,276 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,276 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,279 basehttp 3679199 140614703785536 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,281 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,284 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,285 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,286 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,287 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,287 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,289 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,295 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,295 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,296 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,299 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,303 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,305 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,306 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,306 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,308 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,312 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,313 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,314 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,318 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,320 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,322 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,324 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,331 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,333 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,338 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,341 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,341 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,343 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,348 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,349 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,349 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,360 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,362 basehttp 3679199 140614461019712 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,364 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,375 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,384 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,385 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,385 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,387 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,397 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,403 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,408 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,409 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,411 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,411 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,419 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,422 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,428 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,432 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,434 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,437 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,443 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,449 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,462 basehttp 3679199 140614695392832 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,462 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,462 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,464 basehttp 3679199 140614461019712 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,465 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,466 basehttp 3679199 140614461019712 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,469 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,472 basehttp 3679199 140614461019712 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,488 basehttp 3679199 140614461019712 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,489 basehttp 3679199 140614695392832 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,490 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,490 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,490 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,520 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,524 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,529 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,536 basehttp 3679199 140614715323968 "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,539 basehttp 3679199 140614715323968 "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,539 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,541 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,547 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,550 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,557 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,573 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,574 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,584 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,590 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,591 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,599 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,621 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,628 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,635 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,655 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,672 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,677 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,695 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,697 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,706 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,718 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,724 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,733 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,753 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,783 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,819 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,827 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,842 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,846 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,869 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,888 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,894 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:08,912 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,972 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,984 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:08,993 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,019 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,021 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,076 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,116 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,131 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,137 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,146 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,164 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,170 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,194 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,195 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,201 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,277 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,300 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,300 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,360 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,409 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,427 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,504 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,505 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,517 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,546 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,551 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,606 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,623 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,628 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,687 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,692 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,696 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,741 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,754 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,816 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:09,869 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:09,996 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,018 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,026 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,026 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,085 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,094 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,161 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,171 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,200 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,231 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,239 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,293 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,332 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,358 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,447 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,496 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,554 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,565 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,691 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:10,699 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,846 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,944 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:10,969 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:11,144 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:11,184 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:11,279 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:11,435 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:11,612 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:11,720 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:11,895 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:12,097 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:12,353 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:12,390 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:13,133 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:13,775 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:14,404 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:15,487 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:18,348 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:12:19,225 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:12:24,518 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:13:02,397 basehttp 3679199 *************** "GET / HTTP/1.0" 302 0
INFO 2025-07-29 22:13:02,580 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
INFO 2025-07-29 22:13:52,334 basehttp 3679199 *************** "GET /api/HomePage/ HTTP/1.0" 200 6272
WARNING 2025-07-29 22:13:53,007 basehttp 3679199 *************** "GET /favicon.ico HTTP/1.0" 409 49
WARNING 2025-07-29 22:14:02,130 basehttp 3679199 *************** "GET /favicon.ico HTTP/1.0" 409 49
WARNING 2025-07-29 22:14:45,242 basehttp 3679199 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 402 49
INFO 2025-07-29 22:14:52,707 rate_limiter 3679199 *************** [IP_RATE_LIMIT] IP:************** API:wechat_login_api 调用次数: 1/30
INFO 2025-07-29 22:14:52,708 basehttp 3679199 *************** "POST /api/auth/wechat-login HTTP/1.0" 200 403
INFO 2025-07-29 22:14:55,185 basehttp 3679199 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 22:14:56,710 rate_limiter 3679199 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 121
INFO 2025-07-29 22:14:56,720 basehttp 3679199 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 22:14:58,537 basehttp 3679199 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 22:14:58,741 basehttp 3679199 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-29 22:14:58,831 basehttp 3679199 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-29 22:14:59,011 rate_limiter 3679199 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 122
INFO 2025-07-29 22:14:59,021 basehttp 3679199 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 22:14:59,952 basehttp 3679199 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-29 22:18:55,087 autoreload 3686460 *************** Watching for file changes with StatReloader
INFO 2025-07-29 22:54:42,084 autoreload 3698097 *************** Watching for file changes with StatReloader
INFO 2025-07-29 22:55:05,391 basehttp 3698097 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-29 22:55:05,451 rate_limiter 3698097 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 123
INFO 2025-07-29 22:55:05,461 basehttp 3698097 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 22:55:05,736 basehttp 3698097 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-29 22:55:06,643 basehttp 3698097 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-29 22:55:06,756 basehttp 3698097 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 22:55:08,438 basehttp 3698097 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 22:55:08,494 rate_limiter 3698097 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 124
INFO 2025-07-29 22:55:08,505 basehttp 3698097 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 22:55:08,508 basehttp 3698097 *************** "POST /api/bank/EmptyRequestView/ HTTP/1.0" 200 30
INFO 2025-07-29 22:55:09,096 rate_limiter 3698097 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 125
INFO 2025-07-29 22:55:09,109 basehttp 3698097 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 22:55:09,930 basehttp 3698097 *************** "GET /api/async-bank/badhabit-list/ HTTP/1.0" 200 2602
INFO 2025-07-29 22:55:09,936 basehttp 3698097 *************** "GET /api/async-bank/activity-list/ HTTP/1.0" 200 2003
INFO 2025-07-29 22:55:09,966 basehttp 3698097 *************** "GET /api/async-bank/user-stats/ HTTP/1.0" 200 1561
INFO 2025-07-29 22:55:10,165 rate_limiter 3698097 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 126
INFO 2025-07-29 22:55:10,175 basehttp 3698097 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 22:55:11,173 basehttp 3698097 *************** "POST /api/async-bank/user-card-month/ HTTP/1.0" 200 2223
INFO 2025-07-29 22:55:11,471 rate_limiter 3698097 *************** [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 127
INFO 2025-07-29 22:55:11,481 basehttp 3698097 *************** "POST /api/async-bank/checkmembership/ HTTP/1.0" 200 516
INFO 2025-07-29 22:57:36,712 autoreload 3699513 *************** Watching for file changes with StatReloader
INFO 2025-07-29 23:10:24,198 autoreload 3699513 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/urls.py changed, reloading.
INFO 2025-07-29 23:10:25,513 autoreload 3702738 *************** Watching for file changes with StatReloader
INFO 2025-07-29 23:11:45,389 autoreload 3702738 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py changed, reloading.
INFO 2025-07-29 23:11:46,696 autoreload 3703062 *************** Watching for file changes with StatReloader
INFO 2025-07-29 23:18:28,008 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,111 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,213 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,316 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,418 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,520 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,622 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,724 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,826 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:28,929 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,031 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,133 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,235 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,337 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,440 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,542 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,644 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,746 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,850 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:29,952 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,055 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,157 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,259 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,361 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,464 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,566 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,668 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,770 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,872 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:30,974 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,076 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,178 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,281 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,383 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,485 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,587 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,689 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,791 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,893 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:31,995 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,097 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,199 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,301 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,404 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,507 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,609 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,711 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,813 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:32,915 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,017 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,119 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,221 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,323 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,425 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,529 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,631 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,733 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,835 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:33,937 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,039 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,141 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,243 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,345 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,447 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,549 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,651 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,753 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,855 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:34,957 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,059 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,161 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,263 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,365 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,467 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,569 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,671 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,773 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,875 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:35,978 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,080 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,182 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,284 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,386 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,488 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,590 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,692 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,794 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,896 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:36,998 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:37,100 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:37,202 basehttp 3703062 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:18:43,631 autoreload 3704779 *************** Watching for file changes with StatReloader
INFO 2025-07-29 23:20:00,391 autoreload 3704779 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py changed, reloading.
INFO 2025-07-29 23:20:01,698 autoreload 3705096 *************** Watching for file changes with StatReloader
INFO 2025-07-29 23:20:16,372 autoreload 3705096 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py changed, reloading.
INFO 2025-07-29 23:20:17,565 autoreload 3705235 *************** Watching for file changes with StatReloader
INFO 2025-07-29 23:21:34,652 autoreload 3705235 *************** /home/<USER>/riyue-llm/myproject/demo_api/api/views/bank.py changed, reloading.
INFO 2025-07-29 23:21:35,850 autoreload 3705585 *************** Watching for file changes with StatReloader
INFO 2025-07-29 23:22:43,759 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,093 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,095 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,097 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,100 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,102 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,104 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,107 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,110 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,113 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,116 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,118 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,120 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,123 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,125 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,127 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,129 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,131 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,135 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,137 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,139 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,141 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,144 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,146 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,148 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,150 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,152 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,154 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,157 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,159 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,161 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,163 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,165 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,168 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,170 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,173 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,175 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,178 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,180 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,182 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,184 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,186 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,189 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,191 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,193 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,195 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,197 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,200 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,202 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,204 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:17,206 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,217 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,217 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,217 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,220 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,223 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,226 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,226 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,228 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,229 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,233 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,235 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,236 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,237 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,240 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,243 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,244 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,245 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,248 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,250 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,251 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,251 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,251 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,253 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,256 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,261 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,261 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,261 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,261 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,262 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,271 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,271 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,276 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,276 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,277 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,277 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,279 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,285 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,286 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,287 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,287 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,288 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,296 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,296 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,296 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,297 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,298 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,303 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,303 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,303 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:18,303 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,316 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,316 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,318 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,319 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,319 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,321 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,322 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,323 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,326 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,329 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,331 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,332 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,336 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,339 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,340 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,342 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,342 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,344 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,344 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,346 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,349 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,351 basehttp 3705585 139824128321088 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,353 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,354 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,356 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,358 basehttp 3705585 139824119928384 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,363 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,363 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,364 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,365 basehttp 3705585 139824128321088 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,366 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,367 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,372 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,373 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,373 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,375 basehttp 3705585 139824111535680 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,376 basehttp 3705585 139824119928384 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,381 basehttp 3705585 139824103142976 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,381 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,382 basehttp 3705585 139824128321088 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,383 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,383 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,387 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,395 basehttp 3705585 139824103142976 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,391 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,394 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,395 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,395 basehttp 3705585 139824111535680 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,395 basehttp 3705585 139824119928384 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:19,390 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,406 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,407 basehttp 3705585 139824119928384 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,407 basehttp 3705585 139824111535680 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,412 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,412 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,417 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,417 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,419 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,420 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,421 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,424 basehttp 3705585 139824111535680 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,429 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,432 basehttp 3705585 139824119928384 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,432 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,432 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,432 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,433 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,437 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,440 basehttp 3705585 139824111535680 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,442 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,442 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,443 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,448 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,450 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,453 basehttp 3705585 139824631621184 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,454 basehttp 3705585 139824111535680 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,454 basehttp 3705585 139824119928384 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,455 basehttp 3705585 139824623228480 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,456 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,456 basehttp 3705585 139824128321088 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,461 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,461 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,463 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,464 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,466 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,471 basehttp 3705585 139824111535680 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,471 basehttp 3705585 139824119928384 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,475 basehttp 3705585 139824128321088 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,478 basehttp 3705585 139824648406592 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,478 basehttp 3705585 139824640013888 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,479 basehttp 3705585 139824665192000 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,479 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,480 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:20,481 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:21,428 basehttp 3705585 *************** "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:21,429 basehttp 3705585 139824656799296 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:21,429 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:21,458 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:21,462 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
INFO 2025-07-29 23:28:21,490 basehttp 3705585 139824136713792 "GET /api/HomePage/ HTTP/1.1" 200 6272
WARNING 2025-07-29 23:28:22,559 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,581 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,603 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,624 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,647 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,669 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,691 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,712 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,734 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,755 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,777 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,799 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,822 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,844 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,866 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,888 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,910 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,931 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,953 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,974 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:22,995 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,017 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,039 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,061 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,083 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,106 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,129 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,151 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,172 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,193 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,215 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,237 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,258 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,279 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,301 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,323 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,344 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,366 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,388 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,409 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,431 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,453 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,474 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,496 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,517 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,539 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,561 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,582 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,604 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:23,626 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,662 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,669 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,673 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,673 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,673 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,692 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,702 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,710 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,711 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,717 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,726 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,736 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,743 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,743 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,748 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,757 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,775 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,780 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,780 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,784 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,794 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,806 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,815 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,819 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,819 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,827 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,841 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,849 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,859 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,860 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,866 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,880 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,892 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,903 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,908 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,912 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,923 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,936 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,944 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,953 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,954 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,954 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,982 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,989 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:24,998 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:25,003 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:25,007 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:25,015 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:25,018 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:25,025 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,083 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,092 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,093 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,097 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,107 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,115 basehttp 3705585 139824119928384 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,118 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,119 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,121 basehttp 3705585 139824623228480 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,133 basehttp 3705585 139824128321088 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,153 basehttp 3705585 139824111535680 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,168 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,174 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,179 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,182 basehttp 3705585 139824103142976 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,192 basehttp 3705585 139824119928384 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,193 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,201 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,202 basehttp 3705585 139824623228480 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,219 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,236 basehttp 3705585 139824128321088 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,244 basehttp 3705585 139824111535680 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,250 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,261 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,265 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,265 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,271 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,277 basehttp 3705585 139824119928384 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,280 basehttp 3705585 139824103142976 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,303 basehttp 3705585 139824128321088 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,304 basehttp 3705585 139824623228480 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,315 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,321 basehttp 3705585 139824111535680 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,321 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,328 basehttp 3705585 139824094750272 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,338 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,339 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,349 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,366 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,375 basehttp 3705585 139824103142976 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,382 basehttp 3705585 139824128321088 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,385 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,386 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,393 basehttp 3705585 139824094750272 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,393 basehttp 3705585 139824119928384 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,395 basehttp 3705585 139824623228480 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,400 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,407 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,409 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:26,413 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,488 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,493 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,526 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,535 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,535 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,541 basehttp 3705585 139824128321088 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,549 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,550 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,560 basehttp 3705585 139824119928384 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,562 basehttp 3705585 139824623228480 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,591 basehttp 3705585 139824094750272 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,602 basehttp 3705585 139823599842880 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,603 basehttp 3705585 139824111535680 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,614 basehttp 3705585 139824086357568 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,614 basehttp 3705585 139824103142976 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,620 basehttp 3705585 139823574664768 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,622 basehttp 3705585 139823583057472 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,624 basehttp 3705585 139823557879360 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,629 basehttp 3705585 139823549486656 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,639 basehttp 3705585 139823591450176 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,642 basehttp 3705585 139823566272064 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,662 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,678 basehttp 3705585 139823062971968 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,679 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,695 basehttp 3705585 139824656799296 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,696 basehttp 3705585 139823054579264 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,705 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,711 basehttp 3705585 139823046186560 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,721 basehttp 3705585 139823037793856 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,742 basehttp 3705585 139824623228480 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,744 basehttp 3705585 139824640013888 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,745 basehttp 3705585 139824119928384 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,753 basehttp 3705585 139824136713792 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,754 basehttp 3705585 139824648406592 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,758 basehttp 3705585 139824128321088 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,762 basehttp 3705585 139824094750272 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,764 basehttp 3705585 139823599842880 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,765 basehttp 3705585 139824111535680 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,770 basehttp 3705585 139823029401152 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,771 basehttp 3705585 139823021008448 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,775 basehttp 3705585 139823566272064 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,784 basehttp 3705585 139823549486656 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,788 basehttp 3705585 139823062971968 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,792 basehttp 3705585 139823591450176 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,792 basehttp 3705585 *************** "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,793 basehttp 3705585 139824665192000 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,793 basehttp 3705585 139823557879360 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,797 basehttp 3705585 139824631621184 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:27,799 basehttp 3705585 139823046186560 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:28:28,455 basehttp 3705585 139823046186560 "GET /api/db_health_api/connection-pool-status/ HTTP/1.1" 404 179
WARNING 2025-07-29 23:36:08,875 basehttp 3705585 139823046186560 "GET /ReportServer HTTP/1.0" 409 49
WARNING 2025-07-29 23:58:06,859 basehttp 3705585 139823046186560 "GET /.env HTTP/1.0" 409 49
