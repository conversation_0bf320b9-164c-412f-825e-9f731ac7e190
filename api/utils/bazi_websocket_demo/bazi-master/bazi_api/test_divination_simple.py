#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
算卦系统简单测试
验证核心功能是否正常工作
"""

import os
import sys
import json

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bazi_api.settings')

try:
    import django
    django.setup()
except Exception as e:
    print(f"Django初始化失败: {e}")
    sys.exit(1)

def test_basic_divination():
    """测试基本算卦功能"""
    print("="*50)
    print("测试基本算卦功能")
    print("="*50)
    
    try:
        from api.services.divination_service import divination_data_service
        from api.services.random_service import RandomService
        from api.services.divination_core_service import divination_core_service
        
        # 测试数据加载
        print(f"1. 数据加载状态: {divination_data_service.is_data_loaded()}")
        print(f"2. 卦的总数: {divination_data_service.get_gua_count()}")
        
        # 测试随机数生成
        random_service = RandomService()
        user_input = 123
        gua_number = random_service.generate_divination_number(user_input)
        print(f"3. 用户输入 {user_input} 生成卦数: {gua_number}")
        
        # 测试获取卦象信息
        gua_info = divination_data_service.get_gua_by_id(gua_number)
        if gua_info:
            print(f"4. 第{gua_number}卦: {gua_info['name']}")
            print(f"   卦象: {gua_info['code']}")
            print(f"   卦辞: {gua_info['gua_ci'][:50]}...")
        else:
            print(f"4. 未找到第{gua_number}卦")
        
        # 测试完整算卦流程
        result = divination_core_service.perform_divination(456, "测试问题")
        if result['success']:
            print(f"5. 完整算卦测试成功:")
            print(f"   用户输入: {result['divination_info']['user_input']}")
            print(f"   得卦: {result['gua_info']['name']}")
            print(f"   卦数: {result['divination_info']['gua_number']}")
        else:
            print(f"5. 完整算卦测试失败: {result['error']}")
        
        print("\n✓ 基本算卦功能测试通过")
        
    except Exception as e:
        print(f"✗ 基本算卦功能测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())

def test_prompt_system():
    """测试提示词系统"""
    print("\n" + "="*50)
    print("测试提示词系统")
    print("="*50)
    
    try:
        from api.utils.constants import (
            get_divination_system_prompt,
            get_divination_prompt
        )
        
        # 测试系统提示词
        system_prompt = get_divination_system_prompt("general")
        print(f"1. 系统提示词长度: {len(system_prompt)}")
        print(f"   内容预览: {system_prompt[:80]}...")
        
        # 测试解读提示词
        interpretation_prompt = get_divination_prompt("general_interpretation")
        print(f"2. 解读提示词长度: {len(interpretation_prompt)}")
        print(f"   内容预览: {interpretation_prompt[:80]}...")
        
        # 测试提示词格式化
        formatted_prompt = interpretation_prompt.format(
            gua_name="乾为天",
            gua_code="111111",
            question="测试问题",
            gua_ci="乾：元，亨，利，贞。"
        )
        print(f"3. 格式化提示词长度: {len(formatted_prompt)}")
        print(f"   格式化预览: {formatted_prompt[:100]}...")
        
        print("\n✓ 提示词系统测试通过")
        
    except Exception as e:
        print(f"✗ 提示词系统测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())

def test_random_distribution():
    """测试随机数分布"""
    print("\n" + "="*50)
    print("测试随机数分布")
    print("="*50)
    
    try:
        from api.services.random_service import RandomService
        
        random_service = RandomService()
        
        # 测试100次随机数生成
        distribution = {}
        test_count = 100
        
        for i in range(test_count):
            user_input = i % 1000  # 0-999循环
            gua_number = random_service.generate_divination_number(user_input)
            distribution[gua_number] = distribution.get(gua_number, 0) + 1
        
        print(f"1. 测试次数: {test_count}")
        print(f"2. 生成的不同卦数: {len(distribution)}")
        print(f"3. 最小出现次数: {min(distribution.values())}")
        print(f"4. 最大出现次数: {max(distribution.values())}")
        
        # 显示前10个卦的分布
        sorted_dist = sorted(distribution.items(), key=lambda x: x[1], reverse=True)
        print("5. 前10个卦的分布:")
        for gua_num, count in sorted_dist[:10]:
            print(f"   第{gua_num}卦: {count}次")
        
        print("\n✓ 随机数分布测试通过")
        
    except Exception as e:
        print(f"✗ 随机数分布测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())

def main():
    """主测试函数"""
    print("开始算卦系统简单测试")
    print("当前时间:", __import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 运行测试
    test_basic_divination()
    test_prompt_system()
    test_random_distribution()
    
    print("\n" + "="*50)
    print("算卦系统简单测试完成")
    print("="*50)

if __name__ == "__main__":
    main() 