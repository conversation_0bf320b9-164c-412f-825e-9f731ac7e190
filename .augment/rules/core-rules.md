---
description: "核心规则 - 确保所有开发规范永远生效"
globs: ["**/*"]
alwaysApply: true
---

# 核心规则 - 永远生效

## 重要提醒
- 注意回复我的时候，以及给代码写注释的时候还是要用中文
- 你决不能随意篡改任何代码，否则影响巨大
- 你决不能随便新建ninja路由，否则会出错
- 访问需要权限说明API正常，时刻注意避免NinjaAPI配置冲突
- 记得改完api，ninja还需要改序列化，千万别忘记

## 命令行规范
- 如果需要命令行运行Python，需要首先输入start进入虚拟环境
- 例如：start && python test_mysql_timeout_fix.py

## 测试规范
- 由于中间件机制，测试时使用test_token_user_2.txt中的token
- 如果不用token，过不了中间件
- 使用格式：-H "Authorization: Bearer [token]" -H "Content-Type: application/json"

## 错误排查
- 首先查看：tail -50 logs/error.log
- 如有必要再看：tail -50 logs/info.log

## 强制要求
1. **所有API视图必须使用 `async def`** - 严格强制
2. **数据库操作必须使用 async_utils.py 工具函数**
3. **连接池管理极其重要** - 防止连接泄漏
4. **每个API必须有耗时的print输出**
5. **GET请求必须加入缓存机制**
6. **遵循ninja开发规范，避免循环导入**

## 项目特点
- 在api里直接用 `user_id = request.user_id` 获取用户ID
- 使用API_TIMER装饰器进行性能监控
- 严格按照ninja_apis/__init__.py的模式组织路由
