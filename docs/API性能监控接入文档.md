# API性能监控接入文档 ⚡

## 📋 概述

本系统提供了统一的API性能监控功能，可以自动记录API调用耗时、成功率等关键指标，并在Django Admin界面实时展示。

## 🎯 核心特性

- ✅ **零侵入式** - 只需添加装饰器，不影响现有代码
- ✅ **自动日志记录** - 同时输出到控制台和性能日志文件
- ✅ **Admin界面展示** - 实时查看最慢的5个API、调用总数、平均响应时间
- ✅ **异常安全** - 日志记录失败不影响API正常运行
- ✅ **支持同步异步** - 自动识别函数类型

## 🚀 快速接入

### 1. 导入装饰器

根据您的API所在位置，选择对应的导入方式：

```python
# 方式1: 银行相关API (推荐)
from api.ninja_apis.async_bank_apis import api_timer

# 方式2: 问卷/预后相关API (推荐)  
from api.ninja_apis.questionnaire.utils import api_timer

# 方式3: 新项目统一导入 (最新版本)
from api.utils.api_performance_monitor import api_timer
```

### 2. 添加装饰器

```python
# 异步API示例
@api_timer("检查会员状态")
async def check_membership(request):
    """检查用户会员状态"""
    user_id = request.user_id
    # 您的业务逻辑...
    return {"status": "active"}

# 同步API示例  
@api_timer("获取用户信息")
def get_user_info(request):
    """获取用户基本信息"""
    # 您的业务逻辑...
    return {"name": "张三"}

# 自动函数名 (不推荐，名称不够清晰)
@api_timer()
async def some_function():
    pass
```

## 📊 监控效果

### 控制台输出
```
[API_TIMER] 🚀 检查会员状态 开始执行
[API_TIMER] ✅ 检查会员状态 执行完成，耗时: 0.001秒
```

### 性能日志 (`logs/api_performance.log`)
```
INFO 2025-08-02 02:04:59,762 API_SUCCESS|检查会员状态|0.001
ERROR 2025-08-02 02:04:59,763 API_ERROR|某个失败的API|0.123|具体错误信息
```

### Admin界面展示
访问 `/admin/api/userinfo/` 可以看到：
- 📈 今日API调用总数
- ⏱️ 平均响应时间  
- 🐌 最慢的5个API接口
- ⚡ 最快的5个API接口
- 📊 成功率统计

## 🔧 现有项目接入指南

### 已使用api_timer的项目
如果您的项目已经在使用`api_timer`，**无需任何修改**！系统已经自动升级：

```python
# 这些代码无需修改，已自动支持性能日志记录
from api.ninja_apis.async_bank_apis import api_timer

@api_timer("现有的API")
async def existing_api():
    pass
```

### 新增API的项目
对于新增的API，推荐使用统一导入：

```python
from api.utils.api_performance_monitor import api_timer

@api_timer("新增API名称")
async def new_api():
    pass
```

## 📁 文件结构

```
demo_api/
├── api/
│   ├── utils/
│   │   └── api_performance_monitor.py    # 🆕 统一性能监控工具
│   ├── ninja_apis/
│   │   ├── async_bank_apis.py           # ✅ 已升级支持日志
│   │   └── questionnaire/
│   │       └── utils.py                 # ✅ 已升级支持日志
│   ├── admin/
│   │   └── user_admin.py                # ✅ 集成性能监控面板
│   └── templates/admin/
│       └── userinfo_change_list.html    # ✅ 性能监控界面
├── logs/
│   ├── api_performance.log              # 🆕 专门的性能日志
│   └── info.log                         # 备用日志源
└── demo_api/
    └── settings.py                      # ✅ 已配置性能日志
```

## ⚠️ 注意事项

### 1. 装饰器参数
```python
# ✅ 推荐：使用清晰的中文名称
@api_timer("检查会员状态")

# ❌ 不推荐：使用函数名或英文
@api_timer("check_membership")
@api_timer()  # 会使用函数名
```

### 2. 异步函数必须用async
```python
# ✅ 正确
@api_timer("异步API")
async def async_api():
    pass

# ❌ 错误：异步函数没有async关键字
@api_timer("错误示例")
def should_be_async():  # 这个函数如果内部有await会报错
    pass
```

### 3. 性能日志自动轮转
- 日志文件每天自动轮转
- 保留30天历史数据
- 无需手动清理

## 🔍 故障排查

### 1. 看不到性能数据
```bash
# 检查日志文件是否存在
ls -la logs/api_performance.log

# 查看最新的性能记录
tail -10 logs/api_performance.log
```

### 2. Admin界面显示错误
检查是否有权限访问日志文件：
```bash
# 确保日志文件可读
chmod 644 logs/api_performance.log
```

### 3. 装饰器不生效
确保导入路径正确，并且函数确实被调用了：
```python
# 添加调试输出
@api_timer("测试API")
async def test_api():
    print("API被调用了")  # 确保函数执行
    return "success"
```

## 📈 最佳实践

### 1. 命名规范
```python
# ✅ 好的命名
@api_timer("检查会员状态")
@api_timer("获取用户症状列表") 
@api_timer("创建支付订单")

# ❌ 不好的命名
@api_timer("api1")
@api_timer("test")
@api_timer("function")
```

### 2. 关键API必须监控
```python
# 这些类型的API建议都加上监控
@api_timer("用户登录")          # 认证相关
@api_timer("支付回调处理")       # 支付相关  
@api_timer("AI对话生成")        # AI相关
@api_timer("数据库健康检查")     # 系统相关
```

### 3. 定期查看性能数据
- 每周查看一次Admin界面的性能统计
- 关注最慢的API，考虑优化
- 监控调用量变化趋势




## 🚀 快速参考卡片

### 最常用的导入方式
```python
# 银行/支付相关API
from api.ninja_apis.async_bank_apis import api_timer

# 问卷/预后相关API
from api.ninja_apis.questionnaire.utils import api_timer
```

### 标准用法
```python
@api_timer("API中文名称")
async def your_api_function():
    # 您的代码
    pass
```

### 查看监控数据
- **Admin界面**: `/admin/api/userinfo/`
- **日志文件**: `logs/api_performance.log`
- **控制台**: 实时输出API耗时

### 当前状态
- ✅ 已升级的文件: `async_bank_apis.py`, `questionnaire/utils.py`
- ✅ 新增统一工具: `api_performance_monitor.py`
- ✅ Admin界面: 已集成性能监控面板
- ✅ 日志配置: 自动轮转，保留30天
