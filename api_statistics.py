#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API统计脚本 - 统计整个系统的API数量、缓存使用和API_TIMER使用情况
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path

class APIStatistics:
    def __init__(self):
        self.total_apis = 0
        self.apis_with_timer = 0
        self.apis_with_cache = 0
        self.api_details = []
        self.cache_patterns = [
            r'@cache_page',
            r'@smart_cache',
            r'@prognosis_cache',
            r'@classification_cache',
            r'@therapy_list_cache',
            r'@therapy_detail_cache',
            r'@user_therapy_cache',
            r'@cache_get_requests',
            r'@.*_cache',
        ]
        self.timer_patterns = [
            r'@api_timer',
            r'@async_api_timer',
        ]
        
    def scan_file(self, file_path):
        """扫描单个文件中的API定义"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找API端点定义 - 更精确的匹配
            api_patterns = [
                # Django Ninja 路由 - 最准确的API定义
                r'@\w*router\.(get|post|put|delete|patch)\s*\(["\']([^"\']+)["\']',
                r'@router\.(get|post|put|delete|patch)\s*\(["\']([^"\']+)["\']',
                # Django 类视图 - 只匹配明确的API视图
                r'class\s+(\w+)\s*\([^)]*APIView[^)]*\)',
                r'class\s+(\w+)\s*\([^)]*ListAPIView[^)]*\)',
                r'class\s+(\w+)\s*\([^)]*CreateAPIView[^)]*\)',
                r'class\s+(\w+)\s*\([^)]*UpdateAPIView[^)]*\)',
                r'class\s+(\w+)\s*\([^)]*DestroyAPIView[^)]*\)',
            ]

            # 排除的文件模式 - 避免统计测试文件和工具文件
            exclude_patterns = [
                'test_', 'debug_', 'analyze_', 'check_', 'import_', 'fix_',
                'manage.py', 'wsgi.py', 'asgi.py', 'settings.py'
            ]

            # 检查是否应该排除此文件
            filename = os.path.basename(file_path)
            if any(pattern in filename for pattern in exclude_patterns):
                return
            
            lines = content.split('\n')
            current_api = None
            
            for i, line in enumerate(lines):
                line_stripped = line.strip()
                
                # 检查是否是API定义
                for pattern in api_patterns:
                    match = re.search(pattern, line_stripped)
                    if match:
                        if '@' in pattern and 'router' in pattern:
                            # Ninja路由
                            method = match.group(1).upper()
                            path = match.group(2)
                            api_name = f"{method} {path}"
                        else:
                            # Django视图
                            api_name = match.group(1)
                        
                        current_api = {
                            'name': api_name,
                            'file': str(file_path),
                            'line': i + 1,
                            'has_timer': False,
                            'has_cache': False,
                            'timer_type': None,
                            'cache_type': None
                        }
                        
                        # 检查前几行是否有装饰器
                        start_line = max(0, i - 10)
                        for j in range(start_line, i + 1):
                            if j < len(lines):
                                check_line = lines[j].strip()
                                
                                # 检查API_TIMER
                                for timer_pattern in self.timer_patterns:
                                    if re.search(timer_pattern, check_line):
                                        current_api['has_timer'] = True
                                        current_api['timer_type'] = timer_pattern.replace('@', '').replace('\\', '')
                                        break
                                
                                # 检查缓存装饰器
                                for cache_pattern in self.cache_patterns:
                                    if re.search(cache_pattern, check_line):
                                        current_api['has_cache'] = True
                                        current_api['cache_type'] = cache_pattern.replace('@', '').replace('\\', '')
                                        break
                        
                        self.api_details.append(current_api)
                        self.total_apis += 1
                        
                        if current_api['has_timer']:
                            self.apis_with_timer += 1
                        if current_api['has_cache']:
                            self.apis_with_cache += 1
                        
                        break
                        
        except Exception as e:
            print(f"扫描文件 {file_path} 时出错: {e}")
    
    def scan_directory(self, directory):
        """递归扫描目录中的Python文件"""
        directory = Path(directory)
        
        # 要扫描的文件类型
        file_patterns = ['*.py']
        
        # 要排除的目录
        exclude_dirs = {
            '__pycache__', '.git', 'venv', 'env', 'node_modules',
            'migrations', '.pytest_cache', 'logs', 'static', 'media',
            'mini_build', 'bazi-master'  # 排除构建目录和第三方代码
        }
        
        for pattern in file_patterns:
            for file_path in directory.rglob(pattern):
                # 检查是否在排除目录中
                if any(exclude_dir in file_path.parts for exclude_dir in exclude_dirs):
                    continue
                    
                self.scan_file(file_path)
    
    def generate_report(self):
        """生成统计报告"""
        report = {
            'scan_time': datetime.now().isoformat(),
            'summary': {
                'total_apis': self.total_apis,
                'apis_with_timer': self.apis_with_timer,
                'apis_with_cache': self.apis_with_cache,
                'timer_coverage': f"{(self.apis_with_timer / self.total_apis * 100):.1f}%" if self.total_apis > 0 else "0%",
                'cache_coverage': f"{(self.apis_with_cache / self.total_apis * 100):.1f}%" if self.total_apis > 0 else "0%"
            },
            'details': self.api_details
        }
        
        return report
    
    def save_report(self, filename='api_statistics_report.json'):
        """保存报告到文件"""
        report = self.generate_report()

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        return filename

    def save_summary_report(self, filename='api_summary_report.txt'):
        """保存简洁的摘要报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("🚀 API统计报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"扫描时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("📊 总体统计:\n")
            f.write(f"  总API数量: {self.total_apis}\n")
            f.write(f"  使用API_TIMER的API: {self.apis_with_timer}\n")
            f.write(f"  使用缓存的API: {self.apis_with_cache}\n")
            f.write(f"  API_TIMER覆盖率: {(self.apis_with_timer / self.total_apis * 100):.1f}%\n" if self.total_apis > 0 else "  API_TIMER覆盖率: 0%\n")
            f.write(f"  缓存覆盖率: {(self.apis_with_cache / self.total_apis * 100):.1f}%\n\n" if self.total_apis > 0 else "  缓存覆盖率: 0%\n\n")

            # 按模块分组统计
            module_stats = {}
            for api in self.api_details:
                module = api['file'].split('/')[-2] if '/' in api['file'] else 'root'
                if module not in module_stats:
                    module_stats[module] = {'total': 0, 'timer': 0, 'cache': 0}

                module_stats[module]['total'] += 1
                if api['has_timer']:
                    module_stats[module]['timer'] += 1
                if api['has_cache']:
                    module_stats[module]['cache'] += 1

            f.write("📁 按模块统计:\n")
            for module, stats in sorted(module_stats.items()):
                timer_rate = (stats['timer'] / stats['total'] * 100) if stats['total'] > 0 else 0
                cache_rate = (stats['cache'] / stats['total'] * 100) if stats['total'] > 0 else 0
                f.write(f"  {module}: {stats['total']}个API, Timer:{stats['timer']}({timer_rate:.1f}%), Cache:{stats['cache']}({cache_rate:.1f}%)\n")

            # 需要优化的模块
            f.write("\n🔧 需要优化的模块 (Timer覆盖率<50%):\n")
            for module, stats in sorted(module_stats.items()):
                timer_rate = (stats['timer'] / stats['total'] * 100) if stats['total'] > 0 else 0
                if timer_rate < 50 and stats['total'] > 5:  # 只显示API数量>5且覆盖率<50%的模块
                    f.write(f"  ⚠️  {module}: {stats['total']}个API, Timer覆盖率仅{timer_rate:.1f}%\n")

            # 缓存使用较少的模块
            f.write("\n💾 缓存使用较少的模块 (Cache覆盖率<20%):\n")
            for module, stats in sorted(module_stats.items()):
                cache_rate = (stats['cache'] / stats['total'] * 100) if stats['total'] > 0 else 0
                if cache_rate < 20 and stats['total'] > 5:  # 只显示API数量>5且缓存覆盖率<20%的模块
                    f.write(f"  ⚠️  {module}: {stats['total']}个API, Cache覆盖率仅{cache_rate:.1f}%\n")

        return filename
    
    def print_summary(self):
        """打印统计摘要"""
        print("=" * 60)
        print("🚀 API统计报告")
        print("=" * 60)
        print(f"📊 总API数量: {self.total_apis}")
        print(f"⏱️  使用API_TIMER的API: {self.apis_with_timer}")
        print(f"🗄️  使用缓存的API: {self.apis_with_cache}")
        print(f"📈 API_TIMER覆盖率: {(self.apis_with_timer / self.total_apis * 100):.1f}%" if self.total_apis > 0 else "0%")
        print(f"📈 缓存覆盖率: {(self.apis_with_cache / self.total_apis * 100):.1f}%" if self.total_apis > 0 else "0%")
        print("=" * 60)
        
        # 按模块分组统计
        module_stats = {}
        for api in self.api_details:
            module = api['file'].split('/')[-2] if '/' in api['file'] else 'root'
            if module not in module_stats:
                module_stats[module] = {'total': 0, 'timer': 0, 'cache': 0}
            
            module_stats[module]['total'] += 1
            if api['has_timer']:
                module_stats[module]['timer'] += 1
            if api['has_cache']:
                module_stats[module]['cache'] += 1
        
        print("\n📁 按模块统计:")
        for module, stats in sorted(module_stats.items()):
            timer_rate = (stats['timer'] / stats['total'] * 100) if stats['total'] > 0 else 0
            cache_rate = (stats['cache'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"  {module}: {stats['total']}个API, Timer:{stats['timer']}({timer_rate:.1f}%), Cache:{stats['cache']}({cache_rate:.1f}%)")

def main():
    """主函数"""
    print("开始扫描API...")
    
    # 创建统计器
    stats = APIStatistics()
    
    # 扫描当前目录
    current_dir = os.getcwd()
    stats.scan_directory(current_dir)
    
    # 打印摘要
    stats.print_summary()
    
    # 保存详细报告
    report_file = stats.save_report()
    print(f"\n📄 详细报告已保存到: {report_file}")

    # 保存简洁摘要报告
    summary_file = stats.save_summary_report()
    print(f"📄 摘要报告已保存到: {summary_file}")

    return stats

if __name__ == "__main__":
    main()
