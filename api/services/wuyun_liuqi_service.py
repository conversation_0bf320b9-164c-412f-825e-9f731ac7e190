"""
五运六气服务模块
提供五运六气计算和数据获取功能
"""

import json
from datetime import datetime
import pytz

# 尝试导入五运六气核心模块
import os
import sys
import importlib.util

wuyunliuqi_core = None
WUYUNLIUQI_AVAILABLE = False

try:
    # 使用动态导入解决目录名包含连字符的问题
    current_dir = os.path.dirname(os.path.abspath(__file__))  # /path/to/api/services
    project_root = os.path.dirname(os.path.dirname(current_dir))  # 回到项目根目录

    # 构建模块文件的完整路径
    module_path = os.path.join(
        project_root,
        "api", "utils", "bazi_websocket_demo", "bazi-master",
        "bazi_api", "bazi_api", "complete_wuyunliuqi", "great_contribution.py"
    )

    print(f"🔍 尝试加载五运六气模块: {module_path}")
    print(f"🔍 文件是否存在: {os.path.exists(module_path)}")

    if os.path.exists(module_path):
        # 使用 importlib.util 动态导入模块
        spec = importlib.util.spec_from_file_location("great_contribution", module_path)
        wuyunliuqi_core = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(wuyunliuqi_core)

        # 测试模块是否可用
        if hasattr(wuyunliuqi_core, 'getWuYunLiuQi'):
            WUYUNLIUQI_AVAILABLE = True
            print("✅ 五运六气核心模块导入成功")
        else:
            print("⚠️ 五运六气核心模块导入失败: 缺少 getWuYunLiuQi 函数")
    else:
        print(f"⚠️ 五运六气核心模块导入失败: 文件不存在 {module_path}")

except Exception as e:
    WUYUNLIUQI_AVAILABLE = False
    print(f"⚠️ 五运六气核心模块导入失败: {e}")
    import traceback
    print(f"详细错误信息: {traceback.format_exc()}")


class WuyunLiuqiService:
    """五运六气服务类"""
    
    @staticmethod
    def get_wuyun_liuqi_data(date_str: str) -> dict:
        """
        获取指定日期的五运六气数据
        
        Args:
            date_str (str): 日期字符串，格式为 YYYY-MM-DD
            
        Returns:
            dict: 五运六气数据，包含司天、在泉、大运等信息
        """
        if not WUYUNLIUQI_AVAILABLE:
            return {
                "error": "五运六气系统不可用",
                "available": False
            }
        
        try:
            # 调用五运六气核心计算函数
            result_json = wuyunliuqi_core.getWuYunLiuQi(date_str)
            result_data = json.loads(result_json)[0]
            
            # 返回标准化的数据格式
            return {
                "success": True,
                "date": date_str,
                "data": {
                    "gongli": result_data.get('gongli', ''),
                    "nongli": result_data.get('nongli', ''),
                    "ganzhi": result_data.get('ganzhi', ''),
                    "sitian": result_data.get('sitian', ''),
                    "zaiquan": result_data.get('zaiquan', ''),
                    "dayun": result_data.get('dayun', ''),
                    "keqi": result_data.get('keqi', ''),
                    "zhuqi": result_data.get('zhuqi', ''),
                    "qi_shunxu": result_data.get('qi_shunxu', ''),
                    "jieqi": result_data.get('jieqi', ''),
                    "zaigong": result_data.get('zaigong', '')
                }
            }
            
        except Exception as e:
            return {
                "error": f"五运六气计算失败: {str(e)}",
                "success": False,
                "date": date_str
            }
    
    @staticmethod
    def get_current_wuyun_liuqi() -> dict:
        """
        获取当前日期的五运六气数据
        
        Returns:
            dict: 当前五运六气数据
        """
        # 获取当前日期（东八区）
        current_time = datetime.now(pytz.timezone('Asia/Shanghai'))
        current_date = current_time.strftime("%Y-%m-%d")
        
        result = WuyunLiuqiService.get_wuyun_liuqi_data(current_date)
        
        if result.get("success"):
            result["current_time"] = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        return result
    
    @staticmethod
    def get_birth_and_current_wuyun_liuqi(birth_date: str) -> dict:
        """
        获取出生日期和当前日期的五运六气数据
        
        Args:
            birth_date (str): 出生日期，格式为 YYYY-MM-DD
            
        Returns:
            dict: 包含出生和当前两个日期的五运六气数据
        """
        # 获取出生日期的五运六气
        birth_result = WuyunLiuqiService.get_wuyun_liuqi_data(birth_date)
        
        # 获取当前日期的五运六气
        current_result = WuyunLiuqiService.get_current_wuyun_liuqi()
        
        return {
            "birth": birth_result,
            "current": current_result,
            "comparison": {
                "birth_date": birth_date,
                "current_date": current_result.get("date", ""),
                "available": birth_result.get("success", False) and current_result.get("success", False)
            }
        }
    
    @staticmethod
    def format_wuyun_liuqi_for_analysis(wuyun_data: dict) -> dict:
        """
        格式化五运六气数据用于健康分析
        
        Args:
            wuyun_data (dict): 原始五运六气数据
            
        Returns:
            dict: 格式化后的数据，适合用于AI分析
        """
        if not wuyun_data.get("success") or "data" not in wuyun_data:
            return {
                "error": "五运六气数据无效",
                "formatted": False
            }
        
        data = wuyun_data["data"]
        
        return {
            "formatted": True,
            "analysis_data": {
                "sitian": data.get('sitian', '未知'),
                "zaiquan": data.get('zaiquan', '未知'),
                "dayun": data.get('dayun', '未知'),
                "keqi": data.get('keqi', '未知'),
                "zhuqi": data.get('zhuqi', '未知'),
                "qi_shunxu": data.get('qi_shunxu', '未知'),
                "jieqi": data.get('jieqi', '未知'),
                "zaigong": data.get('zaigong', '未知'),
                "ganzhi": data.get('ganzhi', '未知'),
                "nongli": data.get('nongli', '未知')
            },
            "summary": {
                "date": wuyun_data.get("date", ""),
                "main_qi": data.get('qi_shunxu', '未知'),
                "season_influence": f"{data.get('sitian', '未知')}司天，{data.get('zaiquan', '未知')}在泉",
                "current_solar_term": data.get('jieqi', '未知')
            }
        }
    
    @staticmethod
    def is_available() -> bool:
        """
        检查五运六气系统是否可用
        
        Returns:
            bool: 系统是否可用
        """
        return WUYUNLIUQI_AVAILABLE
    
    @staticmethod
    def get_system_status() -> dict:
        """
        获取五运六气系统状态
        
        Returns:
            dict: 系统状态信息
        """
        return {
            "available": WUYUNLIUQI_AVAILABLE,
            "module_path": "api.utils.bazi_websocket_demo.bazi_master.bazi_api.bazi_api.complete_wuyunliuqi.great_contribution",
            "status": "正常" if WUYUNLIUQI_AVAILABLE else "不可用",
            "description": "五运六气计算系统" if WUYUNLIUQI_AVAILABLE else "五运六气模块导入失败"
        }
