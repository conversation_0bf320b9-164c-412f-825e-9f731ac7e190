"""
凶煞类神煞计算

包含空亡、劫煞、灾煞、十恶大败等凶煞类神煞的计算函数。
"""

from ..utils.helpers import get_jiazi_order, check_relation, get_nayin_wuxing, get_tiangan_yinyang


def kongwang(ganzhi: str, dizhi: str) -> int:
    """
    空亡
    甲子旬在戌亥。甲戌旬在申酉。甲申旬在午未。
    甲午旬在辰巳。甲辰旬在寅卯。甲寅旬在子丑。
    查法：以日/年柱，见余三地支
    
    Args:
        ganzhi: 干支
        dizhi: 地支
        
    Returns:
        1表示是空亡，0表示不是
    """
    idx = get_jiazi_order(ganzhi)
    
    if ((idx <= 10) and dizhi in ["戌", "亥"]) or \
       ((10 < idx <= 20) and dizhi in ["申", "酉"]) or \
       ((20 < idx <= 30) and dizhi in ["午", "未"]) or \
       ((30 < idx <= 40) and dizhi in ["辰", "巳"]) or \
       ((40 < idx <= 50) and dizhi in ["寅", "卯"]) or \
       ((idx > 50) and dizhi in ["子", "丑"]):
        return 1
    
    return 0


def jiesha(base_zhi: str, target_zhi: str) -> int:
    """
    劫煞
    申子辰见巳, 亥卯未见申,
    寅午戌见亥, 巳酉丑见寅.
    查法: 以年柱或日柱为主, 四柱地支见之者为是.
    
    Args:
        base_zhi: 年支/日支
        target_zhi: 目标地支
        
    Returns:
        1表示是劫煞，0表示不是
    """
    groups = {
        "亥": ["寅", "午", "戌"],
        "巳": ["申", "子", "辰"],
        "寅": ["巳", "酉", "丑"],
        "申": ["亥", "卯", "未"]
    }
    
    return 1 if groups.get(target_zhi, []) and base_zhi in groups[target_zhi] else 0


def zaisha(year_zhi: str, target_zhi: str) -> int:
    """
    灾煞
    申子辰见午, 亥卯未见酉,
    寅午戌见子, 巳酉丑见卯.
    查法: 以年支为主, 四柱地支中见之者为是.
    
    Args:
        year_zhi: 年支
        target_zhi: 目标地支
        
    Returns:
        1表示是灾煞，0表示不是
    """
    groups = {
        "午": ["申", "子", "辰"],
        "子": ["寅", "午", "戌"],
        "卯": ["巳", "酉", "丑"],
        "酉": ["亥", "卯", "未"]
    }
    
    return 1 if groups.get(target_zhi, []) and year_zhi in groups[target_zhi] else 0


def wangshen(base_zhi: str, target_zhi: str) -> int:
    """
    亡神
    寅午戌见巳, 亥卯未见寅,
    巳酉丑见申, 申子辰见亥.
    查法: 以年\日支查余三支.
    
    Args:
        base_zhi: 年支/日支
        target_zhi: 目标地支
        
    Returns:
        1表示是亡神，0表示不是
    """
    groups = {
        "亥": ["申", "子", "辰"],
        "巳": ["寅", "午", "戌"],
        "申": ["巳", "酉", "丑"],
        "寅": ["亥", "卯", "未"]
    }
    
    return 1 if groups.get(target_zhi, []) and base_zhi in groups[target_zhi] else 0


def gucheng(year_zhi: str, target_zhi: str) -> int:
    """
    孤辰
    亥子丑人, 见寅为孤, 见戌为寡.
    寅卯辰人, 见巳为孤, 见丑为寡.
    巳午未人, 见申为孤, 见辰为寡.
    申酉戌人, 见亥为孤, 见未为寡.
    查法: 以年支为准, 四柱其它地支见者为是.
    
    Args:
        year_zhi: 年支
        target_zhi: 目标地支
        
    Returns:
        1表示是孤辰，0表示不是
    """
    groups = {
        "寅": ["亥", "子", "丑"],
        "巳": ["寅", "卯", "辰"],
        "申": ["巳", "午", "未"],
        "亥": ["申", "酉", "戌"]
    }
    
    return 1 if groups.get(target_zhi, []) and year_zhi in groups[target_zhi] else 0


def guashu(year_zhi: str, target_zhi: str) -> int:
    """
    寡宿
    亥子丑人, 见寅为孤, 见戌为寡.
    寅卯辰人, 见巳为孤, 见丑为寡.
    巳午未人, 见申为孤, 见辰为寡.
    申酉戌人, 见亥为孤, 见未为寡.
    查法: 以年支为准, 四柱其它地支见者为是.
    
    Args:
        year_zhi: 年支
        target_zhi: 目标地支
        
    Returns:
        1表示是寡宿，0表示不是
    """
    groups = {
        "戌": ["亥", "子", "丑"],
        "丑": ["寅", "卯", "辰"],
        "辰": ["巳", "午", "未"],
        "未": ["申", "酉", "戌"]
    }
    
    return 1 if groups.get(target_zhi, []) and year_zhi in groups[target_zhi] else 0


def tianluo(base_zhi: str, target_zhi: str) -> int:
    """
    天罗
    查法一：以年支/日支查余三支
    戌亥为天罗，辰巳为地网；
    戌见亥, 亥见戌为天罗；辰见巳, 巳见辰为地网。
    
    Args:
        base_zhi: 年支/日支
        target_zhi: 目标地支
        
    Returns:
        1表示是天罗，0表示不是
    """
    return 1 if ((base_zhi == "戌" and target_zhi == "亥") or 
                (base_zhi == "亥" and target_zhi == "戌")) else 0


def diwang(base_zhi: str, target_zhi: str) -> int:
    """
    地网
    查法一：以年支/日支查余三支
    戌亥为天罗，辰巳为地网；
    戌见亥, 亥见戌为天罗；辰见巳, 巳见辰为地网。
    
    Args:
        base_zhi: 年支/日支
        target_zhi: 目标地支
        
    Returns:
        1表示是地网，0表示不是
    """
    return 1 if ((base_zhi == "辰" and target_zhi == "巳") or 
                (base_zhi == "巳" and target_zhi == "辰")) else 0


def shiedabai(day_gan: str, day_zhi: str) -> int:
    """
    十恶大败
    甲辰乙巳与壬申, 丙申丁亥及庚辰,
    戊戌癸亥加辛巳, 己丑都来十位神.
    查法: 四柱日柱逢之即是.
    
    Args:
        day_gan: 日干
        day_zhi: 日支
        
    Returns:
        1表示是十恶大败，0表示不是
    """
    conditions = {
        "甲": "辰", "乙": "巳", "壬": "申", "丙": "申",
        "丁": "亥", "庚": "辰", "戊": "戌", "癸": "亥",
        "辛": "巳", "己": "丑"
    }
    
    return 1 if conditions.get(day_gan) == day_zhi else 0


def taohua(base_zhi: str, target_zhi: str) -> int:
    """
    桃花
    申子辰在酉, 寅午戌在卯,
    巳酉丑在午, 亥卯未在子.
    查法: 以年支或日支查四柱其它地支.
    
    Args:
        base_zhi: 年支/日支
        target_zhi: 目标地支
        
    Returns:
        1表示是桃花，0表示不是
    """
    conditions = {
        "申": "酉", "子": "酉", "辰": "酉",
        "寅": "卯", "午": "卯", "戌": "卯",
        "巳": "午", "酉": "午", "丑": "午",
        "亥": "子", "卯": "子", "未": "子"
    }
    
    return 1 if conditions.get(base_zhi) == target_zhi else 0


def guluan(day_gan: str, day_zhi: str) -> int:
    """
    孤鸾煞
    甲寅日。乙巳日。丙午日。丁巳日。戊午日。戊申日。辛亥日。壬子日。
    查法: 查日柱
    
    Args:
        day_gan: 日干
        day_zhi: 日支
        
    Returns:
        1表示是孤鸾煞，0表示不是
    """
    valid_combinations = {
        "乙巳", "丁巳", "辛亥", "戊申",
        "甲寅", "戊午", "壬子", "丙午"
    }
    
    return 1 if day_gan + day_zhi in valid_combinations else 0


def yingyangchacuo(day_gan: str, day_zhi: str) -> int:
    """
    阴差阳错
    丙子, 丁丑, 戊寅, 辛卯, 壬辰, 癸巳,
    丙午, 丁未, 戊申, 辛酉, 壬戌, 癸亥.
    查法: 日柱见者为是.
    
    Args:
        day_gan: 日干
        day_zhi: 日支
        
    Returns:
        1表示是阴差阳错，0表示不是
    """
    conditions = {
        "丙": ["子", "午"],
        "丁": ["丑", "未"],
        "戊": ["寅", "申"],
        "辛": ["卯", "酉"],
        "壬": ["辰", "戌"],
        "癸": ["巳", "亥"]
    }
    
    return 1 if conditions.get(day_gan, []) and day_zhi in conditions[day_gan] else 0


def sifei(month_zhi: str, day_gan: str, day_zhi: str) -> int:
    """
    四废
    春庚申, 辛酉,
    夏壬子, 癸亥,
    秋甲寅, 乙卯,
    冬丙午, 丁巳.
    查法: 凡四柱日干支生于该季为是.
    
    Args:
        month_zhi: 月支
        day_gan: 日干
        day_zhi: 日支
        
    Returns:
        1表示是四废，0表示不是
    """
    conditions = {
        "寅": ["庚申", "辛酉"], "卯": ["庚申", "辛酉"], "辰": ["庚申", "辛酉"],  # 春
        "巳": ["壬子", "癸亥"], "午": ["壬子", "癸亥"], "未": ["壬子", "癸亥"],  # 夏
        "申": ["甲寅", "乙卯"], "酉": ["甲寅", "乙卯"], "戌": ["甲寅", "乙卯"],  # 秋
        "亥": ["丙午", "丁巳"], "子": ["丙午", "丁巳"], "丑": ["丙午", "丁巳"]   # 冬
    }
    
    valid_combinations = conditions.get(month_zhi, [])
    return 1 if day_gan + day_zhi in valid_combinations else 0


def shangmen(year_zhi: str, target_zhi: str) -> int:
    """
    丧门
    查法：以年支查余三支

    Args:
        year_zhi: 年支
        target_zhi: 目标地支

    Returns:
        1表示是丧门，0表示不是
    """
    return check_relation(year_zhi, target_zhi, "shangmen")


def diaoke(year_zhi: str, target_zhi: str) -> int:
    """
    吊客
    查法：以年支查余三支

    Args:
        year_zhi: 年支
        target_zhi: 目标地支

    Returns:
        1表示是吊客，0表示不是
    """
    return check_relation(year_zhi, target_zhi, "diaoke")


def pima(year_zhi: str, target_zhi: str) -> int:
    """
    披麻
    查法：以年支查余三支

    Args:
        year_zhi: 年支
        target_zhi: 目标地支

    Returns:
        1表示是披麻，0表示不是
    """
    return check_relation(year_zhi, target_zhi, "pima")


def tongzi(month_zhi: str, year_nayin: str, target_zhi: str) -> int:
    """
    童子
    春秋寅子贵，冬夏卯未辰；
    金木马卯合，水火鸡犬多；
    土命逢辰巳，童子定不错。
    查法：
    1、命造生在春季或秋季的（以月令算），日支或时支见寅或子的。
    2、命造生在冬季或夏季的（以月令算），日支或时支见卯、未或辰的。
    3、年柱纳音为金或木的，日支或时支见午或卯的。
    4、年柱纳音为水或火的，日支或时支见酉或戌的。
    5、年柱纳音为土命的，日支或时支见辰或巳的。

    Args:
        month_zhi: 月支
        year_nayin: 年柱纳音
        target_zhi: 日支或时支

    Returns:
        1表示是童子，0表示不是
    """
    # 季节条件
    first_conditions = {
        "寅": ["寅", "子"], "卯": ["寅", "子"], "辰": ["寅", "子"],  # 春
        "申": ["寅", "子"], "酉": ["寅", "子"], "戌": ["寅", "子"],  # 秋
        "巳": ["卯", "未", "辰"], "午": ["卯", "未", "辰"], "未": ["卯", "未", "辰"],  # 夏
        "亥": ["卯", "未", "辰"], "子": ["卯", "未", "辰"], "丑": ["卯", "未", "辰"]   # 冬
    }

    if first_conditions.get(month_zhi, []) and target_zhi in first_conditions[month_zhi]:
        return 1

    # 纳音条件
    element = get_nayin_wuxing(year_nayin)
    second_conditions = {
        "金": ["午", "卯"], "木": ["午", "卯"],
        "水": ["酉", "戌"], "火": ["酉", "戌"],
        "土": ["辰", "巳"]
    }

    if second_conditions.get(element, []) and target_zhi in second_conditions[element]:
        return 1

    return 0


def liuxia(day_gan: str, dizhi: str) -> int:
    """
    流霞
    甲日酉。乙日戌。丙日未。丁日申。戊日巳。
    己日午。庚日辰。辛日卯。壬日亥。癸日寅。
    查法：以日干查四地支

    Args:
        day_gan: 日干
        dizhi: 地支

    Returns:
        1表示是流霞，0表示不是
    """
    valid_combinations = {
        "甲酉", "乙戌", "丙未", "丁申", "戊巳",
        "己午", "庚辰", "辛卯", "壬亥", "癸寅"
    }

    return 1 if day_gan + dizhi in valid_combinations else 0


def hongyan(day_gan: str, dizhi: str) -> int:
    """
    红艳
    甲日午。乙日午。丙日寅。丁日未。戊日辰。
    己日辰。庚日戌。辛日酉。壬日子。癸日申。
    查法：以日干查四地支

    Args:
        day_gan: 日干
        dizhi: 地支

    Returns:
        1表示是红艳，0表示不是
    """
    valid_combinations = {
        "甲午", "乙午", "丙寅", "丁未", "戊辰",
        "己辰", "庚戌", "辛酉", "壬子", "癸申"
    }

    return 1 if day_gan + dizhi in valid_combinations else 0


def kuigang(day_gan: str, day_zhi: str) -> int:
    """
    魁罡贵人
    壬辰庚戌与庚辰, 戊戌魁罡四座神,
    不见财官刑煞并,身行旺地贵无伦.
    查法: 日柱见者为是

    Args:
        day_gan: 日干
        day_zhi: 日支

    Returns:
        1表示是魁罡，0表示不是
    """
    conditions = {
        "壬": "辰",
        "庚": ["戌", "辰"],
        "戊": "戌"
    }

    if day_gan in conditions:
        if isinstance(conditions[day_gan], list):
            return 1 if day_zhi in conditions[day_gan] else 0
        else:
            return 1 if conditions[day_gan] == day_zhi else 0

    return 0


def bazhuan(day_gan: str, day_zhi: str) -> int:
    """
    八专
    甲寅日。乙卯日。丁未日。戊戌日。己未日。庚申日。辛酉日。癸丑日。
    查法: 查日柱

    Args:
        day_gan: 日干
        day_zhi: 日支

    Returns:
        1表示是八专，0表示不是
    """
    valid_combinations = {"甲寅", "乙卯", "丁未", "戊戌", "己未", "庚申", "辛酉", "癸丑"}
    return 1 if day_gan + day_zhi in valid_combinations else 0


def jiuchou(day_gan: str, day_zhi: str) -> int:
    """
    九丑
    丁酉日。戊子日。戊午日。己卯日。己酉日。辛卯日。辛酉日。壬子日。壬午日。
    查法：查日柱

    Args:
        day_gan: 日干
        day_zhi: 日支

    Returns:
        1表示是九丑，0表示不是
    """
    valid_combinations = {"丁酉", "戊子", "戊午", "己卯", "己酉", "辛卯", "辛酉", "壬子", "壬午"}
    return 1 if day_gan + day_zhi in valid_combinations else 0


def shiling(day_gan: str, day_zhi: str) -> int:
    """
    十灵日
    古诀："男带十灵好文章，女带十灵好衣裳。"
    甲辰、乙亥、丙辰、丁酉、戊午、庚戌、庚寅、辛亥、壬寅、癸未
    查法：查日柱

    Args:
        day_gan: 日干
        day_zhi: 日支

    Returns:
        1表示是十灵，0表示不是
    """
    valid_combinations = {
        "甲辰", "乙亥", "丙辰", "丁酉", "戊午",
        "庚戌", "庚寅", "辛亥", "壬寅", "癸未"
    }
    return 1 if day_gan + day_zhi in valid_combinations else 0


def yuancheng(year_zhi: str, target_zhi: str, is_man: bool, year_gan_yinyang: bool) -> int:
    """
    元辰
    阳男阴女：子年未。丑年申。寅年酉。卯年戌。辰年亥。巳年子。午年丑。未年寅。申年卯。酉年辰。戌年巳。亥年午。
    阴男阳女：子年巳。丑年午。寅年未。卯年申。辰年酉。巳年戌。午年亥。未年子。申年丑。酉年寅。戌年卯。亥年辰。
    查法：以年支查余三支

    Args:
        year_zhi: 年支
        target_zhi: 目标地支
        is_man: 性别，True为男性
        year_gan_yinyang: 年干阴阳，True为阳干

    Returns:
        1表示是元辰，0表示不是
    """
    # 阳男阴女
    yang_male_yin_female = {
        "子": "未", "丑": "申", "寅": "酉", "卯": "戌",
        "辰": "亥", "巳": "子", "午": "丑", "未": "寅",
        "申": "卯", "酉": "辰", "戌": "巳", "亥": "午"
    }

    # 阴男阳女
    yin_male_yang_female = {
        "子": "巳", "丑": "午", "寅": "未", "卯": "申",
        "辰": "酉", "巳": "戌", "午": "亥", "未": "子",
        "申": "丑", "酉": "寅", "戌": "卯", "亥": "辰"
    }

    # 判断是阳男阴女还是阴男阳女
    is_yang_male_yin_female = is_man == year_gan_yinyang

    if is_yang_male_yin_female:
        return 1 if yang_male_yin_female.get(year_zhi) == target_zhi else 0
    else:
        return 1 if yin_male_yang_female.get(year_zhi) == target_zhi else 0
