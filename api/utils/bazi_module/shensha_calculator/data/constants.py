"""
基础常量定义

包含六十甲子、天干地支、五行等基础常量数据。
"""

from typing import List

# 六十甲子表（与JavaScript版本完全一致）
JIAZI: List[str] = [
    "　",  # 占位符，使索引从1开始
    "甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "癸酉",
    "甲戌", "乙亥", "丙子", "丁丑", "戊寅", "己卯", "庚辰", "辛巳", "壬午", "癸未",
    "甲申", "乙酉", "丙戌", "丁亥", "戊子", "己丑", "庚寅", "辛卯", "壬辰", "癸巳",
    "甲午", "乙未", "丙申", "丁酉", "戊戌", "己亥", "庚子", "辛丑", "壬寅", "癸卯",
    "甲辰", "乙巳", "丙午", "丁未", "戊申", "己酉", "庚戌", "辛亥", "壬子", "癸丑",
    "甲寅", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申", "辛酉", "壬戌", "癸亥"
]

# 十天干
TIANGAN: List[str] = [
    "甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"
]

# 十二地支
DIZHI: List[str] = [
    "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"
]

# 五行
WUXING: List[str] = [
    "金", "木", "水", "火", "土"
]

# 月份对应地支（用于月令判断）
MONTH_DIZHI: List[str] = [
    "丑",  # 十二月
    "寅",  # 正月
    "卯",  # 二月
    "辰",  # 三月
    "巳",  # 四月
    "午",  # 五月
    "未",  # 六月
    "申",  # 七月
    "酉",  # 八月
    "戌",  # 九月
    "亥",  # 十月
    "子"   # 十一月
]
