# 八字分析系统前端对接文档

## 📋 概述

本文档详细说明八字分析系统的前端对接方案，包括HTTP API数据获取和WebSocket流式AI解读的完整实现。

## 🏗️ 系统架构

### 双重架构设计
- **HTTP API** - 快速获取八字计算数据（RouterTest1）
- **WebSocket** - 流式AI解读体验

### 数据流程
```
用户输入生辰八字 → HTTP API获取数据 → 显示八字信息 → WebSocket获取AI解读 → 流式显示解读内容
```

## 🔌 HTTP API接口对接

### 基础配置

```javascript
// API基础配置
const API_BASE_URL = '/api/routertest1';
const WS_BASE_URL = 'ws://baseurl/ws/bazi_advice/';

// 需要请求头配置

```

### 1. 八字基本信息接口

```javascript
/**
 * 获取八字基本信息
 * @param {Object} birthData - 生辰信息
 * @returns {Promise} API响应
 */
async function getBaziBasicInfo(birthData) {
    try {
        const response = await fetch(`${API_BASE_URL}/bazi/basic`, {
            method: 'POST',
            headers: getHeaders(),
            body: JSON.stringify({
                year: birthData.year,
                month: birthData.month,
                day: birthData.day,
                hour: birthData.hour || 0,
                minute: birthData.minute || 0,
                gender: birthData.gender, // 1男0女
                sect: birthData.sect || 2  // 流派
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('获取八字基本信息失败:', error);
        throw error;
    }
}

// 使用示例
const basicInfo = await getBaziBasicInfo({
    year: 2001,
    month: 7,
    day: 28,
    hour: 5,
    minute: 0,
    gender: 0
});

console.log('八字:', basicInfo.eight_char);
console.log('阳历:', basicInfo.solar_date);
console.log('农历:', basicInfo.lunar_date);
```

### 2. 十神分析接口

```javascript
/**
 * 获取十神分析
 * @param {Object} birthData - 生辰信息
 * @returns {Promise} 十神分析数据
 */
async function getShishenAnalysis(birthData) {
    try {
        const response = await fetch(`${API_BASE_URL}/bazi/shishen`, {
            method: 'POST',
            headers: getHeaders(),
            body: JSON.stringify(birthData)
        });
        
        const result = await response.json();
        return result.success ? result.data : null;
    } catch (error) {
        console.error('获取十神分析失败:', error);
        return null;
    }
}

// 使用示例
const shishenData = await getShishenAnalysis(birthData);
if (shishenData) {
    console.log('天干十神:', shishenData.gan_shishen);
    console.log('地支十神:', shishenData.zhi_shishen);
}
```

### 3. 神煞分析接口

```javascript
/**
 * 获取神煞分析
 * @param {Object} birthData - 生辰信息
 * @returns {Promise} 神煞分析数据
 */
async function getShenshaAnalysis(birthData) {
    try {
        const response = await fetch(`${API_BASE_URL}/bazi/shensha`, {
            method: 'POST',
            headers: getHeaders(),
            body: JSON.stringify(birthData)
        });
        
        const result = await response.json();
        return result.success ? result.data : null;
    } catch (error) {
        console.error('获取神煞分析失败:', error);
        return null;
    }
}

// 使用示例
const shenshaData = await getShenshaAnalysis(birthData);
if (shenshaData) {
    console.log('神煞总数:', shenshaData.total_count);
    console.log('各柱神煞:', shenshaData.pillars);
}
```

### 4. 完整八字分析接口

```javascript
/**
 * 获取完整八字分析
 * @param {Object} birthData - 生辰信息
 * @returns {Promise} 完整分析数据
 */
async function getCompleteAnalysis(birthData) {
    try {
        const response = await fetch(`${API_BASE_URL}/bazi/complete`, {
            method: 'POST',
            headers: getHeaders(),
            body: JSON.stringify(birthData)
        });
        
        const result = await response.json();
        return result.success ? result.data : null;
    } catch (error) {
        console.error('获取完整分析失败:', error);
        return null;
    }
}

// 使用示例
const completeData = await getCompleteAnalysis(birthData);
if (completeData) {
    console.log('基本信息:', completeData.basic_info);
    console.log('十神分析:', completeData.shishen_analysis);
    console.log('神煞分析:', completeData.shensha_analysis);
}
```

## 🌊 WebSocket流式AI解读对接

### WebSocket连接管理

```javascript
class BaziWebSocketClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.messageHandlers = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }
    
    /**
     * 建立WebSocket连接
     */
    connect() {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(WS_BASE_URL);
                
                this.ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    resolve();
                };
                
                this.ws.onmessage = (event) => {
                    this.handleMessage(JSON.parse(event.data));
                };
                
                this.ws.onclose = () => {
                    console.log('WebSocket连接已关闭');
                    this.isConnected = false;
                    this.handleReconnect();
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    reject(error);
                };
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        const { type, content, done } = data;
        
        // 调用对应的消息处理器
        if (this.messageHandlers.has(type)) {
            this.messageHandlers.get(type)(content, done);
        }
    }
    
    /**
     * 注册消息处理器
     */
    onMessage(type, handler) {
        this.messageHandlers.set(type, handler);
    }
    
    /**
     * 发送消息
     */
    send(message) {
        if (this.isConnected && this.ws) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.error('WebSocket未连接');
        }
    }
    
    /**
     * 自动重连
     */
    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => this.connect(), 2000 * this.reconnectAttempts);
        }
    }
    
    /**
     * 关闭连接
     */
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
            this.isConnected = false;
        }
    }
}
```

### AI解读功能实现

```javascript
/**
 * 八字AI解读管理器
 */
class BaziAIInterpreter {
    constructor() {
        this.wsClient = new BaziWebSocketClient();
        this.setupMessageHandlers();
    }
    
    /**
     * 初始化连接
     */
    async init() {
        await this.wsClient.connect();
    }
    
    /**
     * 设置消息处理器
     */
    setupMessageHandlers() {
        // 八字分析建议
        this.wsClient.onMessage('bazi_advice', (content, done) => {
            this.handleBaziAdvice(content, done);
        });
        
        // 养生建议
        this.wsClient.onMessage('yangsheng_advice', (content, done) => {
            this.handleYangshengAdvice(content, done);
        });
        
        // 错误处理
        this.wsClient.onMessage('error', (content) => {
            this.handleError(content);
        });
    }
    
    /**
     * 请求八字AI解读
     * @param {Object} baziData - 八字数据
     * @param {Function} onChunk - 流式内容回调
     * @param {Function} onComplete - 完成回调
     */
    requestBaziInterpretation(baziData, onChunk, onComplete) {
        this.currentOnChunk = onChunk;
        this.currentOnComplete = onComplete;
        
        // 发送八字分析请求
        this.wsClient.send({
            type: 'bazi_analysis',
            data: {
                eight_char: baziData.basic_info.eight_char,
                gender: baziData.basic_info.gender,
                shishen: baziData.shishen_analysis,
                shensha: baziData.shensha_analysis
            }
        });
    }
    
    /**
     * 处理八字建议
     */
    handleBaziAdvice(content, done) {
        if (this.currentOnChunk) {
            this.currentOnChunk(content, done);
        }
        
        if (done && this.currentOnComplete) {
            this.currentOnComplete();
        }
    }
    
    /**
     * 处理养生建议
     */
    handleYangshengAdvice(content, done) {
        // 类似处理逻辑
        console.log('养生建议:', content);
    }
    
    /**
     * 处理错误
     */
    handleError(content) {
        console.error('AI解读错误:', content);
        if (this.currentOnComplete) {
            this.currentOnComplete(content);
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        this.wsClient.disconnect();
    }
}
```

## 🎨 完整前端实现示例

### 🚀 优雅紧凑的八字输入界面

```vue
<template>
  <div class="bazi-container">
    <!-- 背景粒子效果 -->
    <div class="particles-bg" ref="particlesBg"></div>

    <!-- 主标题区域 -->
    <div class="hero-section">
      <h1 class="main-title">
        <span class="title-char" v-for="(char, index) in titleChars" :key="index" :style="{ animationDelay: index * 0.1 + 's' }">
          {{ char }}
        </span>
      </h1>
      <p class="subtitle">探索命运奥秘，解读人生密码</p>
      <div class="divider"></div>
    </div>

    <!-- 紧凑型输入表单 -->
    <div class="input-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="icon-calendar"></i>
          请输入您的生辰信息
        </h3>
        <p class="card-subtitle">精准的时间信息将带来更准确的分析结果</p>
      </div>

      <form @submit.prevent="analyzeBazi" class="bazi-form">
        <!-- 日期输入区域 - 横向排列 -->
        <div class="form-section">
          <label class="section-label">出生日期</label>
          <div class="date-inputs">
            <div class="input-group">
              <input
                v-model="birthData.year"
                type="number"
                placeholder="年"
                min="1900"
                max="2030"
                class="date-input"
                required
              >
              <span class="input-suffix">年</span>
            </div>
            <div class="input-group">
              <input
                v-model="birthData.month"
                type="number"
                placeholder="月"
                min="1"
                max="12"
                class="date-input"
                required
              >
              <span class="input-suffix">月</span>
            </div>
            <div class="input-group">
              <input
                v-model="birthData.day"
                type="number"
                placeholder="日"
                min="1"
                max="31"
                class="date-input"
                required
              >
              <span class="input-suffix">日</span>
            </div>
          </div>
        </div>

        <!-- 时间和性别 - 同行显示 -->
        <div class="form-row">
          <div class="form-section flex-1">
            <label class="section-label">出生时间 <span class="optional">(可选)</span></label>
            <div class="time-inputs">
              <div class="input-group">
                <input
                  v-model="birthData.hour"
                  type="number"
                  placeholder="时"
                  min="0"
                  max="23"
                  class="time-input"
                >
                <span class="input-suffix">时</span>
              </div>
              <div class="input-group">
                <input
                  v-model="birthData.minute"
                  type="number"
                  placeholder="分"
                  min="0"
                  max="59"
                  class="time-input"
                >
                <span class="input-suffix">分</span>
              </div>
            </div>
          </div>

          <div class="form-section flex-1">
            <label class="section-label">性别</label>
            <div class="gender-selector">
              <label class="gender-option" :class="{ active: birthData.gender === 1 }">
                <input type="radio" v-model="birthData.gender" :value="1" hidden>
                <span class="gender-icon">♂</span>
                <span class="gender-text">男</span>
              </label>
              <label class="gender-option" :class="{ active: birthData.gender === 0 }">
                <input type="radio" v-model="birthData.gender" :value="0" hidden>
                <span class="gender-icon">♀</span>
                <span class="gender-text">女</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 提交按钮 -->
        <button type="submit" class="submit-btn" :disabled="loading" :class="{ loading }">
          <span v-if="!loading" class="btn-content">
            <i class="icon-magic"></i>
            开始分析命理
          </span>
          <span v-else class="btn-loading">
            <div class="spinner"></div>
            正在分析中...
          </span>
        </button>
      </form>
    </div>

    <!-- 结果展示区域 -->
    <transition name="slide-up" appear>
      <div v-if="baziData" class="results-section">
        <!-- 八字展示卡片 -->
        <div class="bazi-display-card">
          <div class="card-header">
            <h3 class="card-title">您的八字命盘</h3>
            <div class="bazi-info">
              <div class="bazi-main">{{ baziData.basic_info.eight_char }}</div>
              <div class="bazi-details">
                <span>{{ baziData.basic_info.solar_date }}</span>
                <span>{{ baziData.basic_info.lunar_date }}</span>
              </div>
            </div>
          </div>

          <!-- 四柱可视化 -->
          <div class="pillars-visual">
            <div class="pillar" v-for="(pillar, index) in pillars" :key="index">
              <div class="pillar-header">{{ pillar.name }}</div>
              <div class="pillar-gan" :class="getWuxingClass(pillar.gan)">{{ pillar.gan }}</div>
              <div class="pillar-zhi" :class="getWuxingClass(pillar.zhi)">{{ pillar.zhi }}</div>
            </div>
          </div>
        </div>

        <!-- AI解读区域 -->
        <div class="ai-interpretation-card" v-if="aiInterpretation">
          <div class="card-header">
            <h3 class="card-title">
              <i class="icon-brain"></i>
              AI智能解读
            </h3>
          </div>
          <div class="interpretation-content">
            <div class="typing-text" ref="typingText">{{ displayedText }}</div>
            <div v-if="interpretationLoading" class="ai-thinking">
              <div class="thinking-dots">
                <span></span><span></span><span></span>
              </div>
              <span>AI正在深度分析您的命理...</span>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'BaziAnalysis',
  data() {
    return {
      titleChars: '八字命理分析'.split(''),
      birthData: {
        year: '',
        month: '',
        day: '',
        hour: '',
        minute: '',
        gender: 1
      },
      baziData: null,
      aiInterpretation: '',
      displayedText: '',
      loading: false,
      interpretationLoading: false,
      aiInterpreter: null,
      typingSpeed: 30
    }
  },

  computed: {
    pillars() {
      if (!this.baziData) return [];
      const basic = this.baziData.basic_info;
      return [
        { name: '年柱', gan: basic.year_pillar[0], zhi: basic.year_pillar[1] },
        { name: '月柱', gan: basic.month_pillar[0], zhi: basic.month_pillar[1] },
        { name: '日柱', gan: basic.day_pillar[0], zhi: basic.day_pillar[1] },
        { name: '时柱', gan: basic.time_pillar[0], zhi: basic.time_pillar[1] }
      ];
    }
  },

  async mounted() {
    this.initParticles();
    this.aiInterpreter = new BaziAIInterpreter();
    await this.aiInterpreter.init();
  },

  methods: {
    async analyzeBazi() {
      this.loading = true;
      this.baziData = null;
      this.aiInterpretation = '';
      this.displayedText = '';

      try {
        this.baziData = await getCompleteAnalysis(this.birthData);

        if (this.baziData) {
          this.interpretationLoading = true;
          this.aiInterpreter.requestBaziInterpretation(
            this.baziData,
            this.onAIChunk,
            this.onAIComplete
          );
        }
      } catch (error) {
        this.$message.error('分析失败，请重试');
      } finally {
        this.loading = false;
      }
    },

    onAIChunk(content) {
      this.aiInterpretation += content;
      this.typeText();
    },

    onAIComplete() {
      this.interpretationLoading = false;
    },

    async typeText() {
      const target = this.aiInterpretation;
      while (this.displayedText.length < target.length) {
        this.displayedText += target[this.displayedText.length];
        await new Promise(resolve => setTimeout(resolve, this.typingSpeed));
      }
    },

    getWuxingClass(char) {
      const wuxingMap = {
        '甲': 'wood', '乙': 'wood', '丙': 'fire', '丁': 'fire',
        '戊': 'earth', '己': 'earth', '庚': 'metal', '辛': 'metal',
        '壬': 'water', '癸': 'water', '子': 'water', '亥': 'water',
        '寅': 'wood', '卯': 'wood', '巳': 'fire', '午': 'fire',
        '申': 'metal', '酉': 'metal', '辰': 'earth', '戌': 'earth',
        '丑': 'earth', '未': 'earth'
      };
      return `wuxing-${wuxingMap[char] || 'default'}`;
    },

    initParticles() {
      // 粒子背景效果
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      this.$refs.particlesBg.appendChild(canvas);

      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;

      const particles = [];
      for (let i = 0; i < 50; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          size: Math.random() * 2 + 1
        });
      }

      function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
          particle.x += particle.vx;
          particle.y += particle.vy;

          if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
          if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
          ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
          ctx.fill();
        });

        requestAnimationFrame(animate);
      }
      animate();
    }
  }
}
</script>
```

### 🎨 高级CSS样式设计

```css
/* 全局样式重置和基础设置 */
.bazi-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 粒子背景效果 */
.particles-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

/* 主标题区域 */
.hero-section {
  text-align: center;
  padding: 40px 20px 30px;
  position: relative;
  z-index: 1;
}

.main-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: white;
  margin: 0 0 16px;
  text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.title-char {
  display: inline-block;
  animation: titleFloat 2s ease-in-out infinite;
  animation-fill-mode: both;
}

@keyframes titleFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

.subtitle {
  font-size: 1.1rem;
  color: rgba(255,255,255,0.9);
  margin: 0 0 24px;
  font-weight: 300;
}

.divider {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, white, transparent);
  margin: 0 auto;
  border-radius: 1px;
}

/* 输入卡片 - 紧凑设计 */
.input-card {
  max-width: 480px;
  margin: 0 auto 30px;
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px 28px;
  box-shadow: 0 15px 40px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255,255,255,0.2);
}

.card-header {
  text-align: center;
  margin-bottom: 24px;
}

.card-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.card-subtitle {
  color: #718096;
  font-size: 0.85rem;
  margin: 0;
}

/* 表单样式 - 紧凑布局 */
.bazi-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.flex-1 {
  flex: 1;
}

.section-label {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
}

.optional {
  color: #a0aec0;
  font-weight: 400;
  font-size: 0.8rem;
}

/* 日期输入 - 横向紧凑 */
.date-inputs {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 10px;
}

.time-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input, .time-input {
  width: 100%;
  padding: 12px 14px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
  outline: none;
}

.date-input:focus, .time-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.input-suffix {
  position: absolute;
  right: 14px;
  color: #a0aec0;
  font-size: 0.85rem;
  pointer-events: none;
}

/* 性别选择器 - 紧凑设计 */
.gender-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.gender-option {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  font-size: 0.9rem;
}

.gender-option:hover {
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.gender-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.gender-icon {
  font-size: 1.1rem;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-content, .btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 结果展示区域 */
.results-section {
  max-width: 700px;
  margin: 0 auto;
  padding: 0 20px 40px;
  position: relative;
  z-index: 1;
}

.bazi-display-card, .ai-interpretation-card {
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(20px);
  border-radius: 18px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 12px 30px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
}

/* 八字信息展示 */
.bazi-info {
  text-align: center;
  margin-top: 16px;
}

.bazi-main {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 10px;
  letter-spacing: 3px;
}

.bazi-details {
  display: flex;
  justify-content: center;
  gap: 16px;
  color: #718096;
  font-size: 0.85rem;
}

/* 四柱可视化 */
.pillars-visual {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-top: 20px;
}

.pillar {
  text-align: center;
  padding: 12px;
  background: #f7fafc;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.pillar-header {
  font-size: 0.75rem;
  color: #718096;
  margin-bottom: 6px;
  font-weight: 500;
}

.pillar-gan, .pillar-zhi {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 3px 0;
  padding: 6px;
  border-radius: 6px;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* 五行颜色 */
.wuxing-wood { background: linear-gradient(135deg, #48bb78, #38a169); }
.wuxing-fire { background: linear-gradient(135deg, #f56565, #e53e3e); }
.wuxing-earth { background: linear-gradient(135deg, #ed8936, #dd6b20); }
.wuxing-metal { background: linear-gradient(135deg, #a0aec0, #718096); }
.wuxing-water { background: linear-gradient(135deg, #4299e1, #3182ce); }

/* AI解读区域 */
.interpretation-content {
  margin-top: 16px;
}

.typing-text {
  line-height: 1.7;
  font-size: 0.95rem;
  color: #2d3748;
  white-space: pre-wrap;
  min-height: 80px;
}

.ai-thinking {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #718096;
  font-style: italic;
  margin-top: 12px;
  font-size: 0.9rem;
}

.thinking-dots {
  display: flex;
  gap: 3px;
}

.thinking-dots span {
  width: 5px;
  height: 5px;
  background: #cbd5e0;
  border-radius: 50%;
  animation: thinking 1.4s ease-in-out infinite both;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

/* 过渡动画 */
.slide-up-enter-active {
  transition: all 0.5s ease-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.2rem;
  }

  .input-card {
    margin: 0 16px 24px;
    padding: 24px 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .date-inputs {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .pillars-visual {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .bazi-details {
    flex-direction: column;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 30px 16px 24px;
  }

  .main-title {
    font-size: 1.8rem;
  }

  .input-card {
    margin: 0 12px 20px;
    padding: 20px 16px;
  }

  .date-inputs {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .time-inputs {
    grid-template-columns: 1fr;
  }

  .results-section {
    padding: 0 12px 30px;
  }

  .bazi-display-card, .ai-interpretation-card {
    padding: 18px;
  }
}

/* 图标字体 */
.icon-calendar::before { content: "📅"; }
.icon-magic::before { content: "✨"; }
.icon-brain::before { content: "🧠"; }
```

## 🔧 错误处理和优化

### 错误处理策略


    updateData(baziData) {
        this.updatePillars(baziData.basic_info);
        this.updateShishen(baziData.shishen_analysis);
        this.updateShensha(baziData.shensha_analysis);
    }

    updatePillars(basicInfo) {
        const pillars = ['year', 'month', 'day', 'time'];
        const pillarData = [
            basicInfo.year_pillar,
            basicInfo.month_pillar,
            basicInfo.day_pillar,
            basicInfo.time_pillar
        ];

        pillars.forEach((pillar, index) => {
            const pillarEl = this.container.querySelector(`.${pillar}-pillar`);
            const [gan, zhi] = pillarData[index].split('');

            pillarEl.querySelector('.gan').textContent = gan;
            pillarEl.querySelector('.zhi').textContent = zhi;

            // 添加五行颜色
            this.addWuxingColor(pillarEl.querySelector('.gan'), gan);
            this.addWuxingColor(pillarEl.querySelector('.zhi'), zhi);
        });
    }

    addWuxingColor(element, char) {
        const wuxingMap = {
            '甲': 'wood', '乙': 'wood',
            '丙': 'fire', '丁': 'fire',
            '戊': 'earth', '己': 'earth',
            '庚': 'metal', '辛': 'metal',
            '壬': 'water', '癸': 'water',
            '子': 'water', '亥': 'water',
            '寅': 'wood', '卯': 'wood',
            '巳': 'fire', '午': 'fire',
            '申': 'metal', '酉': 'metal',
            '辰': 'earth', '戌': 'earth', '丑': 'earth', '未': 'earth'
        };

        const wuxing = wuxingMap[char];
        if (wuxing) {
            element.classList.add(`wuxing-${wuxing}`);
        }
    }
}
```

## 🔄 状态管理

### Vuex/Pinia状态管理

```javascript
// stores/bazi.js
import { defineStore } from 'pinia'

export const useBaziStore = defineStore('bazi', {
    state: () => ({
        birthData: {
            year: null,
            month: null,
            day: null,
            hour: 0,
            minute: 0,
            gender: 1
        },
        baziData: null,
        aiInterpretation: '',
        loading: false,
        interpretationLoading: false,
        wsConnected: false,
        history: []
    }),

    actions: {
        async analyzeBazi(birthData) {
            this.loading = true;
            this.birthData = { ...birthData };

            try {
                this.baziData = await getCompleteAnalysis(birthData);
                this.addToHistory(this.baziData);
                return this.baziData;
            } catch (error) {
                console.error('分析失败:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        updateAIInterpretation(content) {
            this.aiInterpretation += content;
        },

        setInterpretationLoading(loading) {
            this.interpretationLoading = loading;
        },

        setWSConnected(connected) {
            this.wsConnected = connected;
        },

        addToHistory(data) {
            this.history.unshift({
                id: Date.now(),
                timestamp: new Date(),
                birthData: { ...this.birthData },
                baziData: data
            });

            // 限制历史记录数量
            if (this.history.length > 10) {
                this.history = this.history.slice(0, 10);
            }
        },

        clearHistory() {
            this.history = [];
        }
    },

    getters: {
        hasData: (state) => !!state.baziData,
        isAnalyzing: (state) => state.loading || state.interpretationLoading,
        recentAnalysis: (state) => state.history.slice(0, 5)
    }
})
```
