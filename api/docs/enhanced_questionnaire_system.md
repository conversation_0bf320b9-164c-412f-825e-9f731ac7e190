# 增强版问卷系统API文档

## 概述

增强版问卷系统是原有问卷系统的升级版，在保留原有功能的基础上，增加了以下特性：

1. 支持增加用户坐标、性别、年龄信息
2. 支持问卷答案的增量更新和版本控制
3. 优化了计算方式，提高准确性
4. 新增计算历史记录，方便追踪分析结果

所有增强版接口路径以 `/api/bank/v2/` 开头，与原有系统并行运行，不影响现有功能。

## 数据模型

增强版问卷系统新增了两个主要数据模型：

1. `EnhancedQuestionnaireResponse`: 增强版问卷响应，存储用户问卷填写记录
2. `QuestionnaireCalculationHistory`: 问卷计算历史，存储体质分析等计算结果历史

## API接口

### 1. 问卷填写流程

#### 1.1 获取问卷详情

- **接口**: `${BASE_URL}/api/bank/v2/questionnaire/`
- **方法**: POST
- **功能**: 获取特定问卷的问题和选项列表
- **参数**:
  ```json
  {
    "id": "问卷ID"
  }
  ```
- **响应示例**:
  ```json
  {
    "questionnaire_id": 1,
    "questionnaire_name": "体质评估问卷",
    "questions": [
      {
        "id": 1,
        "text": "问题内容",
        "options": [
          {
            "id": 1,
            "text": "选项内容",
            "probability": 0.8,
            "weight": 2.5,
            "type": "气虚"
          }
        ]
      }
    ]
  }
  ```

#### 1.2 提交问卷答案

- **接口**: `${BASE_URL}/api/bank/v2/calculate_scores/`
- **方法**: POST
- **功能**: 提交答案并计算得分
- **参数**:
  ```json
  {
    "id": "问卷ID",
    "answers": {
      "1": 2,  // 问题ID: 选项ID
      "2": 3
    },
    "location": "北京市海淀区",  // 可选
    "gender": "男",  // 可选
    "age": 28  // 可选
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "成功完成问卷评分",
    "result": {
      "气虚": {
        "probability": 2.4,
        "weight": 7.5,
        "type": "气虚"
      }
    }
  }
  ```

#### 1.3 更新症状信息

- **接口**: `${BASE_URL}/api/bank/v2/update_symptoms/`
- **方法**: POST
- **功能**: 更新用户症状信息
- **参数**:
  ```json
  {
    "symptoms": [
      {
        "text": "头痛",
        "level": 2
      },
      {
        "text": "疲劳",
        "level": 3
      }
    ],
    "questionnaireId": 1
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "症状更新成功",
    "deleted_count": 5,
    "created_count": 2
  }
  ```

### 2. 问卷分析流程

#### 2.1 分析问卷结果

- **接口**: `${BASE_URL}/api/bank/v2/analyze_questionnaire/`
- **方法**: POST
- **功能**: 分析用户的所有问卷结果，生成体质分析报告
- **参数**: 无需特定参数，基于token识别用户
- **响应示例**:
  ```json
  {
    "status": "success",
    "message": "问卷分析完成",
    "results": [
      {
        "type": "气虚",
        "percent": 45.8,
        "totalWeight": 73.6,
        "totalProbability": 29.2
      },
      {
        "type": "肝",
        "percent": 32.1,
        "totalWeight": 69.05,
        "totalProbability": 25.7
      }
    ],
    "history_id": 123
  }
  ```

### 3. 用户问卷查询

#### 3.1 获取用户问卷列表

- **接口**: `${BASE_URL}/api/bank/v2/get_user_questionnaires/`
- **方法**: POST
- **功能**: 获取用户的问卷填写记录
- **参数**:
  ```json
  {
    "questionnaireId": 1,  // 可选，特定问卷ID
    "includeAllVersions": false  // 可选，是否包含所有版本
  }
  ```
- **响应示例**:
  ```json
  {
    "status": "success",
    "count": 2,
    "data": [
      {
        "user_id": 123,
        "questionnaire_id": 1,
        "answers": {...},
        "total_scores": {...},
        "summary": {...},
        "location": "北京市海淀区",
        "gender": "男",
        "age": 28,
        "version": 2,
        "latest": true,
        "created_at": "2023-08-15T14:30:00.000Z",
        "updated_at": "2023-08-15T14:30:00.000Z"
      }
    ]
  }
  ```

#### 3.2 检查问卷填写状态

- **接口**: `${BASE_URL}/api/bank/v2/check_questionnaire_filled/`
- **方法**: POST
- **功能**: 检查用户是否已填写特定问卷
- **参数**:
  ```json
  {
    "questionnaire_id": 1
  }
  ```
- **响应示例**:
  ```json
  {
    "filled": true,
    "questionnaire_id": 1
  }
  ```

## 迁移指南

### 前端应用迁移

1. 更新API调用路径，将原路径 `/api/bank/` 改为 `/api/bank/v2/`
2. 更新请求参数格式，特别是 `answers` 格式变更为 `{ "问题ID": "选项ID" }`
3. 处理新增的字段，如 `location`, `gender`, `age` 等
4. 处理版本管理相关逻辑，如 `version` 和 `latest` 字段

### 后端兼容性

- 增强版系统与原系统并行运行，共享数据库中的问卷、问题和选项数据
- 症状管理模块沿用原有API，同时提供增强版API
- 增强版系统提供更丰富的返回信息和错误处理 