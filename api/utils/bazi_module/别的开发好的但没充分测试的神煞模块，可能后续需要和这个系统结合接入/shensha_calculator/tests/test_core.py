"""
核心功能测试

测试所有神煞计算函数的正确性。
"""

import unittest
from ..core.noble_spirits import tianyiguiren, taijiguiren, wenchang
from ..core.star_spirits import yima
from ..core.blade_spirits import yangren
from ..core.malefic_spirits import kongwang
from ..utils.helpers import get_jiazi_order, get_dizhi_wuxing, get_tiangan_yinyang
from ..utils.validators import validate_ganzhi, validate_bazi, validate_witch
from .test_data import SHENSHA_TEST_CASES, HELPER_TEST_CASES, VALIDATOR_TEST_CASES


class TestNobleSpirits(unittest.TestCase):
    """测试贵人类神煞"""
    
    def test_tianyiguiren(self):
        """测试天乙贵人"""
        for case in SHENSHA_TEST_CASES["tianyiguiren"]:
            with self.subTest(case=case):
                result = tianyiguiren(case["tiangan"], case["dizhi"])
                self.assertEqual(result, case["expected"])
    
    def test_taijiguiren(self):
        """测试太极贵人"""
        for case in SHENSHA_TEST_CASES["taijiguiren"]:
            with self.subTest(case=case):
                result = taijiguiren(case["tiangan"], case["dizhi"])
                self.assertEqual(result, case["expected"])
    
    def test_wenchang(self):
        """测试文昌贵人"""
        for case in SHENSHA_TEST_CASES["wenchang"]:
            with self.subTest(case=case):
                result = wenchang(case["tiangan"], case["dizhi"])
                self.assertEqual(result, case["expected"])


class TestStarSpirits(unittest.TestCase):
    """测试星煞类神煞"""
    
    def test_yima(self):
        """测试驿马"""
        for case in SHENSHA_TEST_CASES["yima"]:
            with self.subTest(case=case):
                result = yima(case["base_zhi"], case["target_zhi"])
                self.assertEqual(result, case["expected"])


class TestBladeSpirits(unittest.TestCase):
    """测试刃煞类神煞"""
    
    def test_yangren(self):
        """测试羊刃"""
        for case in SHENSHA_TEST_CASES["yangren"]:
            with self.subTest(case=case):
                result = yangren(case["day_gan"], case["dizhi"])
                self.assertEqual(result, case["expected"])


class TestMaleficSpirits(unittest.TestCase):
    """测试凶煞类神煞"""
    
    def test_kongwang(self):
        """测试空亡"""
        for case in SHENSHA_TEST_CASES["kongwang"]:
            with self.subTest(case=case):
                result = kongwang(case["ganzhi"], case["dizhi"])
                self.assertEqual(result, case["expected"])


class TestHelpers(unittest.TestCase):
    """测试工具函数"""
    
    def test_get_jiazi_order(self):
        """测试甲子顺序查询"""
        for case in HELPER_TEST_CASES["get_jiazi_order"]:
            with self.subTest(case=case):
                result = get_jiazi_order(case["ganzhi"])
                self.assertEqual(result, case["expected"])
    
    def test_get_dizhi_wuxing(self):
        """测试地支五行查询"""
        for case in HELPER_TEST_CASES["get_dizhi_wuxing"]:
            with self.subTest(case=case):
                result = get_dizhi_wuxing(case["dizhi"])
                self.assertEqual(result, case["expected"])
    
    def test_get_tiangan_yinyang(self):
        """测试天干阴阳判断"""
        for case in HELPER_TEST_CASES["get_tiangan_yinyang"]:
            with self.subTest(case=case):
                result = get_tiangan_yinyang(case["tiangan"])
                self.assertEqual(result, case["expected"])


class TestValidators(unittest.TestCase):
    """测试验证函数"""
    
    def test_validate_ganzhi(self):
        """测试干支验证"""
        for case in VALIDATOR_TEST_CASES["validate_ganzhi"]:
            with self.subTest(case=case):
                result = validate_ganzhi(case["ganzhi"])
                self.assertEqual(result, case["expected"])
    
    def test_validate_bazi(self):
        """测试八字验证"""
        for case in VALIDATOR_TEST_CASES["validate_bazi"]:
            with self.subTest(case=case):
                result = validate_bazi(case["bazi"])
                self.assertEqual(result, case["expected"])
    
    def test_validate_witch(self):
        """测试柱位验证"""
        for case in VALIDATOR_TEST_CASES["validate_witch"]:
            with self.subTest(case=case):
                result = validate_witch(case["witch"])
                self.assertEqual(result, case["expected"])


if __name__ == "__main__":
    unittest.main()
