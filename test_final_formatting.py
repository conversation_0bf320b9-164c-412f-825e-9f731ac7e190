#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终格式化测试 - 验证发给大模型的文本格式
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/home/<USER>/riyue-llm/myproject/demo_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

def test_final_formatting():
    """测试最终格式化效果"""
    try:
        from api.consumers.bazi_advice_consumer import BaziAdviceConsumer
        
        print("🎯 最终格式化测试...")
        print("=" * 80)
        
        # 使用你提供的真实数据结构
        real_data = {
            "type": "bazi_analysis",
            "data": {
                "eight_char": "己卯 壬申 丙午 癸巳",
                "gender": "男",
                "shishen": {
                    "gan_shishen": {
                        "year": "伤官",
                        "month": "七杀",
                        "day": "日主",
                        "time": "正官"
                    },
                    "zhi_shishen": {
                        "year": ["正印"],
                        "month": ["偏财", "七杀", "食神"],
                        "day": ["劫财", "伤官"],
                        "time": ["比肩", "偏财", "食神"]
                    }
                },
                "shensha": {
                    "pillars": {
                        "年柱": {"ganzhi": "己卯", "shenshas": ["太极", "空亡", "桃花"], "count": 3},
                        "月柱": {"ganzhi": "壬申", "shenshas": ["天乙", "月德", "文昌", "学堂", "驿马", "金舆", "劫煞", "元辰", "空亡"], "count": 9},
                        "日柱": {"ganzhi": "丙午", "shenshas": ["天喜", "羊刃", "孤鸾", "阴差阳错"], "count": 4},
                        "时柱": {"ganzhi": "癸巳", "shenshas": ["天德", "驿马", "禄神", "童子", "天厨", "孤辰", "亡神", "丧门"], "count": 8}
                    },
                    "total_count": 24,
                    "unique_count": 22,
                    "unique_shenshas": ["丧门", "亡神", "元辰", "劫煞", "天乙", "天厨", "天喜", "天德", "太极", "孤辰", "孤鸾", "学堂", "文昌", "月德", "桃花", "禄神", "空亡", "童子", "羊刃", "金舆", "阴差阳错", "驿马"]
                },
                "analysis_direction": "comprehensive_pattern",
                "domain": "overall_pattern",
                "time_scope": "lifetime",
                "user_question": "请进行综合格局分析",
                "detailed_info": {
                    "birth_info": {
                        "solar_date": "1999-08-22 09:00:00 星期日 (邓小平诞辰纪念日) 狮子座",
                        "lunar_date": "一九九九年七月十二 己卯(兔)年 壬申(猴)月 丙午(马)日 巳(蛇)时 纳音[城头土 剑锋金 天河水 长流水] 星期日 南方朱雀 星宿[星日马](凶) 彭祖百忌[丙不修灶必见灾殃 午不苫盖屋主更张] 喜神方位[坤](西南) 阳贵神方位[兑](正西) 阴贵神方位[乾](西北) 福神方位[乾](西北) 财神方位[坤](西南) 冲[(庚子)鼠] 煞[北]",
                        "birth_details": {
                            "year": 1999,
                            "month": 8,
                            "day": 22,
                            "hour": 9,
                            "minute": 0
                        }
                    },
                    "bazi_pillars": {
                        "year_pillar": "己卯",
                        "month_pillar": "壬申",
                        "day_pillar": "丙午",
                        "time_pillar": "癸巳"
                    },
                    "current_dayun": {
                        "ganzhi": "己巳",
                        "start_age": 26,
                        "end_age": 35
                    },
                    "liu_nian_info": {
                        "liuNian": {"ganzhi": "乙巳"},
                        "liuYue": {"ganzhi": "甲申"},
                        "liuRi": {"ganzhi": "庚子"}
                    }
                }
            }
        }
        
        # 创建消费者实例并处理数据
        consumer = BaziAdviceConsumer()
        
        # 处理参数化请求
        system_prompt, user_message = consumer._handle_parameterized_request(real_data)
        
        print("🤖 系统提示词:")
        print("-" * 80)
        print(system_prompt)
        
        print("\n" + "=" * 80)
        print("👤 发给大模型的用户消息:")
        print("-" * 80)
        print(user_message)
        
        print("\n" + "=" * 80)
        print("✅ 格式化测试完成！")
        
        # 验证关键信息是否正确
        print("\n🔍 关键信息验证:")
        print(f"✓ 八字信息: {'己卯 壬申 丙午 癸巳' in user_message}")
        print(f"✓ 性别信息: {'男' in user_message}")
        print(f"✓ 出生日期: {'1999-08-22' in user_message}")
        print(f"✓ 十神信息: {'天干十神' in user_message and '地支十神' in user_message}")
        print(f"✓ 神煞信息: {'重要神煞' in user_message}")
        print(f"✓ 大运信息: {'26-35岁' in user_message}")
        print(f"✓ 流年信息: {'乙巳' in user_message}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_formatting()
