#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试结果查看工具
帮助查看、对比和管理历史测试结果
"""

import os
import json
import glob
from datetime import datetime
from collections import defaultdict

RESULTS_DIR = "performance_test_results"

def ensure_results_directory():
    """确保结果目录存在"""
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)
        print(f"📁 创建测试结果目录: {RESULTS_DIR}")

def list_test_results():
    """列出所有测试结果文件"""
    ensure_results_directory()
    
    # 查找所有结果文件
    summary_files = glob.glob(os.path.join(RESULTS_DIR, "summary_report_*.json"))
    text_files = glob.glob(os.path.join(RESULTS_DIR, "report_*.txt"))
    
    if not summary_files and not text_files:
        print("📭 暂无测试结果文件")
        return []
    
    # 解析文件时间戳
    results = []
    for file_path in summary_files:
        filename = os.path.basename(file_path)
        timestamp_str = filename.replace("summary_report_", "").replace(".json", "")
        try:
            timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            results.append({
                'timestamp': timestamp,
                'timestamp_str': timestamp_str,
                'summary_file': file_path,
                'text_file': os.path.join(RESULTS_DIR, f"report_{timestamp_str}.txt"),
                'detailed_file': os.path.join(RESULTS_DIR, f"detailed_results_{timestamp_str}.json")
            })
        except ValueError:
            continue
    
    # 按时间排序（最新的在前）
    results.sort(key=lambda x: x['timestamp'], reverse=True)
    return results

def show_test_history():
    """显示测试历史"""
    results = list_test_results()
    
    if not results:
        return
    
    print("📊 性能测试历史记录:")
    print("=" * 80)
    
    for i, result in enumerate(results, 1):
        timestamp = result['timestamp']
        print(f"{i:2d}. {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 尝试读取汇总信息
        try:
            with open(result['summary_file'], 'r', encoding='utf-8') as f:
                summary = json.load(f)
                
            total_apis = summary.get('total_apis_tested', 'N/A')
            selected_apis = summary.get('selected_apis', [])
            
            print(f"    📈 测试API数量: {total_apis}")
            if selected_apis and len(selected_apis) <= 5:
                print(f"    🎯 测试API: {', '.join(selected_apis)}")
            elif selected_apis:
                print(f"    🎯 测试API: {', '.join(selected_apis[:3])} 等{len(selected_apis)}个")
                
        except Exception as e:
            print(f"    ⚠️ 无法读取汇总信息: {e}")
        
        print()

def view_specific_result(index_or_timestamp):
    """查看特定的测试结果"""
    results = list_test_results()
    
    if not results:
        print("📭 暂无测试结果")
        return
    
    # 根据索引或时间戳查找
    target_result = None
    if isinstance(index_or_timestamp, int):
        if 1 <= index_or_timestamp <= len(results):
            target_result = results[index_or_timestamp - 1]
    else:
        for result in results:
            if result['timestamp_str'] == index_or_timestamp:
                target_result = result
                break
    
    if not target_result:
        print("❌ 未找到指定的测试结果")
        return
    
    print(f"📊 测试结果详情 - {target_result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 读取详细结果
    try:
        with open(target_result['detailed_file'], 'r', encoding='utf-8') as f:
            detailed_results = json.load(f)
        
        # 按API显示结果
        for api_name, api_results in detailed_results.items():
            print(f"\n🎯 {api_name}:")
            print("   并发数 | QPS   | 平均响应时间 | 缓存命中率 | 成功率")
            print("   -------|-------|-------------|-----------|-------")
            
            for concurrent_users, stats in api_results.items():
                qps = stats.get('qps', 0)
                avg_time = stats.get('avg_response_time', 0) * 1000  # 转换为毫秒
                cache_rate = stats.get('cache_hit_rate', 0)
                success_rate = stats.get('success_rate', 0)
                
                print(f"   {concurrent_users:6s} | {qps:5.1f} | {avg_time:8.1f}ms | {cache_rate:8.1f}% | {success_rate:5.1f}%")
        
    except Exception as e:
        print(f"❌ 读取详细结果失败: {e}")
        
        # 尝试读取文本报告
        try:
            with open(target_result['text_file'], 'r', encoding='utf-8') as f:
                content = f.read()
                print(content)
        except Exception as e2:
            print(f"❌ 读取文本报告也失败: {e2}")

def compare_api_performance():
    """对比不同时间的API性能"""
    results = list_test_results()
    
    if len(results) < 2:
        print("📭 需要至少2次测试结果才能进行对比")
        return
    
    print("📈 API性能对比分析:")
    print("=" * 80)
    
    # 收集所有API的性能数据
    api_performance = defaultdict(list)
    
    for result in results:
        try:
            with open(result['detailed_file'], 'r', encoding='utf-8') as f:
                detailed_results = json.load(f)
            
            timestamp = result['timestamp']
            
            for api_name, api_results in detailed_results.items():
                # 取最高并发数的结果作为代表
                max_concurrent = max(api_results.keys(), key=lambda x: int(x))
                stats = api_results[max_concurrent]
                
                api_performance[api_name].append({
                    'timestamp': timestamp,
                    'concurrent_users': max_concurrent,
                    'qps': stats.get('qps', 0),
                    'avg_response_time': stats.get('avg_response_time', 0),
                    'success_rate': stats.get('success_rate', 0)
                })
                
        except Exception as e:
            print(f"⚠️ 跳过损坏的结果文件: {result['timestamp_str']}")
            continue
    
    # 显示对比结果
    for api_name, performances in api_performance.items():
        if len(performances) < 2:
            continue
            
        print(f"\n🎯 {api_name}:")
        print("   时间           | 并发数 | QPS   | 响应时间  | 成功率")
        print("   ---------------|--------|-------|----------|-------")
        
        for perf in sorted(performances, key=lambda x: x['timestamp']):
            timestamp_str = perf['timestamp'].strftime('%m-%d %H:%M')
            concurrent = perf['concurrent_users']
            qps = perf['qps']
            avg_time = perf['avg_response_time'] * 1000
            success_rate = perf['success_rate']
            
            print(f"   {timestamp_str:14s} | {concurrent:6s} | {qps:5.1f} | {avg_time:7.1f}ms | {success_rate:5.1f}%")

def show_best_performing_apis():
    """显示性能最好的API"""
    results = list_test_results()
    
    if not results:
        print("📭 暂无测试结果")
        return
    
    print("🏆 API性能排行榜:")
    print("=" * 80)
    
    # 收集最新测试的所有API性能
    latest_result = results[0]
    
    try:
        with open(latest_result['detailed_file'], 'r', encoding='utf-8') as f:
            detailed_results = json.load(f)
        
        api_scores = []
        
        for api_name, api_results in detailed_results.items():
            # 取最高并发数的结果
            max_concurrent = max(api_results.keys(), key=lambda x: int(x))
            stats = api_results[max_concurrent]
            
            qps = stats.get('qps', 0)
            avg_time = stats.get('avg_response_time', 0)
            success_rate = stats.get('success_rate', 0)
            
            # 计算综合得分 (QPS权重60%, 响应时间权重30%, 成功率权重10%)
            score = qps * 0.6 + (1/max(avg_time, 0.001)) * 0.3 + success_rate * 0.1
            
            api_scores.append({
                'name': api_name,
                'qps': qps,
                'avg_time': avg_time * 1000,
                'success_rate': success_rate,
                'score': score,
                'concurrent': max_concurrent
            })
        
        # 按得分排序
        api_scores.sort(key=lambda x: x['score'], reverse=True)
        
        print("排名 | API名称                    | QPS   | 响应时间  | 成功率 | 并发数")
        print("-----|---------------------------|-------|----------|-------|-------")
        
        for i, api in enumerate(api_scores, 1):
            name = api['name'][:25]  # 限制长度
            print(f"{i:4d} | {name:25s} | {api['qps']:5.1f} | {api['avg_time']:7.1f}ms | {api['success_rate']:5.1f}% | {api['concurrent']:6s}")
            
    except Exception as e:
        print(f"❌ 分析性能数据失败: {e}")

def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n🔍 性能测试结果查看工具")
        print("=" * 50)
        print("1. 查看测试历史")
        print("2. 查看特定测试结果")
        print("3. API性能对比")
        print("4. 性能排行榜")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            show_test_history()
        elif choice == "2":
            show_test_history()
            if list_test_results():
                try:
                    index = int(input("\n请输入要查看的测试编号: "))
                    view_specific_result(index)
                except ValueError:
                    print("❌ 请输入有效的数字")
        elif choice == "3":
            compare_api_performance()
        elif choice == "4":
            show_best_performing_apis()
        elif choice == "5":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    print("📊 性能测试结果管理工具")
    print("=" * 60)
    
    results = list_test_results()
    if results:
        print(f"📈 发现 {len(results)} 次测试记录")
        interactive_menu()
    else:
        print("📭 暂无测试结果")
        print("💡 运行 python test_concurrent_performance.py 开始性能测试")
