# 八字分析API - 五运六气功能

基于Django Ninja的八字分析API，根据用户输入的出生年月日时，计算生辰八字和五行，并提供中医养生建议。

## 功能特点

1. **八字分析**：根据用户出生年月日时计算生辰八字和五行比例
2. **中医养生建议**：集成华为方舟大模型API，根据八字和五行分析提供个性化养生建议
3. **WebSocket实时响应**：支持使用WebSocket进行流式数据交互，提供实时反馈
4. **API文档自动生成**：基于Django Ninja自动生成OpenAPI规范的API文档

## 安装

1. 克隆仓库：

```bash
git clone <repository_url>
cd bazi_api
```

2. 配置大模型API密钥：

```python
# 打开 bazi_api/settings.py 文件，找到以下部分并填写您的API密钥
LLM_API = {
    'ARK_API_KEY': '请在此处填写您的华为方舟API密钥',
    'ARK_BASE_URL': 'https://ark.cn-beijing.volces.com/api/v3',
    'ARK_MODEL_ID': 'ep-20250424123213-kk2fv',
}
```

3. 安装依赖：

```bash
pip install -r requirements.txt
```

4. 运行开发服务器：

```bash
# 使用daphne启动支持WebSocket的ASGI服务器
cd bazi_api
daphne -b 0.0.0.0 -p 8000 bazi_api.asgi:application

# 或者使用Django默认服务器（不支持WebSocket）
python manage.py runserver
```

## API使用说明

### 八字分析API

- 请求URL: `/api/bazi`
- 请求方法: POST
- 请求参数:

```json
{
  "birth_year": 1990,
  "birth_month": 1,
  "birth_day": 1,
  "birth_time": 0,
  "need_advice": true,
  "include_wuyun_liuqi": true
}
```

- 响应示例:

```json
{
  "shenchenbazi": "庚午-丙子-壬寅-甲子",
  "wuxing": {
    "金": 0.25,
    "木": 0.12,
    "水": 0.38,
    "火": 0.12,
    "土": 0.12
  },
  "shengxiao": "马",
  "current_wuyun_liuqi": {
    "五运": "水运",
    "六气": "风"
  },
  "health_advice": "根据您的八字分析和当前五运六气环境，为您提供以下养生建议：..."
}
```

### 流式八字分析（WebSocket）

1. 访问页面: `/api/bazi/`
2. 输入出生信息并点击"分析八字和养生建议"
3. 系统将实时显示八字分析结果和生成中医养生建议

### 健康检查API

- 请求URL: `/api/health`
- 请求方法: GET
- 响应示例:

```json
{
  "status": "healthy",
  "timestamp": "2023-10-20T12:34:56.789012"
}
```

## 配置

在`bazi_api/settings.py`文件中可以配置以下选项：

- `LLM_API['ARK_API_KEY']`: 华为方舟API密钥
- `LLM_API['ARK_BASE_URL']`: 华为方舟API基础URL (默认: https://ark.cn-beijing.volces.com/api/v3)
- `LLM_API['ARK_MODEL_ID']`: 华为方舟模型ID (默认: ep-20250424123213-kk2fv)

## 注意事项

- birth_time参数应该在0-23之间，表示24小时制的小时
- 本API仅支持公历日期转换为八字
- 使用WebSocket功能时，请确保客户端支持WebSocket协议

## 功能说明

本次更新增加了五运六气功能，使养生建议更加全面和精准：

1. 添加了获取当前时间（东八区）的八字和五运六气功能
2. 在生成养生建议时，结合用户八字和当前五运六气环境进行分析
3. 提供独立的五运六气API，便于前端直接获取当前时令信息

## 新增API端点

### 1. 获取当前五运六气

```
GET /api/wuyun-liuqi
```

返回示例：
```json
{
  "current_bazi": "壬寅-壬寅-庚午",
  "wuyun_liuqi": {
    "五运": "水运",
    "六气": "风"
  },
  "timestamp": "2023-06-15T10:30:45.123456"
}
```

### 2. 修改后的八字分析API

```
POST /api/bazi
```

请求体新增参数：
```json
{
  "birth_year": 1990,
  "birth_month": 10,
  "birth_day": 1,
  "birth_time": 8,
  "need_advice": true,
  "include_wuyun_liuqi": true
}
```

响应新增字段：
```json
{
  "shenchenbazi": "庚午-辛酉-壬子",
  "wuxing": {
    "金": 0.38,
    "木": 0.12,
    "水": 0.25,
    "火": 0.12,
    "土": 0.12
  },
  "shengxiao": "马",
  "current_wuyun_liuqi": {
    "五运": "水运",
    "六气": "风"
  },
  "health_advice": "根据您的八字分析和当前五运六气环境，为您提供以下养生建议：..."
}
```

## WebSocket流式响应

WebSocket端点也支持五运六气功能，请求格式新增`include_wuyun_liuqi`字段：

```js
// 连接WebSocket
const socket = new WebSocket('ws://服务器地址/ws/bazi_advice/');

// 发送请求
socket.send(JSON.stringify({
  birth_info: {
    birth_year: 1990,
    birth_month: 10,
    birth_day: 1,
    birth_time: 8,
    include_wuyun_liuqi: true
  }
}));
```

响应中会包含五运六气信息，并且养生建议会结合当前五运六气环境。 