# management/commands/init_forum_sections.py
from django.core.management.base import BaseCommand
from django.db import transaction
from api.models import TCMForumSection, UserInfo

class Command(BaseCommand):
    help = '初始化论坛板块数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='重置所有板块数据',
        )

    @transaction.atomic
    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('正在重置板块数据...')
            TCMForumSection.objects.all().delete()

        default_sections = [
            {
                "name": "中医基础理论",
                "description": "探讨阴阳五行、脏腑经络等中医基础理论知识",
                "rules": "1. 遵守专业讨论准则\n2. 禁止发布虚假信息\n3. 鼓励理论与实践结合",
                "order": 10,
            },
            {
                "name": "方剂学习交流",
                "description": "交流学习中医方剂的组成、功效与应用",
                "rules": "1. 严禁处方开方\n2. 以学习交流为主\n3. 保持专业性讨论",
                "order": 20,
            },
            {
                "name": "养生保健交流",
                "description": "分享中医养生知识、经验和心得",
                "rules": "1. 分享须谨慎\n2. 注意安全性\n3. 尊重传统理论",
                "order": 30,
            },
            {
                "name": "中药材鉴别",
                "description": "讨论中药材的特性、鉴别方法与使用注意事项",
                "rules": "1. 严禁药材交易\n2. 以知识分享为主\n3. 注意专业性",
                "order": 40,
            },
            {
                "name": "经典著作研习",
                "description": "学习讨论《黄帝内经》《伤寒论》等经典著作",
                "rules": "1. 保持学术性\n2. 鼓励原文引用\n3. 理论与实践结合",
                "order": 50,
            },
            {
                "name": "临床心得分享",
                "description": "分享临床经验与心得体会",
                "rules": "1. 遵医规范\n2. 保护隐私\n3. 谨慎发言",
                "order": 60,
            }
        ]

        created_count = 0
        updated_count = 0

        for section_data in default_sections:
            section, created = TCMForumSection.objects.update_or_create(
                name=section_data['name'],
                defaults={
                    'description': section_data['description'],
                    'rules': section_data['rules'],
                    'order': section_data['order']
                }
            )
            if created:
                created_count += 1
            else:
                updated_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f'初始化完成！新建板块：{created_count}个，更新板块：{updated_count}个'
            )
        )