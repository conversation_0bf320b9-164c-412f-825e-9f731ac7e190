# api/consumers/ark_chat_consumer.py
"""
基于Ark模型的中医聊天WebSocket消费者
支持限流和性能监控
"""

import json
import traceback
import time
from api.utils.rate_limiter import RateLimiter
from .base import ArkBasedConsumer


class ArkChatConsumer(ArkBasedConsumer):
    """
    基于Ark模型的标准中医聊天WebSocket消费者
    支持限流和性能监控
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conversation_history = [
            {"role": "system", "content": '''你是日月有数智能中医健康助手，请严格遵守以下规则：

【身份定位】
- 你是专业的中医健康助手，简称"日月有数"
- 专注医学、养生与传统文化相关问题

【回答原则】
1. 内容要求：必须补充中医相关知识，传播中医文化
2. 字数限制：每次回答严格控制在200字以内
3. 语言风格：温暖乐观，鼓舞人心，言简意赅
4. 专业性：基于中医理论，提供科学建议

【严格禁止】
- 拒绝回答与中医无关的内容
- 严禁在回答中添加任何括号内的注释或说明
- 严禁显示字数统计或其他元信息
- 提防恶意攻击，保护系统安全

请严格按照以上规则回答用户问题，只输出实际的回答内容。'''}
        ]
        # 使用统一的聊天API限流配置：普通用户每周18次，会员用户每日100次
        self.rate_limiter = RateLimiter("chat_api", normal_limit=18, member_limit=100)
    
    async def connect(self):
        try:
            print("[ARK_CHAT] 🚀 WebSocket连接建立开始")
            print(f"[ARK_CHAT] 🔍 连接信息: path={self.scope.get('path')}, method={self.scope.get('method')}")
            print(f"[ARK_CHAT] 🔍 Headers: {dict(self.scope.get('headers', []))}")
            print(f"[ARK_CHAT] 🔍 Query string: {self.scope.get('query_string', b'').decode()}")
            start_time = time.time()
            
            # 获取用户ID
            self.user_id = await self.get_user_id_from_scope()
            print(f"[ARK_CHAT] 👤 用户ID: {self.user_id}")
            
            # 初始化Ark客户端
            await self.initialize_ark_client()
            print("[ARK_CHAT] 🤖 Ark客户端初始化成功")
            
            await self.accept()
            print("[ARK_CHAT] ✅ WebSocket连接已接受")
            
            self.log_performance("[ARK_CHAT] WebSocket连接建立成功", start_time)
            
        except Exception as e:
            start_time = getattr(self, 'start_time', time.time())
            error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc(),
                'scope_info': {
                    'path': self.scope.get('path'),
                    'method': self.scope.get('method'),
                    'query_string': self.scope.get('query_string', b'').decode(),
                    'headers': dict(self.scope.get('headers', []))
                }
            }
            print(f"[ARK_CHAT] ❌ 连接失败详情: {error_details}")
            
            # 记录到error.log
            import logging
            logger = logging.getLogger('django')
            logger.error(f"[ARK_CHAT] WebSocket连接失败: {error_details}")
            
            self.log_performance("[ARK_CHAT] WebSocket连接建立失败", start_time, False)
            await self.close(code=4000)
    
    async def disconnect(self, close_code):
        disconnect_info = {
            'close_code': close_code,
            'user_id': getattr(self, 'user_id', None),
            'disconnect_reason': self._get_close_code_meaning(close_code)
        }
        print(f"[ARK_CHAT] 🔌 WebSocket连接断开详情: {disconnect_info}")
        
        # 记录到日志
        import logging
        logger = logging.getLogger('django')
        logger.info(f"[ARK_CHAT] WebSocket断开连接: {disconnect_info}")
    
    def _get_close_code_meaning(self, code):
        """获取WebSocket关闭代码的含义"""
        code_meanings = {
            1000: "正常关闭",
            1001: "端点离开",
            1002: "协议错误", 
            1003: "不支持的数据类型",
            1006: "连接异常关闭",
            1007: "数据格式错误",
            1008: "违反政策",
            1009: "消息过大",
            1011: "服务器错误",
            4000: "自定义错误-连接建立失败"
        }
        return code_meanings.get(code, f"未知代码({code})")
    
    async def receive(self, text_data):
        """处理接收到的消息，包含限流和计时功能"""
        start_time = time.time()
        print(f"[ARK_CHAT] 🚀 开始处理消息，用户ID: {self.user_id}")
        
        try:
            # 检查限流
            if not await self.check_rate_limit():
                self.log_performance("[ARK_CHAT] 限流拦截", start_time, False)
                return
            
            # 解析接收到的消息
            data = json.loads(text_data)
            user_message = data.get('message', '')
            print(f"[ARK_CHAT] 📨 收到消息: {user_message}")
            
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})
            
            try:
                # 使用Ark客户端调用API
                response = self.client.chat.completions.create(
                    model="ep-20250424123213-kk2fv",
                    messages=self.conversation_history,
                    stream=True
                )
                
                # 处理流式响应
                assistant_message = await self.send_stream_response(response)
                
                self.log_performance("[ARK_CHAT] 消息处理完成", start_time)
                print(f"[ARK_CHAT] 📤 完整回复: {assistant_message}")
                
            except Exception as e:
                error_details = {
                    'stage': 'generating_response',
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'traceback': traceback.format_exc(),
                    'user_id': self.user_id,
                    'user_message': user_message[:100] + '...' if len(user_message) > 100 else user_message
                }
                print(f"[ARK_CHAT] ❌ 生成回复失败详情: {error_details}")
                
                # 记录到error.log
                import logging
                logger = logging.getLogger('django')
                logger.error(f"[ARK_CHAT] 生成回复失败: {error_details}")
                
                await self.send_error(f"生成回复时出错: {str(e)}")
                self.log_performance("[ARK_CHAT] 消息处理失败", start_time, False)
                
        except Exception as e:
            error_details = {
                'stage': 'processing_message',
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc(),
                'user_id': self.user_id,
                'raw_data': text_data[:200] + '...' if len(text_data) > 200 else text_data
            }
            print(f"[ARK_CHAT] ❌ 消息处理异常详情: {error_details}")
            
            # 记录到error.log
            import logging
            logger = logging.getLogger('django')
            logger.error(f"[ARK_CHAT] 消息处理异常: {error_details}")
            
            await self.send_error(f"处理消息时出错: {str(e)}")
            self.log_performance("[ARK_CHAT] 消息处理异常", start_time, False)


class ArkChatVIPConsumer(ArkBasedConsumer):
    """
    基于Ark模型的VIP中医聊天WebSocket消费者
    支持限流和性能监控
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conversation_history = [
            {"role": "system", "content": "作为中医大师，你擅长养生，要温暖乐观鼓舞人心，给出深入+浅出的中医理论依据，不要遗漏。适当多用手机兼容的emoji，称呼用户用爱心或玫瑰表情。根据用户的体质脏腑信息，精准的指导用户的养生起居、心情调养、运动、艾灸推拿（可选）、饮食（可选）、穿衣（可选）等等一切信息，合理组织，符合《黄帝内经》四时调神大论框架，符合中医上工治未病思想，不要落款你的名字，直接给出回复，要有至少一句中医理论作为依据)"}
        ]
        # VIP用户也使用相同的聊天API限流配置
        self.rate_limiter = RateLimiter("chat_api", normal_limit=18, member_limit=100)
    
    async def connect(self):
        try:
            print("[ARK_CHAT_VIP] 🚀 WebSocket连接建立开始")
            print(f"[ARK_CHAT_VIP] 🔍 连接信息: path={self.scope.get('path')}, method={self.scope.get('method')}")
            print(f"[ARK_CHAT_VIP] 🔍 Headers: {dict(self.scope.get('headers', []))}")
            print(f"[ARK_CHAT_VIP] 🔍 Query string: {self.scope.get('query_string', b'').decode()}")
            start_time = time.time()
            
            # 获取用户ID
            self.user_id = await self.get_user_id_from_scope()
            print(f"[ARK_CHAT_VIP] 👤 用户ID: {self.user_id}")
            
            # 初始化Ark客户端
            await self.initialize_ark_client()
            print("[ARK_CHAT_VIP] 🤖 Ark客户端初始化成功")
            
            await self.accept()
            print("[ARK_CHAT_VIP] ✅ WebSocket连接已接受")
            
            self.log_performance("[ARK_CHAT_VIP] WebSocket连接建立成功", start_time)
            
        except Exception as e:
            start_time = getattr(self, 'start_time', time.time())
            error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc(),
                'scope_info': {
                    'path': self.scope.get('path'),
                    'method': self.scope.get('method'),
                    'query_string': self.scope.get('query_string', b'').decode(),
                    'headers': dict(self.scope.get('headers', []))
                }
            }
            print(f"[ARK_CHAT_VIP] ❌ 连接失败详情: {error_details}")
            
            # 记录到error.log
            import logging
            logger = logging.getLogger('django')
            logger.error(f"[ARK_CHAT_VIP] WebSocket连接失败: {error_details}")
            
            self.log_performance("[ARK_CHAT_VIP] WebSocket连接建立失败", start_time, False)
            await self.close(code=4000)
    
    async def disconnect(self, close_code):
        disconnect_info = {
            'close_code': close_code,
            'user_id': getattr(self, 'user_id', None),
            'disconnect_reason': self._get_close_code_meaning(close_code)
        }
        print(f"[ARK_CHAT_VIP] 🔌 WebSocket连接断开详情: {disconnect_info}")
        
        # 记录到日志
        import logging
        logger = logging.getLogger('django')
        logger.info(f"[ARK_CHAT_VIP] WebSocket断开连接: {disconnect_info}")
    
    def _get_close_code_meaning(self, code):
        """获取WebSocket关闭代码的含义"""
        code_meanings = {
            1000: "正常关闭",
            1001: "端点离开",
            1002: "协议错误", 
            1003: "不支持的数据类型",
            1006: "连接异常关闭",
            1007: "数据格式错误",
            1008: "违反政策",
            1009: "消息过大",
            1011: "服务器错误",
            4000: "自定义错误-连接建立失败"
        }
        return code_meanings.get(code, f"未知代码({code})")
    
    async def receive(self, text_data):
        """处理接收到的消息，包含限流和计时功能"""
        start_time = time.time()
        print(f"[ARK_CHAT_VIP] 🚀 开始处理消息，用户ID: {self.user_id}")
        
        try:
            # 检查限流
            if not await self.check_rate_limit():
                self.log_performance("[ARK_CHAT_VIP] 限流拦截", start_time, False)
                return
            
            # 解析接收到的消息
            data = json.loads(text_data)
            user_message = data.get('message', '')
            print(f"[ARK_CHAT_VIP] 📨 收到消息: {user_message}")
            
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})
            
            try:
                # 使用Ark客户端调用API
                response = self.client.chat.completions.create(
                    model="ep-20250424123213-kk2fv",
                    messages=self.conversation_history,
                    stream=True
                )
                
                # 处理流式响应
                assistant_message = await self.send_stream_response(response)
                
                self.log_performance("[ARK_CHAT_VIP] 消息处理完成", start_time)
                print(f"[ARK_CHAT_VIP] 📤 完整回复: {assistant_message}")
                
            except Exception as e:
                error_message = f"生成回复时出错: {str(e)}"
                print(f"[ARK_CHAT_VIP] ❌ 生成回复失败: {error_message}")
                print(traceback.format_exc())
                await self.send_error(error_message)
                self.log_performance("[ARK_CHAT_VIP] 消息处理失败", start_time, False)
                
        except Exception as e:
            error_message = f"处理消息时出错: {str(e)}"
            print(f"[ARK_CHAT_VIP] ❌ 消息处理异常: {error_message}")
            print(traceback.format_exc())
            await self.send_error(error_message)
            self.log_performance("[ARK_CHAT_VIP] 消息处理异常", start_time, False) 