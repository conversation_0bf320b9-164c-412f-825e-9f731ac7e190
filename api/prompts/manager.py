# api/prompts/manager.py
"""
提示词管理器
负责加载、缓存和管理所有八字分析提示词
"""

import os
import json
import yaml
from typing import Dict, Optional, List
from django.conf import settings
from django.core.cache import cache
from .categories import PromptCategory
import logging

logger = logging.getLogger(__name__)


class PromptManager:
    """提示词管理器"""
    
    def __init__(self):
        self.prompts_dir = os.path.join(settings.BASE_DIR, 'api', 'prompts', 'templates')
        self.cache_timeout = 3600  # 1小时缓存
        self._ensure_prompts_dir()
    
    def _ensure_prompts_dir(self):
        """确保提示词目录存在"""
        if not os.path.exists(self.prompts_dir):
            os.makedirs(self.prompts_dir, exist_ok=True)
            logger.info(f"创建提示词目录: {self.prompts_dir}")
    
    def get_prompt(self, category: PromptCategory, version: str = "v1") -> Optional[str]:
        """
        获取指定分类的提示词
        
        Args:
            category: 提示词分类
            version: 版本号，默认v1
            
        Returns:
            提示词内容，如果不存在返回None
        """
        cache_key = f"prompt_{category.value}_{version}"
        
        # 先从缓存获取
        prompt = cache.get(cache_key)
        if prompt:
            logger.debug(f"从缓存获取提示词: {category.value}")
            return prompt
        
        # 从文件加载
        prompt = self._load_prompt_from_file(category, version)
        if prompt:
            # 缓存提示词
            cache.set(cache_key, prompt, self.cache_timeout)
            logger.debug(f"加载并缓存提示词: {category.value}")
        
        return prompt
    
    def _load_prompt_from_file(self, category: PromptCategory, version: str) -> Optional[str]:
        """从文件加载提示词"""
        # 支持多种文件格式
        file_extensions = ['.yaml', '.yml', '.json', '.txt']
        
        for ext in file_extensions:
            file_path = os.path.join(self.prompts_dir, f"{category.value}_{version}{ext}")
            if os.path.exists(file_path):
                try:
                    return self._read_prompt_file(file_path, ext)
                except Exception as e:
                    logger.error(f"读取提示词文件失败: {file_path}, 错误: {e}")
        
        # 如果指定版本不存在，尝试加载默认版本
        if version != "v1":
            logger.warning(f"版本 {version} 不存在，尝试加载默认版本")
            return self._load_prompt_from_file(category, "v1")
        
        logger.warning(f"未找到提示词文件: {category.value}")
        return None
    
    def _read_prompt_file(self, file_path: str, extension: str) -> str:
        """读取提示词文件内容"""
        with open(file_path, 'r', encoding='utf-8') as f:
            if extension in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
                return data.get('prompt', data.get('content', ''))
            elif extension == '.json':
                data = json.load(f)
                return data.get('prompt', data.get('content', ''))
            else:  # .txt
                return f.read()
    
    def get_system_prompt(self, category: PromptCategory, version: str = "v1") -> str:
        """
        获取系统提示词（包含基础设定）
        
        Args:
            category: 提示词分类
            version: 版本号
            
        Returns:
            完整的系统提示词
        """
        base_prompt = self._get_base_system_prompt()
        category_prompt = self.get_prompt(category, version)
        
        if category_prompt:
            return f"{base_prompt}\n\n{category_prompt}"
        else:
            logger.warning(f"使用默认提示词: {category.value}")
            return base_prompt
    
    def _get_base_system_prompt(self) -> str:
        """获取基础系统提示词"""
        return """你是专业的八字命理分析师，擅长根据八字信息提供专业分析。请严格遵守以下规则：

【身份定位】
- 你是专业的八字命理分析师和中医养生专家
- 专注于八字分析、五行调理、传统文化传承

【分析原则】
1. 基于用户提供的八字、十神、神煞信息进行专业分析
2. 结合五行理论和传统命理学知识
3. 提供个性化的建议和指导
4. 语言专业但通俗易懂，温暖贴心

【严格禁止】
- 预测具体的吉凶祸福或重大人生事件
- 给出绝对化的断言
- 涉及迷信或非科学内容
- 提供医疗诊断或治疗建议

请基于用户的八字信息，提供专业、实用的分析建议。"""
    
    def list_available_prompts(self) -> Dict[str, List[str]]:
        """列出所有可用的提示词"""
        available = {}
        
        for category in PromptCategory.get_all_categories():
            versions = []
            for file in os.listdir(self.prompts_dir):
                if file.startswith(f"{category.value}_") and not file.endswith('.bak'):
                    # 提取版本号
                    name_part = file.split('.')[0]  # 去掉扩展名
                    version = name_part.split('_', 1)[1]  # 获取版本部分
                    if version not in versions:
                        versions.append(version)
            
            if versions:
                available[category.value] = sorted(versions)
        
        return available
    
    def reload_prompts(self):
        """重新加载所有提示词（清除缓存）"""
        # 清除所有提示词缓存
        for category in PromptCategory.get_all_categories():
            cache_pattern = f"prompt_{category.value}_*"
            # Django缓存不支持通配符删除，需要逐个删除已知版本
            available = self.list_available_prompts()
            if category.value in available:
                for version in available[category.value]:
                    cache_key = f"prompt_{category.value}_{version}"
                    cache.delete(cache_key)
        
        logger.info("已重新加载所有提示词")


# 全局提示词管理器实例
prompt_manager = PromptManager()
