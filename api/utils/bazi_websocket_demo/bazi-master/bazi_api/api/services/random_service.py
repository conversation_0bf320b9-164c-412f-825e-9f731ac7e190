"""
随机算法服务模块
基于用户输入的0-999数字生成均等分布的随机数
"""
import hashlib
import random
import time
from api.utils.logging import safe_print

class RandomService:
    """随机算法服务类"""
    
    @staticmethod
    def generate_divination_number(user_input: int) -> int:
        """
        基于用户输入生成算卦用的随机数
        
        Args:
            user_input (int): 用户输入的数字 (0-999)
            
        Returns:
            int: 生成的随机数 (1-64)
        """
        # 验证输入范围
        if not (0 <= user_input <= 999):
            safe_print(f"用户输入超出范围: {user_input}，使用默认值0")
            user_input = 0
        
        # 获取当前时间戳的微秒部分作为随机种子的一部分
        current_time = time.time()
        microseconds = int((current_time - int(current_time)) * 1000000)
        
        # 结合用户输入、时间戳和一些固定的"易经"元素创建种子
        seed_string = f"{user_input}_{microseconds}_{current_time}_{user_input*7+13}"
        
        # 使用SHA256哈希确保种子的均匀分布
        hash_object = hashlib.sha256(seed_string.encode())
        hex_dig = hash_object.hexdigest()
        
        # 从哈希值中提取数字
        numeric_hash = int(hex_dig[:16], 16)  # 取前16位十六进制转为整数
        
        # 设置随机种子
        random.seed(numeric_hash)
        
        # 生成1-64之间的随机数
        divination_number = random.randint(1, 64)
        
        safe_print(f"用户输入: {user_input}, 生成卦数: {divination_number}")
        
        return divination_number
    
    @staticmethod
    def generate_enhanced_random(user_input: int, question_hash: str = None) -> int:
        """
        增强版随机数生成，结合用户输入和问题内容
        
        Args:
            user_input (int): 用户输入的数字 (0-999)
            question_hash (str): 问题内容的哈希值（可选）
            
        Returns:
            int: 生成的随机数 (1-64)
        """
        # 验证输入范围
        if not (0 <= user_input <= 999):
            safe_print(f"用户输入超出范围: {user_input}，使用默认值0")
            user_input = 0
        
        # 获取当前时间的多个维度
        current_time = time.time()
        microseconds = int((current_time - int(current_time)) * 1000000)
        nanoseconds = time.time_ns() % 1000000
        
        # 创建复合种子
        seed_components = [
            str(user_input),
            str(microseconds),
            str(nanoseconds),
            str(current_time),
            str(user_input * 13 + 7),  # 用户输入的变换
            str(hash(str(user_input)) % 10000),  # 用户输入的哈希变换
        ]
        
        # 如果有问题哈希，加入种子
        if question_hash:
            seed_components.append(question_hash)
        
        # 合并所有种子组件
        seed_string = "_".join(seed_components)
        
        # 使用多重哈希确保随机性
        hash1 = hashlib.sha256(seed_string.encode()).hexdigest()
        hash2 = hashlib.md5((seed_string + hash1).encode()).hexdigest()
        combined_hash = hash1 + hash2
        
        # 从组合哈希中提取多个数字并组合
        numbers = []
        for i in range(0, min(len(combined_hash), 32), 4):
            try:
                num = int(combined_hash[i:i+4], 16)
                numbers.append(num)
            except ValueError:
                continue
        
        # 计算最终的种子
        final_seed = sum(numbers) + user_input * 1000 + microseconds
        
        # 设置随机种子
        random.seed(final_seed)
        
        # 生成多个随机数并取其中一个，增加随机性
        random_numbers = [random.randint(1, 64) for _ in range(5)]
        divination_number = random_numbers[user_input % 5]
        
        safe_print(f"增强随机 - 用户输入: {user_input}, 生成卦数: {divination_number}")
        
        return divination_number
    
    @staticmethod
    def validate_distribution(iterations: int = 10000) -> dict:
        """
        验证随机数分布的均匀性
        
        Args:
            iterations (int): 测试迭代次数
            
        Returns:
            dict: 分布统计结果
        """
        distribution = {}
        
        for i in range(iterations):
            user_input = i % 1000  # 0-999循环
            gua_number = RandomService.generate_divination_number(user_input)
            distribution[gua_number] = distribution.get(gua_number, 0) + 1
        
        # 计算统计信息
        total = sum(distribution.values())
        expected = total / 64
        variance = sum((count - expected) ** 2 for count in distribution.values()) / 64
        
        return {
            'total_iterations': iterations,
            'distribution': distribution,
            'expected_per_gua': expected,
            'variance': variance,
            'min_count': min(distribution.values()),
            'max_count': max(distribution.values()),
            'uniformity_ratio': min(distribution.values()) / max(distribution.values())
        }
    
    @staticmethod
    def get_question_hash(question: str) -> str:
        """
        生成问题内容的哈希值
        
        Args:
            question (str): 问题内容
            
        Returns:
            str: 问题的哈希值
        """
        if not question:
            return ""
        
        # 清理问题文本
        cleaned_question = question.strip().lower()
        
        # 生成哈希
        hash_object = hashlib.sha256(cleaned_question.encode('utf-8'))
        return hash_object.hexdigest()[:16]  # 取前16位


# 创建全局实例
random_service = RandomService() 