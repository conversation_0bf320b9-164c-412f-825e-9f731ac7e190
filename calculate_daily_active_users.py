#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于性能测试结果估算日活用户支持能力
"""

import math

def calculate_daily_active_users():
    """计算日活用户支持能力"""
    
    print("🚀 基于Uvicorn性能测试结果的日活用户估算")
    print("=" * 60)
    
    # 核心API性能数据 (基于20并发测试)
    api_performance = {
        "预后分析-疗法分类": {"qps": 248, "response_time": 61.9},
        "预后分析-疗法列表": {"qps": 278, "response_time": 56.2},
        "首页": {"qps": 25, "response_time": 204.4},  # 受静态文件限制
    }
    
    # 用户行为模型假设
    user_behavior = {
        "daily_sessions_per_user": 3,      # 每用户每天访问3次
        "requests_per_session": 8,         # 每次会话8个请求
        "peak_hour_ratio": 0.15,           # 高峰期占全天15%的流量
        "concurrent_user_ratio": 0.05,     # 5%的用户同时在线
    }
    
    # 系统配置
    system_config = {
        "uvicorn_workers": 16,             # 16个worker进程
        "safety_margin": 0.7,              # 70%安全边际
        "cache_hit_rate": 0.8,             # 80%缓存命中率(实际有缓存)
    }
    
    print("📋 性能基准数据:")
    for api_name, perf in api_performance.items():
        print(f"   {api_name}: {perf['qps']} QPS, {perf['response_time']:.1f}ms")
    
    print(f"\n🔧 系统配置:")
    print(f"   Uvicorn Workers: {system_config['uvicorn_workers']}")
    print(f"   安全边际: {system_config['safety_margin']*100}%")
    print(f"   缓存命中率: {system_config['cache_hit_rate']*100}%")
    
    # 选择最保守的API作为瓶颈 (首页QPS最低)
    bottleneck_qps = min(perf['qps'] for perf in api_performance.values())
    bottleneck_api = min(api_performance.items(), key=lambda x: x[1]['qps'])
    
    print(f"\n🔍 性能瓶颈分析:")
    print(f"   瓶颈API: {bottleneck_api[0]}")
    print(f"   瓶颈QPS: {bottleneck_qps}")
    
    # 考虑缓存和多worker的实际QPS
    effective_qps = bottleneck_qps * system_config['uvicorn_workers'] * system_config['safety_margin']
    
    # 考虑缓存命中率的QPS提升
    cache_boosted_qps = effective_qps / (1 - system_config['cache_hit_rate'])
    
    print(f"   多Worker有效QPS: {effective_qps:.0f}")
    print(f"   缓存加速后QPS: {cache_boosted_qps:.0f}")
    
    # 计算高峰期支持的并发用户
    peak_concurrent_users = cache_boosted_qps / (
        user_behavior['requests_per_session'] / 3600  # 假设会话持续1小时
    )
    
    # 计算日活用户
    daily_active_users = peak_concurrent_users / user_behavior['concurrent_user_ratio']
    
    print(f"\n📊 用户支持能力估算:")
    print(f"   高峰期并发用户: {peak_concurrent_users:.0f}")
    print(f"   估算日活用户: {daily_active_users:.0f}")
    
    # 不同场景下的估算
    scenarios = [
        {"name": "保守估算", "multiplier": 0.5},
        {"name": "标准估算", "multiplier": 1.0},
        {"name": "乐观估算", "multiplier": 1.5},
    ]
    
    print(f"\n🎯 不同场景下的日活支持能力:")
    for scenario in scenarios:
        estimated_dau = daily_active_users * scenario['multiplier']
        print(f"   {scenario['name']}: {estimated_dau:.0f} 日活用户")
    
    # 按不同用户活跃度计算
    activity_levels = [
        {"name": "轻度用户", "sessions": 1, "requests": 3},
        {"name": "中度用户", "sessions": 3, "requests": 8},
        {"name": "重度用户", "sessions": 6, "requests": 15},
    ]
    
    print(f"\n👥 按用户活跃度分类:")
    for level in activity_levels:
        daily_requests = level['sessions'] * level['requests']
        supported_users = (cache_boosted_qps * 3600 * 24) / daily_requests
        print(f"   {level['name']} ({level['sessions']}会话/{level['requests']}请求): {supported_users:.0f} 用户")
    
    # 资源利用率分析
    print(f"\n💻 资源利用率分析:")
    cpu_usage_per_request = 10  # 假设每请求10ms CPU时间
    memory_per_user = 2  # 假设每用户2MB内存
    
    daily_requests_total = daily_active_users * user_behavior['daily_sessions_per_user'] * user_behavior['requests_per_session']
    cpu_hours_needed = (daily_requests_total * cpu_usage_per_request / 1000) / 3600
    memory_needed_gb = (daily_active_users * user_behavior['concurrent_user_ratio'] * memory_per_user) / 1024
    
    print(f"   预计日请求总数: {daily_requests_total:.0f}")
    print(f"   预计CPU小时数: {cpu_hours_needed:.1f}")
    print(f"   预计内存需求: {memory_needed_gb:.1f} GB")
    
    return {
        'conservative_dau': daily_active_users * 0.5,
        'standard_dau': daily_active_users,
        'optimistic_dau': daily_active_users * 1.5,
        'bottleneck_qps': bottleneck_qps,
        'effective_qps': cache_boosted_qps
    }

if __name__ == "__main__":
    result = calculate_daily_active_users()
    
    print(f"\n🎉 结论:")
    print(f"   当前系统配置下，预计可支持 {result['standard_dau']:.0f} 日活用户")
    print(f"   保守估算: {result['conservative_dau']:.0f} DAU")
    print(f"   乐观估算: {result['optimistic_dau']:.0f} DAU")
    print(f"\n💡 优化建议:")
    print(f"   1. 首页静态文件使用CDN可大幅提升QPS")
    print(f"   2. 数据库查询优化可进一步提升核心API性能")
    print(f"   3. Redis缓存策略优化可提高缓存命中率")
    print(f"   4. 考虑使用负载均衡器水平扩展")
