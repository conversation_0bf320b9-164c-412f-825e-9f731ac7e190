"""
WebSocket基础消费者模块
提供WebSocket基础连接和通信功能
"""
import json
import traceback
from channels.generic.websocket import AsyncWebsocketConsumer
from api.utils.logging import safe_print

class BaseConsumer(AsyncWebsocketConsumer):
    """WebSocket基础消费者类，提供基本的连接和通信功能"""
    
    async def connect(self):
        """建立WebSocket连接"""
        safe_print(f"WebSocket连接尝试建立: {self.scope['path']}")
        await self.accept()
        safe_print(f"WebSocket连接已接受: {self.scope['path']}")

    async def disconnect(self, close_code):
        """断开WebSocket连接"""
        safe_print(f"WebSocket连接断开: {self.scope['path']}, 代码: {close_code}")
    
    async def send_error(self, error_message):
        """
        发送错误消息
        
        Args:
            error_message (str): 错误消息
        """
        await self.send(text_data=json.dumps({
            'error': error_message,
            'done': True
        }))
        
    async def receive(self, text_data):
        """
        接收消息并处理
        
        Args:
            text_data (str): 接收到的消息文本
        """
        try:
            # 确保json模块可用
            import json  # 显式导入，确保局部可用
            
            # 解析接收到的消息
            safe_print(f"收到WebSocket消息: {text_data[:100]}...")
            
            # 确保输入文本是有效的UTF-8字符串
            if isinstance(text_data, bytes):
                text_data = text_data.decode('utf-8')
            
            data = json.loads(text_data)
            
            # 调用具体的消息处理函数
            await self.handle_message(data)
                
        except Exception as e:
            error_message = f"处理消息时出错: {str(e)}\n{traceback.format_exc()}"
            safe_print(error_message)
            await self.send_error(error_message)
    
    async def handle_message(self, data):
        """
        处理接收到的消息，子类应重写此方法
        
        Args:
            data (dict): 解析后的消息数据
        """
        # 这是一个需要被子类重写的方法
        await self.send_error("BaseConsumer.handle_message方法未实现") 