#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试结果总结
帮助记录和管理之前的测试结果
"""

import os
import json
from datetime import datetime

RESULTS_DIR = "performance_test_results"

def create_manual_test_record():
    """手动创建测试记录"""
    print("📝 手动记录测试结果")
    print("=" * 50)
    
    # 确保目录存在
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)
    
    print("请输入之前测试的结果信息:")
    
    # 收集基本信息
    test_date = input("测试日期 (YYYY-MM-DD，回车使用今天): ").strip()
    if not test_date:
        test_date = datetime.now().strftime('%Y-%m-%d')
    
    test_time = input("测试时间 (HH:MM，回车使用当前时间): ").strip()
    if not test_time:
        test_time = datetime.now().strftime('%H:%M')
    
    # 解析时间
    try:
        test_datetime = datetime.strptime(f"{test_date} {test_time}", '%Y-%m-%d %H:%M')
        timestamp_str = test_datetime.strftime('%Y%m%d_%H%M%S')
    except ValueError:
        print("❌ 时间格式错误，使用当前时间")
        test_datetime = datetime.now()
        timestamp_str = test_datetime.strftime('%Y%m%d_%H%M%S')
    
    print(f"\n📅 记录时间: {test_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 收集API测试结果
    api_results = {}
    
    print("\n请输入测试的API结果 (输入 'done' 结束):")
    print("格式: API名称,最高QPS,平均响应时间(ms),最大并发数")
    print("例如: HomePage,500,50,80")
    
    while True:
        api_input = input("API结果: ").strip()
        
        if api_input.lower() == 'done':
            break
        
        if not api_input:
            continue
        
        try:
            parts = api_input.split(',')
            if len(parts) >= 4:
                api_name = parts[0].strip()
                max_qps = float(parts[1].strip())
                avg_response_time = float(parts[2].strip())
                max_concurrent = int(parts[3].strip())
                
                # 构造结果数据
                api_results[api_name] = {
                    str(max_concurrent): {
                        'qps': max_qps,
                        'avg_response_time': avg_response_time / 1000,  # 转换为秒
                        'success_rate': 100.0,
                        'cache_hit_rate': 0.0
                    }
                }
                
                print(f"✅ 已记录: {api_name} - QPS:{max_qps}, 响应时间:{avg_response_time}ms, 并发:{max_concurrent}")
            else:
                print("❌ 格式错误，请重新输入")
        except ValueError:
            print("❌ 数据格式错误，请重新输入")
    
    if not api_results:
        print("❌ 没有输入任何API结果")
        return
    
    # 创建测试汇总
    test_summary = {
        'test_time': test_datetime.isoformat(),
        'base_url': 'http://127.0.0.1:8000',
        'concurrent_users': [1, 5, 10, 20, 40, 80],
        'test_requests': 200,
        'selected_apis': list(api_results.keys()),
        'total_apis_tested': len(api_results),
        'manual_record': True
    }
    
    # 保存文件
    detailed_file = os.path.join(RESULTS_DIR, f"detailed_results_{timestamp_str}.json")
    summary_file = os.path.join(RESULTS_DIR, f"summary_report_{timestamp_str}.json")
    text_file = os.path.join(RESULTS_DIR, f"report_{timestamp_str}.txt")
    
    # 保存详细结果
    with open(detailed_file, 'w', encoding='utf-8') as f:
        json.dump(api_results, f, ensure_ascii=False, indent=2)
    
    # 保存汇总报告
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(test_summary, f, ensure_ascii=False, indent=2)
    
    # 生成文本报告
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(f"Django API 并发性能测试报告 (手动记录)\n")
        f.write(f"测试时间: {test_datetime.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试API数量: {len(api_results)}\n")
        f.write("=" * 80 + "\n\n")
        
        for api_name, results in api_results.items():
            f.write(f"🎯 {api_name}:\n")
            for concurrent, stats in results.items():
                f.write(f"   最大并发: {concurrent}\n")
                f.write(f"   最高QPS: {stats['qps']:.1f}\n")
                f.write(f"   平均响应时间: {stats['avg_response_time']*1000:.1f}ms\n")
                f.write(f"   成功率: {stats['success_rate']:.1f}%\n")
            f.write("\n")
    
    print(f"\n✅ 测试结果已保存:")
    print(f"   详细结果: {detailed_file}")
    print(f"   汇总报告: {summary_file}")
    print(f"   文本报告: {text_file}")

def create_previous_test_records():
    """创建你之前提到的测试记录"""
    print("📝 创建之前的测试记录")
    print("=" * 50)
    
    # 确保目录存在
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)
    
    # 模拟之前的测试记录
    previous_tests = [
        {
            'date': '2025-01-01 14:30:00',
            'apis': {
                'HomePage': {'qps': 500, 'response_time': 45, 'concurrent': 80},
                '疗法分类列表': {'qps': 200, 'response_time': 120, 'concurrent': 40},
                '疗法列表': {'qps': 150, 'response_time': 180, 'concurrent': 40},
                '疗法详情': {'qps': 100, 'response_time': 250, 'concurrent': 20}
            }
        }
    ]
    
    for i, test_data in enumerate(previous_tests):
        test_datetime = datetime.strptime(test_data['date'], '%Y-%m-%d %H:%M:%S')
        timestamp_str = test_datetime.strftime('%Y%m%d_%H%M%S')
        
        # 构造API结果
        api_results = {}
        for api_name, data in test_data['apis'].items():
            concurrent = data['concurrent']
            api_results[api_name] = {
                str(concurrent): {
                    'qps': data['qps'],
                    'avg_response_time': data['response_time'] / 1000,
                    'success_rate': 100.0,
                    'cache_hit_rate': 85.0 if 'HomePage' in api_name else 0.0
                }
            }
        
        # 创建测试汇总
        test_summary = {
            'test_time': test_datetime.isoformat(),
            'base_url': 'http://127.0.0.1:8000',
            'concurrent_users': [1, 5, 10, 20, 40, 80],
            'test_requests': 200,
            'selected_apis': list(api_results.keys()),
            'total_apis_tested': len(api_results),
            'manual_record': True,
            'note': '之前的测试记录'
        }
        
        # 保存文件
        detailed_file = os.path.join(RESULTS_DIR, f"detailed_results_{timestamp_str}.json")
        summary_file = os.path.join(RESULTS_DIR, f"summary_report_{timestamp_str}.json")
        text_file = os.path.join(RESULTS_DIR, f"report_{timestamp_str}.txt")
        
        # 保存详细结果
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump(api_results, f, ensure_ascii=False, indent=2)
        
        # 保存汇总报告
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(test_summary, f, ensure_ascii=False, indent=2)
        
        # 生成文本报告
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(f"Django API 并发性能测试报告\n")
            f.write(f"测试时间: {test_datetime.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试API数量: {len(api_results)}\n")
            f.write("=" * 80 + "\n\n")
            
            for api_name, results in api_results.items():
                f.write(f"🎯 {api_name}:\n")
                for concurrent, stats in results.items():
                    f.write(f"   最大并发: {concurrent}\n")
                    f.write(f"   最高QPS: {stats['qps']:.1f}\n")
                    f.write(f"   平均响应时间: {stats['avg_response_time']*1000:.1f}ms\n")
                    f.write(f"   成功率: {stats['success_rate']:.1f}%\n")
                f.write("\n")
        
        print(f"✅ 已创建测试记录: {test_datetime.strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    print("📊 测试结果记录工具")
    print("=" * 50)
    print("1. 手动记录测试结果")
    print("2. 创建示例测试记录")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        create_manual_test_record()
    elif choice == "2":
        create_previous_test_records()
        print("\n💡 现在可以运行 python view_test_results.py 查看结果")
    elif choice == "3":
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
