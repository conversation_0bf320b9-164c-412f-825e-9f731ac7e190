<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="6fe5a673-6235-4a7d-b030-6db08c01239d" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FUSProjectUsageTrigger">
    <session id="-1158937381">
      <usages-collector id="statistics.lifecycle.project">
        <counts>
          <entry key="project.closed" value="1" />
          <entry key="project.open.time.1" value="2" />
          <entry key="project.opened" value="2" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.open">
        <counts>
          <entry key="md" value="1" />
          <entry key="py" value="7" />
          <entry key="txt" value="3" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.open">
        <counts>
          <entry key="PLAIN_TEXT" value="4" />
          <entry key="Python" value="7" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.edit">
        <counts>
          <entry key="md" value="10" />
          <entry key="py" value="84" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.edit">
        <counts>
          <entry key="PLAIN_TEXT" value="10" />
          <entry key="Python" value="84" />
        </counts>
      </usages-collector>
    </session>
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/main.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="446">
              <caret line="33" lean-forward="true" selection-start-line="33" selection-end-line="33" />
              <folding>
                <element signature="e#0#14#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/readDic.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="400">
              <caret line="25" column="31" selection-start-line="25" selection-start-column="31" selection-end-line="25" selection-end-column="31" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/shijing.txt">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="52576">
              <caret line="3286" selection-start-line="3286" selection-end-line="3286" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/testserver.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="208">
              <caret line="13" column="62" selection-start-line="13" selection-start-column="62" selection-end-line="13" selection-end-column="62" />
              <folding>
                <element signature="e#19#82#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/modern-chinese-dic.txt">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ganzhi.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="5744">
              <caret line="359" selection-start-line="359" selection-end-line="359" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/chuci.txt">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/metaphysic.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="2304">
              <caret line="144" column="26" selection-start-line="144" selection-start-column="26" selection-end-line="144" selection-end-column="26" />
              <folding>
                <element signature="e#0#15#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/wuxingData.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="16">
              <caret line="1" selection-start-line="1" selection-end-line="1" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/README.md">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="64">
              <caret line="4" lean-forward="true" selection-start-line="4" selection-end-line="4" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>wuxingData</find>
      <find>chuci</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/wuxingData.py" />
        <option value="$PROJECT_DIR$/ganzhi.py" />
        <option value="$PROJECT_DIR$/main.py" />
        <option value="$PROJECT_DIR$/metaphysic.py" />
        <option value="$PROJECT_DIR$/README.md" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds">
    <option name="x" value="-248" />
    <option name="y" value="-975" />
    <option name="width" value="1920" />
    <option name="height" value="1013" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="bazi" type="b2602c69:ProjectViewProjectNode" />
              <item name="bazi" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
      <pane id="PackagesPane" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="com.android.tools.idea.instantapp.provision.ProvisionBeforeRunTaskProvider.myTimeStamp" value="1575431470215" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="project.structure.last.edited" value="Project" />
    <property name="project.structure.proportion" value="0.15" />
    <property name="project.structure.side.proportion" value="0.3091954" />
    <property name="settings.editor.selected.configurable" value="configurable.group.language" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Python.main">
    <configuration name="ganzhi" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="bazi" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ganzhi.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="bazi" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="metaphysic" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="bazi" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/metaphysic.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="readDic" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="bazi" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/readDic.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="testserver" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="bazi" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/testserver.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.main" />
      <item itemvalue="Python.metaphysic" />
      <item itemvalue="Python.ganzhi" />
      <item itemvalue="Python.readDic" />
      <item itemvalue="Python.testserver" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.testserver" />
        <item itemvalue="Python.metaphysic" />
        <item itemvalue="Python.readDic" />
        <item itemvalue="Python.ganzhi" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6fe5a673-6235-4a7d-b030-6db08c01239d" name="Default Changelist" comment="" />
      <created>1575426520265</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1575426520265</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="-248" y="-975" width="1920" height="1013" extended-state="0" />
    <editor active="true" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.25239617" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Image Layers" order="2" />
      <window_info id="Designer" order="3" />
      <window_info id="UI Designer" order="4" />
      <window_info id="Capture Tool" order="5" />
      <window_info id="Favorites" order="6" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" visible="true" weight="0.36047775" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.3995657" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Terminal" order="7" weight="0.32899022" />
      <window_info anchor="bottom" id="Event Log" order="8" side_tool="true" />
      <window_info anchor="bottom" id="Version Control" order="9" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Palette" order="3" />
      <window_info anchor="right" id="Capture Analysis" order="4" />
      <window_info anchor="right" id="Theme Preview" order="5" />
      <window_info anchor="right" id="Palette&#9;" order="6" />
      <window_info anchor="right" id="Maven Projects" order="7" />
    </layout>
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/server.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-437" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/shijing.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="52576">
          <caret line="3286" selection-start-line="3286" selection-end-line="3286" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/modern-chinese-dic.txt">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/ganzhi.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="5744">
          <caret line="359" selection-start-line="359" selection-end-line="359" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/chuci.txt">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/metaphysic.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2304">
          <caret line="144" column="26" selection-start-line="144" selection-start-column="26" selection-end-line="144" selection-end-column="26" />
          <folding>
            <element signature="e#0#15#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/wuxingData.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="16">
          <caret line="1" selection-start-line="1" selection-end-line="1" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/testserver.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="208">
          <caret line="13" column="62" selection-start-line="13" selection-start-column="62" selection-end-line="13" selection-end-column="62" />
          <folding>
            <element signature="e#19#82#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/main.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="446">
          <caret line="33" lean-forward="true" selection-start-line="33" selection-end-line="33" />
          <folding>
            <element signature="e#0#14#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/readDic.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="400">
          <caret line="25" column="31" selection-start-line="25" selection-start-column="31" selection-end-line="25" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="64">
          <caret line="4" lean-forward="true" selection-start-line="4" selection-end-line="4" />
        </state>
      </provider>
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ArtifactsStructureConfigurable.UI">
        <settings>
          <artifact-editor />
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="FacetStructureConfigurable.UI">
        <settings>
          <last-edited>No facets are configured</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="GlobalLibrariesConfigurable.UI">
        <settings>
          <last-edited>Python 3.7 (Digi4th_iOS_server) interpreter library</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.3091954" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="JdkListConfigurable.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.3091954" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ModuleStructureConfigurable.UI">
        <settings>
          <last-edited>bazi</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
                <option value="0.6" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectJDKs.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.31241283" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectLibrariesConfigurable.UI">
        <settings>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>