#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Token刷新缓存机制
验证短时间内多次刷新token请求是否能正确使用缓存
"""

import os
import sys
import asyncio
import aiohttp
import time
import json
import jwt
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')

import django
django.setup()

from django.conf import settings
from api.models.user_models import UserInfo

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
REFRESH_TOKEN_URL = f"{BASE_URL}/api/auth/refresh-token"

def generate_test_refresh_token():
    """生成一个测试用的有效refresh token"""
    try:
        # 获取第一个用户作为测试用户
        user = UserInfo.objects.first()
        if not user:
            print("❌ 数据库中没有用户，无法生成测试token")
            return None

        print(f"✅ 使用用户ID {user.id} 生成测试refresh token")

        # 生成refresh token（90天有效期）
        payload = {
            'user_id': user.id,
            'exp': datetime.utcnow() + timedelta(days=90)
        }

        refresh_token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
        return refresh_token, user.id

    except Exception as e:
        print(f"❌ 生成测试token失败: {str(e)}")
        return None

def get_test_token():
    """获取测试用的refresh token"""
    # 首先尝试生成新的refresh token
    result = generate_test_refresh_token()
    if result:
        refresh_token, user_id = result
        print(f"🔑 生成新的refresh token，用户ID: {user_id}")
        return refresh_token

    # 如果生成失败，尝试从文件读取
    try:
        with open('test_token_user_2.txt', 'r') as f:
            content = f.read().strip()
            print("⚠️ 使用文件中的token（可能是access token，测试可能失败）")
            return content
    except FileNotFoundError:
        print("❌ 未找到test_token_user_2.txt文件且无法生成新token")
        return None
    except Exception as e:
        print(f"❌ 读取token文件失败: {e}")
        return None

async def test_single_refresh(session, refresh_token, test_id):
    """测试单次token刷新"""
    headers = {
        "Refresh-Token": refresh_token,
        "Content-Type": "application/json"
    }
    
    start_time = time.time()
    
    try:
        async with session.post(REFRESH_TOKEN_URL, headers=headers) as response:
            end_time = time.time()
            duration = end_time - start_time
            
            response_data = await response.json()
            
            print(f"[测试{test_id}] 状态码: {response.status}, 耗时: {duration:.3f}秒")
            
            if response.status == 200:
                access_token = response_data.get('access_token', '')
                print(f"[测试{test_id}] ✅ 成功获取access_token: {access_token[:20]}...")
                return True, duration, access_token
            else:
                print(f"[测试{test_id}] ❌ 刷新失败: {response_data}")
                return False, duration, None
                
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"[测试{test_id}] ❌ 请求异常: {e}")
        return False, duration, None

async def test_concurrent_refresh(refresh_token, concurrent_count=5):
    """测试并发token刷新"""
    print(f"\n🚀 开始并发测试，同时发送{concurrent_count}个刷新请求...")
    
    async with aiohttp.ClientSession() as session:
        # 创建并发任务
        tasks = []
        for i in range(concurrent_count):
            task = test_single_refresh(session, refresh_token, f"并发{i+1}")
            tasks.append(task)
        
        # 同时执行所有任务
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        print(f"\n📊 并发测试结果 (总耗时: {total_time:.3f}秒):")
        
        success_count = 0
        total_duration = 0
        access_tokens = set()
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"[并发{i+1}] ❌ 异常: {result}")
            else:
                success, duration, access_token = result
                if success:
                    success_count += 1
                    total_duration += duration
                    if access_token:
                        access_tokens.add(access_token)
        
        print(f"✅ 成功: {success_count}/{concurrent_count}")
        print(f"⏱️ 平均响应时间: {total_duration/max(success_count, 1):.3f}秒")
        print(f"🔑 返回的access_token数量: {len(access_tokens)}")
        
        if len(access_tokens) == 1:
            print("🎯 缓存机制工作正常！所有请求返回相同的access_token")
        elif len(access_tokens) > 1:
            print("⚠️ 可能存在缓存问题，返回了不同的access_token")
        
        return success_count, len(access_tokens)

async def test_sequential_refresh(refresh_token, request_count=3, interval=0.1):
    """测试连续token刷新（短时间间隔）"""
    print(f"\n🔄 开始连续测试，间隔{interval}秒发送{request_count}个请求...")
    
    async with aiohttp.ClientSession() as session:
        results = []
        access_tokens = set()
        
        for i in range(request_count):
            if i > 0:
                await asyncio.sleep(interval)
            
            success, duration, access_token = await test_single_refresh(
                session, refresh_token, f"连续{i+1}"
            )
            
            results.append((success, duration))
            if access_token:
                access_tokens.add(access_token)
        
        print(f"\n📊 连续测试结果:")
        success_count = sum(1 for success, _ in results if success)
        avg_duration = sum(duration for _, duration in results) / len(results)
        
        print(f"✅ 成功: {success_count}/{request_count}")
        print(f"⏱️ 平均响应时间: {avg_duration:.3f}秒")
        print(f"🔑 返回的access_token数量: {len(access_tokens)}")
        
        if len(access_tokens) == 1:
            print("🎯 缓存机制工作正常！所有请求返回相同的access_token")
        elif len(access_tokens) > 1:
            print("⚠️ 可能存在缓存问题，返回了不同的access_token")
        
        return success_count, len(access_tokens)

async def main():
    """主测试函数"""
    print("🧪 Token刷新缓存机制测试")
    print("=" * 50)
    
    # 获取测试token
    refresh_token = get_test_token()
    if not refresh_token:
        print("❌ 无法获取测试token，请检查test_token_user_2.txt文件")
        return
    
    print(f"📝 使用refresh_token: {refresh_token[:20]}...")
    
    try:
        # 测试1: 并发请求
        concurrent_success, concurrent_tokens = await test_concurrent_refresh(refresh_token, 5)
        
        # 等待一段时间
        print("\n⏳ 等待2秒...")
        await asyncio.sleep(2)
        
        # 测试2: 连续请求
        sequential_success, sequential_tokens = await test_sequential_refresh(refresh_token, 3, 0.1)
        
        # 总结
        print("\n" + "=" * 50)
        print("📋 测试总结:")
        print(f"并发测试: {concurrent_success}/5 成功, {concurrent_tokens} 个不同token")
        print(f"连续测试: {sequential_success}/3 成功, {sequential_tokens} 个不同token")
        
        if concurrent_tokens == 1 and sequential_tokens == 1:
            print("🎉 缓存机制工作完美！")
        else:
            print("⚠️ 缓存机制可能需要调整")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
