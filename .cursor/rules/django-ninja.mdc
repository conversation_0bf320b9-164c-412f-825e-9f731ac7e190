---
description: 
globs: 
alwaysApply: true
---
# Django Ninja 生产级项目开发规则，你决不能随意篡改任何代码，否则影响巨大，我会杀了你，你决不能随便新建ninja路由，否则我会杀了你。访问需要权限说明API正常，时刻注意避免NinjaAPI配置冲突。
记得改完api，ninja还需要改序列化，千万别忘记。
- 如果需要命令行运行Python，需要首先输入start进入虚拟环境。例如start && python test_mysql_timeout_fix.py

##由于中间件机制，测试时使用该token
test_token_user_2.txt，采用这个测试token，如果不用token，过不了中间件
-H "Authorization: Bearer -H "Content-Type: application/json"
## 异步编程规范 (Async Programming)
- **强制使用异步**: 所有API视图必须使用`async def`，数据库操作使用`async_utils.py`工具函数
- **连接池管理**: 每次数据库操作都会强制关闭连接，防止泄漏，这个问题极其严重，必须要重视
- **异步工具函数**: 统一使用`get_async()`, `filter_async()`, `save_async()`等封装函数

##修复错误
首先查看tail -50 logs/error.log
下面几个日志假如有必要再看，不一定需要tail -50 logs/info.log 
## 模块化架构 (Modular Architecture)
- **分层结构**: `models/` | `views/` | `ninja_apis/` | `serializers/` 严格分离
- **功能模块**: 按业务领域划分(activity, user, questionnaire等)，避免循环依赖
- **API组织**: 每个模块独立的API文件，统一在`ninja_apis/__init__.py`注册
- **Schema定义**: 使用Pydantic模型定义请求/响应结构，放置在`schemas.py`

##数据库设计规范 (Database Design)
- **外键约束**: 使用`ForeignKey`和`related_name`，避免硬删除(`SET_NULL`)
- **时间字段**: 统一使用`auto_now_add`和`auto_now`，支持时区
- **索引优化**: 为查询频繁字段添加`db_index=True`
- **数据完整性**: 使用`unique_together`确保业务唯一性约束

## 性能优化 (Performance Optimization)
- **MYSQL8.0**:JSON字段查询性能提升巨大
- **连接池**: 使用`dj_db_conn_pool`管理数据库连接
- **缓存策略**: Redis缓存热点数据，设置合理过期时间
- **查询优化**: 使用`select_related`和`prefetch_related`减少N+1查询
- **分页处理**: 大数据集必须分页，默认限制100条/页
- 每个api必须要有耗时的print输出，注意优化性能，我们已经有API_TIMER装饰器了
- 多使用缓存机制，GET请求必须要加入缓存机制
- **连接池管理**: 每次数据库操作都会强制关闭连接，防止泄漏，这个问题极其严重，必须要重视
## 代码质量 (Code Quality)
- **类型注解**: 所有函数参数和返回值必须有类型提示
- **命名规范**: 使用有意义的中文注释，变量名采用snake_case
- **代码复用**: 提取公共逻辑到utils模块，避免重复代码

## API设计原则 (API Design Principles)
- **限流保护**: 使用`django-ratelimit`防止API滥用

##项目特点
# 我们在api里直接用这个代码就可以获取用户ID
user_id = request.user_id
##ninja规范，务必遵循ninja的开发规范，否则会出错
# api/ninja_apis/__init__.py
# 使ninja_apis成为一个有效的Python包 
from ninja import NinjaAPI
from api.views import tcmNLP, tcmchat, ninja_chat, invite_views
from .questionnaire_api import questionnaire_router
from .db_health_api import db_health_router

# 简化实现，使用全局变量控制初始化状态
_initialized = False

# 创建API实例
bank_api = NinjaAPI(version="1.0")
doubao_aichat = NinjaAPI(version="2.0")
invite_api = NinjaAPI(version="3.0")
questionnaire_api = NinjaAPI(version="1.0", title="问卷系统API", urls_namespace="questionnaire_api")
db_health_api = NinjaAPI(version="1.1", title="数据库健康监控API", urls_namespace="db_health_api")

# 仅在第一次导入模块时配置路由
def initialize():
    global _initialized
    if not _initialized:
        # 只执行一次路由器注册
        doubao_aichat.add_router("/chat/", ninja_chat.router)
        invite_api.add_router("/invite_api/", invite_views.router)
        questionnaire_api.add_router("", questionnaire_router)  # 注意：这里不使用前缀
        db_health_api.add_router("", db_health_router)  # 注册数据库健康监控路由
        _initialized = True

# 立即调用初始化
initialize() 

如果需要命令行运行Python，需要首先输入start进入虚拟环境。例如start && python test_mysql_timeout_fix.py

避免循环导入根本原因
你的预后系统模块出现404错误的根本原因是循环导入：
api/ninja_apis/__init__.py 直接导入 prognosis_views_refactored.py 中的 router
这导致了循环依赖，路由注册失败，进而导致404错误