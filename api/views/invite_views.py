import traceback
import asyncio
from ninja import Router, Schema
from pydantic import BaseModel
from typing import List, Optional
from volcenginesdkarkruntime import Ark
from api.models import (UserHealthRecord,UserInfo,TCMForumPost, TCMForumSection, TCMComment,TCMForumInteraction,
    PrognosisSymptom,
    PrognosisUserSymptom,
    PrognosisTherapyCategory,
    PrognosisTherapyLike,
    PrognosisTherapyRating,
    PrognosisTherapyComment,
    InvitationCode,
    InvitationRecord
)
from django.db import models
from django.utils import timezone
from asgiref.sync import sync_to_async
from pydantic import ValidationError,BaseModel
from django.http import JsonResponse
from rest_framework import status
from django.db import transaction
import json
import sys
from django.shortcuts import get_object_or_404
from ninja.errors import HttpError
from django.db.models import F
from datetime import datetime
from .content_review import HuaweiContentReview
from django.conf import settings
from django.http import Http404
from asgiref.sync import sync_to_async
import asyncio
import time
from typing import Dict
from asgiref.sync import sync_to_async

from ninja import Schema
from typing import Optional

class InvitationResponse(Schema):
    """邀请相关接口的响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[dict] = None

class InvitationCodeData(Schema):
    """邀请码数据模型"""
    code: str

from ninja import Router
from typing import Dict
from django.db import transaction
import random
import string

router = Router()

async def generate_unique_code(length=8):
    """生成唯一邀请码"""
    from api.ninja_apis.async_utils import exists_async
    while True:
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
        exists = await exists_async(InvitationCode, code=code)
        if not exists:
            return code

@router.post("/invitation/generate", response=InvitationResponse)
async def generate_invitation(request):
    """生成邀请码"""
    try:
        user = await sync_to_async(UserInfo.objects.get)(id=request.auth['user_id'])
        code = await generate_unique_code()
        
        # 包装创建操作
        await sync_to_async(InvitationCode.objects.create)(
            code=code,
            inviter=user
        )
        
        return {
            "success": True,
            "data": {
                "code": code
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"生成邀请码失败: {str(e)}"
        }

@router.get("/my-code")
async def get_or_create_invitation_code(request):
    """获取用户的邀请码，如果没有则创建"""
    try:
        from api.ninja_apis.async_utils import get_async, create_async
        user_id = request.user_id

        # 使用异步工具函数查询
        invitation = await get_async(InvitationCode, inviter_id=user_id)

        if not invitation:
            code = await generate_unique_code()
            # 使用异步工具函数创建
            invitation = await create_async(InvitationCode,
                code=code,
                inviter_id=user_id
            )

        return {
            "success": True,
            "data": {
                "code": invitation.code
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"获取邀请码失败: {str(e)}"
        }

@router.get("/scan/{code}")
async def scan_invitation(request, code: str):
    """扫描邀请码并完成邀请"""
    try:
        # 使用同步的事务上下文管理器
        @sync_to_async
        def process_invitation():
            with transaction.atomic():
                invitee_id = request.user_id
                
                # 获取并验证邀请码
                try:
                    invitation = InvitationCode.objects.select_for_update().get(code=code)
                except InvitationCode.DoesNotExist:
                    return False, "无效的邀请码"
                
                # 检查是否已被邀请
                if InvitationRecord.objects.filter(invitee_id=invitee_id).exists():
                    return False, "您已经被邀请过了"
                
                # 检查是否是自己的邀请码
                if invitation.inviter_id == invitee_id:
                    return False, "不能使用自己的邀请码"
                
                # 创建邀请记录
                InvitationRecord.objects.create(
                    inviter_id=invitation.inviter_id,
                    invitee_id=invitee_id,
                    invitation_code=invitation
                )
                
                return True, "邀请成功"
        
        success, message = await process_invitation()
        return {
            "success": success,
            "message": message
        }
            
    except Exception as e:
        return {
            "success": False,
            "message": f"处理邀请失败: {str(e)}"
        }


@router.get("/records")
async def get_invitation_records(request):
    """获取用户的邀请记录"""
    try:
        from api.ninja_apis.async_utils import values_async
        user_id = request.user_id
        print(f"获取用户 {user_id} 的邀请记录")  # 调试信息

        # 使用异步工具函数查询
        records_queryset = InvitationRecord.objects.filter(
            invitee_id=user_id  # 只获取当前用户作为被邀请者的记录
        ).select_related('inviter').order_by('-created_at').values(
            'inviter__nickname',  # 邀请者的昵称
            'inviter__avatar',    # 邀请者的头像
            'created_at'          # 邀请时间
        )

        records = await values_async(records_queryset)
        print(f"查询到的邀请记录: {records}")  # 调试信息
        total_invites = len(records)

        return {
            "success": True,
            "data": {
                "records": records,
                "total_invites": total_invites
            }
        }
    except Exception as e:
        print(f"获取邀请记录时发生错误: {str(e)}")  # 调试信息
        return {
            "success": False,
            "message": f"获取邀请记录失败: {str(e)}"
        }