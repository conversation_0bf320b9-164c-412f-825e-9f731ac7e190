# -*- coding:utf-8 -*-
"""
TCM NLP - 历法系统模块
包含干支计算、季节判断、节气计算等功能
"""

import traceback
from datetime import datetime, timedelta, timezone
import pytz
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django_ratelimit.decorators import ratelimit

from .constants import SEASON_WUXING_MAP, WUXING_RELATIONSHIPS, WUXING_WEIGHT_COEFFICIENTS, SOLAR_TERMS, ORGAN_TIME_WEIGHTS


def calculate_ganzhi(date):
    """计算干支"""
    gan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
    zhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
    wuxing_dict = {
        '甲': '木', '乙': '木', '丙': '火', '丁': '火', 
        '戊': '土', '己': '土', '庚': '金', '辛': '金', 
        '壬': '水', '癸': '水'
    }

    base_date = datetime(2024, 5, 9)  # 注意: 月份从1开始
    day_diff = (date - base_date.date()).days
    gan_index = (day_diff % 10 + 9) % 10
    zhi_index = (day_diff % 12 + 9) % 12

    gan_zhi = gan[gan_index] + zhi[zhi_index]
    wuxing = wuxing_dict[gan[gan_index]]

    # 定义五行生克关系和权重
    sheng_ke_relationship = {
        '木': {'生': '火', '克': '土'},
        '火': {'生': '土', '克': '金'},
        '土': {'生': '金', '克': '水'},
        '金': {'生': '水', '克': '木'},
        '水': {'生': '木', '克': '火'}
    }

    wuxing_elements = ['木', '火', '土', '金', '水']
    weight_dict = {
        '于子则愈': 0,
        '于胜则传': -1,
        '于母则持': -2,
        '于不胜则死': 2,
        '于己则起': 1.5
    }

    weights = {}

    for element in wuxing_elements:
        if element == wuxing:
            weights[element] = weight_dict['于己则起']
        elif sheng_ke_relationship[wuxing]['生'] == element:
            weights[element] = weight_dict['于子则愈']
        elif sheng_ke_relationship[wuxing]['克'] == element:
            weights[element] = weight_dict['于不胜则死']
        elif sheng_ke_relationship[element]['生'] == wuxing:
            weights[element] = weight_dict['于母则持']
        elif sheng_ke_relationship[element]['克'] == wuxing:
            weights[element] = weight_dict['于胜则传']

    return gan_zhi, wuxing, weights


@method_decorator(ratelimit(key='ip', rate='30/m', block=True))
def get_ganzhi(request):
    """获取干支"""
    try:
        # 获取当前日期
        tz = pytz.timezone('Asia/Shanghai')
        now = timezone.now().astimezone(tz)
        date = now.date()
        
        gan_zhi, wuxing, weights = calculate_ganzhi(date)
        print('干支:', gan_zhi)
        print('五行:', wuxing)
        print('五行权重:', weights)
        return JsonResponse({'ganzhi': gan_zhi, 'wuxing': wuxing, 'weights': weights})
    except Exception as e:
        print('发生错误:')
        traceback.print_exc()
        return JsonResponse({'error': str(e)}, status=500)


def get_current_season11(date):
    """获取当前季节（中医季节分类）"""
    # 获取当前日期的时区信息
    try:
        print('请求get_current_season')
        tz = pytz.timezone('Asia/Shanghai')
        time_difference = timedelta(minutes=6)
        # 定义每年的固定节气日期（根据通常的公历日期）
        year = date.year
        spring_start = tz.localize(datetime(year, 2, 4))  # 立春
        summer_start = tz.localize(datetime(year, 5, 5))  # 立夏
        autumn_start = tz.localize(datetime(year, 8, 7))  # 立秋
        winter_start = tz.localize(datetime(year, 11, 7)) # 立冬
    
        # 定义季节长度
        spleen_days = 18
    
        # 计算各个季节的范围
        spring_end = summer_start - timedelta(days=spleen_days)
        summer_end = autumn_start - timedelta(days=spleen_days)
        autumn_end = winter_start - timedelta(days=spleen_days)
        winter_end = tz.localize(datetime(year + 1, 2, 4)) - timedelta(days=spleen_days)
    
        # 计算长夏的四个阶段
        long_summer_1_start = spring_end
        long_summer_1_end = summer_start - timedelta(days=1)
        long_summer_2_start = summer_end
        long_summer_2_end = autumn_start - timedelta(days=1)
        long_summer_3_start = autumn_end
        long_summer_3_end = winter_start - timedelta(days=1)
        long_summer_4_start = winter_end
        long_summer_4_end = tz.localize(datetime(year + 1, 2, 4)) - timedelta(days=1)
    
        # 确保输入日期也包含时区信息
        date = tz.localize(date) if date.tzinfo is None else date
    
        if spring_start <= date + time_difference < spring_end:
            return "春"
        elif long_summer_1_start <= date + time_difference <= long_summer_1_end:
            return "长夏1"
        elif summer_start <= date + time_difference < summer_end:
            return "夏"
        elif long_summer_2_start <= date + time_difference <= long_summer_2_end:
            return "长夏2"
        elif autumn_start <= date + time_difference < autumn_end:
            return "秋"
        elif long_summer_3_start <= date + time_difference <= long_summer_3_end:
            return "长夏3"
        elif winter_start <= date + time_difference < winter_end:
            return "冬"
        elif long_summer_4_start <= date + time_difference <= long_summer_4_end:
            return "长夏4"
        else:
            # 如果当前日期在立春之前的18天内，则认为是上一年的冬季
            previous_winter_start = tz.localize(datetime(year - 1, 11, 7))
            previous_winter_end = tz.localize(datetime(year, 2, 4)) - timedelta(days=spleen_days)
            if previous_winter_start <= date + time_difference < previous_winter_end:
                return "冬"
            print(f"未知季节: {date.strftime('%Y-%m-%d')}")
            print(f"未知季节: ", date)
            print('秋的起点:', autumn_start)
            return None
    except Exception as e:
        # 捕获所有可能的异常，并打印错误信息
        print(f"错误: {str(e)}")
        return None


@method_decorator(ratelimit(key='ip', rate='30/m', block=True))
def get_current_season(request):
    """获取当前季节"""
    try:
        print('请求get_current_tcm_season')
        tz = pytz.timezone('Asia/Shanghai')
        now = timezone.now().astimezone(tz)
        
        def calculate_season(date):
            time_difference = timedelta(minutes=6)
            year = date.year
            
            # 定义每年的固定节气日期
            spring_start = tz.localize(datetime(year, 2, 4))  # 立春
            summer_start = tz.localize(datetime(year, 5, 5))  # 立夏
            autumn_start = tz.localize(datetime(year, 8, 7))  # 立秋
            winter_start = tz.localize(datetime(year, 11, 7)) # 立冬
            
            spleen_days = 18
            
            # 计算各个季节的范围
            spring_end = summer_start - timedelta(days=spleen_days)
            summer_end = autumn_start - timedelta(days=spleen_days)
            autumn_end = winter_start - timedelta(days=spleen_days)
            winter_end = tz.localize(datetime(year + 1, 2, 4)) - timedelta(days=spleen_days)
            
            # 计算长夏的四个阶段
            long_summer_1_start = spring_end
            long_summer_1_end = summer_start - timedelta(days=1)
            long_summer_2_start = summer_end
            long_summer_2_end = autumn_start - timedelta(days=1)
            long_summer_3_start = autumn_end
            long_summer_3_end = winter_start - timedelta(days=1)
            long_summer_4_start = winter_end
            long_summer_4_end = tz.localize(datetime(year + 1, 2, 4)) - timedelta(days=1)
            
            date = date + time_difference
            
            if spring_start <= date < spring_end:
                return "春"
            elif long_summer_1_start <= date <= long_summer_1_end:
                return "长夏1"
            elif summer_start <= date < summer_end:
                return "夏"
            elif long_summer_2_start <= date <= long_summer_2_end:
                return "长夏2"
            elif autumn_start <= date < autumn_end:
                return "秋"
            elif long_summer_3_start <= date <= long_summer_3_end:
                return "长夏3"
            elif winter_start <= date < winter_end:
                return "冬"
            elif long_summer_4_start <= date <= long_summer_4_end:
                return "长夏4"
            else:
                # 如果当前日期在立春之前的18天内，则认为是上一年的冬季
                previous_winter_start = tz.localize(datetime(year - 1, 11, 7))
                previous_winter_end = tz.localize(datetime(year, 2, 4)) - timedelta(days=spleen_days)
                if previous_winter_start <= date < previous_winter_end:
                    return "冬"
                print(f"未知季节: {date.strftime('%Y-%m-%d')}")
                print(f"未知季节: ", date)
                print('秋的起点:', autumn_start)
                return None

        current_season = calculate_season(now)
        return JsonResponse({"current_season": current_season})
    
    except Exception as e:
        print(f"错误: {str(e)}")
        return JsonResponse({"error": "获取季节时发生错误"}, status=500)


@method_decorator(ratelimit(key='ip', rate='30/m', block=True))
def get_current_tcm_season(request):
    """获取当前中医季节（别名函数）"""
    try:
        print(f'🚀 [DEBUG] get_current_tcm_season 被调用，请求方法: {request.method}')
        print(f'🔧 [DEBUG] 开始处理季节计算...')

        import pytz
        from django.utils import timezone
        from datetime import datetime, timedelta

        tz = pytz.timezone('Asia/Shanghai')
        now = timezone.now().astimezone(tz)
        print(f'⏰ [DEBUG] 当前时间: {now}')

        def calculate_season(date):
            print(f'🔍 [DEBUG] 开始计算季节，输入日期: {date}')
            time_difference = timedelta(minutes=6)
            year = date.year
            print(f'📅 [DEBUG] 年份: {year}')

            # 定义每年的固定节气日期
            spring_start = tz.localize(datetime(year, 2, 4))  # 立春
            summer_start = tz.localize(datetime(year, 5, 5))  # 立夏
            autumn_start = tz.localize(datetime(year, 8, 7))  # 立秋
            winter_start = tz.localize(datetime(year, 11, 7)) # 立冬

            spleen_days = 18

            # 计算各个季节的范围
            spring_end = summer_start - timedelta(days=spleen_days)
            summer_end = autumn_start - timedelta(days=spleen_days)
            autumn_end = winter_start - timedelta(days=spleen_days)

            print(f'🌸 [DEBUG] 春季: {spring_start} - {spring_end}')
            print(f'☀️ [DEBUG] 夏季: {summer_start} - {summer_end}')
            print(f'🍂 [DEBUG] 秋季: {autumn_start} - {autumn_end}')
            print(f'❄️ [DEBUG] 冬季: {winter_start} - 下年立春')

            # 判断当前日期属于哪个季节
            if spring_start <= date < spring_end:
                season = "春"
            elif spring_end <= date < summer_start:
                season = "长夏"  # 春末的脾土时期
            elif summer_start <= date < summer_end:
                season = "夏"
            elif summer_end <= date < autumn_start:
                season = "长夏"  # 夏末的脾土时期
            elif autumn_start <= date < autumn_end:
                season = "秋"
            elif autumn_end <= date < winter_start:
                season = "长夏"  # 秋末的脾土时期
            else:
                season = "冬"

            print(f'🎯 [DEBUG] 计算结果: {season}')
            return season

        print(f'🔄 [DEBUG] 调用季节计算函数...')
        current_season = calculate_season(now)
        print(f'✅ [DEBUG] 季节计算完成: {current_season}')

        response_data = {
            'season': current_season,
            'timestamp': now.isoformat(),
            'year': now.year,
            'month': now.month,
            'day': now.day
        }
        print(f'📦 [DEBUG] 准备返回响应: {response_data}')

        from django.http import JsonResponse
        return JsonResponse(response_data)

    except Exception as e:
        print(f"💥 [ERROR] 获取当前季节时发生错误: {str(e)}")
        print(f"💥 [ERROR] 异常类型: {type(e).__name__}")
        import traceback
        print(f"💥 [ERROR] 完整堆栈: {traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)


def get_season_wuxing(season):
    """获取季节对应的五行"""
    # 根据中医理论，季节对应五行
    return SEASON_WUXING_MAP.get(season, None)


def calculate_season_weights(wuxing):
    """计算季节五行权重"""
    # 定义五行生克关系和权重
    sheng_ke_relationship = WUXING_RELATIONSHIPS
    
    wuxing_elements = ['木', '火', '土', '金', '水']
    weight_dict = WUXING_WEIGHT_COEFFICIENTS
    
    weights = {}
    
    for element in wuxing_elements:
        if element == wuxing:
            weights[element] = weight_dict['于己则起']
        elif sheng_ke_relationship[wuxing]['生'] == element:
            weights[element] = weight_dict['于子则愈']
        elif sheng_ke_relationship[wuxing]['克'] == element:
            weights[element] = weight_dict['于不胜则死']
        elif sheng_ke_relationship[element]['生'] == wuxing:
            weights[element] = weight_dict['于母则持']
        elif sheng_ke_relationship[element]['克'] == wuxing:
            weights[element] = weight_dict['于胜则传']
    
    return weights


@method_decorator(ratelimit(key='ip', rate='30/m', block=True))
def get_season_wuxing_weights():
    """获取当前季节的五行权重"""
    try:
        tz = pytz.timezone('Asia/Shanghai')
        now = timezone.now().astimezone(tz)
        
        # 定义季节计算函数
        def calculate_season(date):
            time_difference = timedelta(minutes=6)
            year = date.year
            
            # 定义每年的固定节气日期
            spring_start = tz.localize(datetime(year, 2, 4))  # 立春
            summer_start = tz.localize(datetime(year, 5, 5))  # 立夏
            autumn_start = tz.localize(datetime(year, 8, 7))  # 立秋
            winter_start = tz.localize(datetime(year, 11, 7)) # 立冬
            
            spleen_days = 18
            
            # 计算各个季节的范围
            spring_end = summer_start - timedelta(days=spleen_days)
            summer_end = autumn_start - timedelta(days=spleen_days)
            autumn_end = winter_start - timedelta(days=spleen_days)
            winter_end = tz.localize(datetime(year + 1, 2, 4)) - timedelta(days=spleen_days)
            
            # 计算长夏的四个阶段
            long_summer_1_start = spring_end
            long_summer_1_end = summer_start - timedelta(days=1)
            long_summer_2_start = summer_end
            long_summer_2_end = autumn_start - timedelta(days=1)
            long_summer_3_start = autumn_end
            long_summer_3_end = winter_start - timedelta(days=1)
            long_summer_4_start = winter_end
            long_summer_4_end = tz.localize(datetime(year + 1, 2, 4)) - timedelta(days=1)
            
            date = date + time_difference
            
            if spring_start <= date < spring_end:
                return "春"
            elif long_summer_1_start <= date <= long_summer_1_end:
                return "长夏"
            elif summer_start <= date < summer_end:
                return "夏"
            elif long_summer_2_start <= date <= long_summer_2_end:
                return "长夏"
            elif autumn_start <= date < autumn_end:
                return "秋"
            elif long_summer_3_start <= date <= long_summer_3_end:
                return "长夏"
            elif winter_start <= date < winter_end:
                return "冬"
            elif long_summer_4_start <= date <= long_summer_4_end:
                return "长夏"
            else:
                # 如果当前日期在立春之前的18天内，则认为是上一年的冬季
                previous_winter_start = tz.localize(datetime(year - 1, 11, 7))
                previous_winter_end = tz.localize(datetime(year, 2, 4)) - timedelta(days=spleen_days)
                if previous_winter_start <= date < previous_winter_end:
                    return "冬"
                return None
        
        # 获取当前季节
        current_season = calculate_season(now)
        
        if current_season:
            # 获取季节对应的五行
            season_wuxing = get_season_wuxing(current_season)
            
            if season_wuxing:
                # 计算五行权重
                weights = calculate_season_weights(season_wuxing)
                
                return JsonResponse({
                    "season": current_season,
                    "wuxing": season_wuxing,
                    "weights": weights
                })
            else:
                return JsonResponse({"error": "无法确定季节对应的五行"}, status=500)
        else:
            return JsonResponse({"error": "无法确定当前季节"}, status=500)
    
    except Exception as e:
        print(f"错误: {str(e)}")
        traceback.print_exc()
        return JsonResponse({"error": "获取季节五行权重时发生错误"}, status=500)


def check_solar_term(date_str):
    """检查是否为节气"""
    month_day = date_str.strftime('%m%d')
    for term in SOLAR_TERMS:
        if term['date'] == month_day:
            return term['name']
    return None


@method_decorator(ratelimit(key='ip', rate='30/m', block=True))
def get_solar_term(request):
    """获取当前节气"""
    try:
        tz = pytz.timezone('Asia/Shanghai')
        now = timezone.now().astimezone(tz)
        term = check_solar_term(now)
        return JsonResponse({"date": now.strftime('%Y-%m-%d'), "solar_term": term})
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


def calculate_organ_weights_by_time(hour):
    """根据时间计算脏腑权重"""
    # 按时辰计算权重
    time_weights = ORGAN_TIME_WEIGHTS
    
    # 计算对应的时辰
    if hour in [23, 0]:
        period = "子时"
    elif hour in [1, 2]:
        period = "丑时"
    elif hour in [3, 4]:
        period = "寅时"
    elif hour in [5, 6]:
        period = "卯时"
    elif hour in [7, 8]:
        period = "辰时"
    elif hour in [9, 10]:
        period = "巳时"
    elif hour in [11, 12]:
        period = "午时"
    elif hour in [13, 14]:
        period = "未时"
    elif hour in [15, 16]:
        period = "申时"
    elif hour in [17, 18]:
        period = "酉时"
    elif hour in [19, 20]:
        period = "戌时"
    elif hour in [21, 22]:
        period = "亥时"
    else:
        period = "未知"
    
    return time_weights.get(period, {}) 