from django.core.management.base import BaseCommand
from django.conf import settings
import subprocess
import os

class Command(BaseCommand):
    help = 'Sync database backups to remote server'

    def handle(self, *args, **options):
        backup_dir = os.path.join(settings.BASE_DIR, 'database_backups')
        remote_dir = 'user@remote_host:/path/to/remote/backup/directory/'
        
        rsync_cmd = [
            'rsync',
            '-avz',
            '-e', 'ssh -p 22',
            backup_dir + '/',
            remote_dir
        ]
        
        subprocess.run(rsync_cmd, check=True)
        
        self.stdout.write(self.style.SUCCESS("Backups synced to remote server"))