"""
配置模块
处理API密钥和相关配置
"""
import os
import sys
import traceback
import openai
from django.conf import settings
from api.utils.logging import safe_print

# 添加路径以便导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

# 检查Python版本和默认编码
def log_environment_info():
    """记录Python环境信息"""
    safe_print(f"Python版本: {sys.version}")
    safe_print(f"默认编码: {sys.getdefaultencoding()}")
    safe_print(f"文件系统编码: {sys.getfilesystemencoding()}")

# 获取华为方舟API参数并配置openai
def init_openai_config():
    """
    初始化openai配置，适配主项目的openai使用方式

    Returns:
        str: model_id 模型ID，或者在出错时返回None
    """
    try:
        api_key = settings.LLM_API.get('ARK_API_KEY', '')
        if not api_key or api_key == 'Bearer ':
            safe_print("API密钥无效或为空，请在settings中设置正确的API_KEY")
            raise ValueError("API密钥无效")

        # 记录API密钥的编码状态
        safe_print(f"API密钥类型: {type(api_key)}, 长度: {len(api_key)}")

        base_url = settings.LLM_API['ARK_BASE_URL']
        model_id = settings.LLM_API['ARK_MODEL_ID']

        # 确保所有参数都正确编码
        safe_print(f"API参数检查: base_url={base_url}, model_id={model_id}")

        # 配置openai全局设置（适配主项目的使用方式）
        openai.api_key = api_key
        openai.api_base = base_url

        safe_print(f"成功配置openai全局设置, 模型ID: {model_id}")
        return model_id
    except Exception as e:
        safe_print(f"初始化openai配置失败: {str(e)}")
        safe_print(traceback.format_exc())
        return None

# 初始化openai配置
MODEL_ID = init_openai_config()

# 为了兼容性，保留client变量（但实际使用openai全局配置）
client = None