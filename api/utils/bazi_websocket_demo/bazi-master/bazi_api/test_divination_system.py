#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
算卦系统测试脚本
用于测试算卦系统的各个组件和功能
"""

import os
import sys
import json
import traceback
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bazi_api.settings')

try:
    import django
    django.setup()
except Exception as e:
    print(f"Django初始化失败: {e}")
    sys.exit(1)

def test_divination_data_service():
    """测试六十四卦数据服务"""
    print("\n" + "="*60)
    print("测试六十四卦数据服务")
    print("="*60)
    
    try:
        from api.services.divination_service import divination_data_service
        
        # 测试数据加载
        print(f"数据是否已加载: {divination_data_service.is_data_loaded()}")
        print(f"卦的总数: {divination_data_service.get_gua_count()}")
        
        # 测试获取卦象信息
        test_gua_ids = [1, 2, 3, 32, 64]
        for gua_id in test_gua_ids:
            gua_info = divination_data_service.get_gua_by_id(gua_id)
            if gua_info:
                print(f"第{gua_id}卦: {gua_info['name']} - {gua_info['code']}")
                print(f"  卦辞: {gua_info['gua_ci'][:50]}...")
            else:
                print(f"第{gua_id}卦: 未找到")
        
        # 测试获取卦象简要信息
        summary = divination_data_service.get_gua_summary(1)
        print(f"\n第1卦简要信息: {summary}")
        
        # 测试根据名称查找
        gua_info = divination_data_service.get_gua_by_name("乾为天")
        if gua_info:
            print(f"\n根据名称查找 '乾为天': {gua_info['name']} (第{gua_info['id']}卦)")
        
        print("✓ 六十四卦数据服务测试通过")
        
    except Exception as e:
        print(f"✗ 六十四卦数据服务测试失败: {str(e)}")
        print(traceback.format_exc())

def test_random_service():
    """测试随机算法服务"""
    print("\n" + "="*60)
    print("测试随机算法服务")
    print("="*60)
    
    try:
        from api.services.random_service import random_service
        
        # 测试基础随机数生成
        test_inputs = [0, 123, 456, 789, 999]
        print("基础随机数生成测试:")
        for user_input in test_inputs:
            gua_number = random_service.generate_divination_number(user_input)
            print(f"  输入: {user_input:3d} -> 卦数: {gua_number:2d}")
        
        # 测试增强随机数生成
        print("\n增强随机数生成测试:")
        question = "我的事业发展如何？"
        question_hash = random_service.get_question_hash(question)
        print(f"问题哈希: {question_hash}")
        
        for user_input in test_inputs:
            gua_number = random_service.generate_enhanced_random(user_input, question_hash)
            print(f"  输入: {user_input:3d} -> 卦数: {gua_number:2d}")
        
        # 测试分布均匀性
        print("\n测试随机数分布均匀性...")
        distribution_stats = random_service.validate_distribution(1000)
        print(f"测试迭代次数: {distribution_stats['total_iterations']}")
        print(f"期望每卦次数: {distribution_stats['expected_per_gua']:.2f}")
        print(f"方差: {distribution_stats['variance']:.2f}")
        print(f"最小次数: {distribution_stats['min_count']}")
        print(f"最大次数: {distribution_stats['max_count']}")
        print(f"均匀性比例: {distribution_stats['uniformity_ratio']:.3f}")
        
        print("✓ 随机算法服务测试通过")
        
    except Exception as e:
        print(f"✗ 随机算法服务测试失败: {str(e)}")
        print(traceback.format_exc())

def test_divination_core_service():
    """测试算卦核心服务"""
    print("\n" + "="*60)
    print("测试算卦核心服务")
    print("="*60)
    
    try:
        from api.services.divination_core_service import divination_core_service
        
        # 测试用户输入验证
        print("用户输入验证测试:")
        test_inputs = [123, "456", 1000, -1, "abc", None]
        for test_input in test_inputs:
            is_valid, value, error_msg = divination_core_service.validate_user_input(test_input)
            print(f"  输入: {test_input} -> 有效: {is_valid}, 值: {value}, 错误: {error_msg}")
        
        # 测试简单算卦
        print("\n简单算卦测试:")
        result = divination_core_service.get_simple_divination(123)
        if result['success']:
            print(f"  用户输入: {result['user_input']}")
            print(f"  卦数: {result['gua_number']}")
            print(f"  卦名: {result['gua_info']['name']}")
            print(f"  卦象: {result['gua_info']['code']}")
        else:
            print(f"  失败: {result['error']}")
        
        # 测试完整算卦
        print("\n完整算卦测试:")
        result = divination_core_service.perform_divination(456, "我的未来如何？")
        if result['success']:
            print(f"  用户输入: {result['divination_info']['user_input']}")
            print(f"  问题: {result['divination_info']['question']}")
            print(f"  卦数: {result['divination_info']['gua_number']}")
            print(f"  卦名: {result['gua_info']['name']}")
            print(f"  简要解释: {result['summary']['brief_explanation']}")
        else:
            print(f"  失败: {result['error']}")
        
        # 测试根据卦数获取卦象
        print("\n根据卦数获取卦象测试:")
        result = divination_core_service.get_gua_by_number(1)
        if result['success']:
            print(f"  卦名: {result['gua_info']['name']}")
            print(f"  卦象: {result['gua_info']['code']}")
            print(f"  卦辞: {result['gua_info']['gua_ci'][:100]}...")
        else:
            print(f"  失败: {result['error']}")
        
        # 测试统计信息
        print("\n系统统计信息测试:")
        stats = divination_core_service.get_divination_statistics()
        if 'error' not in stats:
            print(f"  卦的总数: {stats['gua_count']}")
            print(f"  数据已加载: {stats['data_loaded']}")
            print(f"  随机分布测试: {stats['random_distribution']['total_iterations']}次")
            print(f"  系统版本: {stats['system_info']['version']}")
        else:
            print(f"  失败: {stats['error']}")
        
        print("✓ 算卦核心服务测试通过")
        
    except Exception as e:
        print(f"✗ 算卦核心服务测试失败: {str(e)}")
        print(traceback.format_exc())

def test_divination_prompts():
    """测试算卦提示词配置"""
    print("\n" + "="*60)
    print("测试算卦提示词配置")
    print("="*60)
    
    try:
        from api.utils.constants import (
            get_divination_system_prompt, 
            get_divination_prompt,
            reload_prompts
        )
        
        # 测试系统提示词
        print("系统提示词测试:")
        system_prompt = get_divination_system_prompt("general")
        print(f"  通用系统提示词长度: {len(system_prompt)}")
        print(f"  内容预览: {system_prompt[:100]}...")
        
        professional_prompt = get_divination_system_prompt("professional")
        print(f"  专业系统提示词长度: {len(professional_prompt)}")
        print(f"  内容预览: {professional_prompt[:100]}...")
        
        # 测试解读提示词
        print("\n解读提示词测试:")
        prompt_types = ["general_interpretation", "specific_question", "life_guidance"]
        for prompt_type in prompt_types:
            prompt = get_divination_prompt(prompt_type)
            print(f"  {prompt_type}提示词长度: {len(prompt)}")
            print(f"  内容预览: {prompt[:100]}...")
        
        # 测试提示词格式化
        print("\n提示词格式化测试:")
        template = get_divination_prompt("general_interpretation")
        formatted = template.format(
            gua_name="乾为天",
            gua_code="111111",
            question="我的事业发展如何？",
            gua_ci="乾：元，亨，利，贞。"
        )
        print(f"  格式化后长度: {len(formatted)}")
        print(f"  格式化预览: {formatted[:200]}...")
        
        print("✓ 算卦提示词配置测试通过")
        
    except Exception as e:
        print(f"✗ 算卦提示词配置测试失败: {str(e)}")
        print(traceback.format_exc())

def test_api_endpoints():
    """测试API端点（模拟测试）"""
    print("\n" + "="*60)
    print("测试API端点（模拟测试）")
    print("="*60)
    
    try:
        # 这里只是模拟测试，实际需要启动Django服务器
        from api.services.divination_core_service import divination_core_service
        
        # 模拟算卦API
        print("模拟算卦API测试:")
        user_input = 123
        is_valid, validated_input, error_msg = divination_core_service.validate_user_input(user_input)
        
        if is_valid:
            result = divination_core_service.perform_divination(validated_input, "测试问题")
            if result['success']:
                print(f"  算卦成功: {result['gua_info']['name']}")
                print(f"  卦数: {result['divination_info']['gua_number']}")
            else:
                print(f"  算卦失败: {result['error']}")
        else:
            print(f"  输入验证失败: {error_msg}")
        
        # 模拟卦象信息API
        print("\n模拟卦象信息API测试:")
        gua_result = divination_core_service.get_gua_by_number(1)
        if gua_result['success']:
            print(f"  获取卦象成功: {gua_result['gua_info']['name']}")
        else:
            print(f"  获取卦象失败: {gua_result['error']}")
        
        # 模拟统计API
        print("\n模拟统计API测试:")
        stats = divination_core_service.get_divination_statistics()
        if 'error' not in stats:
            print(f"  统计信息获取成功，卦数: {stats['gua_count']}")
        else:
            print(f"  统计信息获取失败: {stats['error']}")
        
        print("✓ API端点模拟测试通过")
        
    except Exception as e:
        print(f"✗ API端点模拟测试失败: {str(e)}")
        print(traceback.format_exc())

def main():
    """主测试函数"""
    print("开始算卦系统测试")
    print("当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 运行所有测试
    test_divination_data_service()
    test_random_service()
    test_divination_core_service()
    test_divination_prompts()
    test_api_endpoints()
    
    print("\n" + "="*60)
    print("算卦系统测试完成")
    print("="*60)

if __name__ == "__main__":
    main() 