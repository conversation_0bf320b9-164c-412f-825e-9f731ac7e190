# API限流系统使用指南

## 概述

本系统实现了基于用户ID的API调用限流功能，支持区分普通用户和会员用户的不同限额，使用Redis缓存存储限流计数，按天重置。

## 功能特性

- ✅ **用户身份识别**: 自动识别普通用户和会员用户
- ✅ **独立限额**: 每个API独立计数，互不影响
- ✅ **灵活配置**: 支持自定义普通用户和会员用户的限额
- ✅ **优雅降级**: 超限时返回友好错误信息
- ✅ **缓存机制**: 基于Redis的高性能计数存储
- ✅ **自动重置**: 每天00:00自动重置计数
- ✅ **响应头信息**: 在HTTP响应头中提供限流状态
- ✅ **管理工具**: 提供管理员查看和重置限流状态的工具

## 默认限额配置

```python
DEFAULT_LIMITS = {
    'normal_user': 30,      # 普通用户每日限额
    'member_user': 300,     # 会员用户每日限额
}
```

## 使用方法

### 1. 基本装饰器使用

```python
from api.utils.rate_limiter import rate_limit

# 使用默认限额
@rate_limit("my_api")
async def my_api_view(request):
    # API逻辑
    pass

# 自定义限额
@rate_limit("special_api", normal_limit=50, member_limit=500)
async def special_api_view(request):
    # API逻辑
    pass
```

### 2. 预定义装饰器

```python
from api.utils.rate_limiter import (
    chat_rate_limit,
    medical_case_study_rate_limit,
    medical_case_scoring_rate_limit
)

@chat_rate_limit
async def chat_api(request):
    pass

@medical_case_study_rate_limit
async def medical_case_study_api(request):
    pass

@medical_case_scoring_rate_limit
async def medical_case_scoring_api(request):
    pass
```

### 3. 应用到现有API

在 `api/views/modules/chat/chat_views.py` 中：

```python
# 聊天API
@router.post("/chat")
@chat_rate_limit
@api_timer("AI聊天交互")
async def chat(request, chat_request: ChatRequestSchema):
    # ... 现有逻辑

# 医案学习API
@router.post("/chat_YiAnStudy")
@medical_case_study_rate_limit
@api_timer("AI医案交互") 
async def medical_case_study_chat(request, chat_request: ChatRequestSchema):
    # ... 现有逻辑

# 医案评分API
@router.post("/chat_YiAnPingfen/{case_id}")
@medical_case_scoring_rate_limit
@api_timer("AI医案评分")
async def medical_case_scoring_chat(request, case_id: str, score_request: MedicalCaseScoreRequest):
    # ... 现有逻辑
```

## API响应

### 正常响应

当用户未达到限额时，API正常返回，并在响应头中包含限流信息：

```
X-RateLimit-Limit: 300
X-RateLimit-Remaining: 299
X-RateLimit-Reset: 2024-01-02 00:00:00
```

### 超限响应

当用户超过限额时，返回HTTP 429状态码：

```json
{
    "error": "您今日的chat_api调用次数已达上限",
    "error_code": "RATE_LIMIT_EXCEEDED",
    "current_count": 30,
    "limit": 30,
    "reset_time": "2024-01-02 00:00:00",
    "is_member": false,
    "message": "升级会员可享受更多调用次数"
}
```

## 查询限流状态

### 用户端查询

```http
GET /api/chat/rate_limit_status
Authorization: Bearer <token>
```

响应：
```json
{
    "success": true,
    "message": "获取限流状态成功",
    "user_id": 123,
    "stats": {
        "chat_api": {
            "api_name": "chat_api",
            "current_count": 15,
            "limit": 30,
            "remaining": 15,
            "reset_time": "2024-01-02 00:00:00",
            "is_member": false
        },
        "medical_case_study_api": {
            "current_count": 5,
            "limit": 30,
            "remaining": 25,
            "is_member": false
        },
        "medical_case_scoring_api": {
            "current_count": 0,
            "limit": 30,
            "remaining": 30,
            "is_member": false
        }
    }
}
```

## 管理员工具

### 查看用户使用情况

```python
from api.utils.rate_limiter_admin import RateLimitAdmin

# 查看用户今日所有API使用情况
usage = await RateLimitAdmin.get_user_all_api_usage(user_id=123)

# 查看用户指定日期的使用情况
usage = await RateLimitAdmin.get_user_all_api_usage(
    user_id=123, 
    date_str="2024-01-01"
)
```

### 重置用户限流

```python
# 重置用户所有API的今日使用次数
success = await RateLimitAdmin.reset_user_api_usage(user_id=123)

# 重置用户特定API的使用次数
success = await RateLimitAdmin.reset_user_api_usage(
    user_id=123, 
    api_name="chat_api"
)

# 重置用户指定日期的使用次数
success = await RateLimitAdmin.reset_user_api_usage(
    user_id=123, 
    api_name="chat_api",
    date_str="2024-01-01"
)
```

### 设置用户使用次数

```python
# 设置用户特定API的使用次数
success = await RateLimitAdmin.set_user_api_usage(
    user_id=123,
    api_name="chat_api", 
    count=10
)
```

### 监控API健康状态

```python
from api.utils.rate_limiter_admin import RateLimitMonitor

# 获取API健康状态
health = await RateLimitMonitor.get_api_health_status()
```

## 缓存键格式

限流数据存储在Redis中，键格式为：
```
rate_limit:{api_name}:{user_id}:{date}
```

例如：
```
rate_limit:chat_api:123:2024-01-01
rate_limit:medical_case_study_api:456:2024-01-01
```

## 错误处理

### 用户未登录
```json
{
    "error": "用户未登录",
    "error_code": "UNAUTHORIZED"
}
```

### 系统错误
如果缓存服务不可用，限流器会降级处理，记录错误日志但不阻止API调用。

## 性能考虑

1. **缓存性能**: 使用Redis异步操作，性能开销很小
2. **网络开销**: 每次API调用增加1-2次Redis操作
3. **内存使用**: 每个用户每个API每天约占用50字节Redis内存
4. **自动清理**: 缓存数据自动过期，无需手动清理

## 最佳实践

1. **装饰器顺序**: 建议将限流装饰器放在API路由装饰器之后，计时装饰器之前
2. **错误监控**: 监控限流相关的错误日志，及时发现缓存问题
3. **会员推广**: 利用限流提示引导用户升级会员
4. **合理限额**: 根据实际业务需求和服务器负载调整限额
5. **监控使用量**: 定期查看API使用统计，优化限额配置

## 故障排除

### 常见问题

1. **限流不生效**: 检查Redis连接和中间件配置
2. **计数不准确**: 检查系统时间和时区设置
3. **缓存键冲突**: 确保API名称唯一性
4. **会员状态错误**: 检查用户模型的会员状态判断逻辑

### 调试命令

```bash
# 查看Redis中的限流键
redis-cli keys "rate_limit:*"

# 查看特定用户的限流数据
redis-cli get "rate_limit:chat_api:123:2024-01-01"

# 手动清除限流数据
redis-cli del "rate_limit:chat_api:123:2024-01-01"
```

## 扩展功能

### 未来可扩展的功能

1. **数据库记录**: 将使用统计写入数据库，支持历史数据分析
2. **动态限额**: 根据用户行为动态调整限额
3. **API优先级**: 不同API设置不同的优先级
4. **地理位置限制**: 基于用户地理位置的限流策略
5. **时段限制**: 不同时段不同的限额配置 