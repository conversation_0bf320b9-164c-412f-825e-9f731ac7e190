#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示所有可测试的API列表
帮助用户了解当前可以进行性能测试的API端点
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 从API配置文件导入配置
from performance_test_api_config import (
    PERFORMANCE_TEST_APIS as TEST_ENDPOINTS,
    API_CATEGORIES,
    HIGH_PRIORITY_APIS,
    MEDIUM_PRIORITY_APIS,
    BASE_URL
)

# 检查token状态
def check_token_status():
    try:
        with open('test_token_user_2.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        return None

TEST_TOKEN = check_token_status()

def show_api_list():
    """显示所有可测试的API列表"""
    print("🎯 Django API 并发性能测试 - 可用API列表")
    print("=" * 80)
    print(f"🌐 测试地址: {BASE_URL}")
    print(f"🔑 Token状态: {'已配置' if TEST_TOKEN else '未配置'}")
    print(f"📊 总API数量: {len(TEST_ENDPOINTS)}")
    print(f"🔥 高优先级API: {len(HIGH_PRIORITY_APIS)} 个")
    print(f"⚡ 中优先级API: {len(MEDIUM_PRIORITY_APIS)} 个")

    print(f"\n📂 按类别分组 ({len(API_CATEGORIES)} 个类别):")
    print("=" * 80)

    total_count = 0
    for category, apis in API_CATEGORIES.items():
        print(f"\n🔸 {category} ({len(apis)} 个API):")
        print("-" * 70)

        for name, config in apis.items():
            total_count += 1
            method = config.get('method', 'GET')
            url = config['url']
            description = config.get('description', '无描述')
            priority = config.get('priority', '中')
            expected_qps = config.get('expected_qps', 'N/A')

            # 检查是否需要认证
            auth_required = bool(config.get('headers', {}).get('Authorization'))
            auth_status = "🔐" if auth_required else "🔓"

            # 检查是否有请求数据
            has_data = bool(config.get('data'))
            data_status = "📝" if has_data else "📄"

            # 缓存状态
            cache_status = "🟢" if config.get('cache_enabled') else "🔴"

            # 优先级图标
            priority_icon = "🔥" if priority == "高" else "⚡" if priority == "中" else "📋"

            print(f"   {total_count:2d}. {name}")
            print(f"       {auth_status} {method} {url}")
            print(f"       {data_status} {description}")
            print(f"       {priority_icon} 优先级:{priority} | 预期QPS:{expected_qps} | 缓存:{cache_status}")

            if has_data:
                print(f"       📋 请求数据: {config['data']}")
            print()

    print("=" * 80)
    print("图例说明:")
    print("🔐 需要认证  🔓 无需认证")
    print("📝 需要数据  📄 无需数据")
    print("🔥 高优先级  ⚡ 中优先级  📋 低优先级")
    print("🟢 启用缓存  🔴 无缓存")
    print("\n💡 使用方法:")
    print("1. 运行 python test_concurrent_performance.py 开始性能测试")
    print("2. 根据提示选择要测试的API场景")
    print("3. 测试结果将保存到 performance_test_results/ 目录")
    print("4. 推荐先测试核心业务场景，再进行完整测试")

def show_category_summary():
    """显示类别汇总信息"""
    print(f"\n📊 类别统计汇总:")
    print("=" * 90)
    print(f"{'类别':<15} {'总数':<6} {'高优先级':<8} {'需认证':<8} {'有缓存':<8} {'预期QPS范围'}")
    print("-" * 90)

    for category, apis in API_CATEGORIES.items():
        total_count = len(apis)
        high_priority_count = sum(1 for api in apis.values() if api.get('priority') == '高')
        auth_required_count = sum(1 for api in apis.values() if api.get('headers', {}).get('Authorization'))
        cache_enabled_count = sum(1 for api in apis.values() if api.get('cache_enabled'))

        # 计算QPS范围
        qps_values = [api.get('expected_qps') for api in apis.values() if isinstance(api.get('expected_qps'), int)]
        if qps_values:
            qps_range = f"{min(qps_values)}-{max(qps_values)}"
        else:
            qps_range = "N/A"

        print(f"{category:<15} {total_count:<6} {high_priority_count:<8} {auth_required_count:<8} {cache_enabled_count:<8} {qps_range}")

    print(f"\n🎯 测试建议:")
    print(f"1. 优先测试高优先级API ({len(HIGH_PRIORITY_APIS)} 个)")
    print(f"2. 重点关注缓存效果和QPS表现")
    print(f"3. 认证API需要有效token才能测试")
    print(f"4. 预期QPS仅供参考，实际性能取决于服务器配置")

def show_detailed_api_info(api_name):
    """显示特定API的详细信息"""
    if api_name not in TEST_ENDPOINTS:
        print(f"❌ API '{api_name}' 不存在")
        return
    
    config = TEST_ENDPOINTS[api_name]
    print(f"\n🔍 API详细信息: {api_name}")
    print("=" * 60)
    print(f"📂 类别: {config.get('category', '其他')}")
    print(f"🌐 URL: {config['url']}")
    print(f"📋 方法: {config.get('method', 'GET')}")
    print(f"📝 描述: {config.get('description', '无描述')}")
    
    headers = config.get('headers', {})
    if headers:
        print(f"📤 请求头:")
        for key, value in headers.items():
            if key == 'Authorization' and value:
                print(f"   {key}: Bearer [TOKEN]")
            else:
                print(f"   {key}: {value}")
    
    data = config.get('data')
    if data:
        print(f"📋 请求数据: {data}")
    
    print(f"\n💡 测试命令:")
    print(f"   python test_concurrent_performance.py")
    print(f"   然后选择 '{api_name}' 进行测试")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--summary":
            show_category_summary()
        elif sys.argv[1] == "--detail":
            if len(sys.argv) > 2:
                show_detailed_api_info(sys.argv[2])
            else:
                print("❌ 请指定API名称: python show_available_apis.py --detail API名称")
        else:
            print("❌ 未知参数，支持的参数:")
            print("   --summary  显示类别统计")
            print("   --detail API名称  显示特定API详情")
    else:
        show_api_list()
        show_category_summary()
