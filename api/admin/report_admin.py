"""
举报相关模型的Admin配置
包括AI对话举报功能的admin设置
"""

from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from ..models import Report


class ReportResource(resources.ModelResource):
    class Meta:
        model = Report
        fields = ('id', 'message_index', 'message_content', 'report_reason', 'reported_at')
        export_order = fields


@admin.register(Report)
class ReportAdmin(ImportExportModelAdmin):
    resource_class = ReportResource
    list_display = ('id', 'report_reason', 'message_index', 'reported_at')
    list_filter = ('report_reason', 'reported_at')
    search_fields = ('message_content',)
    readonly_fields = ('reported_at',) 