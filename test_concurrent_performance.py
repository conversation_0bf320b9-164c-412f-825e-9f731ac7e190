#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展并发性能测试工具 - 全面测试Django Ninja API性能
支持多种API类型测试，结果保存到指定目录
重点测试：认证、问卷、预后、银行等核心业务API
"""

import requests
import time
import statistics
import threading
import os
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入API配置
from performance_test_api_config import (
    PERFORMANCE_TEST_APIS,
    TEST_SCENARIOS,
    API_CATEGORIES,
    HIGH_PRIORITY_APIS,
    MEDIUM_PRIORITY_APIS,
    BASE_URL,
    AUTH_HEADERS
)

# 测试配置
CONCURRENT_USERS = [1, 5, 10, 20, 40, 80, 150]  # 并发用户数
TEST_REQUESTS = 200  # 每轮测试请求数
RESULTS_DIR = "performance_test_results"  # 测试结果保存目录

# 使用配置文件中的API端点
TEST_ENDPOINTS = PERFORMANCE_TEST_APIS

# 创建结果保存目录
def ensure_results_directory():
    """确保结果保存目录存在"""
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)
        print(f"📁 创建测试结果目录: {RESULTS_DIR}")

def save_test_results(all_results, test_summary):
    """保存测试结果到文件"""
    ensure_results_directory()

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 保存详细结果
    detailed_file = os.path.join(RESULTS_DIR, f"detailed_results_{timestamp}.json")
    with open(detailed_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)

    # 保存汇总报告
    summary_file = os.path.join(RESULTS_DIR, f"summary_report_{timestamp}.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(test_summary, f, ensure_ascii=False, indent=2)

    # 保存可读的文本报告
    text_file = os.path.join(RESULTS_DIR, f"report_{timestamp}.txt")
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(f"Django API 并发性能测试报告\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试地址: {BASE_URL}\n")
        f.write(f"并发用户数: {CONCURRENT_USERS}\n")
        f.write(f"每轮测试请求数: {TEST_REQUESTS}\n")
        f.write("=" * 80 + "\n\n")

        for endpoint_name, endpoint_results in all_results.items():
            f.write(f"🎯 {endpoint_name}:\n")
            f.write("   并发数 | QPS   | 平均响应时间 | 缓存命中率 | 成功率\n")
            f.write("   -------|-------|-------------|-----------|-------\n")

            for concurrent_users, stats in endpoint_results.items():
                f.write(f"   {concurrent_users:6d} | {stats['qps']:5.1f} | {stats['avg_response_time']*1000:8.1f}ms | {stats['cache_hit_rate']:8.1f}% | {stats['success_rate']:5.1f}%\n")
            f.write("\n")

    print(f"📊 测试结果已保存:")
    print(f"   详细结果: {detailed_file}")
    print(f"   汇总报告: {summary_file}")
    print(f"   文本报告: {text_file}")

def make_request(url, method="GET", headers=None, data=None):
    """发送单个HTTP请求"""
    start_time = time.time()
    try:
        if method == "GET":
            response = requests.get(url, headers=headers or {}, timeout=10)
        elif method == "POST":
            response = requests.post(url, headers=headers or {}, json=data or {}, timeout=10)
        elif method == "PUT":
            response = requests.put(url, headers=headers or {}, json=data or {}, timeout=10)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers or {}, timeout=10)
        else:
            response = requests.get(url, headers=headers or {}, timeout=10)

        response_time = time.time() - start_time
        success = response.status_code in [200, 201, 202]

        # 检查缓存命中 - 只检查实际的缓存标识
        cache_hit = False
        if success:
            # 检查HTTP缓存头
            cache_hit = (
                'Cache-Control' in response.headers or
                'ETag' in response.headers or
                'X-Cache' in response.headers or
                'cache' in response.headers.get('X-Cache-Status', '').lower()
            )
            # 检查响应内容中的缓存标识
            try:
                if response.headers.get('content-type', '').startswith('application/json'):
                    response_data = response.json()
                    if isinstance(response_data, dict):
                        cache_hit = cache_hit or response_data.get('cached', False) or response_data.get('from_cache', False)
            except:
                pass

        return {
            'response_time': response_time,
            'success': success,
            'status_code': response.status_code,
            'cache_hit': cache_hit,
            'error': None
        }
    except Exception as e:
        response_time = time.time() - start_time
        return {
            'response_time': response_time,
            'success': False,
            'status_code': 0,
            'cache_hit': False,
            'error': str(e)
        }

def run_concurrent_test(endpoint_name, config, concurrent_users, total_requests):
    """运行并发测试"""
    print(f"\n🚀 开始测试 {endpoint_name} - {concurrent_users} 并发用户，总请求数 {total_requests}")
    print(f"   📝 {config['description']}")

    results = []
    start_time = time.time()

    def worker():
        return make_request(
            config["url"],
            config["method"],
            config.get("headers", {}),
            config.get("data", {})
        )

    # 使用线程池执行并发请求
    with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
        futures = [executor.submit(worker) for _ in range(total_requests)]

        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append({
                    'response_time': 0,
                    'success': False,
                    'status_code': 0,
                    'cache_hit': False,
                    'error': str(e)
                })

    end_time = time.time()
    total_duration = end_time - start_time

    # 计算统计信息
    response_times = [r['response_time'] for r in results]
    success_count = sum(1 for r in results if r['success'])
    cache_hits = sum(1 for r in results if r['cache_hit'])

    stats = {
        'total_requests': len(results),
        'success_count': success_count,
        'success_rate': (success_count / len(results) * 100) if results else 0,
        'cache_hit_rate': (cache_hits / success_count * 100) if success_count > 0 else 0,
        'avg_response_time': statistics.mean(response_times) if response_times else 0,
        'min_response_time': min(response_times) if response_times else 0,
        'max_response_time': max(response_times) if response_times else 0,
        'median_response_time': statistics.median(response_times) if response_times else 0,
        'qps': len(results) / total_duration if total_duration > 0 else 0,
        'total_duration': total_duration
    }

    return stats, results

def print_test_results(endpoint_name, concurrent_users, stats):
    """打印测试结果"""
    print(f"\n📊 {endpoint_name} 测试结果 ({concurrent_users} 并发用户):")
    print(f"   ✅ 总请求数: {stats['total_requests']}")
    print(f"   ✅ 成功率: {stats['success_rate']:.1f}%")
    print(f"   ⚡ 缓存命中率: {stats['cache_hit_rate']:.1f}%")
    print(f"   ⏱️  平均响应时间: {stats['avg_response_time']*1000:.1f}ms")
    print(f"   ⏱️  中位数响应时间: {stats['median_response_time']*1000:.1f}ms")
    print(f"   ⏱️  最大响应时间: {stats['max_response_time']*1000:.1f}ms")
    print(f"   🔥 QPS: {stats['qps']:.1f}")
    print(f"   ⏰ 总耗时: {stats['total_duration']:.2f}秒")

def select_apis_to_test():
    """让用户选择要测试的API"""
    print("🎯 性能测试API选择菜单")
    print("=" * 80)

    print("📋 可用的测试场景:")
    print("  1. 核心业务 - 高优先级API (最重要的业务功能)")
    print("  2. 认证系统 - 登录、Token相关API")
    print("  3. 银行系统 - 用户活动、目标管理API")
    print("  4. 问卷系统 - 问卷、体质分析API")
    print("  5. 学习系统 - 每日题目、答题排名API")
    print("  6. 预后系统 - 疗法分类、疗法管理API")
    print("  7. 完整测试 - 所有API (推荐用于全面性能评估)")
    print("  8. 自定义选择 - 手动选择特定API")

    # 显示各场景的API数量
    print(f"\n📊 各场景API数量:")
    for scenario_name, apis in TEST_SCENARIOS.items():
        print(f"   {scenario_name}: {len(apis)} 个API")

    print(f"\n💡 推荐测试顺序:")
    print(f"   1️⃣ 先测试 '核心业务' ({len(HIGH_PRIORITY_APIS)} 个API)")
    print(f"   2️⃣ 再测试 '完整测试' ({len(PERFORMANCE_TEST_APIS)} 个API)")

    choice = input(f"\n请选择测试场景 (1-8): ").strip()

    if choice == "1":
        print("✅ 选择了核心业务场景")
        return list(HIGH_PRIORITY_APIS.keys())
    elif choice == "2":
        print("✅ 选择了认证系统场景")
        return list(API_CATEGORIES.get("认证系统", {}).keys())
    elif choice == "3":
        print("✅ 选择了银行系统场景")
        return list(API_CATEGORIES.get("银行系统", {}).keys())
    elif choice == "4":
        print("✅ 选择了问卷系统场景")
        return list(API_CATEGORIES.get("问卷系统", {}).keys())
    elif choice == "5":
        print("✅ 选择了学习系统场景")
        return list(API_CATEGORIES.get("学习系统", {}).keys())
    elif choice == "6":
        print("✅ 选择了预后系统场景")
        return list(API_CATEGORIES.get("预后系统", {}).keys())
    elif choice == "7":
        print("✅ 选择了完整测试场景")
        return list(PERFORMANCE_TEST_APIS.keys())
    elif choice == "8":
        return select_custom_apis()
    else:
        print("❌ 无效选择，默认使用核心业务场景")
        return list(HIGH_PRIORITY_APIS.keys())

def select_custom_apis():
    """自定义选择API"""
    print("\n🔧 自定义API选择:")
    print("=" * 60)

    # 按类别分组显示
    api_list = []
    index = 1

    for category, apis in API_CATEGORIES.items():
        print(f"\n📂 {category} ({len(apis)} 个API):")
        for name, config in apis.items():
            priority = config.get('priority', '中')
            expected_qps = config.get('expected_qps', 'N/A')
            cache_status = "🟢缓存" if config.get('cache_enabled') else "🔴无缓存"
            print(f"   {index:2d}. {name} - {config['description']}")
            print(f"       优先级:{priority} | 预期QPS:{expected_qps} | {cache_status}")
            api_list.append(name)
            index += 1

    print(f"\n📊 总共 {len(api_list)} 个API可供测试")
    print("\n选择方式:")
    print("  0. 测试所有API")
    print("  1. 输入API编号 (用逗号分隔，例如: 1,3,5)")

    choice = input("\n请选择 (0/1): ").strip()

    if choice == "0":
        return list(PERFORMANCE_TEST_APIS.keys())
    elif choice == "1":
        selected_indices = input("请输入API编号: ").strip()
        try:
            indices = [int(x.strip()) - 1 for x in selected_indices.split(',')]
            selected_apis = [api_list[i] for i in indices if 0 <= i < len(api_list)]
            if selected_apis:
                return selected_apis
            else:
                print("❌ 没有选择有效的API")
                return list(HIGH_PRIORITY_APIS.keys())
        except:
            print("❌ 输入格式错误，使用核心业务场景")
            return list(HIGH_PRIORITY_APIS.keys())
    else:
        print("❌ 无效选择，使用核心业务场景")
        return list(HIGH_PRIORITY_APIS.keys())

def main():
    """主测试函数"""
    print("🎯 Django API 并发性能测试工具")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 测试地址: {BASE_URL}")
    print(f"🔑 使用Token: {'是' if AUTH_HEADERS else '否'}")
    print(f"👥 并发用户数: {CONCURRENT_USERS}")
    print(f"📊 每轮测试请求数: {TEST_REQUESTS}")
    print(f"📁 结果保存目录: {RESULTS_DIR}")

    # 让用户选择要测试的API
    selected_apis = select_apis_to_test()

    if not selected_apis:
        print("❌ 没有选择任何API进行测试")
        return

    print(f"\n✅ 将测试以下 {len(selected_apis)} 个API:")
    for api in selected_apis:
        category = TEST_ENDPOINTS[api].get('category', '其他')
        print(f"   - {api} ({category})")

    # 确认开始测试
    confirm = input(f"\n是否开始测试? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 测试已取消")
        return

    # 存储所有测试结果
    all_results = {}
    test_summary = {
        'test_time': datetime.now().isoformat(),
        'base_url': BASE_URL,
        'concurrent_users': CONCURRENT_USERS,
        'test_requests': TEST_REQUESTS,
        'selected_apis': selected_apis,
        'total_apis_tested': len(selected_apis)
    }

    # 对选定的端点进行测试
    for i, endpoint_name in enumerate(selected_apis, 1):
        config = TEST_ENDPOINTS[endpoint_name]
        print(f"\n{'='*80}")
        print(f"🎯 测试进度: {i}/{len(selected_apis)} - {endpoint_name}")
        print(f"📂 类别: {config.get('category', '其他')}")
        print(f"📍 URL: {config['url']}")
        print(f"📝 描述: {config['description']}")

        endpoint_results = {}

        # 对每个并发级别进行测试
        for concurrent_users in CONCURRENT_USERS:
            try:
                stats, results = run_concurrent_test(endpoint_name, config, concurrent_users, TEST_REQUESTS)
                endpoint_results[concurrent_users] = stats
                print_test_results(endpoint_name, concurrent_users, stats)

                # 测试间隔，让系统恢复
                time.sleep(1)

            except Exception as e:
                print(f"❌ {endpoint_name} ({concurrent_users}并发) 测试失败: {e}")

        all_results[endpoint_name] = endpoint_results

    # 生成汇总报告
    print_summary_report(all_results)

    # 保存测试结果
    save_test_results(all_results, test_summary)

def print_summary_report(all_results):
    """打印汇总报告"""
    print(f"\n{'='*60}")
    print("📈 性能测试汇总报告")
    print("="*60)

    for endpoint_name, endpoint_results in all_results.items():
        print(f"\n🎯 {endpoint_name}:")
        print("   并发数 | QPS   | 平均响应时间 | 缓存命中率 | 成功率")
        print("   -------|-------|-------------|-----------|-------")

        for concurrent_users, stats in endpoint_results.items():
            print(f"   {concurrent_users:6d} | {stats['qps']:5.1f} | {stats['avg_response_time']*1000:8.1f}ms | {stats['cache_hit_rate']:8.1f}% | {stats['success_rate']:5.1f}%")

if __name__ == "__main__":
    main()
