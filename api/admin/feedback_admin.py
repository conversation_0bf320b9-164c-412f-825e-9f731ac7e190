"""
反馈相关模型的Admin配置
包括FeedbackCategory、Feedback和FeedbackReplyTemplate的admin设置
"""

from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.utils import timezone
from django.utils.safestring import mark_safe

from ..models import FeedbackCategory, Feedback, FeedbackReplyTemplate


@admin.register(FeedbackCategory)
class FeedbackCategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)


@admin.register(FeedbackReplyTemplate)
class FeedbackReplyTemplateAdmin(admin.ModelAdmin):
    list_display = ('title', 'content_preview', 'is_active', 'updated_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('title', 'content')
    fieldsets = (
        (None, {
            'fields': ('title', 'content', 'is_active')
        }),
    )
    
    def content_preview(self, obj):
        if len(obj.content) > 100:
            return f"{obj.content[:100]}..."
        return obj.content
    content_preview.short_description = '内容预览'


class FeedbackResource(resources.ModelResource):
    class Meta:
        model = Feedback
        fields = ('id', 'category__name', 'content', 'wechat', 'phone', 'created_at', 'reply', 'replied_at', 'user__nickname')
        export_order = fields


@admin.register(Feedback)
class FeedbackAdmin(ImportExportModelAdmin):
    resource_class = FeedbackResource
    list_display = ('category', 'content_preview', 'user_display', 'created_at', 'has_reply', 'replied_at')
    list_filter = ('category', 'created_at', 'replied_at')
    search_fields = ('content', 'wechat', 'phone', 'reply', 'user__nickname', 'user__wx_phone_new')
    readonly_fields = ('created_at', 'replied_at', 'user_contact_info')
    raw_id_fields = ('user',)
    autocomplete_fields = ['user']
    fieldsets = (
        ('反馈信息', {
            'fields': ('category', 'content', 'user', 'user_contact_info', 'created_at')
        }),
        ('回复信息', {
            'fields': ('reply', 'replied_at')
        }),
    )
    change_form_template = 'admin/feedback_change_form.html'

    def has_reply(self, obj):
        return bool(obj.reply)
    has_reply.boolean = True
    has_reply.short_description = '已回复'
    
    def content_preview(self, obj):
        # 返回内容预览，限制在50个字符
        if len(obj.content) > 50:
            return f"{obj.content[:50]}..."
        return obj.content
    content_preview.short_description = '反馈内容'
    
    def user_display(self, obj):
        # 优先显示关联用户的昵称
        if obj.user and obj.user.nickname:
            return obj.user.nickname
        # 其次显示微信名，再次是电话
        elif obj.wechat:
            return obj.wechat
        elif obj.phone:
            return obj.phone
        return "未提供联系方式"
    user_display.short_description = '用户'
    
    def user_contact_info(self, obj):
        # 显示用户的所有联系方式，用于编辑页面
        contacts = []
        
        if obj.user:
            contacts.append(f"<strong>用户昵称：{obj.user.nickname}</strong>")
            if obj.user.wx_phone_new:
                contacts.append(f"注册手机：{obj.user.wx_phone_new}")
        
        if obj.wechat:
            contacts.append(f"微信：{obj.wechat}")
        if obj.phone:
            contacts.append(f"电话：{obj.phone}")
        
        if contacts:
            return mark_safe("<br>".join(contacts))
        return "用户未提供联系方式"
    user_contact_info.short_description = '联系方式'
    
    def get_reply_templates(self):
        """获取所有可用的回复模板"""
        return FeedbackReplyTemplate.objects.filter(is_active=True).order_by('title')
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """重写change_view，添加回复模板列表到上下文"""
        extra_context = extra_context or {}
        extra_context['reply_templates'] = self.get_reply_templates()
        return super().change_view(request, object_id, form_url, extra_context)
    
    def save_model(self, request, obj, form, change):
        # 如果添加了回复内容且之前没有回复，则设置回复时间
        if obj.reply and not obj.replied_at:
            obj.replied_at = timezone.now()
        # 如果清空了回复内容，也清空回复时间
        elif not obj.reply:
            obj.replied_at = None
        super().save_model(request, obj, form, change)

    def get_export_filename(self, request, queryset, file_format):
        date_str = timezone.now().strftime('%Y-%m-%d')
        return f"feedback_export_{date_str}.{file_format.get_extension()}" 