"""
算卦核心服务模块
整合随机数生成和卦象选择功能
"""
import traceback
from typing import Dict, Optional, Tuple
from datetime import datetime
import pytz
from api.utils.logging import safe_print
from api.services.divination_service import divination_data_service
from api.services.random_service import random_service

class DivinationCoreService:
    """算卦核心服务类"""
    
    @staticmethod
    def perform_divination(user_input: int, question: str = None) -> Dict:
        """
        执行算卦过程
        
        Args:
            user_input (int): 用户输入的数字 (0-999)
            question (str): 用户的问题（可选）
            
        Returns:
            Dict: 算卦结果
        """
        try:
            # 验证数据服务是否可用
            if not divination_data_service.is_data_loaded():
                return {
                    'success': False,
                    'error': '六十四卦数据未加载',
                    'timestamp': datetime.now(pytz.timezone('Asia/Shanghai')).isoformat()
                }
            
            # 生成问题哈希（如果有问题）
            question_hash = None
            if question:
                question_hash = random_service.get_question_hash(question)
                safe_print(f"问题哈希: {question_hash}")
            
            # 生成随机卦数
            gua_number = random_service.generate_enhanced_random(user_input, question_hash)
            
            # 获取卦象信息
            gua_info = divination_data_service.get_gua_by_id(gua_number)
            
            if not gua_info:
                return {
                    'success': False,
                    'error': f'无法获取卦象信息，卦数: {gua_number}',
                    'timestamp': datetime.now(pytz.timezone('Asia/Shanghai')).isoformat()
                }
            
            # 获取当前时间
            current_time = datetime.now(pytz.timezone('Asia/Shanghai'))
            
            # 构建算卦结果
            result = {
                'success': True,
                'divination_info': {
                    'user_input': user_input,
                    'question': question,
                    'question_hash': question_hash,
                    'gua_number': gua_number,
                    'divination_time': current_time.strftime("%Y-%m-%d %H:%M:%S"),
                    'timestamp': current_time.isoformat()
                },
                'gua_info': gua_info,
                'summary': {
                    'gua_name': gua_info['name'],
                    'gua_code': gua_info['code'],
                    'brief_explanation': gua_info['gua_ci'][:100] + '...' if len(gua_info['gua_ci']) > 100 else gua_info['gua_ci']
                }
            }
            
            safe_print(f"算卦完成 - 用户输入: {user_input}, 得卦: {gua_info['name']} (第{gua_number}卦)")
            
            return result
            
        except Exception as e:
            error_message = f"算卦过程出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            
            return {
                'success': False,
                'error': error_message,
                'timestamp': datetime.now(pytz.timezone('Asia/Shanghai')).isoformat()
            }
    
    @staticmethod
    def get_simple_divination(user_input: int) -> Dict:
        """
        获取简单的算卦结果（仅卦象基本信息）
        
        Args:
            user_input (int): 用户输入的数字 (0-999)
            
        Returns:
            Dict: 简化的算卦结果
        """
        try:
            # 验证数据服务是否可用
            if not divination_data_service.is_data_loaded():
                return {
                    'success': False,
                    'error': '六十四卦数据未加载'
                }
            
            # 生成随机卦数
            gua_number = random_service.generate_divination_number(user_input)
            
            # 获取卦象简要信息
            gua_summary = divination_data_service.get_gua_summary(gua_number)
            
            if not gua_summary:
                return {
                    'success': False,
                    'error': f'无法获取卦象信息，卦数: {gua_number}'
                }
            
            return {
                'success': True,
                'user_input': user_input,
                'gua_number': gua_number,
                'gua_info': gua_summary,
                'timestamp': datetime.now(pytz.timezone('Asia/Shanghai')).isoformat()
            }
            
        except Exception as e:
            error_message = f"简单算卦过程出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            
            return {
                'success': False,
                'error': error_message
            }
    
    @staticmethod
    def validate_user_input(user_input: any) -> Tuple[bool, int, str]:
        """
        验证用户输入
        
        Args:
            user_input: 用户输入的值
            
        Returns:
            Tuple[bool, int, str]: (是否有效, 处理后的值, 错误信息)
        """
        try:
            # 尝试转换为整数
            if isinstance(user_input, str):
                user_input = int(user_input)
            elif not isinstance(user_input, int):
                return False, 0, f"输入类型错误，期望整数，实际: {type(user_input)}"
            
            # 验证范围
            if not (0 <= user_input <= 999):
                return False, 0, f"输入超出范围，必须在0-999之间，实际: {user_input}"
            
            return True, user_input, ""
            
        except ValueError as e:
            return False, 0, f"输入格式错误: {str(e)}"
        except Exception as e:
            return False, 0, f"验证输入时出错: {str(e)}"
    
    @staticmethod
    def get_divination_statistics() -> Dict:
        """
        获取算卦系统统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            stats = {
                'gua_count': divination_data_service.get_gua_count(),
                'data_loaded': divination_data_service.is_data_loaded(),
                'random_distribution': random_service.validate_distribution(1000),  # 测试1000次
                'system_info': {
                    'current_time': datetime.now(pytz.timezone('Asia/Shanghai')).isoformat(),
                    'version': '1.0.0'
                }
            }
            
            return stats
            
        except Exception as e:
            error_message = f"获取统计信息时出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            
            return {
                'error': error_message
            }
    
    @staticmethod
    def get_gua_by_number(gua_number: int) -> Dict:
        """
        根据卦数获取卦象信息
        
        Args:
            gua_number (int): 卦数 (1-64)
            
        Returns:
            Dict: 卦象信息
        """
        try:
            if not (1 <= gua_number <= 64):
                return {
                    'success': False,
                    'error': f'卦数超出范围，必须在1-64之间，实际: {gua_number}'
                }
            
            gua_info = divination_data_service.get_gua_by_id(gua_number)
            
            if not gua_info:
                return {
                    'success': False,
                    'error': f'无法找到第{gua_number}卦的信息'
                }
            
            return {
                'success': True,
                'gua_info': gua_info,
                'timestamp': datetime.now(pytz.timezone('Asia/Shanghai')).isoformat()
            }
            
        except Exception as e:
            error_message = f"获取卦象信息时出错: {str(e)}"
            safe_print(error_message)
            safe_print(traceback.format_exc())
            
            return {
                'success': False,
                'error': error_message
            }


# 创建全局实例
divination_core_service = DivinationCoreService() 