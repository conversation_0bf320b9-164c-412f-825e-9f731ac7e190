#!/bin/bash
# 优化的Uvicorn启动脚本
# 适用于八字分析提示词管理系统

echo "🚀 启动八字分析系统 (Uvicorn)"
echo "================================"

# 激活虚拟环境
source ../venv/bin/activate

# 设置环境变量
export DJANGO_SETTINGS_MODULE=demo_api.settings

# 生产环境配置
if [ "$1" = "prod" ]; then
    echo "📦 生产环境模式"
    uvicorn demo_api.asgi:application \
        --host 0.0.0.0 \
        --port 8000 \
        --workers 4 \
        --loop uvloop \
        --http httptools \
        --access-log \
        --log-level info
        
# 开发环境配置  
else
    echo "🔧 开发环境模式"
    uvicorn demo_api.asgi:application \
        --host 0.0.0.0 \
        --port 8000 \
        --reload \
        --reload-dir api \
        --reload-dir demo_api \
        --loop uvloop \
        --log-level debug \
        --access-log
fi
