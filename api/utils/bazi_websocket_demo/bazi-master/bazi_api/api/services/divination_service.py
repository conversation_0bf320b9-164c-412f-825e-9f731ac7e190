"""
六十四卦数据服务模块
处理六十四卦的数据读取和查询功能
"""
import pandas as pd
import os
import json
import traceback
from typing import Dict, Optional, List
from api.utils.logging import safe_print

class DivinationDataService:
    """六十四卦数据服务类"""
    
    def __init__(self):
        self._gua_data = None
        self._excel_path = None
        self._load_gua_data()
    
    def _load_gua_data(self):
        """加载六十四卦数据"""
        try:
            # 查找Excel文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))
            excel_path = os.path.join(project_root, 'utils', '64gua_system', '六十四卦.xlsx')
            
            if not os.path.exists(excel_path):
                safe_print(f"六十四卦Excel文件不存在: {excel_path}")
                return
            
            self._excel_path = excel_path
            
            # 读取Excel数据
            df = pd.read_excel(excel_path)
            safe_print(f"成功加载六十四卦数据，共{len(df)}卦")
            
            # 转换为字典格式便于查询
            self._gua_data = {}
            for index, row in df.iterrows():
                gua_info = {
                    'id': int(row['id']),
                    'number': row['编号'],
                    'name': row['名称'],
                    'code': row['编码'],
                    'gua_ci': row['卦辞'],
                    'gua_xiang': row['卦象'],
                    'yao_ci': {
                        'shang_yao': row['上爻爻辞'],
                        'wu_yao': row['五爻爻辞'],
                        'si_yao': row['四爻爻辞'],
                        'san_yao': row['三爻爻辞'],
                        'er_yao': row['二爻爻辞'],
                        'chu_yao': row['初爻爻辞']
                    }
                }
                self._gua_data[gua_info['id']] = gua_info
            
            safe_print(f"六十四卦数据加载完成，数据结构: {list(self._gua_data.keys())[:5]}...")
            
        except Exception as e:
            safe_print(f"加载六十四卦数据时出错: {str(e)}")
            safe_print(traceback.format_exc())
            self._gua_data = {}
    
    def get_gua_by_id(self, gua_id: int) -> Optional[Dict]:
        """
        根据卦的ID获取卦象信息
        
        Args:
            gua_id (int): 卦的ID (1-64)
            
        Returns:
            Dict: 卦象信息，如果不存在则返回None
        """
        if not self._gua_data:
            safe_print("六十四卦数据未加载")
            return None
        
        if gua_id < 1 or gua_id > 64:
            safe_print(f"无效的卦ID: {gua_id}，必须在1-64之间")
            return None
        
        return self._gua_data.get(gua_id)
    
    def get_gua_by_name(self, name: str) -> Optional[Dict]:
        """
        根据卦名获取卦象信息
        
        Args:
            name (str): 卦名
            
        Returns:
            Dict: 卦象信息，如果不存在则返回None
        """
        if not self._gua_data:
            safe_print("六十四卦数据未加载")
            return None
        
        for gua_info in self._gua_data.values():
            if name in gua_info['name']:
                return gua_info
        
        return None
    
    def get_all_gua_names(self) -> List[str]:
        """
        获取所有卦名列表
        
        Returns:
            List[str]: 所有卦名列表
        """
        if not self._gua_data:
            return []
        
        return [gua_info['name'] for gua_info in self._gua_data.values()]
    
    def get_gua_count(self) -> int:
        """
        获取卦的总数
        
        Returns:
            int: 卦的总数
        """
        return len(self._gua_data) if self._gua_data else 0
    
    def is_data_loaded(self) -> bool:
        """
        检查数据是否已加载
        
        Returns:
            bool: 数据是否已加载
        """
        return self._gua_data is not None and len(self._gua_data) > 0
    
    def get_gua_summary(self, gua_id: int) -> Optional[Dict]:
        """
        获取卦象的简要信息（用于API返回）
        
        Args:
            gua_id (int): 卦的ID
            
        Returns:
            Dict: 卦象简要信息
        """
        gua_info = self.get_gua_by_id(gua_id)
        if not gua_info:
            return None
        
        return {
            'id': gua_info['id'],
            'number': gua_info['number'],
            'name': gua_info['name'],
            'code': gua_info['code'],
            'gua_ci': gua_info['gua_ci'][:100] + '...' if len(gua_info['gua_ci']) > 100 else gua_info['gua_ci'],
            'gua_xiang': gua_info['gua_xiang'][:200] + '...' if len(gua_info['gua_xiang']) > 200 else gua_info['gua_xiang']
        }
    
    def reload_data(self):
        """重新加载数据"""
        safe_print("重新加载六十四卦数据...")
        self._gua_data = None
        self._load_gua_data()


# 创建全局实例
divination_data_service = DivinationDataService() 