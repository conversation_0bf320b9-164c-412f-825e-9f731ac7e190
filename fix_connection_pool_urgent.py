#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复连接池耗尽问题
立即重置连接池并优化配置
"""

import os
import sys
import django
import gc
import time
from pathlib import Path

# 添加项目路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

from django.db import connections
from django.core.cache import cache
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

def force_close_all_connections():
    """强制关闭所有数据库连接"""
    print("🔥 强制关闭所有数据库连接...")
    
    closed_count = 0
    for alias in connections:
        try:
            conn = connections[alias]
            if conn.connection is not None:
                conn.close()
                closed_count += 1
                print(f"   ✅ 已关闭连接: {alias}")
        except Exception as e:
            print(f"   ⚠️ 关闭连接 {alias} 失败: {e}")
    
    # 强制关闭所有连接
    try:
        connections.close_all()
        print(f"   ✅ 已调用 connections.close_all()")
    except Exception as e:
        print(f"   ⚠️ connections.close_all() 失败: {e}")
    
    print(f"   📊 总共关闭了 {closed_count} 个连接")
    return closed_count

def reset_sqlalchemy_pools():
    """重置SQLAlchemy连接池"""
    print("🏊 重置SQLAlchemy连接池...")
    
    try:
        # 查找并重置所有QueuePool对象
        from sqlalchemy.pool import QueuePool
        pools_found = 0
        
        for obj in gc.get_objects():
            if isinstance(obj, QueuePool):
                try:
                    print(f"   🔍 找到连接池: {type(obj)}")
                    print(f"      - 当前大小: {obj.size()}")
                    print(f"      - 溢出数: {obj._overflow}")
                    
                    # 重置连接池
                    obj.dispose()
                    pools_found += 1
                    print(f"   ✅ 已重置连接池 #{pools_found}")
                except Exception as e:
                    print(f"   ⚠️ 重置连接池失败: {e}")
        
        if pools_found == 0:
            print("   ℹ️ 未找到活跃的连接池对象")
        else:
            print(f"   📊 总共重置了 {pools_found} 个连接池")
            
    except ImportError:
        print("   ⚠️ SQLAlchemy未安装，跳过连接池重置")
    except Exception as e:
        print(f"   ❌ 重置连接池时出错: {e}")

def clear_cache():
    """清理缓存"""
    print("🧹 清理缓存...")
    try:
        cache.clear()
        print("   ✅ 缓存已清理")
    except Exception as e:
        print(f"   ⚠️ 清理缓存失败: {e}")

def force_garbage_collection():
    """强制垃圾回收"""
    print("♻️ 执行垃圾回收...")
    
    before_count = len(gc.get_objects())
    collected = gc.collect()
    after_count = len(gc.get_objects())
    
    print(f"   📊 回收前对象数: {before_count}")
    print(f"   📊 回收后对象数: {after_count}")
    print(f"   📊 回收对象数: {collected}")
    print(f"   ✅ 垃圾回收完成")

def check_current_pool_status():
    """检查当前连接池状态"""
    print("📊 检查当前连接池状态...")
    
    try:
        from sqlalchemy.pool import QueuePool
        pools = [obj for obj in gc.get_objects() if isinstance(obj, QueuePool)]
        
        if pools:
            for i, pool in enumerate(pools):
                print(f"   连接池 #{i+1}:")
                print(f"      - 类型: {type(pool)}")
                print(f"      - 大小: {pool.size()}")
                print(f"      - 溢出: {pool._overflow}")
                print(f"      - 超时: {getattr(pool, '_timeout', 'N/A')}")
        else:
            print("   ℹ️ 未找到活跃的连接池")
            
    except Exception as e:
        print(f"   ❌ 检查连接池状态失败: {e}")

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result and result[0] == 1:
                print("   ✅ 数据库连接正常")
                return True
            else:
                print("   ❌ 数据库连接异常")
                return False
    except Exception as e:
        print(f"   ❌ 数据库连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚨 紧急修复连接池耗尽问题")
    print("=" * 50)
    
    # 1. 检查当前状态
    check_current_pool_status()
    print()
    
    # 2. 强制关闭所有连接
    force_close_all_connections()
    print()
    
    # 3. 重置SQLAlchemy连接池
    reset_sqlalchemy_pools()
    print()
    
    # 4. 清理缓存
    clear_cache()
    print()
    
    # 5. 强制垃圾回收
    force_garbage_collection()
    print()
    
    # 6. 等待一下让系统稳定
    print("⏳ 等待系统稳定...")
    time.sleep(3)
    print()
    
    # 7. 测试数据库连接
    connection_ok = test_database_connection()
    print()
    
    # 8. 再次检查连接池状态
    print("🔍 修复后连接池状态:")
    check_current_pool_status()
    print()
    
    # 9. 显示配置信息
    print("⚙️ 当前连接池配置:")
    pool_options = settings.DATABASES['default'].get('POOL_OPTIONS', {})
    for key, value in pool_options.items():
        print(f"   {key}: {value}")
    print()
    
    if connection_ok:
        print("✅ 连接池修复成功！")
        print("💡 建议重启Django服务以确保配置生效")
    else:
        print("❌ 连接池修复失败，需要进一步检查")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
