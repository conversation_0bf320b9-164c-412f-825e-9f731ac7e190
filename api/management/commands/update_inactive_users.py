from django.core.management.base import BaseCommand
from django.utils import timezone
from api.models import UserInfo

class Command(BaseCommand):
    help = 'Update health scores for inactive users'

    def handle(self, *args, **options):
        today = timezone.now().date()
        inactive_users = UserInfo.objects.filter(last_login_date__lt=today)

        for user in inactive_users:
            days_inactive = (today - user.last_login_date).days
            user.health_score -= min(days_inactive, 1)
            user.consecutive_login_days = 0
            user.health_score = max(0, user.health_score)
            user.save()

        self.stdout.write(self.style.SUCCESS('Successfully updated health scores for inactive users'))