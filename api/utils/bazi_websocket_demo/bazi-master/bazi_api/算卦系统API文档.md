# 算卦系统API文档

## 概述

本文档详细介绍了算卦系统的API接口和使用方法。算卦系统基于传统易经六十四卦理论，结合现代技术实现了智能化的卦象解读服务。

## 系统架构

### 核心组件

1. **六十四卦数据服务** (`divination_service.py`)
   - 负责读取和管理六十四卦的基础数据
   - 提供卦象查询和检索功能

2. **随机算法服务** (`random_service.py`)
   - 基于用户输入生成均等分布的随机数
   - 确保0-999的输入对随机数影响均等

3. **算卦核心服务** (`divination_core_service.py`)
   - 整合随机数生成和卦象选择
   - 提供完整的算卦流程

4. **大模型解读服务** (`api.py`)
   - 结合卦象信息和用户问题
   - 调用AI大模型生成专业解读

5. **WebSocket实时服务** (`divination_consumer.py`)
   - 支持实时流式卦象解读
   - 提供交互式算卦体验

## API接口

### 1. 基础算卦接口

#### POST /api/divination
根据用户输入的数字进行算卦

**请求参数：**
```json
{
  "user_number": 123,           // 用户输入的数字 (0-999)
  "question": "我的事业发展如何？"  // 用户问题（可选）
}
```

**响应示例：**
```json
{
  "success": true,
  "divination_info": {
    "user_input": 123,
    "question": "我的事业发展如何？",
    "question_hash": "a1b2c3d4e5f6",
    "gua_number": 32,
    "divination_time": "2024-04-24 15:30:00",
    "timestamp": "2024-04-24T15:30:00+08:00"
  },
  "gua_info": {
    "id": 32,
    "number": "第32卦",
    "name": "雷风恒",
    "code": "001110",
    "gua_ci": "恒：亨，无咎，利贞，利有攸往。",
    "gua_xiang": "雷风恒卦象征持久恒定...",
    "yao_ci": {
      "shang_yao": "上六爻辞...",
      "wu_yao": "九五爻辞...",
      "si_yao": "九四爻辞...",
      "san_yao": "九三爻辞...",
      "er_yao": "九二爻辞...",
      "chu_yao": "初六爻辞..."
    }
  },
  "summary": {
    "gua_name": "雷风恒",
    "gua_code": "001110",
    "brief_explanation": "恒卦象征持久恒定，表示事物的稳定发展..."
  }
}
```

#### GET /api/divination/simple/{user_number}
简单算卦，只返回基本卦象信息

**路径参数：**
- `user_number`: 用户输入的数字 (0-999)

**响应示例：**
```json
{
  "success": true,
  "user_input": 123,
  "gua_number": 32,
  "gua_info": {
    "id": 32,
    "number": "第32卦",
    "name": "雷风恒",
    "code": "001110",
    "gua_ci": "恒：亨，无咎，利贞，利有攸往...",
    "gua_xiang": "雷风恒卦象征持久恒定..."
  },
  "timestamp": "2024-04-24T15:30:00+08:00"
}
```

### 2. 卦象解读接口

#### POST /api/divination/interpret
根据卦象信息和用户问题生成详细解读

**请求参数：**
```json
{
  "user_number": 123,           // 用户输入的数字 (0-999)
  "question": "我的事业发展如何？",  // 用户问题（必填）
  "gua_number": 32              // 指定卦数（可选）
}
```

**响应示例：**
```json
{
  "success": true,
  "divination_info": {
    "user_input": 123,
    "question": "我的事业发展如何？",
    "gua_number": 32,
    "interpretation_type": "specific_question",
    "timestamp": "2024-04-24T15:30:00+08:00"
  },
  "gua_info": {
    "id": 32,
    "name": "雷风恒",
    "code": "001110",
    "gua_ci": "恒：亨，无咎，利贞，利有攸往。",
    "gua_xiang": "雷风恒卦象征持久恒定..."
  },
  "interpretation": "【专项咨询解读】\n\n根据您抽到的雷风恒卦，针对您的事业发展问题...",
  "summary": {
    "gua_name": "雷风恒",
    "gua_code": "001110",
    "brief_interpretation": "恒卦象征持久恒定，表示事业发展需要坚持不懈..."
  }
}
```

#### GET /api/divination/interpret/{gua_number}
根据指定卦数获取解读

**路径参数：**
- `gua_number`: 卦数 (1-64)

**查询参数：**
- `question`: 用户问题（可选，默认为通用解读）

### 3. 卦象信息接口

#### GET /api/divination/gua/{gua_number}
获取指定卦的详细信息

**路径参数：**
- `gua_number`: 卦数 (1-64)

**响应示例：**
```json
{
  "success": true,
  "gua_info": {
    "id": 1,
    "number": "第1卦",
    "name": "乾为天",
    "code": "111111",
    "gua_ci": "乾：元，亨，利，贞。",
    "gua_xiang": "乾为天卦象征天道...",
    "yao_ci": {
      "shang_yao": "上九，亢龙有悔。",
      "wu_yao": "九五，飞龙在天，利见大人。",
      "si_yao": "九四，或跃在渊，无咎。",
      "san_yao": "九三，君子终日乾乾，夕惕若，厉，无咎。",
      "er_yao": "九二，见龙在田，利见大人。",
      "chu_yao": "初九，潜龙勿用。"
    }
  },
  "timestamp": "2024-04-24T15:30:00+08:00"
}
```

### 4. 系统统计接口

#### GET /api/divination/stats
获取算卦系统统计信息

**响应示例：**
```json
{
  "gua_count": 64,
  "data_loaded": true,
  "random_distribution": {
    "total_iterations": 1000,
    "expected_per_gua": 15.625,
    "variance": 12.34,
    "min_count": 12,
    "max_count": 19,
    "uniformity_ratio": 0.632
  },
  "system_info": {
    "current_time": "2024-04-24T15:30:00+08:00",
    "version": "1.0.0"
  }
}
```

### 5. WebSocket流式接口

#### WebSocket连接
连接地址：`ws://服务器地址/ws/divination/`

**消息格式：**

1. **算卦请求**
```json
{
  "divination_request": {
    "user_number": 123,
    "question": "我的事业发展如何？"
  }
}
```

2. **卦象解读请求**
```json
{
  "interpretation_request": {
    "gua_number": 32,
    "question": "请为我解读这一卦",
    "interpretation_type": "general_interpretation"
  }
}
```

3. **普通聊天**
```json
{
  "message": "请帮我解释一下乾卦的含义"
}
```

**响应消息类型：**

- `divination_result`: 算卦结果
- `gua_info`: 卦象基本信息
- `divination_interpretation`: 流式解读内容
- `chat_response`: 聊天回复
- `error`: 错误信息

## 使用示例

### Python示例

```python
import requests
import json

# 基础算卦
def divination_example():
    url = "http://localhost:8000/api/divination"
    data = {
        "user_number": 123,
        "question": "我的事业发展如何？"
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result['success']:
        print(f"得卦: {result['gua_info']['name']}")
        print(f"卦象: {result['gua_info']['code']}")
        print(f"卦辞: {result['gua_info']['gua_ci']}")
    else:
        print(f"算卦失败: {result['error']}")

# 卦象解读
def interpretation_example():
    url = "http://localhost:8000/api/divination/interpret"
    data = {
        "user_number": 456,
        "question": "我应该如何处理当前的困难？"
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result['success']:
        print(f"卦象: {result['gua_info']['name']}")
        print(f"解读: {result['interpretation']}")
    else:
        print(f"解读失败: {result['error']}")

# 获取卦象信息
def get_gua_info_example():
    gua_number = 1
    url = f"http://localhost:8000/api/divination/gua/{gua_number}"
    
    response = requests.get(url)
    result = response.json()
    
    if result['success']:
        gua_info = result['gua_info']
        print(f"第{gua_info['id']}卦: {gua_info['name']}")
        print(f"卦象: {gua_info['code']}")
        print(f"卦辞: {gua_info['gua_ci']}")
        print("\n爻辞:")
        for yao_name, yao_ci in gua_info['yao_ci'].items():
            print(f"  {yao_name}: {yao_ci}")
    else:
        print(f"获取失败: {result['error']}")
```

### JavaScript/WebSocket示例

```javascript
// WebSocket连接
const ws = new WebSocket('ws://localhost:8000/ws/divination/');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
    
    // 发送算卦请求
    ws.send(JSON.stringify({
        divination_request: {
            user_number: 123,
            question: "我的事业发展如何？"
        }
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'divination_result':
            console.log('算卦结果:', data.data);
            break;
        case 'divination_interpretation':
            console.log('解读内容:', data.content);
            if (data.done) {
                console.log('解读完成');
            }
            break;
        case 'error':
            console.error('错误:', data.error);
            break;
    }
};

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};

ws.onclose = function(event) {
    console.log('WebSocket连接已关闭');
};
```

## 错误处理

### 错误码说明

- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 常见错误

1. **输入验证错误**
```json
{
  "success": false,
  "error": "输入超出范围，必须在0-999之间，实际: 1000",
  "timestamp": "2024-04-24T15:30:00+08:00"
}
```

2. **卦象不存在**
```json
{
  "success": false,
  "error": "卦数超出范围，必须在1-64之间，实际: 65",
  "timestamp": "2024-04-24T15:30:00+08:00"
}
```

3. **数据未加载**
```json
{
  "success": false,
  "error": "六十四卦数据未加载",
  "timestamp": "2024-04-24T15:30:00+08:00"
}
```

## 系统特性

### 1. 随机算法特性

- **均等分布**: 确保0-999的每个输入对最终结果的影响是均等的
- **时间因子**: 结合时间戳增加随机性
- **哈希算法**: 使用SHA256确保分布均匀
- **可重现性**: 相同输入在相同时间会产生相同结果

### 2. 数据结构

- **六十四卦数据**: 包含卦名、卦象、卦辞、爻辞等完整信息
- **模块化设计**: 各组件独立，便于维护和扩展
- **配置化提示词**: 支持动态修改解读模板

### 3. 性能优化

- **数据缓存**: 六十四卦数据一次加载，内存缓存
- **异步处理**: WebSocket支持异步流式处理
- **错误处理**: 完善的异常处理机制

## 部署要求

### 依赖包

```txt
django>=4.2.0
django-ninja>=1.0.0
channels>=4.0.0
daphne>=4.0.0
pandas>=1.5.0
openpyxl>=3.1.0
openai>=1.13.0
pytz
```

### 配置文件

1. **Django设置**
```python
# settings.py
INSTALLED_APPS = [
    # ... 其他应用
    'channels',
    'api',
]

ASGI_APPLICATION = 'your_project.asgi.application'

# 大模型API配置
LLM_API = {
    'ARK_API_KEY': 'your_api_key_here',
    'ARK_BASE_URL': 'https://ark.cn-beijing.volces.com/api/v3',
    'ARK_MODEL_ID': 'your_model_id',
}
```

2. **ASGI配置**
```python
# asgi.py
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from api.routing import websocket_urlpatterns

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(websocket_urlpatterns)
    ),
})
```

### 文件结构

```
project/
├── api/
│   ├── services/
│   │   ├── divination_service.py      # 六十四卦数据服务
│   │   ├── random_service.py          # 随机算法服务
│   │   ├── divination_core_service.py # 算卦核心服务
│   │   └── llm_service.py             # 大模型服务
│   ├── websockets/
│   │   └── divination_consumer.py     # WebSocket消费者
│   ├── utils/
│   │   ├── prompts.json              # 提示词配置
│   │   └── constants.py              # 常量配置
│   ├── api.py                        # API接口
│   └── routing.py                    # WebSocket路由
├── utils/
│   └── 64gua_system/
│       └── 六十四卦.xlsx             # 六十四卦数据
└── test_divination_system.py         # 测试脚本
```

## 测试

运行测试脚本：
```bash
python test_divination_system.py
```

测试内容包括：
- 六十四卦数据服务测试
- 随机算法服务测试
- 算卦核心服务测试
- 提示词配置测试
- API端点模拟测试

## 总结

算卦系统提供了完整的易经六十四卦解读服务，具有以下特点：

1. **传统与现代结合**: 基于传统易经理论，结合现代AI技术
2. **模块化设计**: 各组件独立，便于维护和扩展
3. **多种接口**: 支持RESTful API和WebSocket实时接口
4. **智能解读**: 结合用户问题提供个性化解读
5. **高可用性**: 完善的错误处理和性能优化

系统可以广泛应用于：
- 在线算卦平台
- 传统文化教育
- 心理咨询辅助
- 决策参考工具

通过本文档，开发者可以快速了解和使用算卦系统的各项功能。 