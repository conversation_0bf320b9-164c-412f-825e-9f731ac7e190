# 八字分析提示词管理系统

## 📋 概述

这是一个专业的八字分析提示词管理系统，支持多种分析类型的提示词管理，采用最佳实践设计，便于维护和迭代。

## 🏗️ 架构设计

### 目录结构
```
api/prompts/
├── __init__.py              # 模块初始化
├── categories.py            # 分析类型定义
├── manager.py              # 提示词管理器
├── templates/              # 提示词模板目录
│   ├── health_v1.yaml     # 健康养生提示词
│   ├── wealth_v1.yaml     # 财运分析提示词
│   ├── career_v1.yaml     # 事业发展提示词
│   ├── marriage_v1.yaml   # 婚恋情感提示词
│   ├── education_v1.yaml  # 学业运势提示词
│   └── fortune_v1.yaml    # 祸福吉凶提示词
└── README.md               # 本文档
```

### 核心组件

1. **PromptCategory**: 分析类型枚举，定义所有支持的分析类别
2. **PromptManager**: 提示词管理器，负责加载、缓存和管理提示词
3. **BaziAnalysisRouter**: WebSocket路由器，统一处理所有分析请求

## 🎯 支持的分析类型

### 基础分析类
- `health` - 健康养生：基于八字五行分析体质特点
- `wealth` - 财运分析：分析财星配置和财运走势
- `career` - 事业发展：分析职业天赋和事业发展
- `education` - 学业运势：分析学习能力和学业运势
- `marriage` - 婚恋情感：分析感情特质和婚恋运势
- `fortune` - 祸福吉凶：分析命运走势和吉凶趋势

### 专业分析类
- `wuxing` - 五行调理：深入分析五行强弱配置
- `shishen` - 十神分析：详细解读十神组合特征
- `shensha` - 神煞解读：解读神煞组合含义

### 时运分析类
- `yearly` - 流年运势：分析流年运势变化
- `monthly` - 流月运势：分析流月运势起伏
- `daily` - 每日运势：分析每日运势变化

## 🔌 前端接口

### WebSocket连接
```javascript
// 连接八字分析WebSocket
const ws = new WebSocket('ws://localhost:8000/api/ws/bazi_analysis/');

// 发送分析请求
ws.send(JSON.stringify({
    type: 'health',  // 分析类型
    data: {
        eight_char: '己卯 壬申 丙午 壬辰',
        gender: '男',
        shishen: { /* 十神数据 */ },
        shensha: { /* 神煞数据 */ }
    }
}));

// 接收流式响应
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.content) {
        // 显示AI分析内容
        console.log(data.content);
    }
    if (data.done) {
        // 分析完成
        console.log('分析完成');
    }
};
```

### HTTP API

#### 获取分析类型
```http
GET /api/bazi_analysis_api/categories
```

响应示例：
```json
{
    "success": true,
    "data": {
        "categories": [
            {
                "group": "basic",
                "group_name": "基础分析",
                "categories": [
                    {
                        "type": "health",
                        "name": "健康养生",
                        "description": "基于八字五行分析体质特点..."
                    }
                ]
            }
        ],
        "websocket_url": "/api/ws/bazi_analysis/",
        "total_count": 15
    }
}
```

#### 获取提示词信息
```http
GET /api/bazi_analysis_api/prompts/available
```

#### 重新加载提示词
```http
POST /api/bazi_analysis_api/prompts/reload
```

## 🛠️ 管理命令

### 列出所有提示词
```bash
python manage.py manage_prompts list
```

### 创建新提示词
```bash
python manage.py manage_prompts create --category health --version v2
```

### 验证提示词文件
```bash
python manage.py manage_prompts validate
```

### 重新加载缓存
```bash
python manage.py manage_prompts reload
```

## 📝 提示词文件格式

提示词文件采用YAML格式，支持版本管理：

```yaml
# 健康养生分析提示词 v1.0
metadata:
  category: "health"
  version: "v1"
  description: "健康养生分析专用提示词"
  author: "八字分析团队"
  created_date: "2025-07-20"
  last_updated: "2025-07-20"

prompt: |
  【专业领域】健康养生分析
  
  【分析重点】
  1. 五行体质分析
  2. 脏腑功能评估
  3. 养生建议
  
  【回答格式】
  - 先分析体质特点
  - 再提供养生建议
  - 最后给出注意事项
```

## 🔧 开发指南

### 添加新的分析类型

1. 在 `categories.py` 中添加新的枚举值
2. 在 `templates/` 目录创建对应的提示词文件
3. 更新 `bazi_analysis_api.py` 中的描述信息
4. 测试新分析类型的功能

### 更新提示词

1. 创建新版本的提示词文件（如 `health_v2.yaml`）
2. 使用管理命令验证文件格式
3. 重新加载缓存使新版本生效

### 缓存机制

- 提示词会被缓存1小时，提高响应速度
- 支持手动重新加载缓存
- 缓存键格式：`prompt_{category}_{version}`

## 🚀 部署注意事项

1. 确保 `api/prompts/templates/` 目录有正确的读写权限
2. 生产环境建议使用Redis作为缓存后端
3. 定期备份提示词文件
4. 监控WebSocket连接数和响应时间

## 📊 监控和日志

- WebSocket连接日志：`[BAZI_ROUTER]` 前缀
- 提示词加载日志：`PromptManager` 相关
- API调用日志：标准Django日志

## 🔒 安全考虑

- 提示词文件仅存储在后端，前端无法访问
- WebSocket连接需要通过中间件认证
- 支持限流机制防止滥用
- 敏感信息不会暴露给前端
