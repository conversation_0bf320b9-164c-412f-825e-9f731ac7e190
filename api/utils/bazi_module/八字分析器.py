# -*- coding: utf-8 -*-
"""
八字分析器 - 完整的八字信息输出工具
根据输入的日期时间，输出完整的八字分析信息
"""

from lunar_python import Solar, Lunar
import argparse
import sys
from datetime import datetime

# 导入神煞计算器
try:
    from shensha_calculator import query_shensha
    SHENSHA_AVAILABLE = True
except ImportError:
    SHENSHA_AVAILABLE = False
    print("警告: 神煞计算器模块未找到，神煞分析功能将被禁用")


def print_separator(title="", width=80):
    """打印分隔线"""
    if title:
        print(f"\n{'='*10} {title} {'='*(width-len(title)-22)}")
    else:
        print("="*width)


def analyze_shensha(eight_char, lunar, gender):
    """分析神煞信息"""
    if not SHENSHA_AVAILABLE:
        return None

    try:
        # 构造八字数组 [年干, 年支, 月干, 月支, 日干, 日支, 时干, 时支]
        bazi = [
            eight_char.getYearGan(), eight_char.getYear<PERSON>hi(),
            eight_char.getMonthGan(), eight_char.getMonthZhi(),
            eight_char.getDayGan(), eight_char.getDayZhi(),
            eight_char.getTimeGan(), eight_char.getTimeZhi()
        ]

        # 获取年柱纳音
        nian_nayin = eight_char.getYearNaYin()

        # 性别转换 (1男0女 -> True男False女)
        is_man = gender == 1

        # 构造四柱信息
        pillars = [
            ("年柱", eight_char.getYear(), 1),
            ("月柱", eight_char.getMonth(), 2),
            ("日柱", eight_char.getDay(), 3),
            ("时柱", eight_char.getTime(), 4)
        ]

        shensha_results = {}
        all_shenshas = []

        # 分析每一柱的神煞
        for name, ganzhi, witch in pillars:
            try:
                shenshas = query_shensha(ganzhi, bazi, is_man, witch, nian_nayin)
                shensha_results[name] = {
                    "干支": ganzhi,
                    "神煞": shenshas,
                    "数量": len(shenshas)
                }
                all_shenshas.extend(shenshas)
            except Exception as e:
                shensha_results[name] = {
                    "干支": ganzhi,
                    "错误": str(e)
                }

        # 统计信息
        unique_shenshas = list(set(all_shenshas))

        # 神煞分类
        categories = {
            "贵人类": ["天乙", "太极", "文昌", "国印", "金舆", "福星", "天厨", "德秀"],
            "德合类": ["天德", "月德", "天德合", "月德合", "天赦"],
            "星煞类": ["驿马", "华盖", "将星", "禄神", "红鸾", "天喜", "五鬼", "天医", "学堂", "词馆"],
            "刃煞类": ["羊刃", "飞刃", "血刃", "金神"],
            "凶煞类": ["空亡", "劫煞", "灾煞", "亡神", "孤辰", "寡宿", "天罗", "地网", "十恶大败",
                      "桃花", "孤鸾", "阴差阳错", "四废", "丧门", "吊客", "披麻", "童子", "流霞",
                      "红艳", "魁罡", "八专", "九丑", "十灵", "元辰"]
        }

        category_stats = {}
        for category, shensha_list in categories.items():
            category_shenshas = [s for s in unique_shenshas if s in shensha_list]
            if category_shenshas:
                category_stats[category] = category_shenshas

        return {
            "pillars": shensha_results,
            "total_count": len(all_shenshas),
            "unique_count": len(unique_shenshas),
            "unique_shenshas": sorted(unique_shenshas),
            "categories": category_stats
        }

    except Exception as e:
        return {"error": f"神煞分析出错: {e}"}


def analyze_eight_char(year, month, day, hour=0, minute=0, second=0, gender=1, sect=2):
    """
    分析八字信息
    
    Args:
        year: 年份
        month: 月份
        day: 日期
        hour: 小时 (默认0)
        minute: 分钟 (默认0) 
        second: 秒钟 (默认0)
        gender: 性别 (1男0女，默认1)
        sect: 流派 (1或2，默认2)
    """
    try:
        # 创建阳历对象
        solar = Solar.fromYmdHms(year, month, day, hour, minute, second)
        lunar = solar.getLunar()
        eight_char = lunar.getEightChar()
        eight_char.setSect(sect)
        
        print_separator("基本信息")
        print(f"阳历日期: {solar.toFullString()}")
        print(f"农历日期: {lunar.toFullString()}")
        print(f"完整八字: {eight_char}")
        print(f"性别: {'男' if gender == 1 else '女'}")
        print(f"流派: {sect}")
        
        print_separator("四柱详细信息")
        
        # 年柱信息
        print(f"年柱: {eight_char.getYear()}")
        print(f"  年干: {eight_char.getYearGan()}")
        print(f"  年支: {eight_char.getYearZhi()}")
        print(f"  年柱五行: {eight_char.getYearWuXing()}")
        print(f"  年柱纳音: {eight_char.getYearNaYin()}")
        print(f"  年干十神: {eight_char.getYearShiShenGan()}")
        print(f"  年支十神: {', '.join(eight_char.getYearShiShenZhi())}")
        print(f"  年支藏干: {', '.join(eight_char.getYearHideGan())}")
        print(f"  年柱地势: {eight_char.getYearDiShi()}")
        print(f"  年柱旬: {eight_char.getYearXun()}")
        print(f"  年柱旬空: {eight_char.getYearXunKong()}")
        
        # 月柱信息
        print(f"\n月柱: {eight_char.getMonth()}")
        print(f"  月干: {eight_char.getMonthGan()}")
        print(f"  月支: {eight_char.getMonthZhi()}")
        print(f"  月柱五行: {eight_char.getMonthWuXing()}")
        print(f"  月柱纳音: {eight_char.getMonthNaYin()}")
        print(f"  月干十神: {eight_char.getMonthShiShenGan()}")
        print(f"  月支十神: {', '.join(eight_char.getMonthShiShenZhi())}")
        print(f"  月支藏干: {', '.join(eight_char.getMonthHideGan())}")
        print(f"  月柱地势: {eight_char.getMonthDiShi()}")
        print(f"  月柱旬: {eight_char.getMonthXun()}")
        print(f"  月柱旬空: {eight_char.getMonthXunKong()}")
        
        # 日柱信息
        print(f"\n日柱: {eight_char.getDay()}")
        print(f"  日干: {eight_char.getDayGan()}")
        print(f"  日支: {eight_char.getDayZhi()}")
        print(f"  日柱五行: {eight_char.getDayWuXing()}")
        print(f"  日柱纳音: {eight_char.getDayNaYin()}")
        print(f"  日干十神: {eight_char.getDayShiShenGan()}")
        print(f"  日支十神: {', '.join(eight_char.getDayShiShenZhi())}")
        print(f"  日支藏干: {', '.join(eight_char.getDayHideGan())}")
        print(f"  日柱地势: {eight_char.getDayDiShi()}")
        print(f"  日柱旬: {eight_char.getDayXun()}")
        print(f"  日柱旬空: {eight_char.getDayXunKong()}")
        
        # 时柱信息
        print(f"\n时柱: {eight_char.getTime()}")
        print(f"  时干: {eight_char.getTimeGan()}")
        print(f"  时支: {eight_char.getTimeZhi()}")
        print(f"  时柱五行: {eight_char.getTimeWuXing()}")
        print(f"  时柱纳音: {eight_char.getTimeNaYin()}")
        print(f"  时干十神: {eight_char.getTimeShiShenGan()}")
        print(f"  时支十神: {', '.join(eight_char.getTimeShiShenZhi())}")
        print(f"  时支藏干: {', '.join(eight_char.getTimeHideGan())}")
        print(f"  时柱地势: {eight_char.getTimeDiShi()}")
        print(f"  时柱旬: {eight_char.getTimeXun()}")
        print(f"  时柱旬空: {eight_char.getTimeXunKong()}")
        
        print_separator("十神分析")
        print(f"年柱十神: {eight_char.getYear()} - {eight_char.getYearShiShenGan()}")
        print(f"月柱十神: {eight_char.getMonth()} - {eight_char.getMonthShiShenGan()}")
        print(f"日柱十神: {eight_char.getDay()} - {eight_char.getDayShiShenGan()}")
        print(f"时柱十神: {eight_char.getTime()} - {eight_char.getTimeShiShenGan()}")

        print(f"\n地支藏干十神:")
        print(f"年支藏干: {', '.join(eight_char.getYearHideGan())} -> {', '.join(eight_char.getYearShiShenZhi())}")
        print(f"月支藏干: {', '.join(eight_char.getMonthHideGan())} -> {', '.join(eight_char.getMonthShiShenZhi())}")
        print(f"日支藏干: {', '.join(eight_char.getDayHideGan())} -> {', '.join(eight_char.getDayShiShenZhi())}")
        print(f"时支藏干: {', '.join(eight_char.getTimeHideGan())} -> {', '.join(eight_char.getTimeShiShenZhi())}")

        print_separator("长生十二神")
        print(f"年柱地势: {eight_char.getYear()} - {eight_char.getYearDiShi()}")
        print(f"月柱地势: {eight_char.getMonth()} - {eight_char.getMonthDiShi()}")
        print(f"日柱地势: {eight_char.getDay()} - {eight_char.getDayDiShi()}")
        print(f"时柱地势: {eight_char.getTime()} - {eight_char.getTimeDiShi()}")

        print_separator("特殊宫位")
        print(f"胎元: {eight_char.getTaiYuan()}")
        print(f"胎元纳音: {eight_char.getTaiYuanNaYin()}")
        print(f"胎息: {eight_char.getTaiXi()}")
        print(f"胎息纳音: {eight_char.getTaiXiNaYin()}")
        print(f"命宫: {eight_char.getMingGong()}")
        print(f"命宫纳音: {eight_char.getMingGongNaYin()}")
        print(f"身宫: {eight_char.getShenGong()}")
        print(f"身宫纳音: {eight_char.getShenGongNaYin()}")
        
        print_separator("九星信息")
        
        # 年九星
        year_nine_star = lunar.getYearNineStar(sect)
        print(f"年九星: {year_nine_star.toString()}")
        print(f"  数字: {year_nine_star.getNumber()}")
        print(f"  颜色: {year_nine_star.getColor()}")
        print(f"  五行: {year_nine_star.getWuXing()}")
        print(f"  方位: {year_nine_star.getPosition()}({year_nine_star.getPositionDesc()})")
        print(f"  北斗: {year_nine_star.getNameInBeiDou()}")
        print(f"  玄空: {year_nine_star.getNameInXuanKong()}({year_nine_star.getLuckInXuanKong()})")
        print(f"  奇门: {year_nine_star.getNameInQiMen()}({year_nine_star.getLuckInQiMen()})")
        print(f"  太乙: {year_nine_star.getNameInTaiYi()}({year_nine_star.getTypeInTaiYi()})")
        
        # 月九星
        month_nine_star = lunar.getMonthNineStar(sect)
        print(f"\n月九星: {month_nine_star.toString()}")
        print(f"  数字: {month_nine_star.getNumber()}")
        print(f"  颜色: {month_nine_star.getColor()}")
        print(f"  五行: {month_nine_star.getWuXing()}")
        print(f"  方位: {month_nine_star.getPosition()}({month_nine_star.getPositionDesc()})")
        print(f"  北斗: {month_nine_star.getNameInBeiDou()}")
        print(f"  玄空: {month_nine_star.getNameInXuanKong()}({month_nine_star.getLuckInXuanKong()})")
        print(f"  奇门: {month_nine_star.getNameInQiMen()}({month_nine_star.getLuckInQiMen()})")
        print(f"  太乙: {month_nine_star.getNameInTaiYi()}({month_nine_star.getTypeInTaiYi()})")
        
        # 日九星
        day_nine_star = lunar.getDayNineStar()
        print(f"\n日九星: {day_nine_star.toString()}")
        print(f"  数字: {day_nine_star.getNumber()}")
        print(f"  颜色: {day_nine_star.getColor()}")
        print(f"  五行: {day_nine_star.getWuXing()}")
        print(f"  方位: {day_nine_star.getPosition()}({day_nine_star.getPositionDesc()})")
        print(f"  北斗: {day_nine_star.getNameInBeiDou()}")
        print(f"  玄空: {day_nine_star.getNameInXuanKong()}({day_nine_star.getLuckInXuanKong()})")
        print(f"  奇门: {day_nine_star.getNameInQiMen()}({day_nine_star.getLuckInQiMen()})")
        print(f"  太乙: {day_nine_star.getNameInTaiYi()}({day_nine_star.getTypeInTaiYi()})")
        
        # 时九星
        time_nine_star = lunar.getTimeNineStar()
        print(f"\n时九星: {time_nine_star.toString()}")
        print(f"  数字: {time_nine_star.getNumber()}")
        print(f"  颜色: {time_nine_star.getColor()}")
        print(f"  五行: {time_nine_star.getWuXing()}")
        print(f"  方位: {time_nine_star.getPosition()}({time_nine_star.getPositionDesc()})")
        print(f"  北斗: {time_nine_star.getNameInBeiDou()}")
        print(f"  玄空: {time_nine_star.getNameInXuanKong()}({time_nine_star.getLuckInXuanKong()})")
        print(f"  奇门: {time_nine_star.getNameInQiMen()}({time_nine_star.getLuckInQiMen()})")
        print(f"  太乙: {time_nine_star.getNameInTaiYi()}({time_nine_star.getTypeInTaiYi()})")
        
        print_separator("运势分析")

        # 获取运势
        yun = eight_char.getYun(gender, sect)
        print(f"起运时间: {yun.getStartYear()}年{yun.getStartMonth()}个月{yun.getStartDay()}天后")
        print(f"起运阳历: {yun.getStartSolar().toYmd()}")

        # 大运信息
        da_yun_list = yun.getDaYun(8)  # 获取8步大运
        print(f"\n大运信息:")
        for i, da_yun in enumerate(da_yun_list):
            if i == 0 and da_yun.getIndex() < 1:
                print(f"  大运[{i}]: {da_yun.getStartYear()}年-{da_yun.getEndYear()}年 "
                      f"{da_yun.getStartAge()}岁-{da_yun.getEndAge()}岁 (起运前)")
            else:
                print(f"  大运[{i}]: {da_yun.getStartYear()}年-{da_yun.getEndYear()}年 "
                      f"{da_yun.getStartAge()}岁-{da_yun.getEndAge()}岁 {da_yun.getGanZhi()}")

        # 第一步大运的流年（如果有大运干支）
        if len(da_yun_list) > 1:
            first_da_yun = da_yun_list[1] if da_yun_list[0].getIndex() < 1 else da_yun_list[0]
            liu_nian_list = first_da_yun.getLiuNian(5)  # 获取前5年流年
            print(f"\n{first_da_yun.getGanZhi()}大运流年(前5年):")
            for i, liu_nian in enumerate(liu_nian_list):
                print(f"  流年[{i}]: {liu_nian.getYear()}年 {liu_nian.getAge()}岁 {liu_nian.getGanZhi()}")

        # 神煞分析
        if SHENSHA_AVAILABLE:
            print_separator("神煞分析")

            shensha_result = analyze_shensha(eight_char, lunar, gender)

            if shensha_result and "error" not in shensha_result:
                # 显示各柱神煞
                for pillar_name, pillar_info in shensha_result["pillars"].items():
                    print(f"{pillar_name}: {pillar_info['干支']}")

                    if "神煞" in pillar_info:
                        shenshas = pillar_info["神煞"]
                        if shenshas:
                            print(f"  神煞({pillar_info['数量']}个): {', '.join(shenshas)}")

                            # 分类显示神煞
                            categories = {
                                "贵人类": ["天乙", "太极", "文昌", "国印", "金舆", "福星", "天厨", "德秀"],
                                "德合类": ["天德", "月德", "天德合", "月德合", "天赦"],
                                "星煞类": ["驿马", "华盖", "将星", "禄神", "红鸾", "天喜", "五鬼", "天医", "学堂", "词馆"],
                                "刃煞类": ["羊刃", "飞刃", "血刃", "金神"],
                                "凶煞类": ["空亡", "劫煞", "灾煞", "亡神", "孤辰", "寡宿", "天罗", "地网", "十恶大败",
                                          "桃花", "孤鸾", "阴差阳错", "四废", "丧门", "吊客", "披麻", "童子", "流霞",
                                          "红艳", "魁罡", "八专", "九丑", "十灵", "元辰"]
                            }

                            for category, shensha_list in categories.items():
                                category_shenshas = [s for s in shenshas if s in shensha_list]
                                if category_shenshas:
                                    print(f"    {category}: {', '.join(category_shenshas)}")
                        else:
                            print("  无神煞")
                    else:
                        print(f"  错误: {pillar_info.get('错误', '未知错误')}")

                    print()

                # 总结统计
                print("-" * 60)
                print("神煞总结:")
                for pillar_name, pillar_info in shensha_result["pillars"].items():
                    if "神煞" in pillar_info and pillar_info["神煞"]:
                        print(f"{pillar_name}: {pillar_info['干支']}")
                        print(f"  神煞({pillar_info['数量']}个): {', '.join(pillar_info['神煞'])}")
                    elif "神煞" in pillar_info:
                        print(f"{pillar_name}: {pillar_info['干支']}")
                        print(f"  无神煞")

            elif shensha_result and "error" in shensha_result:
                print(f"神煞分析出错: {shensha_result['error']}")
            else:
                print("神煞分析失败")
        else:
            print_separator("神煞分析")
            print("神煞计算器模块未安装，跳过神煞分析")

        print_separator("", 80)
        print("分析完成！")

    except Exception as e:
        print(f"错误: {e}")
        return False

    return True


def interactive_mode():
    """交互模式"""
    print("="*60)
    print("           八字分析器 - 交互模式")
    print("="*60)

    try:
        # 获取用户输入
        print("\n请输入出生信息:")
        year = int(input("年份 (如1988): "))
        month = int(input("月份 (1-12): "))
        day = int(input("日期 (1-31): "))

        hour_input = input("小时 (0-23, 直接回车默认0): ").strip()
        hour = int(hour_input) if hour_input else 0

        minute_input = input("分钟 (0-59, 直接回车默认0): ").strip()
        minute = int(minute_input) if minute_input else 0

        second_input = input("秒钟 (0-59, 直接回车默认0): ").strip()
        second = int(second_input) if second_input else 0

        gender_input = input("性别 (1男0女, 直接回车默认1): ").strip()
        gender = int(gender_input) if gender_input else 1

        sect_input = input("流派 (1或2, 直接回车默认2): ").strip()
        sect = int(sect_input) if sect_input else 2

        # 验证输入
        if not (1 <= month <= 12):
            print("月份必须在1-12之间")
            return
        if not (1 <= day <= 31):
            print("日期必须在1-31之间")
            return
        if not (0 <= hour <= 23):
            print("小时必须在0-23之间")
            return
        if not (0 <= minute <= 59):
            print("分钟必须在0-59之间")
            return
        if not (0 <= second <= 59):
            print("秒钟必须在0-59之间")
            return
        if gender not in [0, 1]:
            print("性别必须是0或1")
            return
        if sect not in [1, 2]:
            print("流派必须是1或2")
            return

        # 验证日期
        try:
            datetime(year, month, day, hour, minute, second)
        except ValueError as e:
            print(f"日期时间错误: {e}")
            return

        # 执行分析
        analyze_eight_char(year, month, day, hour, minute, second, gender, sect)

    except ValueError:
        print("输入格式错误，请输入数字")
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"发生错误: {e}")


def batch_mode():
    """批量模式 - 从文件读取多个八字进行分析"""
    print("="*60)
    print("           八字分析器 - 批量模式")
    print("="*60)

    filename = input("请输入包含八字数据的文件名 (格式: 年,月,日,时,分,秒,性别,流派): ").strip()

    # 如果没有输入文件名，使用默认示例文件
    if not filename:
        filename = "八字示例数据.csv"
        print(f"使用默认文件: {filename}")

    try:
        # 尝试不同的编码方式
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        lines = None

        for encoding in encodings:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    lines = f.readlines()
                print(f"成功读取文件 (编码: {encoding})")
                break
            except UnicodeDecodeError:
                continue
            except FileNotFoundError:
                break

        if lines is None:
            raise FileNotFoundError(f"无法读取文件 {filename}")

        processed_count = 0
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):  # 跳过空行和注释
                continue

            try:
                parts = [p.strip() for p in line.split(',')]
                if len(parts) < 3:
                    print(f"第{i}行格式错误: {line}")
                    continue

                year = int(parts[0])
                month = int(parts[1])
                day = int(parts[2])
                hour = int(parts[3]) if len(parts) > 3 and parts[3] else 0
                minute = int(parts[4]) if len(parts) > 4 and parts[4] else 0
                second = int(parts[5]) if len(parts) > 5 and parts[5] else 0
                gender = int(parts[6]) if len(parts) > 6 and parts[6] else 1
                sect = int(parts[7]) if len(parts) > 7 and parts[7] else 2

                processed_count += 1
                print(f"\n{'='*20} 第{processed_count}个八字 (第{i}行) {'='*20}")
                analyze_eight_char(year, month, day, hour, minute, second, gender, sect)

            except ValueError as e:
                print(f"第{i}行数据错误: {line}, 错误: {e}")
                continue

        print(f"\n批量分析完成，共处理 {processed_count} 个八字")

    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        print("请确保文件路径正确，或使用默认示例文件")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='八字分析器 - 完整的八字信息输出工具')
    parser.add_argument('year', type=int, nargs='?', help='年份 (如: 1988)')
    parser.add_argument('month', type=int, nargs='?', help='月份 (1-12)')
    parser.add_argument('day', type=int, nargs='?', help='日期 (1-31)')
    parser.add_argument('--hour', type=int, default=0, help='小时 (0-23, 默认0)')
    parser.add_argument('--minute', type=int, default=0, help='分钟 (0-59, 默认0)')
    parser.add_argument('--second', type=int, default=0, help='秒钟 (0-59, 默认0)')
    parser.add_argument('--gender', type=int, choices=[0, 1], default=1,
                       help='性别 (1男0女, 默认1)')
    parser.add_argument('--sect', type=int, choices=[1, 2], default=2,
                       help='流派 (1或2, 默认2)')
    parser.add_argument('-i', '--interactive', action='store_true',
                       help='交互模式')
    parser.add_argument('-b', '--batch', action='store_true',
                       help='批量模式')

    args = parser.parse_args()

    # 交互模式
    if args.interactive:
        interactive_mode()
        return

    # 批量模式
    if args.batch:
        batch_mode()
        return

    # 如果没有提供年月日参数，显示帮助信息和示例
    if args.year is None or args.month is None or args.day is None:
        parser.print_help()
        print("\n使用示例:")
        print("  命令行模式:")
        print("    python 八字分析器.py 1988 2 15")
        print("    python 八字分析器.py 1988 2 15 --hour 23 --minute 30")
        print("    python 八字分析器.py 1988 2 15 --hour 23 --minute 30 --gender 0")
        print("    python 八字分析器.py 1988 2 15 --hour 23 --minute 30 --gender 0 --sect 1")
        print("\n  交互模式:")
        print("    python 八字分析器.py -i")
        print("    python 八字分析器.py --interactive")
        print("\n  批量模式:")
        print("    python 八字分析器.py -b")
        print("    python 八字分析器.py --batch")
        print("\n  批量文件格式 (CSV):")
        print("    年,月,日,时,分,秒,性别,流派")
        print("    1988,2,15,23,30,0,1,2")
        print("    1990,5,20,8,0,0,0,1")
        print("    # 这是注释行")
        return

    # 验证日期
    try:
        datetime(args.year, args.month, args.day, args.hour, args.minute, args.second)
    except ValueError as e:
        print(f"日期时间错误: {e}")
        return

    # 执行分析
    analyze_eight_char(
        args.year, args.month, args.day,
        args.hour, args.minute, args.second,
        args.gender, args.sect
    )


if __name__ == "__main__":
    main()
