# api/consumers/schemas.py
"""
WebSocket消费者相关的数据模式定义
"""

from pydantic import BaseModel
from typing import Optional, List


class MessageSchema(BaseModel):
    """消息模式"""
    role: str
    content: str


class ChatRequestSchema(BaseModel):
    """聊天请求模式"""
    message: str
    history: Optional[List[MessageSchema]] = None


class ChatResponse(BaseModel):
    """聊天响应模式"""
    content: Optional[str] = None
    error: Optional[str] = None
    finish: Optional[bool] = None
    keepalive: Optional[bool] = None
    done: Optional[bool] = None 