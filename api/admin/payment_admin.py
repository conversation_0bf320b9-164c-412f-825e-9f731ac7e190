"""
支付系统相关模型的Admin配置
包括BannedPaymentIP等支付安全相关的admin设置
"""

from django.contrib import admin
from django.utils import timezone
from datetime import timedelta

from ..models.payment_models import BannedPaymentIP


@admin.register(BannedPaymentIP)
class BannedPaymentIPAdmin(admin.ModelAdmin):
    """支付系统拉黑IP管理界面"""
    
    list_display = [
        'ip_address', 
        'reason', 
        'status_display',
        'auto_banned',
        'banned_at', 
        'banned_until',
        'created_by',
        'is_active'
    ]
    
    list_filter = [
        'auto_banned',
        'is_permanent', 
        'is_active',
        'banned_at',
        'created_by'
    ]
    
    search_fields = [
        'ip_address',
        'reason',
        'notes'
    ]
    
    readonly_fields = [
        'banned_at',
        'is_expired',
        'status_display'
    ]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('ip_address', 'reason', 'notes')
        }),
        ('拉黑设置', {
            'fields': ('is_permanent', 'banned_until', 'is_active')
        }),
        ('系统信息', {
            'fields': ('auto_banned', 'created_by', 'banned_at', 'status_display'),
            'classes': ('collapse',)
        })
    )
    
    actions = [
        'make_permanent_ban',
        'make_temporary_ban', 
        'activate_ban',
        'deactivate_ban',
        'clear_expired_bans'
    ]
    
    def make_permanent_ban(self, request, queryset):
        """设为永久拉黑"""
        updated = queryset.update(
            is_permanent=True, 
            banned_until=None,
            is_active=True
        )
        self.message_user(request, f"已将 {updated} 个IP设为永久拉黑")
    make_permanent_ban.short_description = "设为永久拉黑"
    
    def make_temporary_ban(self, request, queryset):
        """设为临时拉黑（24小时）"""
        banned_until = timezone.now() + timedelta(hours=24)
        updated = queryset.update(
            is_permanent=False,
            banned_until=banned_until,
            is_active=True
        )
        self.message_user(request, f"已将 {updated} 个IP设为24小时临时拉黑")
    make_temporary_ban.short_description = "设为临时拉黑(24小时)"
    
    def activate_ban(self, request, queryset):
        """启用拉黑"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"已启用 {updated} 个IP的拉黑")
    activate_ban.short_description = "启用拉黑"
    
    def deactivate_ban(self, request, queryset):
        """禁用拉黑"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"已禁用 {updated} 个IP的拉黑")
    deactivate_ban.short_description = "禁用拉黑"
    
    def clear_expired_bans(self, request, queryset):
        """清理过期的拉黑记录"""
        expired_count = 0
        for obj in queryset:
            if obj.is_expired:
                obj.is_active = False
                obj.save()
                expired_count += 1
        
        self.message_user(request, f"已清理 {expired_count} 个过期的拉黑记录")
    clear_expired_bans.short_description = "清理过期记录"
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related()
    
    def has_delete_permission(self, request, obj=None):
        """限制删除权限，只有超级管理员可以删除"""
        return request.user.is_superuser 