# api/consumers/bazi_advice_consumer.py
"""
八字分析WebSocket消费者
基于Ark模型的八字分析和健康建议
"""

import json
import traceback
import time
from api.utils.rate_limiter import RateLimiter
from api.prompts.prompt_engine import prompt_engine
from .base import ArkBasedConsumer


class BaziAdviceConsumer(ArkBasedConsumer):
    """
    基于Ark模型的八字分析WebSocket消费者
    支持限流和性能监控
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 使用prompt_engine管理的默认提示词，避免重复定义
        default_system_prompt, _ = prompt_engine._get_default_prompts()
        self.conversation_history = [
            {"role": "system", "content": default_system_prompt}
        ]
        # 临时关闭限流用于测试
        self.rate_limiter = RateLimiter("bazi_advice_api", normal_limit=0, member_limit=10)
        print('初始化BaziAdviceConsumer - 基于Ark模型，使用prompt_engine统一管理提示词')

    async def connect(self):
        try:
            print("[BAZI_ADVICE] 🚀 WebSocket连接建立开始")
            print(f"[BAZI_ADVICE] 🔍 连接信息: path={self.scope.get('path')}, method={self.scope.get('method')}")
            print(f"[BAZI_ADVICE] 🔍 Headers: {dict(self.scope.get('headers', []))}")
            print(f"[BAZI_ADVICE] 🔍 Query string: {self.scope.get('query_string', b'').decode()}")
            start_time = time.time()

            # 获取用户ID
            self.user_id = await self.get_user_id_from_scope()
            print(f"[BAZI_ADVICE] 👤 用户ID: {self.user_id}")

            # 初始化Ark客户端
            await self.initialize_ark_client()
            print("[BAZI_ADVICE] 🤖 Ark客户端初始化成功")

            await self.accept()
            print("[BAZI_ADVICE] ✅ WebSocket连接已接受")

            self.log_performance("[BAZI_ADVICE] WebSocket连接建立成功", start_time)

        except Exception as e:
            start_time = getattr(self, 'start_time', time.time())
            error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc(),
                'scope_info': {
                    'path': self.scope.get('path'),
                    'method': self.scope.get('method'),
                    'query_string': self.scope.get('query_string', b'').decode(),
                    'headers': dict(self.scope.get('headers', []))
                }
            }
            print(f"[BAZI_ADVICE] ❌ 连接失败详情: {error_details}")

            # 记录到error.log
            import logging
            logger = logging.getLogger('django')
            logger.error(f"[BAZI_ADVICE] WebSocket连接失败: {error_details}")

            self.log_performance("[BAZI_ADVICE] WebSocket连接建立失败", start_time, False)
            await self.close(code=4000)

    async def disconnect(self, close_code):
        disconnect_info = {
            'close_code': close_code,
            'user_id': getattr(self, 'user_id', None),
            'disconnect_reason': self._get_close_code_meaning(close_code)
        }
        print(f"[BAZI_ADVICE] 🔌 WebSocket连接断开详情: {disconnect_info}")

        # 记录到日志
        import logging
        logger = logging.getLogger('django')
        logger.info(f"[BAZI_ADVICE] WebSocket断开连接: {disconnect_info}")

    def _get_close_code_meaning(self, code):
        """获取WebSocket关闭代码的含义"""
        code_meanings = {
            1000: "正常关闭",
            1001: "端点离开",
            1002: "协议错误",
            1003: "不支持的数据类型",
            1006: "连接异常关闭",
            1007: "数据格式错误",
            1008: "违反政策",
            1009: "消息过大",
            1011: "服务器错误",
            4000: "自定义错误-连接建立失败"
        }
        return code_meanings.get(code, f"未知代码({code})")

    async def receive(self, text_data):
        """处理接收到的消息，包含限流和计时功能"""
        start_time = time.time()
        print(f"[BAZI_ADVICE] 🚀 开始处理消息，用户ID: {self.user_id}")

        try:
            # 检查限流
            if not await self.check_rate_limit():
                self.log_performance("[BAZI_ADVICE] 限流拦截", start_time, False)
                return

            # 解析接收到的消息
            data = json.loads(text_data)

            # 打印接收到的数据结构用于调试
            # print(f"[BAZI_ADVICE] 📨 收到前端数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

            # 处理新的参数化八字分析请求
            if self._is_parameterized_request(data):
                print(f"[BAZI_ADVICE] 🎯 收到参数化八字分析请求")
                system_prompt, user_message = self._handle_parameterized_request(data)
                # 更新对话历史中的系统提示
                self.conversation_history[0] = {"role": "system", "content": system_prompt}
                print(f"[BAZI_ADVICE] 📝 生成的用户消息: {user_message[:200]}...")
            # 处理五运六气健康分析请求（保持兼容）
            elif data.get('type') == 'health_wuyun_liuqi_analysis' and 'data' in data:
                print(f"[BAZI_ADVICE] 🌟 收到五运六气健康分析请求")
                user_message = self._format_wuyun_liuqi_health_message(data['data'])
                print(f"[BAZI_ADVICE] 📝 格式化后的五运六气健康分析消息: {user_message[:200]}...")
            # 处理八字分析请求（原有格式，保持兼容）
            elif data.get('type') == 'bazi_analysis' and 'data' in data:
                print(f"[BAZI_ADVICE] 📨 收到八字分析请求")
                user_message = self._format_bazi_message(data['data'])
                print(f"[BAZI_ADVICE] 📝 格式化后的消息: {user_message[:200]}...")
            # 处理普通消息格式
            elif 'message' in data:
                user_message = data.get('message', '')
                print(f"[BAZI_ADVICE] 📨 收到普通消息: {user_message[:100]}...")
            else:
                await self.send_error('请发送正确格式的八字分析数据')
                return

            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})

            try:
                # 使用Ark客户端调用API
                response = self.client.chat.completions.create(
                    model="ep-20250424123213-kk2fv",
                    messages=self.conversation_history,
                    stream=True,
                    temperature=0.3  # 降低温度参数，提高回答稳定性，减少胡说八道
                )

                # 处理流式响应
                assistant_message = await self.send_stream_response(response)

                self.log_performance("[BAZI_ADVICE] 消息处理完成", start_time)
                print(f"[BAZI_ADVICE] 📤 完整回复: {assistant_message[:200]}...")

            except Exception as e:
                error_details = {
                    'stage': 'generating_response',
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'traceback': traceback.format_exc(),
                    'user_id': self.user_id,
                    'user_message': user_message[:100] + '...' if len(user_message) > 100 else user_message
                }
                print(f"[BAZI_ADVICE] ❌ 生成回复失败详情: {error_details}")

                # 记录到error.log
                import logging
                logger = logging.getLogger('django')
                logger.error(f"[BAZI_ADVICE] 生成回复失败: {error_details}")

                await self.send_error(f"生成回复时出错: {str(e)}")
                self.log_performance("[BAZI_ADVICE] 消息处理失败", start_time, False)

        except Exception as e:
            error_details = {
                'stage': 'processing_message',
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc(),
                'user_id': self.user_id,
                'raw_data': text_data[:200] + '...' if len(text_data) > 200 else text_data
            }
            print(f"[BAZI_ADVICE] ❌ 消息处理异常详情: {error_details}")

            # 记录到error.log
            import logging
            logger = logging.getLogger('django')
            logger.error(f"[BAZI_ADVICE] 消息处理异常: {error_details}")

            await self.send_error(f"处理消息时出错: {str(e)}")
            self.log_performance("[BAZI_ADVICE] 消息处理异常", start_time, False)

    def _is_parameterized_request(self, data):
        """检查是否为参数化请求"""
        # 检查data字段中是否包含必要的参数
        data_content = data.get('data', {})
        required_fields = ['analysis_direction', 'domain', 'time_scope']
        return all(field in data_content for field in required_fields)

    def _handle_parameterized_request(self, data):
        """处理参数化请求"""
        try:
            # 提取参数 - 从data字段中获取
            data_content = data.get('data', {})
            analysis_direction = data_content.get('analysis_direction')
            domain = data_content.get('domain')
            time_scope = data_content.get('time_scope')
            user_question = data_content.get('user_question', '')

            print(f"[BAZI_ADVICE] 🔧 参数解析: direction={analysis_direction}, domain={domain}, time_scope={time_scope}")

            # 使用提示词工程生成提示词
            system_prompt, user_prompt_template = prompt_engine.generate_prompt(
                analysis_direction, domain, time_scope, user_question
            )

            # 填充用户提示词模板 - 使用data_content作为八字数据
            user_message = self._fill_user_prompt_template(user_prompt_template, data_content, user_question)

            return system_prompt, user_message

        except Exception as e:
            print(f"[BAZI_ADVICE] ❌ 处理参数化请求失败: {str(e)}")
            # 返回默认提示词
            return prompt_engine._get_default_prompts()

    def _fill_user_prompt_template(self, template, bazi_data, user_question=None):
        """填充用户提示词模板"""
        try:
            # 提取核心信息
            core_info = self._extract_core_bazi_info(bazi_data)

            # 填充基础模板
            filled_template = template.format(
                eight_char=core_info['eight_char'],
                gender=core_info['gender'],
                birth_date=core_info['birth_date']
            )

            # 如果有用户问题，添加到模板中
            if user_question and '{user_question}' in template:
                filled_template = filled_template.replace('{user_question}', user_question)

            # 添加精简的核心信息
            filled_template += f"\n\n【十神信息】\n{core_info['shishen_summary']}"
            filled_template += f"\n\n【神煞信息】\n{core_info['shensha_summary']}"
            filled_template += f"\n\n【大运流年】\n{core_info['timing_info']}"

            # 打印最终传递给大模型的精简信息
            print(f"[BAZI_ADVICE] 🎯 传递给大模型的精简信息:")
            print(f"[BAZI_ADVICE] 📝 {filled_template}")

            return filled_template

        except Exception as e:
            print(f"[BAZI_ADVICE] ❌ 填充模板失败: {str(e)}")
            return f"八字：{bazi_data.get('eight_char', '未知')}，请进行分析。"

    def _extract_core_bazi_info(self, bazi_data):
        """提取核心八字信息，去除垃圾信息"""
        try:
            # 基础信息
            eight_char = bazi_data.get('eight_char', '未提供八字')
            gender = bazi_data.get('gender', '未知')

            # 从detailed_info中提取出生日期
            birth_date = '未提供出生日期'
            detailed_info = bazi_data.get('detailed_info', {})
            if detailed_info:
                birth_info = detailed_info.get('birth_info', {})
                if birth_info.get('solar_date'):
                    # 提取简洁的出生日期
                    solar_date = birth_info['solar_date']
                    birth_date = solar_date.split(' ')[0] if ' ' in solar_date else solar_date
                elif birth_info.get('birth_details'):
                    details = birth_info['birth_details']
                    birth_date = f"{details.get('year', '')}-{details.get('month', ''):02d}-{details.get('day', ''):02d} {details.get('hour', ''):02d}:{details.get('minute', ''):02d}"

            # 精简十神信息
            shishen_summary = ""
            shishen = bazi_data.get('shishen', {})
            if shishen:
                gan_shishen = shishen.get('gan_shishen', {})
                if gan_shishen:
                    shishen_summary += f"天干十神：年{gan_shishen.get('year', '')} 月{gan_shishen.get('month', '')} 日{gan_shishen.get('day', '')} 时{gan_shishen.get('time', '')}\n"

                zhi_shishen = shishen.get('zhi_shishen', {})
                if zhi_shishen:
                    zhi_parts = []
                    pillar_names = {"year": "年", "month": "月", "day": "日", "time": "时"}
                    for pillar, gods in zhi_shishen.items():
                        if isinstance(gods, list) and gods:
                            pillar_name = pillar_names.get(pillar, pillar)
                            gods_str = ''.join(gods)
                            zhi_parts.append(f"{pillar_name}{gods_str}")
                    if zhi_parts:
                        shishen_summary += f"地支十神：{' '.join(zhi_parts)}"

            # 精简神煞信息（保留四柱分布）
            shensha_summary = ""
            shensha = bazi_data.get('shensha', {})
            if shensha:
                pillars = shensha.get('pillars', {})
                if pillars:
                    # 重要神煞列表
                    important_shensha_set = {
                        '天乙', '天德', '月德', '文昌', '学堂', '禄神', '驿马', '桃花',
                        '羊刃', '劫煞', '空亡', '童子', '孤鸾', '阴差阳错', '太极', '天喜'
                    }

                    pillar_summaries = []
                    for pillar_name, pillar_info in pillars.items():
                        if isinstance(pillar_info, dict) and 'shenshas' in pillar_info:
                            # 过滤重要神煞
                            important_in_pillar = [s for s in pillar_info['shenshas'] if s in important_shensha_set]
                            if important_in_pillar:
                                pillar_summaries.append(f"{pillar_name}[{' '.join(important_in_pillar)}]")

                    if pillar_summaries:
                        shensha_summary = f"神煞分布：{' '.join(pillar_summaries)}"
                    else:
                        # 如果没有重要神煞，显示前8个
                        unique_shenshas = shensha.get('unique_shenshas', [])
                        if unique_shenshas:
                            shensha_summary = f"神煞：{' '.join(unique_shenshas[:8])}"

            # 精简大运流年信息
            timing_info = ""
            if detailed_info:
                current_dayun = detailed_info.get('current_dayun', {})
                if current_dayun:
                    # 支持两种格式：start_year/end_year 或 start_age/end_age
                    start_info = current_dayun.get('start_year') or current_dayun.get('start_age', '')
                    end_info = current_dayun.get('end_year') or current_dayun.get('end_age', '')
                    if start_info and end_info:
                        # 判断是年份还是年龄
                        if current_dayun.get('start_age'):
                            timing_info += f"当前大运：{current_dayun.get('ganzhi', '')}({start_info}-{end_info}岁)\n"
                        else:
                            timing_info += f"当前大运：{current_dayun.get('ganzhi', '')}({start_info}-{end_info}年)\n"
                    else:
                        timing_info += f"当前大运：{current_dayun.get('ganzhi', '')}\n"

                liu_nian_info = detailed_info.get('liu_nian_info', {})
                if liu_nian_info:
                    liu_nian = liu_nian_info.get('liuNian', {}).get('ganzhi', '')
                    liu_yue = liu_nian_info.get('liuYue', {}).get('ganzhi', '')
                    liu_ri = liu_nian_info.get('liuRi', {}).get('ganzhi', '')
                    timing_info += f"流年：{liu_nian} 流月：{liu_yue} 流日：{liu_ri}"

            return {
                'eight_char': eight_char,
                'gender': gender,
                'birth_date': birth_date,
                'shishen_summary': shishen_summary.strip(),
                'shensha_summary': shensha_summary.strip(),
                'timing_info': timing_info.strip()
            }

        except Exception as e:
            print(f"[BAZI_ADVICE] ❌ 提取核心信息失败: {str(e)}")
            return {
                'eight_char': bazi_data.get('eight_char', '未知'),
                'gender': bazi_data.get('gender', '未知'),
                'birth_date': '未知',
                'shishen_summary': '信息提取失败',
                'shensha_summary': '信息提取失败',
                'timing_info': '信息提取失败'
            }

    def _format_bazi_message(self, bazi_data):
        """将八字数据格式化为适合AI分析的消息（精简版）"""
        try:
            # 提取核心信息
            core_info = self._extract_core_bazi_info(bazi_data)

            message_parts = [
                "请分析以下八字信息并提供健康养生建议：",
                "",
                f"八字：{core_info['eight_char']}",
                f"性别：{core_info['gender']}",
                f"出生日期：{core_info['birth_date']}",
                "",
                "【十神信息】",
                core_info['shishen_summary'],
                "",
                "【神煞信息】",
                core_info['shensha_summary'],
                "",
                "【大运流年】",
                core_info['timing_info'],
                "",
                "请基于以上八字信息，从中医养生角度提供：",
                "1. 五行强弱分析和调理建议",
                "2. 适合的饮食调养方案",
                "3. 运动和作息建议",
                "4. 情绪调节和心理养生",
                "5. 其他个性化的健康建议"
            ]

            formatted_message = "\n".join(message_parts)

            # 打印最终传递给大模型的精简信息
            print(f"[BAZI_ADVICE] 🎯 传递给大模型的精简信息:")
            print(f"[BAZI_ADVICE] 📝 {formatted_message}")

            return formatted_message

        except Exception as e:
            print(f"[BAZI_ADVICE] ❌ 格式化八字消息失败: {str(e)}")
            return f"八字信息解析出错，请重新发送。错误：{str(e)}"

    def _format_wuyun_liuqi_health_message(self, data):
        """将五运六气健康分析数据格式化为适合AI分析的消息"""
        try:
            eight_char = data.get('eight_char', '未知八字')
            birth_date = data.get('birth_date', '未知')
            analysis_date = data.get('analysis_date', '未知')
            wuyun_liuqi = data.get('wuyun_liuqi', {})

            message_parts = [
                "【五运六气健康分析】",
                f"八字：{eight_char}",
                f"出生日期：{birth_date}",
                f"分析日期：{analysis_date}",
                "",
                "当前五运六气环境：",
                f"司天：{wuyun_liuqi.get('sitian', '未知')}",
                f"在泉：{wuyun_liuqi.get('zaiquan', '未知')}",
                f"大运：{wuyun_liuqi.get('dayun', '未知')}",
                f"客气：{wuyun_liuqi.get('keqi', '未知')}",
                f"主气：{wuyun_liuqi.get('zhuqi', '未知')}",
                f"六气阶段：{wuyun_liuqi.get('qi_shunxu', '未知')}",
                f"节气：{wuyun_liuqi.get('jieqi', '未知')}",
                f"灾宫：{wuyun_liuqi.get('zaigong', '未知')}",
                "",
                "请基于以上八字体质（内因）和五运六气环境（外因），",
                "分析健康状态和疾病变化趋势，提供精准的时间医学指导。",
                "",
                "要求：",
                "1. 分析当前五运六气对个体体质的具体影响",
                "2. 预测疾病易发的时间节点",
                "3. 提供针对性的调理方案",
                "4. 给出时间医学指导（最佳调理时辰、关键节气等）"
            ]

            formatted_message = "\n".join(message_parts)

            # 打印最终传递给大模型的精简信息
            print(f"[BAZI_ADVICE] 🎯 传递给大模型的五运六气信息:")
            print(f"[BAZI_ADVICE] 📝 {formatted_message}")

            return formatted_message

        except Exception as e:
            print(f"[BAZI_ADVICE] ❌ 格式化五运六气健康分析消息失败: {str(e)}")
            return f"八字：{data.get('eight_char', '未知八字')}，当前五运六气环境下的健康分析。"
