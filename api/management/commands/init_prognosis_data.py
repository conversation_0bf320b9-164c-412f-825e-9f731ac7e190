"""
初始化预后系统的疗法数据
包括疗法分类和具体疗法的示例数据
"""

from django.core.management.base import BaseCommand
from api.models import PrognosisTherapyClassification, PrognosisTherapyCategory


class Command(BaseCommand):
    help = '初始化预后系统的疗法分类和疗法数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化预后系统疗法数据...'))
        
        # 创建疗法分类
        classifications_data = [
            {
                'name': '艾灸疗法',
                'code': 'moxibustion',
                'description': '通过艾草燃烧产生的温热刺激特定穴位，调理气血，温通经络',
                'icon': 'https://example.com/icons/moxibustion.png',
                'color': '#FF6B35',
                'sort_order': 1
            },
            {
                'name': '食疗疗法',
                'code': 'food_therapy',
                'description': '通过合理搭配食材，调理身体，增强体质，防治疾病',
                'icon': 'https://example.com/icons/food.png',
                'color': '#4ECDC4',
                'sort_order': 2
            },
            {
                'name': '运动疗法',
                'code': 'exercise',
                'description': '通过适当的运动锻炼，增强体质，改善身体机能',
                'icon': 'https://example.com/icons/exercise.png',
                'color': '#45B7D1',
                'sort_order': 3
            },
            {
                'name': '按摩疗法',
                'code': 'massage',
                'description': '通过手法按摩特定穴位和经络，疏通气血，缓解疲劳',
                'icon': 'https://example.com/icons/massage.png',
                'color': '#96CEB4',
                'sort_order': 4
            },
            {
                'name': '茶疗疗法',
                'code': 'tea_therapy',
                'description': '通过饮用不同功效的茶品，调理身体，养生保健',
                'icon': 'https://example.com/icons/tea.png',
                'color': '#FECA57',
                'sort_order': 5
            }
        ]
        
        created_classifications = {}
        for data in classifications_data:
            classification, created = PrognosisTherapyClassification.objects.get_or_create(
                code=data['code'],
                defaults=data
            )
            created_classifications[data['code']] = classification
            if created:
                self.stdout.write(f'✅ 创建疗法分类: {classification.name}')
            else:
                self.stdout.write(f'⚠️ 疗法分类已存在: {classification.name}')
        
        # 创建具体疗法
        therapies_data = [
            # 艾灸疗法
            {
                'classification_code': 'moxibustion',
                'name': '艾灸足三里',
                'description': '艾灸足三里穴，具有调理脾胃、增强免疫力的功效',
                'instructions': '1. 找准足三里穴位（膝盖下方约3寸处）\n2. 点燃艾条，距离穴位2-3厘米\n3. 施灸15-20分钟\n4. 每日1次，连续7天',
                'image': 'https://example.com/images/zusanli_moxibustion.jpg',
                'color': '#FF6B35',
                'duration': '15-20分钟',
                'difficulty_level': 1,
                'contraindications': '孕妇禁用，皮肤破损处禁灸',
                'is_recommended': True
            },
            {
                'classification_code': 'moxibustion',
                'name': '艾灸神阙',
                'description': '艾灸神阙穴（肚脐），温阳益气，调理肠胃',
                'instructions': '1. 仰卧，露出肚脐\n2. 在肚脐上放置隔姜片\n3. 点燃艾柱，放在姜片上\n4. 施灸20-30分钟',
                'image': 'https://example.com/images/shenque_moxibustion.jpg',
                'color': '#FF6B35',
                'duration': '20-30分钟',
                'difficulty_level': 2,
                'contraindications': '孕妇、月经期女性禁用',
                'is_recommended': True
            },
            # 食疗疗法
            {
                'classification_code': 'food_therapy',
                'name': '健脾祛湿茶',
                'description': '由薏米、茯苓、陈皮等组成，具有健脾祛湿的功效',
                'instructions': '材料：薏米30g，茯苓15g，陈皮10g，红枣3枚\n做法：\n1. 薏米提前浸泡2小时\n2. 所有材料加水1000ml\n3. 大火煮开后转小火煮30分钟\n4. 去渣取汁，分2-3次温服',
                'image': 'https://example.com/images/spleen_dampness_tea.jpg',
                'color': '#4ECDC4',
                'duration': '制作30分钟，全天饮用',
                'difficulty_level': 1,
                'contraindications': '便秘者慎用',
                'is_recommended': True
            },
            {
                'classification_code': 'food_therapy',
                'name': '当归生姜羊肉汤',
                'description': '温阳补血，适合气血不足、畏寒怕冷的人群',
                'instructions': '材料：当归15g，生姜30g，羊肉500g\n做法：\n1. 羊肉洗净切块，焯水去腥\n2. 当归、生姜洗净\n3. 所有材料放入砂锅，加水适量\n4. 大火煮开后转小火炖2小时\n5. 调味即可',
                'image': 'https://example.com/images/danggui_yangrou_soup.jpg',
                'color': '#4ECDC4',
                'duration': '制作2.5小时',
                'difficulty_level': 2,
                'contraindications': '阴虚火旺者慎用',
                'is_recommended': True
            },
            # 运动疗法
            {
                'classification_code': 'exercise',
                'name': '八段锦',
                'description': '传统养生功法，通过八个动作调理气血，强身健体',
                'instructions': '1. 双手托天理三焦\n2. 左右开弓似射雕\n3. 调理脾胃须单举\n4. 五劳七伤往后瞧\n5. 摇头摆尾去心火\n6. 背后七颠百病消\n7. 攒拳怒目增气力\n8. 两手攀足固肾腰\n每个动作重复8-12次',
                'image': 'https://example.com/images/baduanjin.jpg',
                'color': '#45B7D1',
                'duration': '20-30分钟',
                'difficulty_level': 1,
                'contraindications': '严重心脏病患者须在医生指导下进行',
                'is_recommended': True
            },
            # 按摩疗法
            {
                'classification_code': 'massage',
                'name': '头部按摩',
                'description': '通过按摩头部穴位，缓解头痛，改善睡眠',
                'instructions': '1. 用双手十指指腹从前额发际线向后推至枕部\n2. 按摩太阳穴，顺时针转圈10次\n3. 按摩百会穴，轻压3-5秒\n4. 从上往下梳理头发10次\n5. 整个过程重复3遍',
                'image': 'https://example.com/images/head_massage.jpg',
                'color': '#96CEB4',
                'duration': '10-15分钟',
                'difficulty_level': 1,
                'contraindications': '头部有外伤或炎症者禁用',
                'is_recommended': True
            },
            # 茶疗疗法
            {
                'classification_code': 'tea_therapy',
                'name': '安神茶',
                'description': '由酸枣仁、茯苓、龙眼肉组成，具有安神助眠的功效',
                'instructions': '材料：酸枣仁15g，茯苓10g，龙眼肉10g\n做法：\n1. 酸枣仁炒制至微黄\n2. 所有材料洗净，加开水300ml\n3. 焖泡15分钟即可\n4. 睡前1小时饮用',
                'image': 'https://example.com/images/anshen_tea.jpg',
                'color': '#FECA57',
                'duration': '冲泡15分钟',
                'difficulty_level': 1,
                'contraindications': '孕妇慎用',
                'is_recommended': True
            }
        ]
        
        for data in therapies_data:
            classification = created_classifications[data.pop('classification_code')]
            therapy, created = PrognosisTherapyCategory.objects.get_or_create(
                name=data['name'],
                defaults={**data, 'classification': classification}
            )
            if created:
                self.stdout.write(f'✅ 创建疗法: {therapy.name}')
            else:
                self.stdout.write(f'⚠️ 疗法已存在: {therapy.name}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 预后系统疗法数据初始化完成！'))
        self.stdout.write(self.style.SUCCESS('您现在可以在Django admin界面中管理这些数据了。'))
        self.stdout.write(self.style.WARNING('请访问: http://您的域名/admin/')) 