# RouterTest1 接入八字模块化分析系统示例

## 🎯 目标
将模块化八字分析系统接入到主项目的 `routertest1` 中，实现组件化调用。

## 📁 文件结构规划

```
api/
├── ninja_apis/
│   ├── __init__.py                 # 主路由注册
│   ├── routertest1.py             # 现有测试路由
│   └── bazi_modular_apis.py       # 新增：八字模块化API
├── services/
│   ├── bazi_analysis_core.py      # 新增：八字分析核心
│   └── bazi_ai_interpreter.py     # 新增：AI解读服务
└── utils/
    ├── lunar_python/              # 新增：农历库
    └── shensha_calculator/        # 新增：神煞计算器
```

## 🔧 具体实施步骤

### 步骤1: 创建八字模块化API文件

创建 `api/ninja_apis/bazi_modular_apis.py`：

```python
"""
八字模块化分析API - 集成到主项目
"""
from ninja import Router
from ninja.schema import Schema
from typing import Optional
from datetime import datetime
import json
import traceback

# 导入八字分析核心服务
from api.services.bazi_analysis_core import create_bazi_analyzer
from api.services.bazi_ai_interpreter import bazi_ai_interpreter

# 创建八字分析路由器
bazi_router = Router(tags=["八字分析"])

# 输入Schema
class BaziInputSchema(Schema):
    year: int
    month: int  
    day: int
    hour: Optional[int] = 0
    minute: Optional[int] = 0
    second: Optional[int] = 0
    gender: Optional[int] = 1  # 1男0女
    sect: Optional[int] = 2    # 流派

# 基础分析接口
@bazi_router.post("/basic", summary="获取八字基本信息")
def get_bazi_basic_info(request, data: BaziInputSchema):
    """获取八字基本信息"""
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day, 
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        basic_info = analyzer.get_basic_info()
        
        return {
            "success": True,
            "data": basic_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# 十神分析接口
@bazi_router.post("/shishen", summary="十神分析")
def get_shishen_analysis(request, data: BaziInputSchema):
    """专门的十神分析接口"""
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        shishen_analysis = analyzer.get_shishen_analysis()
        
        return {
            "success": True,
            "data": shishen_analysis,
            "analysis_type": "十神分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# 神煞分析接口
@bazi_router.post("/shensha", summary="神煞分析")
def get_shensha_analysis(request, data: BaziInputSchema):
    """专门的神煞分析接口"""
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        shensha_analysis = analyzer.get_shensha_analysis()
        
        return {
            "success": True,
            "data": shensha_analysis,
            "analysis_type": "神煞分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# AI解读接口
@bazi_router.post("/shishen_ai", summary="十神AI解读")
def get_shishen_with_ai(request, data: BaziInputSchema):
    """十神分析 + AI专业解读"""
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        basic_info = analyzer.get_basic_info()
        shishen_analysis = analyzer.get_shishen_analysis()
        
        # AI解读
        ai_interpretation = bazi_ai_interpreter.interpret_shishen(shishen_analysis, basic_info)
        
        return {
            "success": True,
            "data": {
                "shishen_data": shishen_analysis,
                "ai_interpretation": ai_interpretation
            },
            "analysis_type": "十神分析+AI解读",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# 完整分析接口
@bazi_router.post("/complete", summary="完整八字分析")
def get_complete_analysis(request, data: BaziInputSchema):
    """获取完整的八字分析"""
    try:
        analyzer = create_bazi_analyzer(
            data.year, data.month, data.day,
            data.hour, data.minute, data.second,
            data.gender, data.sect
        )
        
        # 整合所有分析结果
        complete_analysis = {
            "basic_info": analyzer.get_basic_info(),
            "four_pillars": analyzer.get_four_pillars_detail(),
            "shishen_analysis": analyzer.get_shishen_analysis(),
            "shensha_analysis": analyzer.get_shensha_analysis(),
            "dishi_analysis": analyzer.get_dishi_analysis(),
            "special_positions": analyzer.get_special_positions(),
            "nine_star_analysis": analyzer.get_nine_star_analysis(),
            "fortune_analysis": analyzer.get_fortune_analysis()
        }
        
        return {
            "success": True,
            "data": complete_analysis,
            "analysis_type": "完整八字分析",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
```

### 步骤2: 修改主路由注册文件

修改 `api/ninja_apis/__init__.py`：

```python
# 现有导入保持不变
from ninja import NinjaAPI
from api.views import tcmNLP, tcmchat, ninja_chat, invite_views
from .questionnaire_api import questionnaire_router
from .db_health_api import db_health_router

# 新增导入
from .bazi_modular_apis import bazi_router

# 简化实现，使用全局变量控制初始化状态
_initialized = False

# 现有API实例保持不变
bank_api = NinjaAPI(version="1.0")
doubao_aichat = NinjaAPI(version="2.0")
invite_api = NinjaAPI(version="3.0")
questionnaire_api = NinjaAPI(version="1.0", title="问卷系统API", urls_namespace="questionnaire_api")
db_health_api = NinjaAPI(version="1.1", title="数据库健康监控API", urls_namespace="db_health_api")

# 新增：八字分析API实例
bazi_analysis_api = NinjaAPI(version="1.0", title="八字分析API", urls_namespace="bazi_analysis_api")

def initialize():
    global _initialized
    if not _initialized:
        # 现有路由注册保持不变
        doubao_aichat.add_router("/chat/", ninja_chat.router)
        invite_api.add_router("/invite_api/", invite_views.router)
        questionnaire_api.add_router("", questionnaire_router)
        db_health_api.add_router("", db_health_router)
        
        # 新增：注册八字分析路由
        bazi_analysis_api.add_router("/bazi", bazi_router)
        
        _initialized = True

# 立即调用初始化
initialize()
```

### 步骤3: 添加URL配置

在主项目的 `urls.py` 中添加：

```python
from django.urls import path, include
from api.ninja_apis import (
    bank_api, doubao_aichat, invite_api, 
    questionnaire_api, db_health_api, bazi_analysis_api  # 新增
)

urlpatterns = [
    # 现有URL配置保持不变
    path("api/bank/", bank_api.urls),
    path("api/doubao/", doubao_aichat.urls),
    path("api/invite/", invite_api.urls),
    path("api/questionnaire/", questionnaire_api.urls),
    path("api/health/", db_health_api.urls),
    
    # 新增：八字分析API
    path("api/bazi/", bazi_analysis_api.urls),
]
```

### 步骤4: 复制依赖文件

```bash
# 复制核心分析模块
cp api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/services/bazi_analysis_core.py \
   api/services/

# 复制AI解读服务  
cp api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/services/bazi_ai_interpreter.py \
   api/services/

# 复制依赖库
cp -r api/utils/bazi_websocket_demo/bazi-master/bazi_api/lunar_python \
      api/utils/
cp -r api/utils/bazi_websocket_demo/bazi-master/bazi_api/shensha_calculator \
      api/utils/
```

### 步骤5: 修改导入路径

修改 `api/services/bazi_analysis_core.py` 的导入：

```python
# 将原来的导入
from lunar_python import Solar, Lunar
from shensha_calculator import query_shensha

# 修改为
from api.utils.lunar_python import Solar, Lunar
from api.utils.shensha_calculator import query_shensha
```

## 🧪 测试接口

### 测试命令

```bash
# 测试基本信息
curl -X POST "http://localhost:8000/api/bazi/bazi/basic" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"year": 2001, "month": 7, "day": 28, "hour": 5, "minute": 0, "gender": 0}'

# 测试十神分析
curl -X POST "http://localhost:8000/api/bazi/bazi/shishen" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"year": 2001, "month": 7, "day": 28, "hour": 5, "minute": 0, "gender": 0}'

# 测试十神AI解读
curl -X POST "http://localhost:8000/api/bazi/bazi/shishen_ai" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"year": 2001, "month": 7, "day": 28, "hour": 5, "minute": 0, "gender": 0}'
```

### 前端调用示例

```javascript
// 调用八字基本信息
const getBaziBasic = async (birthData) => {
    const response = await fetch('/api/bazi/bazi/basic', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(birthData)
    });
    return await response.json();
};

// 调用十神分析
const getShishenAnalysis = async (birthData) => {
    const response = await fetch('/api/bazi/bazi/shishen', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(birthData)
    });
    return await response.json();
};
```

## 📊 API文档访问

接入完成后，可以通过以下地址访问API文档：
- 主API文档: `http://localhost:8000/api/bazi/docs`
- 八字分析接口: `http://localhost:8000/api/bazi/bazi/`

## 🔍 验证清单

- [ ] 依赖文件复制完成
- [ ] 导入路径修改正确
- [ ] 路由注册成功
- [ ] URL配置添加
- [ ] 基本接口测试通过
- [ ] AI解读接口测试通过
- [ ] API文档可正常访问
- [ ] 权限验证正常工作

完成以上步骤后，八字模块化分析系统就成功接入到主项目的routertest1中了！
