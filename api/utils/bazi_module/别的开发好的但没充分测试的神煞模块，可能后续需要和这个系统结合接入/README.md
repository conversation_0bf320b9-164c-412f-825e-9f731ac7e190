# 神煞计算器 - Python版本

这是一个用于计算八字神煞的Python库，从JavaScript版本重构而来。提供组件化、模块化的设计，便于集成到其他项目中。

## 特性

- ✅ **完整功能**：支持40+种神煞的计算
- ✅ **组件化设计**：模块化架构，便于维护和扩展
- ✅ **类型安全**：使用类型提示，提高代码可读性
- ✅ **全面测试**：包含完整的单元测试
- ✅ **易于集成**：提供简洁的API接口
- ✅ **兼容性**：完全兼容原JavaScript版本的计算逻辑

## 支持的神煞

### 贵人类
- 天乙贵人、太极贵人、文昌贵人、国印贵人
- 金舆、福星、天厨贵人、德秀贵人

### 德合类
- 天德贵人、月德贵人、天德合、月德合、天赦

### 星煞类
- 驿马、华盖、将星、禄神、红鸾、天喜
- 五鬼、天医、学堂、词馆

### 刃煞类
- 羊刃、飞刃、血刃、金神

### 凶煞类
- 空亡、劫煞、灾煞、亡神、孤辰、寡宿
- 天罗、地网、十恶大败、桃花、孤鸾、阴差阳错
- 四废、丧门、吊客、披麻、童子、流霞、红艳
- 魁罡、八专、九丑、十灵、元辰

## 安装

将`shensha_calculator`文件夹复制到您的项目中，然后导入使用：

```python
from shensha_calculator import query_shensha
```

## 快速开始

### 基本用法

```python
from shensha_calculator import query_shensha

# 八字：甲子年、丙寅月、戊午日、癸亥时
bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]

# 查询日柱神煞
result = query_shensha("戊午", bazi, True, 3, "海中金")
print(f"日柱神煞: {result}")
# 输出: ['羊刃', '禄神', ...]

# 查询年柱神煞
result = query_shensha("甲子", bazi, True, 1, "海中金")
print(f"年柱神煞: {result}")
# 输出: ['太极', ...]
```

### 完整示例

```python
from shensha_calculator import query_shensha

def analyze_bazi(bazi, nian_nayin, is_man=True):
    """分析八字的所有神煞"""
    
    # 构造四柱干支
    pillars = [
        ("年柱", bazi[0] + bazi[1], 1),
        ("月柱", bazi[2] + bazi[3], 2),
        ("日柱", bazi[4] + bazi[5], 3),
        ("时柱", bazi[6] + bazi[7], 4)
    ]
    
    results = {}
    
    for name, ganzhi, witch in pillars:
        try:
            shenshas = query_shensha(ganzhi, bazi, is_man, witch, nian_nayin)
            results[name] = {
                "干支": ganzhi,
                "神煞": shenshas,
                "数量": len(shenshas)
            }
        except ValueError as e:
            results[name] = {"错误": str(e)}
    
    return results

# 示例八字
bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]
nian_nayin = "海中金"

# 分析八字
results = analyze_bazi(bazi, nian_nayin, True)

# 打印结果
for pillar, info in results.items():
    print(f"\n{pillar}: {info['干支']}")
    if "神煞" in info:
        print(f"  神煞({info['数量']}个): {', '.join(info['神煞'])}")
    else:
        print(f"  {info['错误']}")
```

## API文档

### query_shensha(ganzhi, bazi, is_man, witch, nian_nayin=None)

查询指定干支的神煞。

**参数：**
- `ganzhi` (str): 要查询的干支，如"甲子"
- `bazi` (List[str]): 八字数组，8个元素分别是年干、年支、月干、月支、日干、日支、时干、时支
- `is_man` (bool): 性别，True为男性，False为女性
- `witch` (int): 柱位，1-4分别代表年/月/日/时柱，5-8代表大运/流年/流月/流时
- `nian_nayin` (str, 可选): 年柱纳音，查询学堂、词馆时需要

**返回：**
- `List[str]`: 神煞名称列表

**异常：**
- `ValueError`: 当输入参数不正确时抛出

### 输入验证

所有输入都会进行严格验证：

```python
# 有效的干支
valid_ganzhi = ["甲子", "乙丑", "丙寅", ...]

# 有效的八字格式
valid_bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]

# 有效的柱位
valid_witch = [1, 2, 3, 4, 5, 6, 7, 8]

# 有效的纳音
valid_nayin = ["海中金", "炉中火", "大林木", ...]
```

## 高级用法

### 单独使用神煞计算函数

```python
from shensha_calculator.core.noble_spirits import tianyiguiren, wenchang
from shensha_calculator.core.star_spirits import yima, huagai

# 直接调用神煞计算函数
result1 = tianyiguiren("甲", "丑")  # 返回1表示是天乙贵人
result2 = wenchang("甲", "巳")     # 返回1表示是文昌贵人
result3 = yima("子", "寅")         # 返回1表示是驿马
```

### 使用工具函数

```python
from shensha_calculator.utils.helpers import (
    get_jiazi_order, get_dizhi_wuxing, get_tiangan_yinyang
)

# 获取干支在六十甲子中的顺序
order = get_jiazi_order("甲子")  # 返回1

# 获取地支对应的五行
wuxing = get_dizhi_wuxing("子")  # 返回"水"

# 判断天干阴阳
yinyang = get_tiangan_yinyang("甲")  # 返回True（阳干）
```

## 测试

运行单元测试：

```bash
cd Desktop
python -m pytest shensha_calculator/tests/ -v
```

或者运行特定测试：

```bash
python -m shensha_calculator.tests.test_core
python -m shensha_calculator.tests.test_api
```

## 项目结构

```
shensha_calculator/
├── __init__.py              # 包初始化
├── data/                    # 基础数据
│   ├── constants.py         # 常量定义
│   └── mappings.py          # 映射关系
├── core/                    # 核心计算
│   ├── noble_spirits.py     # 贵人类神煞
│   ├── virtue_harmony.py    # 德合类神煞
│   ├── star_spirits.py      # 星煞类神煞
│   ├── blade_spirits.py     # 刃煞类神煞
│   └── malefic_spirits.py   # 凶煞类神煞
├── utils/                   # 工具函数
│   ├── helpers.py           # 辅助函数
│   └── validators.py        # 验证函数
├── api/                     # 接口
│   └── query.py             # 主查询接口
└── tests/                   # 测试
    ├── test_data.py         # 测试数据
    ├── test_core.py         # 核心测试
    └── test_api.py          # 接口测试
```

## 注意事项

1. **输入格式**：确保八字格式正确，天干地支必须是有效的中文字符
2. **柱位参数**：witch参数决定查询哪一柱，不同柱位的神煞计算规则不同
3. **性别影响**：某些神煞（如元辰）的计算会受到性别影响
4. **纳音要求**：学堂、词馆神煞需要提供年柱纳音

## 兼容性

- Python 3.6+
- 完全兼容原JavaScript版本的计算逻辑
- 所有神煞计算结果与原版本一致

## 许可证

本项目重构自JavaScript版本，保持开源协议。
