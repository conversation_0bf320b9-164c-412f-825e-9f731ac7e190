"""
星煞类神煞计算

包含驿马、华盖、将星、禄神等星煞类神煞的计算函数。
"""

from ..utils.helpers import get_nayin_wuxing


def yima(base_zhi: str, target_zhi: str) -> int:
    """
    驿马
    申子辰马在寅, 寅午戌马在申,
    巳酉丑马在亥, 亥卯未马在巳.
    查法：以年、日支查余三支
    
    Args:
        base_zhi: 年支/日支
        target_zhi: 目标地支
        
    Returns:
        1表示是驿马，0表示不是
    """
    conditions = {
        "申": "寅", "子": "寅", "辰": "寅",
        "寅": "申", "午": "申", "戌": "申",
        "亥": "巳", "卯": "巳", "未": "巳",
        "巳": "亥", "酉": "亥", "丑": "亥"
    }
    
    return 1 if conditions.get(base_zhi) == target_zhi else 0


def huagai(base_zhi: str, target_zhi: str) -> int:
    """
    华盖
    寅午戌见戌, 亥卯未见未,
    申子辰见辰, 巳酉丑见丑.
    查法： 以年支或日支为主, 凡四柱中所见者为有华盖星.
    
    Args:
        base_zhi: 年支/日支
        target_zhi: 目标地支
        
    Returns:
        1表示是华盖，0表示不是
    """
    conditions = {
        "申": "辰", "子": "辰", "辰": "辰",
        "寅": "戌", "午": "戌", "戌": "戌",
        "巳": "丑", "酉": "丑", "丑": "丑",
        "亥": "未", "卯": "未", "未": "未"
    }
    
    return 1 if conditions.get(base_zhi) == target_zhi else 0


def jiangxing(base_zhi: str, target_zhi: str) -> int:
    """
    将星
    寅午戌见午, 巳酉丑见酉,
    申子辰见子, 辛卯未见卯.
    查法: 以年支或日支查其余各支, 见者为将星.
    
    Args:
        base_zhi: 年支或日支
        target_zhi: 目标地支
        
    Returns:
        1表示是将星，0表示不是
    """
    conditions = {
        "申": "子", "子": "子",
        "寅": "午", "午": "午", "戌": "午",
        "巳": "酉", "酉": "酉", "丑": "酉",
        "亥": "卯", "卯": "卯", "未": "卯"
    }
    
    return 1 if conditions.get(base_zhi) == target_zhi else 0


def lushen(day_gan: str, dizhi: str) -> int:
    """
    禄神
    甲禄在寅, 乙禄在卯, 丙戊禄在巳, 丁己禄在午, 庚禄在申, 辛禄在酉, 壬禄在亥, 癸禄在子.
    查法: 以日干查四支, 见之者为是.
    
    Args:
        day_gan: 日干
        dizhi: 地支
        
    Returns:
        1表示是禄神，0表示不是
    """
    conditions = {
        "甲": "寅",
        "乙": "卯",
        "丙": "巳",
        "丁": "午",
        "戊": "巳",
        "己": "午",
        "庚": "申",
        "辛": "酉",
        "壬": "亥",
        "癸": "子"
    }
    
    return 1 if conditions.get(day_gan) == dizhi else 0


def hongluan(year_zhi: str, target_zhi: str) -> int:
    """
    红鸾
    红鸾年支: 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
    其他地支见: 卯 寅 丑 子 亥 戌 酉 申 未 午 巳 辰
    查法：以年支查余地支。如子年生人见卯为红鸾，见酉为天喜。
    
    Args:
        year_zhi: 年支
        target_zhi: 目标地支
        
    Returns:
        1表示是红鸾，0表示不是
    """
    conditions = {
        "子": "卯", "丑": "寅", "寅": "丑", "卯": "子",
        "辰": "亥", "巳": "戌", "午": "酉", "未": "申",
        "申": "未", "酉": "午", "戌": "巳", "亥": "辰"
    }
    
    return 1 if conditions.get(year_zhi) == target_zhi else 0


def tianxi(year_zhi: str, target_zhi: str) -> int:
    """
    天喜
    天喜年支: 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
    其他地支见: 酉 申 未 午 巳 辰 卯 寅 丑 子 亥 戌
    
    Args:
        year_zhi: 年支
        target_zhi: 目标地支
        
    Returns:
        1表示是天喜，0表示不是
    """
    conditions = {
        "子": "酉", "丑": "申", "寅": "未", "卯": "午",
        "辰": "巳", "巳": "辰", "午": "卯", "未": "寅",
        "申": "丑", "酉": "子", "戌": "亥", "亥": "戌"
    }
    
    return 1 if conditions.get(year_zhi) == target_zhi else 0


def wugui(month_zhi: str, target_zhi: str) -> int:
    """
    五鬼星
    子月支见辰支，丑月支见巳支，寅月支见午支，卯月支见未支，
    辰月支见申支，巳月支见酉支，午月支见戌支，未月支见亥支，
    申月支见子支，酉月支见丑支，戌月支见寅支，亥月支见卯支。
    
    Args:
        month_zhi: 月支
        target_zhi: 目标地支
        
    Returns:
        1表示是五鬼，0表示不是
    """
    conditions = {
        "子": "辰", "丑": "巳", "寅": "午", "卯": "未",
        "辰": "申", "巳": "酉", "午": "戌", "未": "亥",
        "申": "子", "酉": "丑", "戌": "寅", "亥": "卯"
    }
    
    return 1 if conditions.get(month_zhi) == target_zhi else 0


def tianyi(month_zhi: str, target_zhi: str) -> int:
    """
    天医
    正月生见丑, 二月生见寅, 三月生见卯, 四月生见辰,五月生见巳, 六月生见午,
    七月生见未, 八月生见申,九月生见酉, 十月生见戌, 十一月生见亥, 十二月生见子.
    查法: 以月支查其它地支, 见者为是.
    
    Args:
        month_zhi: 月支
        target_zhi: 目标地支
        
    Returns:
        1表示是天医，0表示不是
    """
    conditions = {
        "寅": "丑",  # 正月
        "卯": "寅",  # 二月
        "辰": "卯",  # 三月
        "巳": "辰",  # 四月
        "午": "巳",  # 五月
        "未": "午",  # 六月
        "申": "未",  # 七月
        "酉": "申",  # 八月
        "戌": "酉",  # 九月
        "亥": "戌",  # 十月
        "子": "亥",  # 十一月
        "丑": "子"   # 十二月
    }
    
    return 1 if conditions.get(month_zhi) == target_zhi else 0


def xuetang(year_nayin: str, tiangan: str, dizhi: str) -> int:
    """
    学堂
    年柱纳音为金命见其他三支有"巳"为学堂，见"辛巳"为正学堂；
    年柱纳音为木命见其他三支有"亥"为学堂，见"己亥"为正学堂；
    年柱纳音为水命见其他三支有"申"为学堂，见"甲申"为正学堂；
    年柱纳音为土命见其他三支有"申"为学堂，见"戊申"为正学堂；
    年柱纳音为火命见其他三支有"寅"为学堂，见"丙寅"为正学堂。
    
    Args:
        year_nayin: 年柱纳音
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是学堂，0表示不是
    """
    wuxing = get_nayin_wuxing(year_nayin)
    
    conditions = {
        "金": ["巳", ["辛", "巳"]],
        "木": ["亥", ["己", "亥"]],
        "水": ["申", ["甲", "申"]],
        "土": ["申", ["戊", "申"]],
        "火": ["寅", ["丙", "寅"]]
    }
    
    if wuxing in conditions:
        main_match, sub_match = conditions[wuxing]
        if dizhi == main_match or (tiangan == sub_match[0] and dizhi == sub_match[1]):
            return 1
    
    return 0


def ciguan(year_nayin: str, tiangan: str, dizhi: str) -> int:
    """
    词馆
    年柱纳音为金命见其他三支有"申"为学堂，见"壬申"为正学堂；
    年柱纳音为木命见其他三支有"寅"为学堂，见"庚寅"为正学堂；
    年柱纳音为水命见其他三支有"亥"为学堂，见"癸亥"为正学堂；
    年柱纳音为土命见其他三支有"亥"为学堂，见"丁亥"为正学堂；
    年柱纳音为火命见其他三支有"巳"为学堂，见"乙巳"为正学堂。
    
    Args:
        year_nayin: 年柱纳音
        tiangan: 天干
        dizhi: 地支
        
    Returns:
        1表示是词馆，0表示不是
    """
    wuxing = get_nayin_wuxing(year_nayin)
    
    conditions = {
        "金": ["申", ["壬", "卯"]],  # 注意：原代码中是"卯"，可能是错误
        "木": ["寅", ["庚", "寅"]],
        "水": ["亥", ["癸", "亥"]],
        "土": ["亥", ["丁", "亥"]],
        "火": ["巳", ["乙", "巳"]]
    }
    
    if wuxing in conditions:
        main_match, sub_match = conditions[wuxing]
        if dizhi == main_match or (tiangan == sub_match[0] and dizhi == sub_match[1]):
            return 1
    
    return 0
