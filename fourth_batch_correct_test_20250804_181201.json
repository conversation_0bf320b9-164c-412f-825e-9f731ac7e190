{"test_time": "2025-08-04T18:12:01.150705", "single_test_results": [{"api_name": "日活动", "success": true, "status_code": 200, "response_time": 0.*****************, "category": "会员系统", "risk_level": "低", "method": "POST", "url": "http://127.0.0.1:8000/api/bank/DayActivitiesView/", "data": {"date": "2025-08-03", "activities": [], "score_added": false}, "error": null}, {"api_name": "用户卡片", "success": false, "status_code": 500, "response_time": 0.029566049575805664, "category": "会员系统", "risk_level": "低", "method": "POST", "url": "http://127.0.0.1:8000/api/bank/UserCardView/", "data": {"status": "error", "message": "Expecting value: line 1 column 1 (char 0)"}, "error": null}, {"api_name": "用户问答历史", "success": false, "status_code": 500, "response_time": 0.020308732986450195, "category": "学习系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/async-bank/user-quiz-history/", "data": {"error": "服务器错误"}, "error": null}, {"api_name": "创建帖子", "success": false, "status_code": 404, "response_time": 0.*****************, "category": "论坛系统", "risk_level": "中", "method": "POST", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/create_post", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "搜索帖子", "success": false, "status_code": 404, "response_time": 0.021108150482177734, "category": "论坛系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/search_posts", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "帖子评论", "success": false, "status_code": 404, "response_time": 0.0219271183013916, "category": "论坛系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/post_comments", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "时间效率排行", "success": true, "status_code": 200, "response_time": 0.021021127700805664, "category": "医案系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_time_efficiency_ranking", "data": {"success": false, "message": "获取排行榜失败: Cannot resolve keyword 'limit' into field. Choices are: average_score, average_time_per_case, best_case_id, created_at, highest_score, id, last_case_date, total_cases_completed, total_exp, total_score_sum, total_time_spent, updated_at, user, user_id", "error_code": null, "details": null}, "error": null}, {"api_name": "提交游戏记录", "success": false, "status_code": 404, "response_time": 0.021146774291992188, "category": "医案系统", "risk_level": "中", "method": "POST", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_game_record", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "增加经验值", "success": false, "status_code": 404, "response_time": 0.021501779556274414, "category": "医案系统", "risk_level": "中", "method": "POST", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_add_exp", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "经验值历史", "success": false, "status_code": 404, "response_time": 0.020160913467407227, "category": "医案系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_exp_history", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "游戏历史记录", "success": false, "status_code": 404, "response_time": 0.02275538444519043, "category": "医案系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_game_history", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "案例排行榜", "success": true, "status_code": 200, "response_time": 0.03448820114135742, "category": "医案系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_cases_ranking", "data": {"success": true, "message": "获取完成医案数排行榜成功", "ranking_type": "cases", "period": "all", "total_count": 2, "data": [{"rank": 1, "user_id": 56, "nickname": "嘿嘿嘿新头像", "avatar": "/static/images/icon/shuimo-nan1.png", "total_cases": 44, "total_exp": 1294, "average_score": 81.39, "completion_rate": 100.0}, {"rank": 2, "user_id": 2, "nickname": "测试2", "avatar": "/static/images/icon/9.png?t=1735453204430?t=1735453281806?t=1735453362910?t=1735453397689?t=1735453597319?t=1740645760217", "total_cases": 1, "total_exp": 5, "average_score": 50.0, "completion_rate": 100.0}]}, "error": null}, {"api_name": "平均得分排行", "success": true, "status_code": 200, "response_time": 0.031279563903808594, "category": "医案系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/doubao_aichat/chat/medical_case_avg_score_ranking", "data": {"success": true, "message": "获取平均得分排行榜成功", "ranking_type": "avg_score", "period": "all", "total_count": 2, "data": [{"rank": 1, "user_id": 56, "nickname": "嘿嘿嘿新头像", "avatar": "/static/images/icon/shuimo-nan1.png", "average_score": 81.39, "total_cases": 44, "total_score": 3581, "highest_score": 95}, {"rank": 2, "user_id": 2, "nickname": "测试2", "avatar": "/static/images/icon/9.png?t=1735453204430?t=1735453281806?t=1735453362910?t=1735453397689?t=1735453597319?t=1740645760217", "average_score": 50.0, "total_cases": 1, "total_score": 50, "highest_score": 50}]}, "error": null}, {"api_name": "添加自定义症状", "success": false, "status_code": 500, "response_time": 0.01977086067199707, "category": "症状管理", "risk_level": "中", "method": "POST", "url": "http://127.0.0.1:8000/api/tcmchat/add_custom_symptom/", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Server Error (500)</title>\n</head>\n<body>\n  <h1>Server Error (500)</h1><p></p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "执行搜索", "success": false, "status_code": 422, "response_time": 0.019927263259887695, "category": "预后系统", "risk_level": "低", "method": "POST", "url": "http://127.0.0.1:8000/api/routertest1/prognosis/search", "data": {"detail": [{"type": "missing", "loc": ["body", "payload", "keyword"], "msg": "Field required"}]}, "error": null}, {"api_name": "我的邀请码", "success": false, "status_code": 404, "response_time": 0.019763469696044922, "category": "邀请系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/invite_url/invite_api/my-code", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "邀请记录", "success": false, "status_code": 404, "response_time": 0.020046234130859375, "category": "邀请系统", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/invite_url/invite_api/records", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "系统心跳", "success": false, "status_code": 404, "response_time": 0.019575834274291992, "category": "系统功能", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/ping/", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Not Found</title>\n</head>\n<body>\n  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "干支查询", "success": false, "status_code": 500, "response_time": 0.019705533981323242, "category": "中医功能", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/tcmNLP/ganzhi/", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Server Error (500)</title>\n</head>\n<body>\n  <h1>Server Error (500)</h1><p></p>\n</body>\n</html>\n"}, "error": null}, {"api_name": "获取当前季节", "success": false, "status_code": 500, "response_time": 0.01944589614868164, "category": "中医功能", "risk_level": "低", "method": "GET", "url": "http://127.0.0.1:8000/api/tcmNLP/get_current_season/", "data": {"content": "\n<!doctype html>\n<html lang=\"en\">\n<head>\n  <title>Server Error (500)</title>\n</head>\n<body>\n  <h1>Server Error (500)</h1><p></p>\n</body>\n</html>\n"}, "error": null}], "concurrent_test_results": [{"api_name": "日活动", "concurrent": 100, "success_count": 38, "error_count": 62, "success_rate": 38.0, "avg_response_time": 19.215274937152863, "qps": 3.249001819642235, "total_time": 30.77868390083313, "category": "会员系统", "risk_level": "低"}, {"api_name": "时间效率排行", "concurrent": 100, "success_count": 15, "error_count": 85, "success_rate": 15.0, "avg_response_time": 26.41913475751877, "qps": 3.2247061568794715, "total_time": 31.01057744026184, "category": "医案系统", "risk_level": "低"}, {"api_name": "案例排行榜", "concurrent": 100, "success_count": 0, "error_count": 100, "success_rate": 0.0, "avg_response_time": 30.999975810050966, "qps": 3.224215042600409, "total_time": 31.015300989151, "category": "医案系统", "risk_level": "低"}, {"api_name": "平均得分排行", "concurrent": 100, "success_count": 0, "error_count": 100, "success_rate": 0.0, "avg_response_time": 30.98513017654419, "qps": 3.225669261324674, "total_time": 31.00131845474243, "category": "医案系统", "risk_level": "低"}], "summary": {"total_apis": 20, "available_apis": 4, "availability_rate": 20.0, "avg_success_rate": 13.25, "avg_qps": 3.230898070111697}}