import metaphysic

def main():
    print("=" * 50)
    print("               八字分析系统")
    print("=" * 50)
    print("\n基于您的出生时间，本系统可以分析您的八字五行情况。")
    print("请依次输入以下信息：\n")
    
    birthYear = input("请输入您的出生年份(如1990): ")
    birthMonth = input("请输入您的出生月份(1-12): ")
    birthDay = input("请输入您的出生日期(1-31): ")
    birthTime = input("请输入您的出生时辰(0-23): ")
    
    try:
        birthYear = int(birthYear)
        birthMonth = int(birthMonth)
        birthDay = int(birthDay)
        birthTime = int(birthTime)
        
        print("\n正在分析您的八字...")
        
        shenchenbazi = metaphysic.getShenChenBaZi(birthYear, birthMonth, birthDay, birthTime)
        print("\n您的生辰八字是: %s" % (she<PERSON><PERSON><PERSON>))
        
        wuxing = metaphysic.getWuXing(she<PERSON><PERSON><PERSON>)
        print("\n您的生辰八字五行指数分析:")
        print("  金：%.2f" % wuxing[0])
        print("  木：%.2f" % wuxing[1])
        print("  水：%.2f" % wuxing[2])
        print("  火：%.2f" % wuxing[3])
        print("  土：%.2f" % wuxing[4])
        
        # 找出最弱的五行
        minValue = min(wuxing)
        lackingWuxings = []
        for index, value in enumerate(wuxing):
            if value == minValue:
                lackingWuxings.append(metaphysic.wuxingNames[index])
        
        print("\n您缺少的五行是：%s" % "、".join(lackingWuxings))
        
        # 五行解释
        print("\n五行说明:")
        print("  金：主义气、刚毅、威严")
        print("  木：主仁德、谦和、勃勃生机")
        print("  水：主智慧、灵活、通达")
        print("  火：主礼节、热情、光明")
        print("  土：主信义、稳重、包容")
        
        print("\n注意：此分析仅供参考，请勿过度依赖。")
        print("=" * 50)
        
    except ValueError:
        print("\n错误：输入格式错误，请确保年、月、日、时为有效数字。")
    except Exception as e:
        print("\n错误：", str(e))

if __name__ == '__main__':
    main()
