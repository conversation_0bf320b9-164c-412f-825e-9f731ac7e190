#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试提交答题得分API的连接池问题修复
"""

import requests
import json
import time

# 读取测试token
def get_test_token():
    try:
        with open('test_token_user_2.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print("❌ 未找到测试token文件 test_token_user_2.txt")
        return None

def test_submit_quiz_score():
    """测试提交答题得分API"""
    token = get_test_token()
    if not token:
        return
    
    url = "http://localhost:8000/api/async-bank/daily-quiz-score/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    test_data = {
        "score": 85,
        "time_spent": 120,  # 2分钟
        "question_count": 5,
        "correct_count": 4,
        "difficulty": 2,  # 中级
        "category_id": 1,
        "question_details": [
            {"question_id": 1, "correct": True, "time_spent": 25},
            {"question_id": 2, "correct": True, "time_spent": 30},
            {"question_id": 3, "correct": False, "time_spent": 35},
            {"question_id": 4, "correct": True, "time_spent": 20},
            {"question_id": 5, "correct": True, "time_spent": 10}
        ]
    }
    
    print(f"🧪 测试提交答题得分API...")
    print(f"📡 URL: {url}")
    print(f"📊 测试数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=test_data, timeout=30)
        end_time = time.time()
        
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
        print(f"📈 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📋 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 请求失败!")
            print(f"📋 错误响应: {response.text}")
            
    except requests.exceptions.Timeout:
        print(f"⏰ 请求超时 (30秒)")
    except requests.exceptions.ConnectionError:
        print(f"🔌 连接错误 - 请确保服务器正在运行")
    except Exception as e:
        print(f"💥 请求异常: {str(e)}")

def test_multiple_requests():
    """测试多次请求，检查连接池是否正常"""
    print(f"\n🔄 测试多次请求 (检查连接池)...")
    
    for i in range(5):
        print(f"\n--- 第 {i+1} 次请求 ---")
        test_submit_quiz_score()
        time.sleep(1)  # 间隔1秒

if __name__ == "__main__":
    print("🚀 开始测试提交答题得分API...")
    
    # 单次测试
    test_submit_quiz_score()
    
    # 多次测试
    test_multiple_requests()
    
    print("\n🏁 测试完成!")
