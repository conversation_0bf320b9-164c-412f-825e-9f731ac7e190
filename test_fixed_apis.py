#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已修复的API
专门测试我们已经修复了同步异步混用问题的API
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VhDgOlaTZFjxZaGUdPhe7r658Vqfq48TOc6jTH-6zSA"

headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json",
    "User-Agent": "FixedAPITester/1.0"
}

# 已修复的API列表
FIXED_APIS = [
    # 会员系统API - 已修复
    {
        "name": "日活动API",
        "method": "POST",
        "url": f"{BASE_URL}/api/bank/DayActivitiesView/",
        "data": {},
        "description": "已修复同步异步混用问题"
    },
    
    # 症状管理API - 已修复
    {
        "name": "获取自定义症状API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmchat/get_custom_symptoms/",
        "data": None,
        "description": "已修复异步工具函数导入"
    },
    
    # 其他功能API - 已修复
    {
        "name": "获取当前季节API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmNLP/get_current_season/",
        "data": None,
        "description": "已修复函数调用问题"
    },
    
    # 支付系统API - 已修复
    {
        "name": "检查支付状态API",
        "method": "POST",
        "url": f"{BASE_URL}/api/tcmNLP/CheckPaymentStatusView/",
        "data": {"out_trade_no": "test_order_123"},
        "description": "已修复同步异步混用问题"
    }
]

def test_single_api(api_config):
    """测试单个API"""
    print(f"\n🔧 测试已修复的API: {api_config['name']}")
    print(f"📍 {api_config['method']} {api_config['url']}")
    print(f"🛠️  修复内容: {api_config['description']}")
    
    try:
        start_time = time.time()
        
        if api_config['method'].upper() == 'GET':
            response = requests.get(api_config['url'], headers=headers, timeout=10)
        elif api_config['method'].upper() == 'POST':
            response = requests.post(api_config['url'], headers=headers, json=api_config['data'], timeout=10)
        
        response_time = time.time() - start_time
        
        print(f"📊 状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        
        if response.status_code == 200:
            print("✅ 修复成功！API正常工作")
            try:
                json_data = response.json()
                print(f"📄 响应数据: {str(json_data)[:150]}...")
            except:
                print(f"📄 响应内容: {response.text[:150]}...")
        elif response.status_code == 404:
            print("❌ 404错误 - API路径可能不正确")
        elif response.status_code == 500:
            print("🔥 500错误 - 修复可能不完整，需要进一步检查")
            print(f"🚨 错误内容: {response.text[:300]}")
        else:
            print(f"⚠️  其他错误 - HTTP {response.status_code}")
            print(f"📄 响应内容: {response.text[:200]}")
        
        return {
            "name": api_config['name'],
            "status_code": response.status_code,
            "response_time": response_time,
            "success": response.status_code == 200,
            "fixed": response.status_code == 200
        }
        
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return {"name": api_config['name'], "status_code": 0, "success": False, "fixed": False}
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误")
        return {"name": api_config['name'], "status_code": 0, "success": False, "fixed": False}
    except Exception as e:
        print(f"💥 异常: {str(e)}")
        return {"name": api_config['name'], "status_code": 0, "success": False, "fixed": False}

def main():
    """主测试函数"""
    print("🎯 已修复API测试开始")
    print("=" * 80)
    
    results = []
    fixed_count = 0
    
    for api_config in FIXED_APIS:
        result = test_single_api(api_config)
        results.append(result)
        
        if result['fixed']:
            fixed_count += 1
        
        # 短暂休息
        time.sleep(1)
    
    # 生成报告
    print("\n" + "=" * 80)
    print("📊 修复验证报告")
    print("=" * 80)
    
    total_count = len(results)
    fix_success_rate = (fixed_count / total_count) * 100
    
    print(f"🎯 修复成功率: {fixed_count}/{total_count} ({fix_success_rate:.1f}%)")
    
    print(f"\n📋 详细结果:")
    for result in results:
        status = "✅ 修复成功" if result['fixed'] else "❌ 仍有问题"
        print(f"   {status} - {result['name']} (HTTP {result['status_code']})")
    
    if fix_success_rate == 100:
        print(f"\n🎉 恭喜！所有修复都成功了！")
        print(f"✅ 第四批API的同步异步混用问题已全部解决")
        print(f"🚀 可以进行正式的并发测试了")
    else:
        print(f"\n⚠️  还有一些API需要进一步修复")
        print(f"🔧 建议检查服务器日志获取更多错误信息")

if __name__ == "__main__":
    main()
