# UniApp项目API接口清单

## 📋 概述
本文档记录了整个UniApp项目中的所有API接口，按功能模块分类整理。



### 📊 分类统计（重新优化分类）
- 💎 **会员系统API**: 32 个
- 🔐 **认证相关API**: 6 个
- 💬 **AI聊天功能API**: 5 个
- � **论坛系统API**: 8 个
- 🏥 **医疗健康API**: 15 个
- 🎮 **医案系统API**: 15 个
- 🔍 **预后搜索API**: 25 个
- � **八字相关API**: 6 个
- � **书籍管理API**: 3 个（已废弃）
- 💰 **支付系统API**: 4 个
- 🎯 **邀请系统API**: 3 个
- 📊 **症状管理API**: 6 个
- 🍃 **饮食建议API**: 2 个
- 🌐 **WebSocket连接**: 3 个
- 🔧 **其他功能API**: 18 个


---

## 💎 会员系统API (32个)

### 核心会员功能
1. `${BASE_URL}/api/bank/EmptyRequestView/` - POST - 空请求保持会话
2. `${BASE_URL}/api/tcmNLP/CheckPaymentStatusView/` - POST - 检查支付状态-需要测试
3. `${BASE_URL}/api/async-bank/checkmembership/` - POST - 检查会员状态
4. `${BASE_URL}/api/user/vip_status/` - GET - 获取VIP状态-废弃
5. `${BASE_URL}/api/bank/activate_membership/` - POST - 激活会员-废弃
6. `${BASE_URL}/api/bank/MemberExpRankingView/` - POST - 会员经验排行-废弃

### 目标系统
7. `${BASE_URL}/api/async-bank/abstract-goal/` - GET - 获取抽象目标
8. `${BASE_URL}/api/bank/AbstractGoalRecordView/` - POST - 记录目标
9. `${BASE_URL}/api/async-bank/user-goal/` - GET - 用户目标

### 打卡系统
10. `${BASE_URL}/api/async-bank/activity-list/` - GET - 活动列表
11. `${BASE_URL}/api/bank/CustomActivityView/` - POST - 自定义活动
12. `${BASE_URL}/api/bank/DayActivitiesView/` - POST - 日活动
13. `${BASE_URL}/api/bank/CreateCustomActivityView/` - POST - 创建自定义活动

### 挑战系统，这个可能是错误创建，都不用测试
14. `${BASE_URL}/api/async-bank/challenge-levels/` - GET - 挑战等级
15. `${BASE_URL}/api/async-bank/challenge-level/{id}/` - GET - 特定挑战
16. `${BASE_URL}/api/async-bank/challenge-level/{id}/submit/` - POST - 提交挑战

### 用户数据统计
17. `${BASE_URL}/api/bank/UserCardView/` - POST - 用户卡片
18. `${BASE_URL}/api/async-bank/user-stats/` - GET - 用户统计
19. `${BASE_URL}/api/bank/check_points/` - POST - 检查积分-暂时不用没有实装
20. `${BASE_URL}/api/bank/AddSharePointsView/` - POST - 添加分享积分-暂时不用没有实装

### 问答系统
21. `${BASE_URL}/api/async-bank/daily-tcm-questions/` - GET - 每日中医问题
22. `${BASE_URL}/api/async-bank/daily-quiz-score/` - POST - 每日问答得分
23. `${BASE_URL}/api/async-bank/quiz-ranking/` - GET - 问答排行
24. `${BASE_URL}/api/async-bank/user-quiz-history/` - GET - 用户问答历史

### 症状管理
25. `${BASE_URL}/api/bank/update_symptoms/` - POST - 更新症状
26. `${BASE_URL}/api/bank/delete_all_symptoms/` - DELETE - 删除所有症状

---

## 🔐 认证相关API (6个)

1. `${BASE_URL}/api/auth/wechat-login` - POST - 微信授权登录
2. `${BASE_URL}/api/auth/refresh-token` - POST - 刷新访问令牌
3. `${BASE_URL}/api/auth/phone-login` - POST - 手机号登录
4. `${BASE_URL}/api/test/login-with-token/` - POST - Token登录测试-可能已经废弃
5. `${BASE_URL}/api/test/wechat-login11/` - POST - 微信登录测试-可能已经废弃
6. `${BASE_URL}/api/async-bank/check-register-user/` - POST - 检查注册用户-可能已经废弃

---

## 💬 AI聊天功能API (5个)

### AI对话核心-这里5个api都禁止性能测试，因为成本太高
1. `${BASE_URL}/api/doubao_aichat/chat/chat` - POST - 通用AI聊天对话 禁止性能测试，因为成本太高
2. `${BASE_URL}/api/doubao_aichat/chat/chat_YiAnStudy` - POST - 医案学习专用AI对话 禁止性能测试，因为成本太高
3. `${BASE_URL}/api/tcmNLP/aichat/` - POST - 传统AI聊天接口 禁止性能测试，因为成本太高
4. `${BASE_URL}/api/tcmNLP/ai_analysis/` - POST - 中医健康分析 禁止性能测试，因为成本太高
5. `${BASE_URL}/api/tcmNLP/ai_analysis_symptoms/` - POST - 症状相关健康分析 禁止性能测试，因为成本太高

---

## 💭 论坛系统API (8个)

1. `${BASE_URL}/api/doubao_aichat/chat/posts` - GET - 获取论坛帖子
2. `${BASE_URL}/api/doubao_aichat/chat/sections` - GET - 获取论坛版块
3. `${BASE_URL}/api/doubao_aichat/chat/create_post` - POST - 创建帖子
4. `${BASE_URL}/api/doubao_aichat/chat/posts/{id}` - GET - 获取特定帖子
5. `${BASE_URL}/api/doubao_aichat/chat/posts/{id}/like` - POST - 帖子点赞
6. `${BASE_URL}/api/doubao_aichat/chat/user_posts` - GET - 用户帖子
7. `${BASE_URL}/api/doubao_aichat/chat/search_posts` - GET - 搜索帖子
8. `${BASE_URL}/api/doubao_aichat/chat/post_comments` - GET - 帖子评论

---

## 🎮 医案系统API (15个)

1. `${BASE_URL}/api/doubao_aichat/chat/medical_case_exp_ranking` - GET - 经验值排行榜
2. `${BASE_URL}/api/doubao_aichat/chat/medical_case_total_score_ranking` - GET - 总得分排行榜
3. `${BASE_URL}/api/doubao_aichat/chat/medical_case_time_efficiency_ranking` - GET - 时间效率排行榜
4. `${BASE_URL}/api/doubao_aichat/chat/medical_case_user_stats` - GET - 用户统计信息
5. `${BASE_URL}/api/doubao_aichat/chat/medical_case_game_record` - POST - 提交游戏记录
6. `${BASE_URL}/api/doubao_aichat/chat/medical_case_add_exp` - POST - 增加经验值
7. `${BASE_URL}/api/doubao_aichat/chat/medical_case_exp_history` - GET - 经验值历史记录
8. `${BASE_URL}/api/doubao_aichat/chat/medical_case_game_history` - GET - 游戏历史记录
9. `${BASE_URL}/api/doubao_aichat/chat/medical_case_cases_ranking` - GET - 案例排行榜
10. `${BASE_URL}/api/doubao_aichat/chat/medical_case_avg_score_ranking` - GET - 平均得分排行榜
11. `${BASE_URL}/api/doubao_aichat/chat/medical_case_user_ranking/{id}` - GET - 用户排名
12. `${BASE_URL}/api/doubao_aichat/chat/chat_YiAnPingfen/{id}` - POST - 医案评分
13. `${BASE_URL}/api/doubao_aichat/chat/add_health_record` - POST - 添加健康记录
14. `${BASE_URL}/api/doubao_aichat/chat/get_health_records` - GET - 获取健康记录
15. `${BASE_URL}/api/doubao_aichat/chat/delete_health_record/{id}` - DELETE - 删除健康记录

---

## 📊 症状管理API (6个)

1. `${BASE_URL}/api/tcmchat/get_all_symptoms/` - GET - 获取所有症状
2. `${BASE_URL}/api/tcmchat/add_symptom/` - POST - 添加症状
3. `${BASE_URL}/api/tcmchat/update_symptom/` - PUT - 更新症状
4. `${BASE_URL}/api/tcmchat/delete_symptom/` - DELETE - 删除症状
5. `${BASE_URL}/api/tcmchat/get_custom_symptoms/` - GET - 获取自定义症状
6. `${BASE_URL}/api/tcmchat/add_custom_symptom/` - POST - 添加自定义症状

---

## 💰 支付系统API (4个) 涉及支付，可能无法测试，需要ai亲自去检查，暂时无法通过通用脚本进行测试

1. `${BASE_URL}/api/wechatV3_pay_app/` - GET - 微信支付
2. `${BASE_URL}/api/tcmNLP/AlipayView/` - POST - 支付宝支付
3. `${BASE_URL}/api/tcmNLP/CheckPaymentStatusView/` - POST - 检查支付状态
4. `${BASE_URL}/api/feedbacks/create/` - POST - 创建反馈

---

## 🎯 邀请系统API (3个)

1. `${BASE_URL}/api/invite_url/invite_api/my-code` - GET - 获取我的邀请码
2. `${BASE_URL}/api/invite_url/invite_api/scan/{id}` - POST - 扫描邀请码
3. `${BASE_URL}/api/invite_url/invite_api/records` - GET - 邀请记录

---

## 🍃 饮食建议API (2个)

1. `${BASE_URL}/api/tcmNLP/OwnAIdiet/` - POST - 个人AI饮食建议
2. `${BASE_URL}/api/tcmNLP/NLPdiet/` - POST - NLP饮食分析

---

## 🏥 医疗健康API (15个)

### 问卷系统
1. `${BASE_URL}/api/questionnaire/v1/analyze_questionnaire/` - POST - 分析用户问卷数据
2. `${BASE_URL}/api/questionnaire/v1/get_user_questionnaires/` - POST - 获取用户问卷列表
3. `${BASE_URL}/api/questionnaire/v1/get_latest_calculation/` - POST - 获取最新计算结果
4. `${BASE_URL}/api/questionnaire/v1/calculate_scores/` - POST - 计算问卷得分
5. `${BASE_URL}/api/questionnaire/v1/questionnaire/` - GET - 获取问卷
6. `${BASE_URL}/api/questionnaire/v1/user_info/` - GET - 获取用户信息
7. `${BASE_URL}/api/questionnaire/v1/update_user_info/` - POST - 更新用户信息
8. `${BASE_URL}/api/questionnaire/calculation_histories/` - GET - 计算历史
9. `${BASE_URL}/api/questionnaire/calculation_history/{id}/` - GET - 特定计算历史

### 体质分析
10. `${BASE_URL}/api/bank/AnalyzeQuestionnaireView/` - POST - 问卷分析视图
11. `${BASE_URL}/api/bank/AnalyzeQuestionnaireViewcomplex/` - POST - 复杂问卷分析
12. `${BASE_URL}/api/bank/GetUserQuestionnairesView/` - GET - 获取用户问卷视图
13. `${BASE_URL}/api/tizhi/history/{id}` - GET - 体质历史记录

### 健康排行
14. `${BASE_URL}/api/bank/HealthExpRankingView/` - POST - 健康经验排行榜

### 季节健康
15. `${BASE_URL}/api/tcmNLP/get_current_season/` - GET - 获取当前季节

---

## 🔍 预后搜索API

### 1. 获取热门关键词
- **URL**: `${BASE_URL}/api/routertest1/prognosis/popular-keywords`
- **方式**: GET
- **说明**: 获取搜索热门关键词

### 2. 获取搜索建议
- **URL**: `${BASE_URL}/api/routertest1/prognosis/search-suggestions`
- **方式**: GET
- **说明**: 获取搜索建议列表

### 3. 执行搜索
- **URL**: `${BASE_URL}/api/routertest1/prognosis/search`
- **方式**: POST
- **说明**: 执行预后搜索

### 4. 获取疗法分类
- **URL**: `${BASE_URL}/api/routertest1/prognosis/therapy-classifications`
- **方式**: GET
- **说明**: 获取疗法分类列表

### 5. 获取分类下的疗法
- **URL**: `${BASE_URL}/api/routertest1/prognosis/therapy-classifications/{id}/therapies`
- **方式**: GET
- **说明**: 获取指定分类下的疗法

### 6. 获取疗法详情
- **URL**: `${BASE_URL}/api/routertest1/prognosis/therapies/{id}/`
- **方式**: GET
- **说明**: 获取疗法详细信息

### 7. 获取所有疗法
- **URL**: `${BASE_URL}/api/routertest1/prognosis/therapies`
- **方式**: GET
- **说明**: 获取所有疗法列表（有缓存）

### 8. 疗法点赞
- **URL**: `${BASE_URL}/api/routertest1/prognosis/therapies/{id}/like/`
- **方式**: POST
- **说明**: 为疗法点赞

---

## 🔮 八字相关API (6个)

1. `${BASE_URL}/api/routertest1/bazi/basic` - POST - 获取八字基本信息
2. `${BASE_URL}/api/routertest1/bazi/complete` - POST - 获取八字完整分析数据
3. `${BASE_URL}/api/routertest1/bazi/shishen` - POST - 十神分析
4. `${BASE_URL}/api/routertest1/bazi/shensha` - POST - 神煞分析
5. `${BASE_URL}/api/routertest1/bazi/analysis` - POST - 八字分析
6. `${BASE_URL}/api/routertest1/bazi/destiny-transformation` - POST - 命运转化

---

## 📚 书籍管理API，宣布废弃，因为成本太高，无法盈利，且实际意义不行

### 1. 获取书籍列表 宣布废弃，因为成本太高，无法盈利，且实际意义不行
- **URL**: `${BASE_URL}/api/tcm_books/v2/books/`
- **方式**: GET
- **说明**: 获取中医书籍列表

### 2. 搜索书籍 宣布废弃，因为成本太高，无法盈利，且实际意义不行
- **URL**: `${BASE_URL}/api/tcm_books/v2/search/`
- **方式**: GET
- **说明**: 搜索中医书籍

### 3. 获取章节内容 宣布废弃，因为成本太高，无法盈利，且实际意义不行
- **URL**: `${BASE_URL}/api/tcm_books/books/{bookId}/{chapterLink}`
- **方式**: GET
- **说明**: 获取书籍章节内容



## 🔧 其他功能API (18个)

### 系统功能
1. `${BASE_URL}/api/ping/` - GET - 系统心跳检测
2. `${BASE_URL}/api/logout/` - POST - 用户登出
3. `${BASE_URL}/api/delete-account/` - DELETE - 删除账户
4. `${BASE_URL}/api/test-async/` - GET - 流式传输测试接口
5. `${BASE_URL}/api/qrcode/{id}` - GET - 生成二维码

### 中医功能 实际上都处在闲置状态
6. `${BASE_URL}/api/tcmNLP/ganzhi/` - GET - 干支查询
7. `${BASE_URL}/api/tcmNLP/TotalWeightsView/` - POST - 总权重视图
8. `${BASE_URL}/api/tcmNLP/Only_analysis_with_deepseek_symptoms/` - POST - DeepSeek症状分析 禁止性能测试，因为成本太高
9. `${BASE_URL}/api/tcmNLP/analysis_with_deepseek_For_Now_advice/` - POST - DeepSeek当前建议 禁止性能测试，因为成本太高

### 药物相关
10. `${BASE_URL}/api/ninja_api/drug/{id}` - GET - 获取药物详情

### 反馈系统
11. `${BASE_URL}/api/feedbacks/user/` - GET - 获取用户反馈
12. `${BASE_URL}/api/tcmchat/report/` - POST - 举报功能

### 静态资源（图片等）
13. `${BASE_URL}/default_avatar.png` - 默认头像
14. `${BASE_URL}/media/default_avatar.png` - 媒体默认头像
15. `${BASE_URL}/media/riyuellm/tubiao/level/normal/{id}` - 普通等级图标
16. `${BASE_URL}/media/riyuellm/lunbotu/pic1.jpg` - 轮播图1
17. `${BASE_URL}/media/riyuellm/tubiao/logo1.png` - Logo图标
18. `${BASE_URL}/media/riyuellm/tubiao/taiji.png` - 太极图标

---

## 🌐 WebSocket连接、注意不能压力测试，否则会破产 禁止性能测试，因为成本太高

### 1. 八字建议WebSocket 禁止性能测试，因为成本太高
- **URL**: `wss://riyuetcm.com/api/ws/bazi_advice/`
- **说明**: 八字建议流式输出连接

### 2. 深度分析WebSocket 禁止性能测试，因为成本太高
- **URL**: `wss://riyuetcm.com/api/ws/deepseek_chat_vip/`
- **说明**: VIP深度分析流式输出连接 

### 3. 健康提醒WebSocket 禁止性能测试，因为成本太高
- **URL**: `wss://riyuetcm.com/api/ws/liushi_health_reminder/`
- **说明**: 六时健康提醒流式输出连接







