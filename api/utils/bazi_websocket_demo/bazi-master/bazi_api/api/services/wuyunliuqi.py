"""
五运六气模块接口
负责加载和提供五运六气分析功能
"""
import os
import sys
import traceback
import importlib.util
from api.utils.logging import safe_print

# 添加路径以便导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

# 系统可用状态
WUYUNLIUQI_AVAILABLE = False
wuyunliuqi_core = None

def load_wuyunliuqi_module():
    """
    加载五运六气模块
    
    Returns:
        bool: 加载是否成功
    """
    global WUYUNLIUQI_AVAILABLE, wuyunliuqi_core
    
    # 尝试找到完整五运六气模块
    wuyunliuqi_module_paths = [
        # 添加可能的导入路径
        os.path.join(project_root, "bazi_api", "bazi_api", "complete_wuyunliuqi", "great_contribution.py"),
        "bazi_api.bazi_api.complete_wuyunliuqi.great_contribution",
        "bazi_api.complete_wuyunliuqi.great_contribution",
        "complete_wuyunliuqi.great_contribution",
    ]
    
    # 导入完整的五运六气系统
    wuyunliuqi_import_errors = []
    
    # 尝试多种导入方式
    for import_path in wuyunliuqi_module_paths:
        try:
            if import_path.endswith('.py'):
                # 直接从文件路径导入
                safe_print(f"尝试从文件导入: {import_path}")
                if os.path.exists(import_path):
                    spec = importlib.util.spec_from_file_location("great_contribution", import_path)
                    wuyunliuqi_core = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(wuyunliuqi_core)
                    safe_print(f"成功从文件路径导入: {import_path}")
                else:
                    safe_print(f"文件不存在: {import_path}")
                    continue
            else:
                # 从模块路径导入
                safe_print(f"尝试导入模块: {import_path}")
                wuyunliuqi_core = importlib.import_module(import_path)
                safe_print(f"成功导入模块: {import_path}")
            
            # 验证导入是否正确
            safe_print(f"导入成功，模块: {wuyunliuqi_core}")
            safe_print(f"模块属性: {dir(wuyunliuqi_core)}")
            
            # 测试函数可用性
            test_date = "2023-01-01"
            try:
                test_result = wuyunliuqi_core.getWuYunLiuQi(test_date)
                safe_print(f"五运六气系统测试成功: {test_result[:100]}...")
                WUYUNLIUQI_AVAILABLE = True
                safe_print(f"**** 成功导入五运六气系统: {import_path} ****")
                return True  # 成功导入，返回成功
            except Exception as test_e:
                safe_print(f"导入路径 {import_path} 的函数测试失败: {str(test_e)}")
                safe_print(f"错误详情: {traceback.format_exc()}")
                wuyunliuqi_import_errors.append((import_path, str(test_e), traceback.format_exc()))
        except ImportError as e:
            safe_print(f"导入路径 {import_path} 失败: {e}")
            wuyunliuqi_import_errors.append((import_path, str(e), traceback.format_exc()))
            continue
        except Exception as e:
            safe_print(f"尝试导入 {import_path} 时出现未知错误: {str(e)}")
            safe_print(f"错误详情: {traceback.format_exc()}")
            wuyunliuqi_import_errors.append((import_path, str(e), traceback.format_exc()))
            continue
    
    # 如果所有导入方式都失败
    safe_print("所有导入尝试都失败，五运六气系统不可用")
    safe_print("导入错误汇总:")
    for i, (path, error, traceback_str) in enumerate(wuyunliuqi_import_errors):
        safe_print(f"{i+1}. 路径: {path}")
        safe_print(f"   错误: {error}")
        safe_print(f"   详情: {traceback_str}")
        safe_print("---")
    
    return False

def get_wuyunliuqi(date):
    """
    获取指定日期的五运六气信息
    
    Args:
        date (str): 日期字符串，格式为 YYYY-MM-DD
        
    Returns:
        dict: 五运六气信息，如果模块不可用则返回错误信息
    """
    global WUYUNLIUQI_AVAILABLE, wuyunliuqi_core
    
    if not WUYUNLIUQI_AVAILABLE:
        return {"error": "五运六气系统不可用"}
    
    try:
        result_json = wuyunliuqi_core.getWuYunLiuQi(date)
        import json
        result_data = json.loads(result_json)[0]
        return result_data
    except Exception as e:
        safe_print(f"获取五运六气信息失败: {str(e)}")
        safe_print(traceback.format_exc())
        return {"error": f"获取五运六气信息失败: {str(e)}"}

# 初始化时加载模块
load_wuyunliuqi_module() 