# api/ninja_apis/bazi_analysis_api.py
"""
八字分析API端点
提供分析类型查询和配置管理
"""

from ninja import Router
from typing import List, Dict, Any
from django.http import JsonResponse, HttpResponse
from django.template import loader
from api.prompts import PromptManager, PromptCategory
import time
import logging

logger = logging.getLogger(__name__)

bazi_analysis_router = Router()
prompt_manager = PromptManager()


@bazi_analysis_router.get("/categories", summary="获取所有分析类型")
def get_analysis_categories(request) -> JsonResponse:
    """
    获取所有可用的八字分析类型
    
    Returns:
        包含所有分析类型的JSON响应
    """
    try:
        categories = []
        
        # 基础分析类
        basic_categories = PromptCategory.get_basic_categories()
        categories.append({
            "group": "basic",
            "group_name": "基础分析",
            "categories": [
                {
                    "type": category.value,
                    "name": PromptCategory.get_display_name(category),
                    "description": _get_category_description(category)
                }
                for category in basic_categories
            ]
        })
        
        # 专业分析类
        professional_categories = PromptCategory.get_professional_categories()
        categories.append({
            "group": "professional", 
            "group_name": "专业分析",
            "categories": [
                {
                    "type": category.value,
                    "name": PromptCategory.get_display_name(category),
                    "description": _get_category_description(category)
                }
                for category in professional_categories
            ]
        })
        
        # 时运分析类
        timing_categories = PromptCategory.get_timing_categories()
        categories.append({
            "group": "timing",
            "group_name": "时运分析", 
            "categories": [
                {
                    "type": category.value,
                    "name": PromptCategory.get_display_name(category),
                    "description": _get_category_description(category)
                }
                for category in timing_categories
            ]
        })
        
        return JsonResponse({
            "success": True,
            "data": {
                "categories": categories,
                "websocket_url": "/api/ws/bazi_analysis/",
                "total_count": sum(len(group["categories"]) for group in categories)
            }
        })
        
    except Exception as e:
        logger.error(f"获取分析类型失败: {str(e)}")
        return JsonResponse({
            "success": False,
            "error": f"获取分析类型失败: {str(e)}"
        }, status=500)


@bazi_analysis_router.get("/prompts/available", summary="获取可用的提示词")
def get_available_prompts(request) -> JsonResponse:
    """
    获取所有可用的提示词版本信息
    
    Returns:
        包含提示词版本信息的JSON响应
    """
    try:
        available_prompts = prompt_manager.list_available_prompts()
        
        prompt_info = []
        for category_value, versions in available_prompts.items():
            try:
                category = PromptCategory(category_value)
                prompt_info.append({
                    "category": category_value,
                    "name": PromptCategory.get_display_name(category),
                    "versions": versions,
                    "latest_version": max(versions) if versions else None
                })
            except ValueError:
                # 跳过无效的分类
                continue
        
        return JsonResponse({
            "success": True,
            "data": {
                "prompts": prompt_info,
                "total_categories": len(prompt_info)
            }
        })
        
    except Exception as e:
        logger.error(f"获取提示词信息失败: {str(e)}")
        return JsonResponse({
            "success": False,
            "error": f"获取提示词信息失败: {str(e)}"
        }, status=500)


@bazi_analysis_router.get("/prompts/content/{category}/{version}", summary="获取提示词内容")
def get_prompt_content(request, category: str, version: str) -> JsonResponse:
    """
    获取指定提示词的内容

    Args:
        category: 提示词分类
        version: 版本号

    Returns:
        提示词内容的JSON响应
    """
    try:
        # 验证分类是否有效
        try:
            cat_enum = PromptCategory(category)
        except ValueError:
            return JsonResponse({
                "success": False,
                "error": f"无效的分类: {category}"
            }, status=400)

        # 获取提示词内容
        prompt_content = prompt_manager.get_prompt(cat_enum, version)
        if not prompt_content:
            return JsonResponse({
                "success": False,
                "error": f"未找到提示词: {category} {version}"
            }, status=404)

        return JsonResponse({
            "success": True,
            "data": {
                "category": category,
                "version": version,
                "content": prompt_content,
                "display_name": PromptCategory.get_display_name(cat_enum)
            }
        })

    except Exception as e:
        logger.error(f"获取提示词内容失败: {str(e)}")
        return JsonResponse({
            "success": False,
            "error": f"获取提示词内容失败: {str(e)}"
        }, status=500)


@bazi_analysis_router.post("/prompts/reload", summary="重新加载提示词")
def reload_prompts(request) -> JsonResponse:
    """
    重新加载所有提示词（清除缓存）
    
    Returns:
        操作结果的JSON响应
    """
    try:
        prompt_manager.reload_prompts()
        
        return JsonResponse({
            "success": True,
            "message": "提示词已重新加载",
            "timestamp": str(time.time())
        })
        
    except Exception as e:
        logger.error(f"重新加载提示词失败: {str(e)}")
        return JsonResponse({
            "success": False,
            "error": f"重新加载提示词失败: {str(e)}"
        }, status=500)


@bazi_analysis_router.get("/manager", summary="提示词管理界面")
def prompt_manager_page(request):
    """
    提示词管理Web界面

    Returns:
        HTML页面响应
    """
    try:
        template = loader.get_template('prompt_manager.html')
        return HttpResponse(template.render({}, request))
    except Exception as e:
        logger.error(f"加载管理界面失败: {str(e)}")
        return HttpResponse(f"加载管理界面失败: {str(e)}", status=500)


def _get_category_description(category: PromptCategory) -> str:
    """获取分析类型的描述"""
    descriptions = {
        PromptCategory.HEALTH: "基于八字五行分析体质特点，提供个性化的健康养生建议",
        PromptCategory.WEALTH: "分析财星配置和财运走势，提供理财和投资建议",
        PromptCategory.CAREER: "分析职业天赋和事业发展，提供职场规划建议",
        PromptCategory.EDUCATION: "分析学习能力和学业运势，提供教育发展建议",
        PromptCategory.MARRIAGE: "分析感情特质和婚恋运势，提供情感经营建议",
        PromptCategory.FORTUNE: "分析命运走势和吉凶趋势，提供趋吉避凶建议",
        PromptCategory.WUXING: "深入分析五行强弱配置，提供五行调理方案",
        PromptCategory.SHISHEN: "详细解读十神组合特征，分析性格和能力特点",
        PromptCategory.SHENSHA: "解读神煞组合含义，分析特殊运势和注意事项",
        PromptCategory.LIUYAO: "运用六爻理论进行预测分析",
        PromptCategory.QIMEN: "运用奇门遁甲进行深度分析",
        PromptCategory.YEARLY: "分析流年运势变化和重要时机",
        PromptCategory.MONTHLY: "分析流月运势起伏和月度规划",
        PromptCategory.DAILY: "分析每日运势变化和日常指导",
        PromptCategory.COMPREHENSIVE: "综合多个维度进行全面分析",
        PromptCategory.PERSONALITY: "深度分析性格特征和行为模式",
        PromptCategory.RELATIONSHIP: "分析人际关系和社交特点",
    }
    return descriptions.get(category, "专业的八字分析服务")
