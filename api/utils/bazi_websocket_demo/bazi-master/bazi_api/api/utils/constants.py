"""
常量配置文件
存储系统提示词和其他常量
"""

import json
import os

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
prompts_file = os.path.join(current_dir, 'prompts.json')

# 从JSON文件加载提示词
def load_prompts():
    """从JSON文件加载提示词配置"""
    try:
        with open(prompts_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"警告：提示词文件 {prompts_file} 不存在")
        return {}
    except json.JSONDecodeError as e:
        print(f"错误：提示词文件格式错误 - {e}")
        return {}
    except Exception as e:
        print(f"错误：加载提示词文件时出错 - {e}")
        return {}

# 加载提示词配置
_prompts_config = load_prompts()

# 不同咨询类型的系统提示
SYSTEM_PROMPTS = _prompts_config.get('system_prompts', {})

# 不同咨询类型的提示模板
CONSULT_TEMPLATES = _prompts_config.get('consult_templates', {})

# 特殊健康提示词
SPECIAL_HEALTH_PROMPTS = _prompts_config.get('special_health_prompts', {})

# API系统消息
API_SYSTEM_MESSAGES = _prompts_config.get('api_system_messages', {})

# API用户提示词
API_USER_PROMPTS = _prompts_config.get('api_user_prompts', {})

# 算卦系统提示词
DIVINATION_SYSTEM_PROMPTS = _prompts_config.get('divination_system_prompts', {})

# 算卦提示词模板
DIVINATION_PROMPTS = _prompts_config.get('divination_prompts', {})

# 生肖列表
SHENGXIAO_LIST = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]

# 提供获取提示词的函数
def get_system_prompt(consult_type):
    """获取系统提示词"""
    return SYSTEM_PROMPTS.get(consult_type, SYSTEM_PROMPTS.get('health', ''))

def get_consult_template(consult_type, template_type):
    """获取咨询模板"""
    return CONSULT_TEMPLATES.get(consult_type, {}).get(template_type, '')

def get_special_health_prompt(template_type):
    """获取特殊健康提示词"""
    return SPECIAL_HEALTH_PROMPTS.get(template_type, '')

def get_api_system_message(consult_type):
    """获取API系统消息"""
    return API_SYSTEM_MESSAGES.get(consult_type, API_SYSTEM_MESSAGES.get('health', ''))

def get_api_user_prompt(consult_type, template_type):
    """获取API用户提示词"""
    return API_USER_PROMPTS.get(consult_type, {}).get(template_type, '')

def get_divination_system_prompt(prompt_type="general"):
    """获取算卦系统提示词"""
    return DIVINATION_SYSTEM_PROMPTS.get(prompt_type, DIVINATION_SYSTEM_PROMPTS.get('general', ''))

def get_divination_prompt(prompt_type):
    """获取算卦提示词模板"""
    return DIVINATION_PROMPTS.get(prompt_type, '')

# 重新加载提示词配置的函数（用于动态更新）
def reload_prompts():
    """重新加载提示词配置"""
    global _prompts_config, SYSTEM_PROMPTS, CONSULT_TEMPLATES, SPECIAL_HEALTH_PROMPTS, API_SYSTEM_MESSAGES, API_USER_PROMPTS, DIVINATION_SYSTEM_PROMPTS, DIVINATION_PROMPTS
    _prompts_config = load_prompts()
    SYSTEM_PROMPTS = _prompts_config.get('system_prompts', {})
    CONSULT_TEMPLATES = _prompts_config.get('consult_templates', {})
    SPECIAL_HEALTH_PROMPTS = _prompts_config.get('special_health_prompts', {})
    API_SYSTEM_MESSAGES = _prompts_config.get('api_system_messages', {})
    API_USER_PROMPTS = _prompts_config.get('api_user_prompts', {})
    DIVINATION_SYSTEM_PROMPTS = _prompts_config.get('divination_system_prompts', {})
    DIVINATION_PROMPTS = _prompts_config.get('divination_prompts', {})
    print("提示词配置已重新加载")