<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字分析与中医养生建议</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #b02a37;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #b02a37;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        button:hover {
            background-color: #a52834;
        }
        .results {
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .results h2 {
            color: #b02a37;
            margin-bottom: 15px;
        }
        .bazi-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .health-advice {
            background-color: #fff8f8;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #b02a37;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .dot-typing {
            position: relative;
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-right: 10px;
            background-color: #b02a37;
            border-radius: 50%;
            animation: dotTyping 1.5s infinite linear;
        }
        .dot-typing::before,
        .dot-typing::after {
            content: '';
            position: absolute;
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #b02a37;
            border-radius: 50%;
        }
        .dot-typing::before {
            left: -15px;
            animation: dotTyping 1.5s infinite linear;
            animation-delay: 0s;
        }
        .dot-typing::after {
            left: 15px;
            animation: dotTyping 1.5s infinite linear;
            animation-delay: 0.5s;
        }
        @keyframes dotTyping {
            0% { transform: scale(1); }
            25% { transform: scale(1.3); }
            50% { transform: scale(1); }
        }
        .wuxing-chart {
            margin-top: 15px;
            height: 200px;
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
        }
        .wuxing-bar {
            width: 15%;
            background-color: #b02a37;
            text-align: center;
            color: white;
            padding-top: 10px;
            border-radius: 4px 4px 0 0;
            position: relative;
        }
        .wuxing-bar span {
            position: absolute;
            bottom: -25px;
            left: 0;
            right: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>八字分析与中医养生建议</h1>
        
        <div class="form-group">
            <label for="birth-year">出生年份</label>
            <input type="number" id="birth-year" placeholder="如：1990" min="1900" max="2100">
        </div>
        
        <div class="form-group">
            <label for="birth-month">出生月份</label>
            <input type="number" id="birth-month" placeholder="1-12" min="1" max="12">
        </div>
        
        <div class="form-group">
            <label for="birth-day">出生日期</label>
            <input type="number" id="birth-day" placeholder="1-31" min="1" max="31">
        </div>
        
        <div class="form-group">
            <label for="birth-time">出生时辰（小时）</label>
            <input type="number" id="birth-time" placeholder="0-23" min="0" max="23">
        </div>
        
        <button id="analyze-btn">分析八字和养生建议</button>
        
        <div class="loading" id="loading">
            <div class="dot-typing"></div>
            <p>正在分析您的八字和生成养生建议，请稍候...</p>
        </div>
        
        <div class="results" id="results" style="display: none;">
            <h2>您的八字分析结果</h2>
            
            <div class="bazi-info" id="bazi-info">
                <p><strong>生辰八字：</strong> <span id="shenchenbazi"></span></p>
                <p><strong>生肖：</strong> <span id="shengxiao"></span></p>
                <p><strong>五行分析：</strong></p>
                <div class="wuxing-chart" id="wuxing-chart">
                    <!-- 五行柱状图将在这里动态生成 -->
                </div>
            </div>
            
            <h2>中医养生建议</h2>
            <div class="health-advice" id="health-advice">
                <!-- 养生建议将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const analyzeBtn = document.getElementById('analyze-btn');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const shenchenbaziEl = document.getElementById('shenchenbazi');
            const shengxiaoEl = document.getElementById('shengxiao');
            const wuxingChartEl = document.getElementById('wuxing-chart');
            const healthAdviceEl = document.getElementById('health-advice');
            
            let socket = null;
            
            analyzeBtn.addEventListener('click', function() {
                const birthYear = parseInt(document.getElementById('birth-year').value);
                const birthMonth = parseInt(document.getElementById('birth-month').value);
                const birthDay = parseInt(document.getElementById('birth-day').value);
                const birthTime = parseInt(document.getElementById('birth-time').value);
                
                // 验证输入
                if (!birthYear || !birthMonth || !birthDay || isNaN(birthTime)) {
                    alert('请填写完整的出生信息');
                    return;
                }
                
                // 重置结果区域
                healthAdviceEl.innerHTML = '';
                results.style.display = 'none';
                loading.style.display = 'block';
                
                // 关闭之前的WebSocket连接（如果有）
                if (socket !== null) {
                    socket.close();
                }
                
                // 创建WebSocket连接
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/bazi_advice/`;
                socket = new WebSocket(wsUrl);
                
                socket.onopen = function(e) {
                    console.log('WebSocket 连接已打开', e);
                    // 发送八字分析请求
                    socket.send(JSON.stringify({
                        birth_info: {
                            birth_year: birthYear,
                            birth_month: birthMonth,
                            birth_day: birthDay,
                            birth_time: birthTime
                        }
                    }));
                };
                
                socket.onmessage = function(e) {
                    console.log('收到WebSocket消息:', e.data);
                    const data = JSON.parse(e.data);
                    
                    // 处理错误
                    if (data.error) {
                        loading.style.display = 'none';
                        alert('发生错误: ' + data.error);
                        return;
                    }
                    
                    // 处理八字结果
                    if (data.type === 'bazi_result') {
                        shenchenbaziEl.textContent = data.shenchenbazi;
                        shengxiaoEl.textContent = data.shengxiao;
                        
                        // 生成五行图表
                        wuxingChartEl.innerHTML = '';
                        for (const [wuxing, value] of Object.entries(data.wuxing)) {
                            const height = value * 200;  // 最高高度为200px
                            const bar = document.createElement('div');
                            bar.className = 'wuxing-bar';
                            bar.style.height = `${height}px`;
                            bar.innerHTML = `${(value * 100).toFixed(0)}%<span>${wuxing}</span>`;
                            wuxingChartEl.appendChild(bar);
                        }
                        
                        results.style.display = 'block';
                    }
                    
                    // 处理健康建议
                    if (data.type === 'health_advice') {
                        if (data.content) {
                            healthAdviceEl.innerHTML += data.content;
                        }
                        
                        if (data.done) {
                            loading.style.display = 'none';
                        }
                    }
                };
                
                socket.onclose = function(e) {
                    console.log('WebSocket 连接已关闭');
                    loading.style.display = 'none';
                };
                
                socket.onerror = function(e) {
                    console.error('WebSocket 错误:', e);
                    loading.style.display = 'none';
                    alert('WebSocket 连接错误，请刷新页面重试');
                };
            });
        });
    </script>
</body>
</html> 