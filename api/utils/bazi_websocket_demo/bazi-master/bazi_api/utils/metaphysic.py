def getCurrentBazi():
    """
    获取当前时间的八字
    返回格式：{'year': '乙巳', 'month': '丙辰', 'day': '丁巳', 'hour': '丙午'}
    """
    # 获取当前时间（东八区）
    current_time = datetime.now(pytz.timezone('Asia/Shanghai'))
    
    # 获取年月日时的数字
    year = current_time.year
    month = current_time.month
    day = current_time.day
    hour = current_time.hour
    
    # 计算八字
    gz_year = getYearGZ(year)
    gz_month = getMonthGZ(year, month)
    gz_day = getDayGZ(year, month, day)
    gz_hour = getHourGZ(year, month, day, hour)
    
    return {
        'year': gz_year,
        'month': gz_month,
        'day': gz_day,
        'hour': gz_hour
    }

def getCurrentWuXing(year):
    """
    根据年份获取五运六气信息
    
    Args:
        year (int): 年份，如2024
        
    Returns:
        dict: 包含五运和六气信息的字典
        {
            "wuxing": "木运不及",
            "liuqi": "少阳相火司天，厥阴风木在泉"
        }
    """
    try:
        # 计算年份的五运
        year_remainder = year % 10
        wuxing_map = {
            0: "土运太过",
            1: "金运不及",
            2: "金运太过",
            3: "水运不及",
            4: "水运太过",
            5: "木运不及",
            6: "木运太过",
            7: "火运不及",
            8: "火运太过",
            9: "土运不及"
        }
        wuxing = wuxing_map[year_remainder]
        
        # 计算年份的六气
        liuqi_map = {
            0: "太阳寒水司天，少阴君火在泉",
            1: "厥阴风木司天，太阴湿土在泉",
            2: "少阴君火司天，少阳相火在泉",
            3: "太阴湿土司天，阳明燥金在泉",
            4: "少阳相火司天，厥阴风木在泉",
            5: "阳明燥金司天，太阳寒水在泉",
            6: "太阳寒水司天，少阴君火在泉",
            7: "厥阴风木司天，太阴湿土在泉",
            8: "少阴君火司天，少阳相火在泉",
            9: "太阴湿土司天，阳明燥金在泉"
        }
        liuqi = liuqi_map[year_remainder]
        
        return {
            "wuxing": wuxing,
            "liuqi": liuqi
        }
    except Exception as e:
        raise ValueError(f"计算五运六气出错: {str(e)}") 