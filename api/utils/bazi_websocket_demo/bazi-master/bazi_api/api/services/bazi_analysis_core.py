"""
八字分析核心服务模块
提供模块化的八字分析功能，支持组件化调用
"""

import sys
import os
from datetime import datetime
import json

from lunar_python import Solar, Lunar

# 导入神煞计算器
try:
    from shensha_calculator import query_shensha
    SHENSHA_AVAILABLE = True
except ImportError:
    SHENSHA_AVAILABLE = False


class BaziAnalysisCore:
    """八字分析核心类"""
    
    def __init__(self, year, month, day, hour=0, minute=0, second=0, gender=1, sect=2):
        """
        初始化八字分析
        
        Args:
            year: 年份
            month: 月份
            day: 日期
            hour: 小时 (默认0)
            minute: 分钟 (默认0)
            second: 秒钟 (默认0)
            gender: 性别 (1男0女，默认1)
            sect: 流派 (1或2，默认2)
        """
        self.year = year
        self.month = month
        self.day = day
        self.hour = hour
        self.minute = minute
        self.second = second
        self.gender = gender
        self.sect = sect
        
        # 创建阳历和农历对象
        self.solar = Solar.fromYmdHms(year, month, day, hour, minute, second)
        self.lunar = self.solar.getLunar()
        self.eight_char = self.lunar.getEightChar()
        self.eight_char.setSect(sect)
    
    def get_basic_info(self):
        """获取基本信息"""
        return {
            "solar_date": self.solar.toFullString(),
            "lunar_date": self.lunar.toFullString(),
            "eight_char": str(self.eight_char),
            "gender": "男" if self.gender == 1 else "女",
            "sect": self.sect,
            "year_pillar": self.eight_char.getYear(),
            "month_pillar": self.eight_char.getMonth(),
            "day_pillar": self.eight_char.getDay(),
            "time_pillar": self.eight_char.getTime()
        }
    
    def get_four_pillars_detail(self):
        """获取四柱详细信息"""
        return {
            "year_pillar": {
                "ganzhi": self.eight_char.getYear(),
                "gan": self.eight_char.getYearGan(),
                "zhi": self.eight_char.getYearZhi(),
                "wuxing": self.eight_char.getYearWuXing(),
                "nayin": self.eight_char.getYearNaYin(),
                "hide_gan": self.eight_char.getYearHideGan(),
                "dishi": self.eight_char.getYearDiShi(),
                "xun": self.eight_char.getYearXun(),
                "xunkong": self.eight_char.getYearXunKong()
            },
            "month_pillar": {
                "ganzhi": self.eight_char.getMonth(),
                "gan": self.eight_char.getMonthGan(),
                "zhi": self.eight_char.getMonthZhi(),
                "wuxing": self.eight_char.getMonthWuXing(),
                "nayin": self.eight_char.getMonthNaYin(),
                "hide_gan": self.eight_char.getMonthHideGan(),
                "dishi": self.eight_char.getMonthDiShi(),
                "xun": self.eight_char.getMonthXun(),
                "xunkong": self.eight_char.getMonthXunKong()
            },
            "day_pillar": {
                "ganzhi": self.eight_char.getDay(),
                "gan": self.eight_char.getDayGan(),
                "zhi": self.eight_char.getDayZhi(),
                "wuxing": self.eight_char.getDayWuXing(),
                "nayin": self.eight_char.getDayNaYin(),
                "hide_gan": self.eight_char.getDayHideGan(),
                "dishi": self.eight_char.getDayDiShi(),
                "xun": self.eight_char.getDayXun(),
                "xunkong": self.eight_char.getDayXunKong()
            },
            "time_pillar": {
                "ganzhi": self.eight_char.getTime(),
                "gan": self.eight_char.getTimeGan(),
                "zhi": self.eight_char.getTimeZhi(),
                "wuxing": self.eight_char.getTimeWuXing(),
                "nayin": self.eight_char.getTimeNaYin(),
                "hide_gan": self.eight_char.getTimeHideGan(),
                "dishi": self.eight_char.getTimeDiShi(),
                "xun": self.eight_char.getTimeXun(),
                "xunkong": self.eight_char.getTimeXunKong()
            }
        }
    
    def get_shishen_analysis(self):
        """获取十神分析"""
        return {
            "gan_shishen": {
                "year": self.eight_char.getYearShiShenGan(),
                "month": self.eight_char.getMonthShiShenGan(),
                "day": self.eight_char.getDayShiShenGan(),
                "time": self.eight_char.getTimeShiShenGan()
            },
            "zhi_shishen": {
                "year": self.eight_char.getYearShiShenZhi(),
                "month": self.eight_char.getMonthShiShenZhi(),
                "day": self.eight_char.getDayShiShenZhi(),
                "time": self.eight_char.getTimeShiShenZhi()
            },
            "hide_gan_shishen": {
                "year": {
                    "hide_gan": self.eight_char.getYearHideGan(),
                    "shishen": self.eight_char.getYearShiShenZhi()
                },
                "month": {
                    "hide_gan": self.eight_char.getMonthHideGan(),
                    "shishen": self.eight_char.getMonthShiShenZhi()
                },
                "day": {
                    "hide_gan": self.eight_char.getDayHideGan(),
                    "shishen": self.eight_char.getDayShiShenZhi()
                },
                "time": {
                    "hide_gan": self.eight_char.getTimeHideGan(),
                    "shishen": self.eight_char.getTimeShiShenZhi()
                }
            }
        }
    
    def get_dishi_analysis(self):
        """获取长生十二神分析"""
        return {
            "year_dishi": self.eight_char.getYearDiShi(),
            "month_dishi": self.eight_char.getMonthDiShi(),
            "day_dishi": self.eight_char.getDayDiShi(),
            "time_dishi": self.eight_char.getTimeDiShi()
        }
    
    def get_special_positions(self):
        """获取特殊宫位"""
        return {
            "taiyuan": {
                "ganzhi": self.eight_char.getTaiYuan(),
                "nayin": self.eight_char.getTaiYuanNaYin()
            },
            "taixi": {
                "ganzhi": self.eight_char.getTaiXi(),
                "nayin": self.eight_char.getTaiXiNaYin()
            },
            "minggong": {
                "ganzhi": self.eight_char.getMingGong(),
                "nayin": self.eight_char.getMingGongNaYin()
            },
            "shengong": {
                "ganzhi": self.eight_char.getShenGong(),
                "nayin": self.eight_char.getShenGongNaYin()
            }
        }
    
    def get_nine_star_analysis(self):
        """获取九星信息"""
        year_nine_star = self.lunar.getYearNineStar(self.sect)
        month_nine_star = self.lunar.getMonthNineStar(self.sect)
        day_nine_star = self.lunar.getDayNineStar()
        time_nine_star = self.lunar.getTimeNineStar()
        
        def format_nine_star(nine_star):
            return {
                "name": nine_star.toString(),
                "number": nine_star.getNumber(),
                "color": nine_star.getColor(),
                "wuxing": nine_star.getWuXing(),
                "position": nine_star.getPosition(),
                "position_desc": nine_star.getPositionDesc(),
                "beidou": nine_star.getNameInBeiDou(),
                "xuankong": {
                    "name": nine_star.getNameInXuanKong(),
                    "luck": nine_star.getLuckInXuanKong()
                },
                "qimen": {
                    "name": nine_star.getNameInQiMen(),
                    "luck": nine_star.getLuckInQiMen()
                },
                "taiyi": {
                    "name": nine_star.getNameInTaiYi(),
                    "type": nine_star.getTypeInTaiYi()
                }
            }
        
        return {
            "year_nine_star": format_nine_star(year_nine_star),
            "month_nine_star": format_nine_star(month_nine_star),
            "day_nine_star": format_nine_star(day_nine_star),
            "time_nine_star": format_nine_star(time_nine_star)
        }
    
    def get_fortune_analysis(self):
        """获取运势分析"""
        yun = self.eight_char.getYun(self.gender, self.sect)
        
        # 大运信息
        da_yun_list = yun.getDaYun(8)
        da_yun_info = []
        
        for i, da_yun in enumerate(da_yun_list):
            if i == 0 and da_yun.getIndex() < 1:
                da_yun_info.append({
                    "index": i,
                    "start_year": da_yun.getStartYear(),
                    "end_year": da_yun.getEndYear(),
                    "start_age": da_yun.getStartAge(),
                    "end_age": da_yun.getEndAge(),
                    "ganzhi": "起运前",
                    "is_pre_start": True
                })
            else:
                da_yun_info.append({
                    "index": i,
                    "start_year": da_yun.getStartYear(),
                    "end_year": da_yun.getEndYear(),
                    "start_age": da_yun.getStartAge(),
                    "end_age": da_yun.getEndAge(),
                    "ganzhi": da_yun.getGanZhi(),
                    "is_pre_start": False
                })
        
        # 流年信息（第一步大运的前5年）
        liu_nian_info = []
        if len(da_yun_list) > 1:
            first_da_yun = da_yun_list[1] if da_yun_list[0].getIndex() < 1 else da_yun_list[0]
            liu_nian_list = first_da_yun.getLiuNian(5)
            for i, liu_nian in enumerate(liu_nian_list):
                liu_nian_info.append({
                    "index": i,
                    "year": liu_nian.getYear(),
                    "age": liu_nian.getAge(),
                    "ganzhi": liu_nian.getGanZhi()
                })
        
        return {
            "start_info": {
                "start_year": yun.getStartYear(),
                "start_month": yun.getStartMonth(),
                "start_day": yun.getStartDay(),
                "start_solar": yun.getStartSolar().toYmd()
            },
            "da_yun": da_yun_info,
            "liu_nian": liu_nian_info
        }

    def get_shensha_analysis(self):
        """获取神煞分析"""
        if not SHENSHA_AVAILABLE:
            return {"error": "神煞计算器模块未安装"}

        try:
            # 构造八字数组
            bazi = [
                self.eight_char.getYearGan(), self.eight_char.getYearZhi(),
                self.eight_char.getMonthGan(), self.eight_char.getMonthZhi(),
                self.eight_char.getDayGan(), self.eight_char.getDayZhi(),
                self.eight_char.getTimeGan(), self.eight_char.getTimeZhi()
            ]

            # 获取年柱纳音
            nian_nayin = self.eight_char.getYearNaYin()

            # 性别转换
            is_man = self.gender == 1

            # 构造四柱信息
            pillars = [
                ("年柱", self.eight_char.getYear(), 1),
                ("月柱", self.eight_char.getMonth(), 2),
                ("日柱", self.eight_char.getDay(), 3),
                ("时柱", self.eight_char.getTime(), 4)
            ]

            shensha_results = {}
            all_shenshas = []

            # 分析每一柱的神煞
            for name, ganzhi, witch in pillars:
                try:
                    shenshas = query_shensha(ganzhi, bazi, is_man, witch, nian_nayin)
                    shensha_results[name] = {
                        "ganzhi": ganzhi,
                        "shenshas": shenshas,
                        "count": len(shenshas)
                    }
                    all_shenshas.extend(shenshas)
                except Exception as e:
                    shensha_results[name] = {
                        "ganzhi": ganzhi,
                        "error": str(e)
                    }

            # 统计信息
            unique_shenshas = list(set(all_shenshas))

            # 神煞分类
            categories = {
                "贵人类": ["天乙", "太极", "文昌", "国印", "金舆", "福星", "天厨", "德秀"],
                "德合类": ["天德", "月德", "天德合", "月德合", "天赦"],
                "星煞类": ["驿马", "华盖", "将星", "禄神", "红鸾", "天喜", "五鬼", "天医", "学堂", "词馆"],
                "刃煞类": ["羊刃", "飞刃", "血刃", "金神"],
                "凶煞类": ["空亡", "劫煞", "灾煞", "亡神", "孤辰", "寡宿", "天罗", "地网", "十恶大败",
                          "桃花", "孤鸾", "阴差阳错", "四废", "丧门", "吊客", "披麻", "童子", "流霞",
                          "红艳", "魁罡", "八专", "九丑", "十灵", "元辰"]
            }

            category_stats = {}
            for category, shensha_list in categories.items():
                category_shenshas = [s for s in unique_shenshas if s in shensha_list]
                if category_shenshas:
                    category_stats[category] = category_shenshas

            return {
                "pillars": shensha_results,
                "total_count": len(all_shenshas),
                "unique_count": len(unique_shenshas),
                "unique_shenshas": sorted(unique_shenshas),
                "categories": category_stats
            }

        except Exception as e:
            return {"error": f"神煞分析出错: {e}"}


def create_bazi_analyzer(year, month, day, hour=0, minute=0, second=0, gender=1, sect=2):
    """
    创建八字分析器实例的工厂函数

    Args:
        year: 年份
        month: 月份
        day: 日期
        hour: 小时 (默认0)
        minute: 分钟 (默认0)
        second: 秒钟 (默认0)
        gender: 性别 (1男0女，默认1)
        sect: 流派 (1或2，默认2)

    Returns:
        BaziAnalysisCore: 八字分析器实例
    """
    return BaziAnalysisCore(year, month, day, hour, minute, second, gender, sect)
