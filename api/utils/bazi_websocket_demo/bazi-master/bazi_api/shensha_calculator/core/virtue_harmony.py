"""
德合类神煞计算

包含天德、月德、天德合、月德合、天赦等德合类神煞的计算函数。
"""


def tiandeguiren(month_zhi: str, ganzhi: str) -> int:
    """
    天德贵人
    正月生者见丁, 二月生者见申, 三月生者见壬, 四月生者见辛,
    五月生者见亥, 六月生者见甲, 七月生者见癸, 八月生者见寅,
    九月生者见丙, 十月生者见乙,十一月生者见巳, 十二月生者见庚.
    查法：以月支查四柱干支
    
    Args:
        month_zhi: 月支
        ganzhi: 干或支
        
    Returns:
        1表示是天德贵人，0表示不是
    """
    conditions = {
        "寅": "丁",  # 正月
        "卯": "申",  # 二月
        "辰": "壬",  # 三月
        "巳": "辛",  # 四月
        "午": "亥",  # 五月
        "未": "甲",  # 六月
        "申": "癸",  # 七月
        "酉": "寅",  # 八月
        "戌": "丙",  # 九月
        "亥": "乙",  # 十月
        "子": "巳",  # 十一月
        "丑": "庚"   # 十二月
    }
    
    return 1 if conditions.get(month_zhi) == ganzhi else 0


def yuede(month_zhi: str, tiangan: str) -> int:
    """
    月德贵人
    寅午戌月生者见丙, 申子辰月生者见壬,
    亥卯未月生者见甲,巳酉丑月生者见庚.
    凡柱中年月日时干上见者为有月德贵人.
    查法：以月支查四柱干支
    
    Args:
        month_zhi: 月支
        tiangan: 天干
        
    Returns:
        1表示是月德贵人，0表示不是
    """
    conditions = {
        "寅": "丙", "午": "丙", "戌": "丙",
        "申": "壬", "子": "壬", "辰": "壬",
        "亥": "甲", "卯": "甲", "未": "甲",
        "巳": "庚", "酉": "庚", "丑": "庚"
    }
    
    return 1 if conditions.get(month_zhi) == tiangan else 0


def tiandehe(month_zhi: str, ganzhi: str) -> int:
    """
    天德合
    寅月壬。卯月巳。辰月丁。巳月丙。午月寅。未月己。
    申月戊。酉月亥。戌月辛。亥月庚。子月申。丑月乙。
    查法：以月支查其它干/支
    
    Args:
        month_zhi: 月支
        ganzhi: 天干或地支
        
    Returns:
        1表示是天德合，0表示不是
    """
    conditions = {
        "寅": "壬",
        "卯": "巳",
        "辰": "丁",
        "巳": "丙",
        "午": "寅",
        "未": "己",
        "申": "戊",
        "酉": "亥",
        "戌": "辛",
        "亥": "庚",
        "子": "申",
        "丑": "乙"
    }
    
    return 1 if conditions.get(month_zhi) == ganzhi else 0


def yuedehe(month_zhi: str, tiangan: str) -> int:
    """
    月德合
    寅午戌月见辛，申子辰月见丁，巳酉丑月见乙，亥卯未月见己。
    查法：以月支查天干
    
    Args:
        month_zhi: 月支
        tiangan: 天干
        
    Returns:
        1表示是月德合，0表示不是
    """
    conditions = {
        "辛": ["寅", "午", "戌"],
        "丁": ["申", "子", "辰"],
        "乙": ["巳", "酉", "丑"],
        "己": ["亥", "卯", "未"]
    }
    
    return 1 if conditions.get(tiangan, []) and month_zhi in conditions[tiangan] else 0


def tianshe(month_zhi: str, day_gan: str, day_zhi: str) -> int:
    """
    天赦
    春戊寅, 夏甲午, 秋戊申, 冬甲子.
    查法: 寅卯辰月生戊寅日, 巳午未月生甲午日, 申酉戌月生戊申日, 亥子丑月生甲子日.
    
    Args:
        month_zhi: 月支
        day_gan: 日干
        day_zhi: 日支
        
    Returns:
        1表示是天赦，0表示不是
    """
    conditions = {
        "寅": day_gan == "戊" and day_zhi == "寅",
        "卯": day_gan == "戊" and day_zhi == "寅",
        "辰": day_gan == "戊" and day_zhi == "寅",
        "巳": day_gan == "甲" and day_zhi == "午",
        "午": day_gan == "甲" and day_zhi == "午",
        "未": day_gan == "甲" and day_zhi == "午",
        "申": day_gan == "戊" and day_zhi == "申",
        "酉": day_gan == "戊" and day_zhi == "申",
        "戌": day_gan == "戊" and day_zhi == "申",
        "亥": day_gan == "甲" and day_zhi == "子",
        "子": day_gan == "甲" and day_zhi == "子",
        "丑": day_gan == "甲" and day_zhi == "子"
    }
    
    return 1 if conditions.get(month_zhi, False) else 0
