"""
神煞计算器 - Python版本

这是一个用于计算八字神煞的Python库，从JavaScript版本重构而来。
提供组件化、模块化的设计，便于集成到其他项目中。

主要功能：
- 支持40+种神煞的计算
- 提供简洁的API接口
- 输入八字，输出神煞列表
- 完全兼容原JavaScript版本的计算逻辑

使用示例：
    from shensha_calculator import query_shensha
    
    # 八字：甲子年、丙寅月、戊午日、癸亥时
    bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]
    
    # 查询日柱神煞
    result = query_shensha("戊午", bazi, True, 3, "海中金")
    print(result)  # ['天乙', '文昌', ...]
"""

from .api.query import query_shensha

__version__ = "1.0.0"
__author__ = "重构自JavaScript版本"

# 导出主要接口
__all__ = ["query_shensha"]
