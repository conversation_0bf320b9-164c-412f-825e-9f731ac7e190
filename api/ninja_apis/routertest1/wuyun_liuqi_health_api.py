"""
五运六气健康分析API
基于五运六气外因和用户体质内因的健康状态分析
集成到routertest1模块中
"""

from ninja import Router, Schema
from typing import Optional
from datetime import datetime
import pytz
import yaml
import os

from api.services.wuyun_liuqi_service import WuyunLiuqiService

# 使用routertest1的路由实例，而不是创建新的
from api.ninja_apis.routertest1.test_api import routertest1_router

class WuyunLiuqiHealthRequest(Schema):
    """五运六气健康分析请求"""
    eight_char: str  # 八字
    birth_date: str  # 出生日期 YYYY-MM-DD
    analysis_date: Optional[str] = None  # 分析日期，默认为当前日期
    gender: Optional[str] = "unknown"  # 性别

class WuyunLiuqiHealthResponse(Schema):
    """五运六气健康分析响应"""
    success: bool
    message: str
    data: Optional[dict] = None

def load_health_wuyun_liuqi_template():
    """加载五运六气健康分析提示词模板"""
    try:
        template_path = os.path.join(
            os.path.dirname(__file__), 
            "..", "..", 
            "prompts", 
            "templates", 
            "health_wuyun_liuqi_v1.yaml"
        )
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_data = yaml.safe_load(f)
            return template_data['prompt']
    except Exception as e:
        return f"提示词模板加载失败: {str(e)}"

@routertest1_router.post("/wuyun-liuqi-health/analyze", response=WuyunLiuqiHealthResponse)
def analyze_wuyun_liuqi_health(request, data: WuyunLiuqiHealthRequest):
    """
    五运六气健康分析
    
    基于用户八字体质（内因）和当前五运六气环境（外因），
    分析健康状态和疾病变化趋势
    """
    try:
        # 检查五运六气系统是否可用
        if not WuyunLiuqiService.is_available():
            return WuyunLiuqiHealthResponse(
                success=False,
                message="五运六气系统不可用，请检查系统配置"
            )
        
        # 确定分析日期
        if data.analysis_date:
            analysis_date = data.analysis_date
        else:
            # 使用当前日期（东八区）
            current_time = datetime.now(pytz.timezone('Asia/Shanghai'))
            analysis_date = current_time.strftime("%Y-%m-%d")
        
        # 获取五运六气数据
        wuyun_result = WuyunLiuqiService.get_birth_and_current_wuyun_liuqi(data.birth_date)
        
        if not wuyun_result["comparison"]["available"]:
            return WuyunLiuqiHealthResponse(
                success=False,
                message="获取五运六气数据失败"
            )
        
        # 格式化当前日期的五运六气数据用于分析
        current_wuyun_formatted = WuyunLiuqiService.format_wuyun_liuqi_for_analysis(
            wuyun_result["current"]
        )
        
        if not current_wuyun_formatted.get("formatted"):
            return WuyunLiuqiHealthResponse(
                success=False,
                message="五运六气数据格式化失败"
            )
        
        # 加载提示词模板
        prompt_template = load_health_wuyun_liuqi_template()
        
        # 构建WebSocket消息格式的数据
        websocket_data = {
            "type": "health_wuyun_liuqi_analysis",
            "data": {
                "eight_char": data.eight_char,
                "birth_date": data.birth_date,
                "analysis_date": analysis_date,
                "gender": data.gender,
                "wuyun_liuqi": current_wuyun_formatted["analysis_data"]
            }
        }
        
        # 返回分析结果 - 只包含前端需要的数据
        result_data = {
            "analysis_info": {
                "eight_char": data.eight_char,
                "birth_date": data.birth_date,
                "analysis_date": analysis_date,
                "gender": data.gender
            },
            "wuyun_liuqi_summary": {
                "current_date": analysis_date,
                "qi_phase": current_wuyun_formatted["summary"]["main_qi"],
                "season_influence": current_wuyun_formatted["summary"]["season_influence"],
                "solar_term": current_wuyun_formatted["summary"]["current_solar_term"]
            },
            "websocket_message": websocket_data
        }
        
        return WuyunLiuqiHealthResponse(
            success=True,
            message="五运六气健康分析数据准备完成",
            data=result_data
        )
        
    except Exception as e:
        return WuyunLiuqiHealthResponse(
            success=False,
            message=f"分析过程出错: {str(e)}"
        )

@routertest1_router.get("/wuyun-liuqi-health/current")
def get_current_wuyun_liuqi_info(request):
    """
    获取当前五运六气信息
    """
    try:
        if not WuyunLiuqiService.is_available():
            return {
                "success": False,
                "message": "五运六气系统不可用"
            }
        
        current_data = WuyunLiuqiService.get_current_wuyun_liuqi()
        
        return {
            "success": True,
            "message": "获取当前五运六气信息成功",
            "data": current_data
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"获取五运六气信息失败: {str(e)}"
        }

@routertest1_router.get("/wuyun-liuqi-health/status")
def get_wuyun_liuqi_system_status(request):
    """
    获取五运六气系统状态
    """
    try:
        status = WuyunLiuqiService.get_system_status()
        
        return {
            "success": True,
            "message": "系统状态获取成功",
            "data": status
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"获取系统状态失败: {str(e)}"
        }

@routertest1_router.get("/wuyun-liuqi-health/template")
def get_prompt_template(request):
    """
    获取五运六气健康分析提示词模板
    """
    try:
        template = load_health_wuyun_liuqi_template()
        
        return {
            "success": True,
            "message": "获取提示词模板成功",
            "data": {
                "template": template,
                "template_file": "api/prompts/templates/health_wuyun_liuqi_v1.yaml",
                "usage": "此模板用于WebSocket消费者中的五运六气健康分析"
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"获取提示词模板失败: {str(e)}"
        }

@routertest1_router.post("/wuyun-liuqi-health/test")
def test_wuyun_liuqi_analysis(request):
    """
    测试五运六气健康分析功能
    使用示例数据进行测试
    """
    try:
        # 示例数据
        test_data = WuyunLiuqiHealthRequest(
            eight_char="甲子 乙丑 丙寅 丁卯",
            birth_date="1990-05-15",
            gender="male"
        )

        # 调用分析函数
        result = analyze_wuyun_liuqi_health(request, test_data)

        return {
            "success": True,
            "message": "五运六气健康分析测试完成",
            "test_data": test_data.dict(),
            "analysis_result": result.dict()
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"测试失败: {str(e)}"
        }

@routertest1_router.post("/wuyun-liuqi-health/debug", include_in_schema=False)
def debug_wuyun_liuqi_analysis(request, data: WuyunLiuqiHealthRequest):
    """
    开发者调试接口 - 返回完整的分析数据和使用说明
    不包含在API文档中，仅供开发调试使用
    """
    try:
        # 检查五运六气系统是否可用
        if not WuyunLiuqiService.is_available():
            return {
                "success": False,
                "message": "五运六气系统不可用，请检查系统配置"
            }

        # 确定分析日期
        if data.analysis_date:
            analysis_date = data.analysis_date
        else:
            current_time = datetime.now(pytz.timezone('Asia/Shanghai'))
            analysis_date = current_time.strftime("%Y-%m-%d")

        # 获取五运六气数据
        wuyun_result = WuyunLiuqiService.get_birth_and_current_wuyun_liuqi(data.birth_date)

        if not wuyun_result["comparison"]["available"]:
            return {
                "success": False,
                "message": "获取五运六气数据失败"
            }

        # 格式化当前日期的五运六气数据用于分析
        current_wuyun_formatted = WuyunLiuqiService.format_wuyun_liuqi_for_analysis(
            wuyun_result["current"]
        )

        # 加载提示词模板
        prompt_template = load_health_wuyun_liuqi_template()

        # 构建WebSocket消息格式的数据
        websocket_data = {
            "type": "health_wuyun_liuqi_analysis",
            "data": {
                "eight_char": data.eight_char,
                "birth_date": data.birth_date,
                "analysis_date": analysis_date,
                "gender": data.gender,
                "wuyun_liuqi": current_wuyun_formatted["analysis_data"]
            }
        }

        # 返回完整的调试信息
        debug_data = {
            "analysis_info": {
                "eight_char": data.eight_char,
                "birth_date": data.birth_date,
                "analysis_date": analysis_date,
                "gender": data.gender
            },
            "wuyun_liuqi_data": {
                "birth": wuyun_result["birth"],
                "current": wuyun_result["current"],
                "formatted_for_analysis": current_wuyun_formatted
            },
            "websocket_message": websocket_data,
            "prompt_template": prompt_template,
            "usage_instructions": {
                "websocket_endpoint": "ws://服务器地址/ws/bazi_advice/",
                "message_format": "发送上述websocket_message到WebSocket端点",
                "expected_response": "流式返回五运六气健康分析结果",
                "frontend_usage": "前端只需要使用websocket_message，其他信息仅供开发调试"
            }
        }

        return {
            "success": True,
            "message": "五运六气健康分析调试数据",
            "data": debug_data
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"调试过程出错: {str(e)}"
        }
