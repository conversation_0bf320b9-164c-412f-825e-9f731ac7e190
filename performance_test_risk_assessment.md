# 🚨 性能测试风险评估报告 (修正版)

## 📋 重要说明

**之前的风险判断有误！** 计算密集型和数据库查询API恰恰是性能测试的**核心目标**，不是风险！

## 🔴 真正的高风险API (绝对不能高并发测试)

### 1. 外部服务调用API
- **微信登录** (`/api/auth/wechat-login`)
  - **风险**: 调用微信API `https://api.weixin.qq.com/sns/oauth2/access_token`
  - **后果**: 触发微信API限流，影响正常业务，可能被微信封禁
  - **状态**: ✅ 已从测试配置中排除

- **手机号登录** (`/api/auth/phone-login`)
  - **风险**: 触发安全验证机制
  - **后果**: IP被封禁，影响正常用户访问
  - **状态**: ✅ 已从测试配置中排除

### 2. AI大模型调用API
- **DeepSeek分析** (`/api/tcmchat/analysis_with_deepseek/`)
  - **风险**: 每次调用产生费用（约0.1-1元/次）
  - **后果**: 高并发测试可能产生数千元费用
  - **状态**: ✅ 已从测试配置中排除

- **AI聊天API** (`/api/tcmchat/chat_with_deepseek/`)
  - **风险**: 流式调用，费用更高
  - **后果**: 可能产生巨额费用
  - **状态**: ✅ 已从测试配置中排除

### 3. 支付相关API
- **微信支付、支付宝支付**
  - **风险**: 涉及真实资金流动
  - **后果**: 可能产生真实交易
  - **状态**: ✅ 未包含在测试配置中

## 🎯 性能测试的重点目标 (应该大力测试)

### 1. 计算密集型API - 核心测试目标！
- **计算证型指标** (`/api/questionnaire/calculate_constitution_indicators/`)
  - **目标**: 测试CPU计算性能，找到性能瓶颈
  - **价值**: 优化算法，提升用户体验
  - **建议**: 高并发测试，监控CPU、内存使用

- **分析问卷结果** (`/api/questionnaire/analyze_questionnaire/`)
  - **目标**: 测试复杂业务逻辑性能
  - **价值**: 优化数据库查询，提升响应速度
  - **建议**: 重点测试，这是用户最关心的功能

### 2. 数据库查询API - 重要测试目标！
- **用户统计** (`/api/async-bank/user-stats/`)
  - **目标**: 测试数据库查询性能
  - **价值**: 优化SQL查询，减少响应时间

- **活动列表** (`/api/async-bank/activity-list/`)
  - **目标**: 测试列表查询和分页性能
  - **价值**: 优化数据加载速度

### 3. 缓存优化API - 验证缓存效果！
- **HomePage** - 验证2小时缓存是否有效
- **疗法分类列表** - 测试缓存命中率
- **问卷详情** - 验证缓存对性能的提升

## 🟢 低风险API (可以正常测试)

### 1. 缓存优化API
- **HomePage** - 有2小时缓存
- **检查会员状态** - 有缓存机制
- **疗法分类列表** - 有长期缓存
- **问卷详情** - 有缓存机制

### 2. 简单查询API
- **空请求** - 无业务逻辑
- **用户目标** - 简单数据库查询
- **活动列表** - 有缓存的列表查询

## 🛡️ 安全测试建议

### 1. 测试环境隔离
- ✅ 确保在开发/测试环境进行
- ✅ 不要在生产环境进行高并发测试
- ✅ 使用测试数据，避免影响真实用户

### 2. 并发控制策略
```python
# 建议的并发配置
SAFE_CONCURRENT_USERS = [1, 3, 5, 10, 15]  # 降低并发数
SAFE_TEST_REQUESTS = 50  # 降低总请求数

# 高风险API特殊配置
HIGH_RISK_CONCURRENT_USERS = [1, 2, 3]
HIGH_RISK_TEST_REQUESTS = 10
```

### 3. 监控指标
- **数据库连接池使用率** - 避免连接耗尽
- **CPU使用率** - 避免服务器过载
- **内存使用率** - 监控内存泄漏
- **外部API调用次数** - 控制成本

### 4. 测试时间控制
- **单次测试时长** ≤ 10分钟
- **测试间隔** ≥ 30秒，让系统恢复
- **每日测试次数** ≤ 3次

## 🚫 绝对禁止测试的API

### 1. AI聊天和分析API
- `/api/tcmchat/analysis_with_deepseek/`
- `/api/tcmchat/chat_with_deepseek/`
- `/api/tcmchat/analysis_with_deepseek_For_Now_advice/`
- WebSocket AI分析接口

### 2. 支付相关API
- 微信支付回调
- 支付宝支付回调
- 订单创建API

### 3. 短信和通知API
- 短信验证码发送
- 推送通知API

## 📊 推荐测试方案 (修正版)

### 方案A: 缓存性能测试
- **目标**: 验证缓存机制效果
- **API**: HomePage、疗法列表、问卷详情等缓存API
- **并发**: 1, 5, 10, 20, 40, 80
- **请求数**: 200
- **关注指标**: 缓存命中率、响应时间

### 方案B: 计算性能测试 (重点推荐)
- **目标**: 测试计算密集型API性能瓶颈
- **API**: 问卷分析、证型计算等
- **并发**: 1, 5, 10, 20, 40
- **请求数**: 100
- **关注指标**: CPU使用率、响应时间、吞吐量

### 方案C: 数据库性能测试
- **目标**: 测试数据库查询和写入性能
- **API**: 用户统计、活动列表、答题得分提交
- **并发**: 1, 5, 10, 20, 40
- **请求数**: 150
- **关注指标**: 数据库连接池、查询时间、锁竞争

### 方案D: 综合压力测试
- **目标**: 找到系统整体性能瓶颈
- **API**: 所有安全API（排除外部服务调用）
- **并发**: 1, 5, 10, 20, 40, 80, 150
- **请求数**: 200
- **关注指标**: 系统整体负载、响应时间分布

## 🔧 实施建议

1. **先运行风险评估脚本**，确认当前配置安全性
2. **从方案A开始**，逐步升级到方案B
3. **实时监控系统指标**，发现异常立即停止
4. **保存测试结果**，建立性能基线
5. **定期回顾**，根据系统变化调整测试策略

## ⚠️ 紧急停止条件

如果出现以下情况，立即停止测试：
- CPU使用率 > 80%
- 数据库连接池使用率 > 90%
- 响应时间 > 10秒
- 错误率 > 10%
- 收到外部API限流警告
- 系统出现异常日志

## 📞 联系方式

如有疑问或发现新的风险点，请及时沟通确认测试方案。
