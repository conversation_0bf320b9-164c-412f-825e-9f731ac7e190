import datetime
import math
from . import ganzhi


tiangans = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
dizhis = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
wuxingNames = ["金", "木", "水", "火", "土"]

wuxingDicForTiangan = {
    "甲": "木",
    "乙": "木",
    "丙": "火",
    "丁": "火",
    "戊": "土",
    "己": "土",
    "庚": "金",
    "辛": "金",
    "壬": "水",
    "癸": "水"
}
wuxingDicForDizhi = {
    "子": "水",
    "丑": "土",
    "寅": "木",
    "卯": "木",
    "辰": "土",
    "巳": "火",
    "午": "火",
    "未": "土",
    "申": "金",
    "酉": "金",
    "戌": "土",
    "亥": "水"
}


def calculateTime(tianganOfDay, time):  # tiangan%10 : 10
    tianganIndex = (2 * tianganOfDay - 1) % 10
    if time == 23 or time == 0 or time == 24:
        dizhi = dizhis[0]
        dizhiIndex = 0
    else:
        dizhiIndex = time / 2
        if dizhiIndex >= 0.5:
            dizhiIndex = math.ceil(dizhiIndex)
        else:
            dizhiIndex = round(dizhiIndex)
        dizhi = dizhis[dizhiIndex]
    return tiangans[(tianganIndex - 1 + dizhiIndex) % 10] + dizhi


def getShenChenBaZi(year, month, day, time):
    data = ganzhi.day(year, month, day)
    tianganOfDay = data[2]
    tianganOfDaySymbol = tiangans.index(tianganOfDay) + 1
    ganzhiOfTime = calculateTime(tianganOfDaySymbol, time)
    return data[0] + '-' + ganzhiOfTime


def getWuXing(bazi):
    bazilist = str(bazi).split("-")
    wuxingList = []
    countForJin = 0
    countForMu = 0
    countForShui = 0
    countForHuo = 0
    countForTu = 0
    for bazi in bazilist:
        wuxingList.append(wuxingDicForTiangan[bazi[0]])
        wuxingList.append(wuxingDicForDizhi[bazi[1]])
    for wuxing in wuxingList:
        if wuxing == "金":
            countForJin = countForJin + 1
        elif wuxing == "木":
            countForMu = countForMu + 1
        elif wuxing == "水":
            countForShui = countForShui + 1
        elif wuxing == "火":
            countForHuo = countForHuo + 1
        else:
            countForTu = countForTu + 1
    scoreForJin = round(countForJin / 8, 2)
    scoreForMu = round(countForMu / 8, 2)
    scoreForShui = round(countForShui / 8, 2)
    scoreForHuo = round(countForHuo / 8, 2)
    scoreForTu = round(countForTu / 8, 2)
    return (scoreForJin, scoreForMu, scoreForShui, scoreForHuo, scoreForTu)

def getCurrentBazi():
    """
    获取当前时间（东八区）的八字
    """
    # 获取当前时间
    now = datetime.datetime.now()
    year = now.year
    month = now.month
    day = now.day
    hour = now.hour
    
    # 获取当前时间的八字
    current_bazi = getShenChenBaZi(year, month, day, hour)
    return current_bazi

def getWuYunLiuQi(bazi):
    """
    根据八字获取五运六气信息
    
    五运：金运、木运、水运、火运、土运
    六气：风、寒、暑、湿、燥、火
    
    返回格式：{"五运": "xxx运", "六气": "xxx"}
    """
    bazilist = str(bazi).split("-")
    
    # 简化处理：基于年柱和月柱确定五运六气
    year_gan = bazilist[0][0]  # 年干
    year_zhi = bazilist[0][1]  # 年支
    month_gan = bazilist[1][0]  # 月干
    month_zhi = bazilist[1][1]  # 月支
    
    # 五运判断（基于年干）
    wuxing_map = {
        "甲": "木运", "乙": "木运",
        "丙": "火运", "丁": "火运",
        "戊": "土运", "己": "土运",
        "庚": "金运", "辛": "金运",
        "壬": "水运", "癸": "水运"
    }
    
    # 六气判断（基于月支）
    liuqi_map = {
        "子": "寒", "丑": "寒", 
        "寅": "风", "卯": "风",
        "辰": "湿", "巳": "暑",
        "午": "暑", "未": "湿",
        "申": "燥", "酉": "燥",
        "戌": "寒", "亥": "寒"
    }
    
    wuyun = wuxing_map.get(year_gan, "未知")
    liuqi = liuqi_map.get(month_zhi, "未知")
    
    return {
        "五运": wuyun,
        "六气": liuqi
    }

def getCurrentWuYunLiuQi():
    """
    获取当前时间的五运六气
    """
    current_bazi = getCurrentBazi()
    return getWuYunLiuQi(current_bazi) 