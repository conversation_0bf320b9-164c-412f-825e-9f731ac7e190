# 模块化八字分析系统接入文档

## 📋 概述

本文档详细说明如何将模块化八字分析系统接入到主项目的Ninja API系统中，实现组件化的八字分析功能。

## 🏗️ 系统架构

### 核心组件
- **BaziAnalysisCore**: 八字分析核心类
- **BaziAIInterpreter**: AI解读服务
- **modular_bazi_router**: 模块化API路由器

### 模块化接口列表
| 接口路径 | 功能 | 返回数据 |
|---------|------|----------|
| `/basic_info` | 基本信息 | 阳历、农历、八字、四柱 |
| `/four_pillars` | 四柱详细 | 干支、五行、纳音、藏干等 |
| `/shishen_analysis` | 十神分析 | 天干十神、地支十神、藏干十神 |
| `/shensha_analysis` | 神煞分析 | 各柱神煞、分类统计 |
| `/dishi_analysis` | 长生十二神 | 各柱地势状态 |
| `/special_positions` | 特殊宫位 | 胎元、胎息、命宫、身宫 |
| `/nine_star_analysis` | 九星分析 | 年月日时九星信息 |
| `/fortune_analysis` | 运势分析 | 起运、大运、流年 |
| `/complete_analysis` | 完整分析 | 所有模块整合 |

### AI解读接口
| 接口路径 | 功能 | 特点 |
|---------|------|------|
| `/shishen_with_ai` | 十神+AI解读 | 专业十神分析+智能解读 |
| `/shensha_with_ai` | 神煞+AI解读 | 神煞数据+趋吉避凶建议 |
| `/fortune_with_ai` | 运势+AI解读 | 运势数据+人生规划指导 |
| `/dishi_with_ai` | 长生十二神+AI解读 | 地势分析+发展建议 |
| `/nine_star_with_ai` | 九星+AI解读 | 九星数据+实用指导 |

## 🔧 接入步骤

### 步骤1: 复制核心文件

将以下文件复制到主项目中：

```bash
# 复制核心分析模块
cp api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/services/bazi_analysis_core.py \
   api/services/

# 复制AI解读服务
cp api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/services/bazi_ai_interpreter.py \
   api/services/

# 复制模块化API
cp api/utils/bazi_websocket_demo/bazi-master/bazi_api/api/modular_bazi_api.py \
   api/ninja_apis/

# 复制依赖模块
cp -r api/utils/bazi_websocket_demo/bazi-master/bazi_api/lunar_python \
      api/utils/
cp -r api/utils/bazi_websocket_demo/bazi-master/bazi_api/shensha_calculator \
      api/utils/
```

### 步骤2: 修改导入路径

修改 `api/services/bazi_analysis_core.py` 的导入：

```python
# 原来的导入
from lunar_python import Solar, Lunar
from shensha_calculator import query_shensha

# 修改为
from api.utils.lunar_python import Solar, Lunar
from api.utils.shensha_calculator import query_shensha
```

### 步骤3: 创建路由器文件

创建 `api/ninja_apis/bazi_modular_router.py`：

```python
"""
八字模块化分析路由器
"""
from ninja import Router
from api.modular_bazi_api import modular_bazi_router

# 创建八字模块化路由器
bazi_modular_router = Router()

# 添加所有模块化接口
bazi_modular_router.add_router("", modular_bazi_router)
```

### 步骤4: 注册到主路由系统

修改 `api/ninja_apis/__init__.py`：

```python
# 现有导入
from ninja import NinjaAPI
from api.views import tcmNLP, tcmchat, ninja_chat, invite_views
from .questionnaire_api import questionnaire_router
from .db_health_api import db_health_router

# 新增导入
from .bazi_modular_router import bazi_modular_router

# 现有API实例
bank_api = NinjaAPI(version="1.0")
doubao_aichat = NinjaAPI(version="2.0")
invite_api = NinjaAPI(version="3.0")
questionnaire_api = NinjaAPI(version="1.0", title="问卷系统API")
db_health_api = NinjaAPI(version="1.1", title="数据库健康监控API")

# 新增八字分析API
bazi_analysis_api = NinjaAPI(version="1.0", title="模块化八字分析API", urls_namespace="bazi_analysis_api")

def initialize():
    global _initialized
    if not _initialized:
        # 现有路由注册
        doubao_aichat.add_router("/chat/", ninja_chat.router)
        invite_api.add_router("/invite_api/", invite_views.router)
        questionnaire_api.add_router("", questionnaire_router)
        db_health_api.add_router("", db_health_router)
        
        # 新增八字分析路由
        bazi_analysis_api.add_router("/bazi", bazi_modular_router)
        
        _initialized = True
```

### 步骤5: 添加URL配置

修改主项目的 `urls.py`：

```python
from django.urls import path, include
from api.ninja_apis import (
    bank_api, doubao_aichat, invite_api, 
    questionnaire_api, db_health_api, bazi_analysis_api
)

urlpatterns = [
    # 现有URL配置
    path("api/bank/", bank_api.urls),
    path("api/doubao/", doubao_aichat.urls),
    path("api/invite/", invite_api.urls),
    path("api/questionnaire/", questionnaire_api.urls),
    path("api/health/", db_health_api.urls),
    
    # 新增八字分析API
    path("api/analysis/", bazi_analysis_api.urls),
]
```

## 📝 使用示例

### 基本调用示例

```python
# 获取基本信息
POST /api/analysis/bazi/basic_info
{
    "year": 2001,
    "month": 7,
    "day": 28,
    "hour": 5,
    "minute": 0,
    "gender": 0,
    "sect": 2
}

# 获取十神分析
POST /api/analysis/bazi/shishen_analysis
{
    "year": 2001,
    "month": 7,
    "day": 28,
    "hour": 5,
    "minute": 0,
    "gender": 0
}

# 获取十神+AI解读
POST /api/analysis/bazi/shishen_with_ai
{
    "year": 2001,
    "month": 7,
    "day": 28,
    "hour": 5,
    "minute": 0,
    "gender": 0
}
```

### 前端集成示例

```javascript
// 调用十神分析接口
async function getShishenAnalysis(birthData) {
    const response = await fetch('/api/analysis/bazi/shishen_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify(birthData)
    });
    
    const result = await response.json();
    if (result.success) {
        return result.data;
    } else {
        throw new Error(result.error);
    }
}

// 调用AI解读接口
async function getAIInterpretation(birthData, analysisType) {
    const endpoint = `/api/analysis/bazi/${analysisType}_with_ai`;
    const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify(birthData)
    });
    
    const result = await response.json();
    return result.data;
}
```

## 🔒 权限配置

如果需要添加权限验证，修改路由器：

```python
from api.middleware.auth import require_auth

@bazi_modular_router.post("/shishen_analysis")
@require_auth
def get_shishen_analysis(request, data: BaziInputSchema):
    # 现有逻辑
    pass
```

## 🚀 性能优化建议

1. **缓存策略**: 对相同八字数据进行缓存
2. **异步处理**: AI解读接口使用异步调用
3. **限流保护**: 添加API调用频率限制
4. **数据验证**: 加强输入参数验证

## 🔧 自定义扩展

### 添加新的分析模块

1. 在 `BaziAnalysisCore` 中添加新方法
2. 在 `modular_bazi_api.py` 中添加新接口
3. 在 `BaziAIInterpreter` 中添加对应解读方法

### 示例：添加五行分析模块

```python
# 在 BaziAnalysisCore 中添加
def get_wuxing_analysis(self):
    """获取五行分析"""
    return {
        "year_wuxing": self.eight_char.getYearWuXing(),
        "month_wuxing": self.eight_char.getMonthWuXing(),
        "day_wuxing": self.eight_char.getDayWuXing(),
        "time_wuxing": self.eight_char.getTimeWuXing(),
        # 五行统计、强弱分析等
    }

# 在 modular_bazi_api.py 中添加接口
@modular_bazi_router.post("/wuxing_analysis")
def get_wuxing_analysis(request, data: BaziInputSchema):
    # 实现逻辑
    pass
```

## 📞 技术支持

如有问题，请参考：
1. 八字分析器文档：`api/utils/bazi_module/八字分析器使用说明.md`
2. 神煞计算器文档：`api/utils/bazi_module/shensha_calculator/README.md`
3. 主项目Ninja API规范：`.augment/rules/`

---

**注意**: 接入前请确保已安装所有依赖模块，并测试各个接口的正常工作。
