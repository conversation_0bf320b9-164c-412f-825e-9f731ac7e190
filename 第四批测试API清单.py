#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第四批测试API清单
专门用于第四批补充测试API的性能测试
包括：会员系统、论坛系统、医案系统、症状管理、预后系统、邀请系统、其他功能API
"""

# 第四批测试API列表 - 补充之前遗漏的API（已移除高风险API）
FOURTH_BATCH_APIS = [
    # 会员系统API (2个) - 移除高风险API
    "日活动",
    "用户问答历史",

    # 论坛系统API (3个)
    "创建帖子",
    "搜索帖子",
    "帖子评论",

    # 医案系统API (7个) - 2个中风险API降低并发
    "时间效率排行",
    "提交游戏记录",    # 中风险：限制并发≤15
    "增加经验值",      # 中风险：限制并发≤15
    "经验值历史",
    "游戏历史记录",
    "案例排行榜",
    "平均得分排行",

    # 症状管理API (1个) - 已修复异步
    "添加自定义症状",  # 已修复：异步版本

    # 预后系统API (1个)
    "执行搜索",

    # 邀请系统API (2个)
    "我的邀请码",
    "邀请记录",

    # 其他功能API (3个)
    "系统心跳",
    "干支查询",
    "获取当前季节"
]

# 第四批API详细配置 - 包含具体的API端点信息
FOURTH_BATCH_API_CONFIGS = {
    # 会员系统API
    "日活动": {
        "url": "/api/async-bank/daily-activity/",
        "method": "GET",
        "description": "获取用户日活动数据",
        "category": "会员系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "用户问答历史": {
        "url": "/api/daily_tcm_question/user_quiz_history/",
        "method": "GET",
        "description": "获取用户问答历史记录",
        "category": "会员系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },

    # 论坛系统API
    "创建帖子": {
        "url": "/api/forum/posts/",
        "method": "POST",
        "data": {
            "title": "测试帖子标题",
            "content": "这是一个测试帖子内容",
            "category": "讨论"
        },
        "description": "创建新的论坛帖子",
        "category": "论坛系统",
        "risk_level": "中",
        "max_concurrent": 15,
        "cache_enabled": False
    },
    "搜索帖子": {
        "url": "/api/forum/posts/search/",
        "method": "GET",
        "params": {"q": "中医", "limit": 10},
        "description": "搜索论坛帖子",
        "category": "论坛系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "帖子评论": {
        "url": "/api/forum/posts/1/comments/",
        "method": "GET",
        "description": "获取帖子评论列表",
        "category": "论坛系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },

    # 医案系统API
    "时间效率排行": {
        "url": "/api/medical-case/ranking/efficiency/",
        "method": "GET",
        "description": "获取时间效率排行榜",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "提交游戏记录": {
        "url": "/api/medical-case/game-record/",
        "method": "POST",
        "data": {
            "game_type": "诊断练习",
            "score": 85,
            "time_spent": 120,
            "correct_answers": 8,
            "total_questions": 10
        },
        "description": "提交医案游戏记录",
        "category": "医案系统",
        "risk_level": "中",
        "max_concurrent": 15,
        "cache_enabled": False
    },
    "增加经验值": {
        "url": "/api/medical-case/add-exp/",
        "method": "POST",
        "data": {
            "exp_points": 10,
            "source": "完成练习",
            "description": "诊断练习获得经验"
        },
        "description": "增加用户经验值",
        "category": "医案系统",
        "risk_level": "中",
        "max_concurrent": 15,
        "cache_enabled": False
    },
    "经验值历史": {
        "url": "/api/medical-case/exp-history/",
        "method": "GET",
        "description": "获取经验值历史记录",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "游戏历史记录": {
        "url": "/api/medical-case/game-history/",
        "method": "GET",
        "description": "获取游戏历史记录",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "案例排行榜": {
        "url": "/api/medical-case/ranking/cases/",
        "method": "GET",
        "description": "获取案例排行榜",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "平均得分排行": {
        "url": "/api/medical-case/ranking/avg-score/",
        "method": "GET",
        "description": "获取平均得分排行榜",
        "category": "医案系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },

    # 症状管理API
    "添加自定义症状": {
        "url": "/api/tcmchat/add_custom_symptom/",
        "method": "POST",
        "data": {
            "symptom_name": "测试症状",
            "description": "这是一个测试症状",
            "severity": "轻度"
        },
        "description": "添加自定义症状",
        "category": "症状管理",
        "risk_level": "中",
        "max_concurrent": 15,
        "cache_enabled": False
    },

    # 预后系统API
    "执行搜索": {
        "url": "/api/routertest1/search_symptoms_and_recommend_therapies/",
        "method": "POST",
        "data": {
            "symptoms": ["头痛", "失眠"],
            "limit": 10
        },
        "description": "执行症状搜索和疗法推荐",
        "category": "预后系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },

    # 邀请系统API
    "我的邀请码": {
        "url": "/api/invite_api/my-code/",
        "method": "GET",
        "description": "获取我的邀请码",
        "category": "邀请系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "邀请记录": {
        "url": "/api/invite_api/records/",
        "method": "GET",
        "description": "获取邀请记录",
        "category": "邀请系统",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },

    # 其他功能API
    "系统心跳": {
        "url": "/api/health/heartbeat/",
        "method": "GET",
        "description": "系统心跳检测",
        "category": "其他功能",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": False
    },
    "干支查询": {
        "url": "/api/tcmNLP/get_ganzhi/",
        "method": "GET",
        "description": "获取干支信息",
        "category": "其他功能",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    },
    "获取当前季节": {
        "url": "/api/tcmNLP/get_current_season/",
        "method": "GET",
        "description": "获取当前季节信息",
        "category": "其他功能",
        "risk_level": "低",
        "max_concurrent": 100,
        "cache_enabled": True
    }
}

# 按业务模块分组
FOURTH_BATCH_BY_MODULE = {
    "会员系统": [
        "记录目标",     # 跳过：同步异步混用问题
        "自定义活动",   # 跳过：同步异步混用问题
        "用户卡片",     # 跳过：同步异步混用问题
        "日活动",
        "用户问答历史"
    ],
    "论坛系统": [
        "创建帖子",
        "搜索帖子",
        "帖子评论"
    ],
    "医案系统": [
        "时间效率排行",
        "提交游戏记录",
        "增加经验值",
        "经验值历史",
        "游戏历史记录",
        "案例排行榜",
        "平均得分排行"
    ],
    "症状管理": [
        "添加自定义症状"   # 已修复：异步版本
    ],
    "预后系统": [
        "执行搜索"
    ],
    "邀请系统": [
        "我的邀请码",
        "邀请记录"
    ],
    "其他功能": [
        "系统心跳",
        "干支查询",
        "获取当前季节"
    ]
}

# 按风险等级分组
FOURTH_BATCH_BY_RISK = {
    "低风险": [
        "日活动",
        "用户卡片",
        "用户问答历史",
        "搜索帖子",
        "帖子评论",
        "时间效率排行",
        "经验值历史",
        "游戏历史记录",
        "案例排行榜",
        "平均得分排行",
        "执行搜索",
        "我的邀请码",
        "邀请记录",
        "系统心跳",
        "干支查询",
        "获取当前季节"
    ],
    "中风险": [
        "记录目标",  # 跳过：同步异步混用问题
        "自定义活动",
        "创建帖子",
        "提交游戏记录",
        "增加经验值",
        "添加症状",
        "更新症状",
        "添加自定义症状"
    ]
}

# 按API类型分组
FOURTH_BATCH_BY_TYPE = {
    "读取操作": [
        "日活动",
        "用户卡片",
        "用户问答历史",
        "搜索帖子",
        "帖子评论",
        "时间效率排行",
        "经验值历史",
        "游戏历史记录",
        "案例排行榜",
        "平均得分排行",
        "我的邀请码",
        "邀请记录",
        "系统心跳",
        "干支查询",
        "获取当前季节"
    ],
    "写入操作": [
        "记录目标",  # 跳过：同步异步混用问题
        "自定义活动",
        "创建帖子",
        "提交游戏记录",
        "增加经验值",
        "添加症状",
        "更新症状",
        "添加自定义症状"
    ],
    "搜索操作": [
        "执行搜索",
        "搜索帖子"
    ]
}

def print_fourth_batch_summary():
    """打印第四批测试API汇总信息"""
    print("第四批测试API清单:")
    print("=" * 60)
    
    print(f"\n📊 总体统计:")
    print(f"   总API数量: {len(FOURTH_BATCH_APIS)} 个")
    print(f"   低风险API: {len(FOURTH_BATCH_BY_RISK['低风险'])} 个")
    print(f"   中风险API: {len(FOURTH_BATCH_BY_RISK['中风险'])} 个")
    print(f"   读取操作: {len(FOURTH_BATCH_BY_TYPE['读取操作'])} 个")
    print(f"   写入操作: {len(FOURTH_BATCH_BY_TYPE['写入操作'])} 个")
    print(f"   搜索操作: {len(FOURTH_BATCH_BY_TYPE['搜索操作'])} 个")
    print(f"   ⚠️ 跳过API: 1个 (记录目标 - 同步异步混用问题)")
    
    print(f"\n📋 按业务模块分组:")
    for module, apis in FOURTH_BATCH_BY_MODULE.items():
        print(f"\n🔹 {module} ({len(apis)}个API):")
        for i, api in enumerate(apis, 1):
            risk = "中风险" if api in FOURTH_BATCH_BY_RISK['中风险'] else "低风险"
            api_type = "写入" if api in FOURTH_BATCH_BY_TYPE['写入操作'] else "读取"
            if api in FOURTH_BATCH_BY_TYPE['搜索操作']:
                api_type = "搜索"
            print(f"   {i:2d}. {api:<20} [{risk}] [{api_type}]")

    print(f"\n⚠️ 测试建议:")
    print(f"   • 低风险API可以正常并发测试")
    print(f"   • 中风险API建议低并发测试（≤15并发）")
    print(f"   • 写入操作API需要控制测试数据量")
    print(f"   • 搜索操作API注意缓存命中率测试")

if __name__ == "__main__":
    print_fourth_batch_summary()
