from django.core.management.base import BaseCommand
from django.conf import settings
import subprocess
import gzip
import glob
import os
import tempfile

class Command(BaseCommand):
    help = 'Verify the latest MySQL database backup'

    def handle(self, *args, **options):
        db_settings = settings.DATABASES['default']
        backup_dir = os.path.join(settings.BASE_DIR, 'database_backups')
        
        # 获取最新的备份文件
        latest_backup = max(glob.glob(f"{backup_dir}/*.sql.gz"), key=os.path.getctime)
        
        # 创建一个临时文件来存储解压后的SQL
        with tempfile.NamedTemporaryFile(mode='wb', delete=False) as temp_sql:
            with gzip.open(latest_backup, 'rb') as gz_file:
                temp_sql.write(gz_file.read())
        
        try:
            # 使用 mysql 命令来验证 SQL 文件的语法
            check_cmd = [
                'mysql',
                '--protocol=tcp',
                '-h', db_settings['HOST'],
                '-P', db_settings['PORT'],
                '-u', db_settings['USER'],
                f"-p{db_settings['PASSWORD']}",
                '--database', db_settings['NAME'],
                '-e', f"source {temp_sql.name}"
            ]
            
            result = subprocess.run(check_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.stdout.write(self.style.SUCCESS(f"Backup verification completed successfully for {latest_backup}"))
            else:
                self.stdout.write(self.style.ERROR(f"Backup verification failed for {latest_backup}"))
                self.stdout.write(self.style.ERROR(f"Error: {result.stderr}"))
        
        finally:
            # 清理临时文件
            os.unlink(temp_sql.name)