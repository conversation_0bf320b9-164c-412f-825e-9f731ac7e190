#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试

测试神煞计算器的性能表现。
"""

import time
from shensha_calculator import query_shensha


def performance_test():
    """性能测试"""
    
    # 测试数据
    test_cases = [
        {
            "ganzhi": "甲子",
            "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"],
            "is_man": True,
            "witch": 1,
            "nian_nayin": "海中金"
        },
        {
            "ganzhi": "丙寅",
            "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"],
            "is_man": True,
            "witch": 2,
            "nian_nayin": "海中金"
        },
        {
            "ganzhi": "戊午",
            "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"],
            "is_man": True,
            "witch": 3,
            "nian_nayin": "海中金"
        },
        {
            "ganzhi": "癸亥",
            "bazi": ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"],
            "is_man": True,
            "witch": 4,
            "nian_nayin": "海中金"
        }
    ]
    
    # 预热
    for case in test_cases:
        query_shensha(**case)
    
    # 性能测试
    iterations = 10000
    
    print(f"性能测试 - 执行{iterations}次查询")
    print("-" * 50)
    
    start_time = time.time()
    
    for i in range(iterations):
        for case in test_cases:
            result = query_shensha(**case)
    
    end_time = time.time()
    
    total_time = end_time - start_time
    total_queries = iterations * len(test_cases)
    avg_time_per_query = total_time / total_queries * 1000  # 毫秒
    
    print(f"总执行时间: {total_time:.4f} 秒")
    print(f"总查询次数: {total_queries}")
    print(f"平均每次查询时间: {avg_time_per_query:.4f} 毫秒")
    print(f"每秒查询次数: {total_queries / total_time:.0f} QPS")
    
    # 内存使用测试
    import sys
    
    # 创建大量查询结果
    results = []
    for i in range(1000):
        for case in test_cases:
            result = query_shensha(**case)
            results.append(result)
    
    print(f"\n内存使用测试:")
    print(f"存储1000次查询结果后，结果列表大小: {sys.getsizeof(results)} 字节")
    
    # 验证结果一致性
    print(f"\n一致性验证:")
    reference_results = []
    for case in test_cases:
        reference_results.append(query_shensha(**case))
    
    # 多次执行验证结果一致
    for i in range(100):
        current_results = []
        for case in test_cases:
            current_results.append(query_shensha(**case))
        
        if current_results != reference_results:
            print("错误：结果不一致！")
            return
    
    print("✓ 100次执行结果完全一致")
    
    # 显示具体结果
    print(f"\n查询结果示例:")
    for i, case in enumerate(test_cases):
        result = query_shensha(**case)
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]
        print(f"{pillar_names[i]} {case['ganzhi']}: {result} ({len(result)}个)")


def stress_test():
    """压力测试"""
    
    print("\n压力测试 - 大量不同八字组合")
    print("-" * 50)
    
    # 生成测试数据
    tiangans = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    dizhis = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
    
    test_count = 0
    error_count = 0
    
    start_time = time.time()
    
    # 测试不同的八字组合
    for i in range(0, len(tiangans), 2):  # 减少测试量以节省时间
        for j in range(0, len(dizhis), 2):
            for k in range(0, len(tiangans), 3):
                for l in range(0, len(dizhis), 3):
                    try:
                        bazi = [
                            tiangans[i], dizhis[j],
                            tiangans[k], dizhis[l],
                            tiangans[(i+1) % len(tiangans)], dizhis[(j+1) % len(dizhis)],
                            tiangans[(k+1) % len(tiangans)], dizhis[(l+1) % len(dizhis)]
                        ]
                        
                        ganzhi = bazi[4] + bazi[5]  # 日柱
                        
                        result = query_shensha(ganzhi, bazi, True, 3, "海中金")
                        test_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        if error_count <= 5:  # 只显示前5个错误
                            print(f"错误: {e}")
    
    end_time = time.time()
    
    print(f"压力测试完成:")
    print(f"测试用例数: {test_count}")
    print(f"错误数: {error_count}")
    print(f"成功率: {(test_count / (test_count + error_count) * 100):.2f}%")
    print(f"总耗时: {end_time - start_time:.4f} 秒")


if __name__ == "__main__":
    performance_test()
    stress_test()
    
    print("\n" + "=" * 50)
    print("性能测试完成！")
    print("Python神煞计算器性能表现良好，结果稳定一致。")
