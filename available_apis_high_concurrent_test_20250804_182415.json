{"test_time": "2025-08-04T18:24:15.085675", "test_config": {"concurrent_levels": [1, 10, 20, 50, 100], "requests_per_user": 10, "total_apis": 10}, "results": [{"api_name": "HomePage", "success_count": 10, "error_count": 0, "total_requests": 10, "avg_response_time": 0.00988163948059082, "qps": 843.3474082116861, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "HomePage", "success_count": 100, "error_count": 0, "total_requests": 100, "avg_response_time": 0.40591150760650635, "qps": 68.0709295065767, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "HomePage", "success_count": 200, "error_count": 0, "total_requests": 200, "avg_response_time": 0.10237092614173889, "qps": 823.403862689591, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "HomePage", "success_count": 500, "error_count": 0, "total_requests": 500, "avg_response_time": 0.339930908203125, "qps": 672.9118542944997, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "HomePage", "success_count": 1000, "error_count": 0, "total_requests": 1000, "avg_response_time": 0.5365878446102142, "qps": 907.0793340608474, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "日活动", "success_count": 10, "error_count": 0, "total_requests": 10, "avg_response_time": 0.26038801670074463, "qps": 36.6345795233308, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "日活动", "success_count": 38, "error_count": 62, "total_requests": 100, "avg_response_time": 19.29152510881424, "qps": 3.233200393728089, "error_rate": 62.0, "cache_hit_rate": 0.0}, {"api_name": "日活动", "success_count": 0, "error_count": 200, "total_requests": 200, "avg_response_time": 20.97148393988609, "qps": 6.450297527628982, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "日活动", "success_count": 0, "error_count": 500, "total_requests": 500, "avg_response_time": 14.940943083763123, "qps": 16.131351404434657, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "日活动", "success_count": 0, "error_count": 1000, "total_requests": 1000, "avg_response_time": 12.87965172791481, "qps": 32.26511689633857, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "时间效率排行", "success_count": 0, "error_count": 10, "total_requests": 10, "avg_response_time": 30.98188712596893, "qps": 0.3227527903434563, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "时间效率排行", "success_count": 0, "error_count": 100, "total_requests": 100, "avg_response_time": 31.006925332546235, "qps": 3.2234527385892067, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "时间效率排行", "success_count": 0, "error_count": 200, "total_requests": 200, "avg_response_time": 20.95117336988449, "qps": 6.458425861401524, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "时间效率排行", "success_count": 14, "error_count": 486, "total_requests": 500, "avg_response_time": 14.676610681533813, "qps": 16.12615363808647, "error_rate": 97.2, "cache_hit_rate": 0.0}, {"api_name": "时间效率排行", "success_count": 0, "error_count": 1000, "total_requests": 1000, "avg_response_time": 12.938089867830277, "qps": 32.27077442875598, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "案例排行榜", "success_count": 0, "error_count": 10, "total_requests": 10, "avg_response_time": 30.996266913414, "qps": 0.3226013822121199, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "案例排行榜", "success_count": 0, "error_count": 100, "total_requests": 100, "avg_response_time": 31.00664515256882, "qps": 3.2237512837097877, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "案例排行榜", "success_count": 0, "error_count": 200, "total_requests": 200, "avg_response_time": 20.961938574314118, "qps": 6.453815987976339, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "案例排行榜", "success_count": 0, "error_count": 500, "total_requests": 500, "avg_response_time": 14.951685554981232, "qps": 16.131735946207197, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "案例排行榜", "success_count": 513, "error_count": 487, "total_requests": 1000, "avg_response_time": 9.764795961380004, "qps": 80.3272702577721, "error_rate": 48.699999999999996, "cache_hit_rate": 0.0}, {"api_name": "平均得分排行", "success_count": 10, "error_count": 0, "total_requests": 10, "avg_response_time": 0.061483907699584964, "qps": 147.22435168414697, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "平均得分排行", "success_count": 100, "error_count": 0, "total_requests": 100, "avg_response_time": 0.42774359226226805, "qps": 141.133162644349, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "平均得分排行", "success_count": 200, "error_count": 0, "total_requests": 200, "avg_response_time": 0.8389535856246948, "qps": 132.3548493775923, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "平均得分排行", "success_count": 500, "error_count": 0, "total_requests": 500, "avg_response_time": 2.0013813309669493, "qps": 113.25487109159954, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "平均得分排行", "success_count": 1000, "error_count": 0, "total_requests": 1000, "avg_response_time": 4.085290156841278, "qps": 119.03880267337118, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "每日中医题目", "success_count": 10, "error_count": 0, "total_requests": 10, "avg_response_time": 0.2403433084487915, "qps": 39.29753026271409, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "每日中医题目", "success_count": 100, "error_count": 0, "total_requests": 100, "avg_response_time": 0.6642286944389343, "qps": 133.24180109273232, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "每日中医题目", "success_count": 200, "error_count": 0, "total_requests": 200, "avg_response_time": 1.289751214981079, "qps": 118.7818801378325, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "每日中医题目", "success_count": 500, "error_count": 0, "total_requests": 500, "avg_response_time": 2.224034619808197, "qps": 124.52875339838802, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "每日中医题目", "success_count": 1000, "error_count": 0, "total_requests": 1000, "avg_response_time": 4.185734804153443, "qps": 117.4148742273827, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "用户症状", "success_count": 0, "error_count": 10, "total_requests": 10, "avg_response_time": 0.05284402370452881, "qps": 166.91953501514266, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "用户症状", "success_count": 0, "error_count": 100, "total_requests": 100, "avg_response_time": 0.3309430527687073, "qps": 176.2192657474575, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "用户症状", "success_count": 0, "error_count": 200, "total_requests": 200, "avg_response_time": 0.692296028137207, "qps": 134.91572801043478, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "用户症状", "success_count": 0, "error_count": 500, "total_requests": 500, "avg_response_time": 1.93879718542099, "qps": 117.72560952128501, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "用户症状", "success_count": 0, "error_count": 1000, "total_requests": 1000, "avg_response_time": 4.085110816001892, "qps": 115.89232863311729, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "症状历史", "success_count": 0, "error_count": 10, "total_requests": 10, "avg_response_time": 0.05180678367614746, "qps": 176.97261216102748, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "症状历史", "success_count": 0, "error_count": 100, "total_requests": 100, "avg_response_time": 0.36795673370361326, "qps": 158.1678894187167, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "症状历史", "success_count": 0, "error_count": 200, "total_requests": 200, "avg_response_time": 0.7178210270404816, "qps": 145.70927028013824, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "症状历史", "success_count": 0, "error_count": 500, "total_requests": 500, "avg_response_time": 1.9981835646629333, "qps": 105.3844166990787, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "症状历史", "success_count": 0, "error_count": 1000, "total_requests": 1000, "avg_response_time": 4.066778792858123, "qps": 117.63406520874943, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "热门关键词", "success_count": 10, "error_count": 0, "total_requests": 10, "avg_response_time": 0.07417523860931396, "qps": 124.508828383818, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "热门关键词", "success_count": 100, "error_count": 0, "total_requests": 100, "avg_response_time": 0.6891868376731872, "qps": 127.73815393676963, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "热门关键词", "success_count": 200, "error_count": 0, "total_requests": 200, "avg_response_time": 1.3287527680397033, "qps": 114.07072144094005, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "热门关键词", "success_count": 500, "error_count": 0, "total_requests": 500, "avg_response_time": 2.5878250532150266, "qps": 119.32370156830164, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "热门关键词", "success_count": 1000, "error_count": 0, "total_requests": 1000, "avg_response_time": 4.759645195007324, "qps": 119.13180285530338, "error_rate": 0.0, "cache_hit_rate": 0.0}, {"api_name": "搜索建议", "success_count": 0, "error_count": 10, "total_requests": 10, "avg_response_time": 0.05750882625579834, "qps": 156.48577962996816, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "搜索建议", "success_count": 0, "error_count": 100, "total_requests": 100, "avg_response_time": 0.5204373621940612, "qps": 127.81437115712379, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "搜索建议", "success_count": 0, "error_count": 200, "total_requests": 200, "avg_response_time": 0.7662592387199402, "qps": 143.26051508566658, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "搜索建议", "success_count": 0, "error_count": 500, "total_requests": 500, "avg_response_time": 1.9712339482307435, "qps": 116.57242975588824, "error_rate": 100.0, "cache_hit_rate": 0.0}, {"api_name": "搜索建议", "success_count": 0, "error_count": 1000, "total_requests": 1000, "avg_response_time": 4.129601512908936, "qps": 111.10089242992092, "error_rate": 100.0, "cache_hit_rate": 0.0}]}