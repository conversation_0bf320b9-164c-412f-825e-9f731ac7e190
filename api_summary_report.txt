============================================================
🚀 API统计报告
============================================================
扫描时间: 2025-08-02 21:13:57

📊 总体统计:
  总API数量: 1209
  使用API_TIMER的API: 293
  使用缓存的API: 42
  API_TIMER覆盖率: 24.2%
  缓存覆盖率: 3.5%

📁 按模块统计:
  admin: 47个API, Timer:0(0.0%), Cache:0(0.0%)
  ai_analysis: 18个API, Timer:12(66.7%), Cache:10(55.6%)
  ai_chat: 6个API, Timer:4(66.7%), Cache:6(100.0%)
  api: 63个API, Timer:0(0.0%), Cache:0(0.0%)
  base: 1个API, Timer:0(0.0%), Cache:0(0.0%)
  chat: 18个API, Timer:5(27.8%), Cache:0(0.0%)
  demo_api: 15个API, Timer:0(0.0%), Cache:0(0.0%)
  elasticsearch_app: 4个API, Timer:0(0.0%), Cache:0(0.0%)
  elasticsearch_app_placeholder: 1个API, Timer:0(0.0%), Cache:0(0.0%)
  forum: 36个API, Timer:18(50.0%), Cache:0(0.0%)
  health: 6个API, Timer:3(50.0%), Cache:0(0.0%)
  newapi: 1个API, Timer:0(0.0%), Cache:0(0.0%)
  ninja_apis: 83个API, Timer:25(30.1%), Cache:0(0.0%)
  payment: 12个API, Timer:7(58.3%), Cache:2(16.7%)
  prognosis: 127个API, Timer:83(65.4%), Cache:0(0.0%)
  prognosis重构！: 127个API, Timer:83(65.4%), Cache:0(0.0%)
  questionnaire: 6个API, Timer:3(50.0%), Cache:0(0.0%)
  routertest1: 91个API, Timer:34(37.4%), Cache:13(14.3%)
  routertest2: 8个API, Timer:0(0.0%), Cache:0(0.0%)
  routertest3: 12个API, Timer:0(0.0%), Cache:0(0.0%)
  tcm_nlp: 32个API, Timer:0(0.0%), Cache:0(0.0%)
  user_management: 11个API, Timer:0(0.0%), Cache:1(9.1%)
  utilities: 5个API, Timer:4(80.0%), Cache:2(40.0%)
  utils: 5个API, Timer:0(0.0%), Cache:0(0.0%)
  views: 472个API, Timer:12(2.5%), Cache:8(1.7%)
  websockets: 2个API, Timer:0(0.0%), Cache:0(0.0%)

🔧 需要优化的模块 (Timer覆盖率<50%):
  ⚠️  admin: 47个API, Timer覆盖率仅0.0%
  ⚠️  api: 63个API, Timer覆盖率仅0.0%
  ⚠️  chat: 18个API, Timer覆盖率仅27.8%
  ⚠️  demo_api: 15个API, Timer覆盖率仅0.0%
  ⚠️  ninja_apis: 83个API, Timer覆盖率仅30.1%
  ⚠️  routertest1: 91个API, Timer覆盖率仅37.4%
  ⚠️  routertest2: 8个API, Timer覆盖率仅0.0%
  ⚠️  routertest3: 12个API, Timer覆盖率仅0.0%
  ⚠️  tcm_nlp: 32个API, Timer覆盖率仅0.0%
  ⚠️  user_management: 11个API, Timer覆盖率仅0.0%
  ⚠️  views: 472个API, Timer覆盖率仅2.5%

💾 缓存使用较少的模块 (Cache覆盖率<20%):
  ⚠️  admin: 47个API, Cache覆盖率仅0.0%
  ⚠️  api: 63个API, Cache覆盖率仅0.0%
  ⚠️  chat: 18个API, Cache覆盖率仅0.0%
  ⚠️  demo_api: 15个API, Cache覆盖率仅0.0%
  ⚠️  forum: 36个API, Cache覆盖率仅0.0%
  ⚠️  health: 6个API, Cache覆盖率仅0.0%
  ⚠️  ninja_apis: 83个API, Cache覆盖率仅0.0%
  ⚠️  payment: 12个API, Cache覆盖率仅16.7%
  ⚠️  prognosis: 127个API, Cache覆盖率仅0.0%
  ⚠️  prognosis重构！: 127个API, Cache覆盖率仅0.0%
  ⚠️  questionnaire: 6个API, Cache覆盖率仅0.0%
  ⚠️  routertest1: 91个API, Cache覆盖率仅14.3%
  ⚠️  routertest2: 8个API, Cache覆盖率仅0.0%
  ⚠️  routertest3: 12个API, Cache覆盖率仅0.0%
  ⚠️  tcm_nlp: 32个API, Cache覆盖率仅0.0%
  ⚠️  user_management: 11个API, Cache覆盖率仅9.1%
  ⚠️  views: 472个API, Cache覆盖率仅1.7%
