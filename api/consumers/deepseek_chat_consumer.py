# api/consumers/deepseek_chat_consumer.py
"""
已弃用的Deepseek聊天消费者
保留作为备份，建议使用ArkChatConsumer
"""

import json
import traceback
import openai
from channels.generic.websocket import AsyncWebsocketConsumer
from api.utils.rate_limiter import RateLimiter

# Deepseek API配置
openai.api_key = '***********************************'
openai.api_base = "https://api.deepseek.com"


class DeepseekChatConsumer(AsyncWebsocketConsumer):
    """
    已弃用：请使用ArkChatConsumer
    基于Deepseek模型的聊天消费者（仅作备份）
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conversation_history = [
            {"role": "system", "content": "作为中医大师，你擅长养生，要温暖乐观鼓舞人心。根据用户的体质脏腑信息，精准的指导用户的养生起居、心情调养、运动、艾灸推拿（可选）、饮食（可选）、穿衣（可选）等等一切信息，合理组织，不要冗长，不要落款你的名字。)"}
        ]
        self.user_id = None
        # 添加限流器（已弃用但保持一致性）
        self.rate_limiter = RateLimiter("chat_api", normal_limit=18, member_limit=100)
    
    async def connect(self):
        print("WebSocket连接建立")
        await self.accept()
        
        # 获取用户ID
        user_obj = self.scope.get('user')
        if hasattr(user_obj, 'id'):
            self.user_id = user_obj.id
    
    async def disconnect(self, close_code):
        print(f"WebSocket连接断开: {close_code}")

    async def check_rate_limit(self):
        """检查限流"""
        if not self.rate_limiter or not self.user_id:
            return True
            
        limit_info = await self.rate_limiter.check_rate_limit(self.user_id)
        if not limit_info['allowed']:
            error_message = f'您今日的聊天次数已达上限 ({limit_info["current_count"]}/{limit_info["limit"]})'
            if not limit_info['is_member']:
                error_message += '，升级会员可享受更多调用次数'
            
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            }))
            return False
        
        # 增加调用次数
        await self.rate_limiter.increment_count(self.user_id)
        return True
    
    async def receive(self, text_data):
        try:
            # 检查限流
            if not await self.check_rate_limit():
                return
                
            # 解析接收到的消息
            data = json.loads(text_data)
            user_message = data.get('message', '')
            print(f"收到消息: {user_message}")
            
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})
            
            try:
                # 调用API
                response = await openai.ChatCompletion.acreate(
                    model="deepseek-chat",
                    messages=self.conversation_history,
                    max_tokens=4000,
                    stream=True
                )
                
                assistant_message = ""
                # 实时处理和发送每个数据块
                async for chunk in response:
                    if chunk.choices and chunk.choices[0].delta and hasattr(chunk.choices[0].delta, 'content'):
                        content = chunk.choices[0].delta.content
                        if content:
                            assistant_message += content
                            # 立即发送每个数据块到客户端
                            await self.send(text_data=json.dumps({
                                'content': content,
                                'done': False
                            }))
                
                # 发送完成标记
                await self.send(text_data=json.dumps({
                    'content': '',
                    'done': True
                }))
                
                # 将完整回复添加到历史记录
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                print(f"完整回复: {assistant_message}")
                
            except Exception as e:
                error_message = f"生成回复时出错: {str(e)}\n{traceback.format_exc()}"
                print(error_message)
                await self.send(text_data=json.dumps({
                    'error': error_message,
                    'done': True
                }))
                
        except Exception as e:
            error_message = f"处理消息时出错: {str(e)}\n{traceback.format_exc()}"
            print(error_message)
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            }))


class DeepseekChatVIPConsumer(AsyncWebsocketConsumer):
    """
    已弃用：请使用ArkChatVIPConsumer
    基于Deepseek模型的VIP聊天消费者（仅作备份）
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conversation_history = [
            {"role": "system", "content": "作为中医大师，你擅长养生，要温暖乐观鼓舞人心，给出深入+浅出的中医理论依据，不要遗漏。适当多用手机兼容的emoji，称呼用户用爱心或玫瑰表情。根据用户的体质脏腑信息，精准的指导用户的养生起居、心情调养、运动、艾灸推拿（可选）、饮食（可选）、穿衣（可选）等等一切信息，合理组织，符合《黄帝内经》四时调神大论框架，符合中医上工治未病思想，不要落款你的名字，直接给出回复，要有至少一句中医理论作为依据)"}
        ]
        self.user_id = None
        # 添加限流器（已弃用但保持一致性）
        self.rate_limiter = RateLimiter("chat_api", normal_limit=18, member_limit=100)
    
    async def connect(self):
        print("WebSocket连接建立")
        await self.accept()
        
        # 获取用户ID
        user_obj = self.scope.get('user')
        if hasattr(user_obj, 'id'):
            self.user_id = user_obj.id
    
    async def disconnect(self, close_code):
        print(f"WebSocket连接断开: {close_code}")

    async def check_rate_limit(self):
        """检查限流"""
        if not self.rate_limiter or not self.user_id:
            return True
            
        limit_info = await self.rate_limiter.check_rate_limit(self.user_id)
        if not limit_info['allowed']:
            error_message = f'您今日的聊天次数已达上限 ({limit_info["current_count"]}/{limit_info["limit"]})'
            if not limit_info['is_member']:
                error_message += '，升级会员可享受更多调用次数'
            
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            }))
            return False
        
        # 增加调用次数
        await self.rate_limiter.increment_count(self.user_id)
        return True

    async def receive(self, text_data):
        try:
            # 检查限流
            if not await self.check_rate_limit():
                return
                
            # 解析接收到的消息
            data = json.loads(text_data)
            user_message = data.get('message', '')
            print(f"收到消息: {user_message}")
            
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": user_message})
            
            try:
                # 调用API
                response = await openai.ChatCompletion.acreate(
                    model="deepseek-chat",
                    messages=self.conversation_history,
                    max_tokens=4000,
                    stream=True
                )
                
                assistant_message = ""
                # 实时处理和发送每个数据块
                async for chunk in response:
                    if chunk.choices and chunk.choices[0].delta and hasattr(chunk.choices[0].delta, 'content'):
                        content = chunk.choices[0].delta.content
                        if content:
                            assistant_message += content
                            # 立即发送每个数据块到客户端
                            await self.send(text_data=json.dumps({
                                'content': content,
                                'done': False
                            }))
                
                # 发送完成标记
                await self.send(text_data=json.dumps({
                    'content': '',
                    'done': True
                }))
                
                # 将完整回复添加到历史记录
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                print(f"完整回复: {assistant_message}")
                
            except Exception as e:
                error_message = f"生成回复时出错: {str(e)}\n{traceback.format_exc()}"
                print(error_message)
                await self.send(text_data=json.dumps({
                    'error': error_message,
                    'done': True
                }))
                
        except Exception as e:
            error_message = f"处理消息时出错: {str(e)}\n{traceback.format_exc()}"
            print(error_message)
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            })) 