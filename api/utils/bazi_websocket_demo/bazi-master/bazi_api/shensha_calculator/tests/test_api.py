"""
API接口测试

测试主要查询接口的正确性。
"""

import unittest
from ..api.query import query_shensha
from .test_data import TEST_BAZI_CASES


class TestQueryAPI(unittest.TestCase):
    """测试查询API"""
    
    def test_query_shensha_basic(self):
        """测试基本查询功能"""
        # 测试甲子年柱
        bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]
        result = query_shensha("甲子", bazi, True, 1, "海中金")
        
        # 甲子年柱应该包含天乙贵人（甲见丑未，但这里是子，所以不应该有）
        # 实际上甲子年柱应该有太极贵人（甲见子午）
        self.assertIn("太极", result)
    
    def test_query_shensha_day_pillar(self):
        """测试日柱查询"""
        bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]
        result = query_shensha("戊午", bazi, True, 3, "海中金")
        
        # 戊午日柱应该包含禄神（戊禄在巳，但这里是午，所以不应该有）
        # 应该包含羊刃（戊羊刃在午）
        self.assertIn("羊刃", result)
    
    def test_query_shensha_with_test_cases(self):
        """使用测试用例进行验证"""
        for test_case in TEST_BAZI_CASES:
            bazi = test_case["bazi"]
            nian_nayin = test_case["nian_nayin"]
            is_man = test_case["is_man"]
            
            for witch, expected_shenshas in test_case["expected_results"].items():
                with self.subTest(case=test_case["name"], witch=witch):
                    # 构造干支
                    if witch == 1:  # 年柱
                        ganzhi = bazi[0] + bazi[1]
                    elif witch == 2:  # 月柱
                        ganzhi = bazi[2] + bazi[3]
                    elif witch == 3:  # 日柱
                        ganzhi = bazi[4] + bazi[5]
                    elif witch == 4:  # 时柱
                        ganzhi = bazi[6] + bazi[7]
                    
                    result = query_shensha(ganzhi, bazi, is_man, witch, nian_nayin)
                    
                    # 检查预期的神煞是否都在结果中
                    for expected_shensha in expected_shenshas:
                        self.assertIn(expected_shensha, result, 
                                    f"在{test_case['name']}的第{witch}柱中应该包含{expected_shensha}")
    
    def test_query_shensha_invalid_input(self):
        """测试无效输入"""
        # 测试无效干支
        with self.assertRaises(ValueError):
            query_shensha("无效", ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"], True, 1)
        
        # 测试无效八字
        with self.assertRaises(ValueError):
            query_shensha("甲子", ["甲", "子", "丙"], True, 1)
        
        # 测试无效柱位
        with self.assertRaises(ValueError):
            query_shensha("甲子", ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"], True, 0)
    
    def test_query_shensha_edge_cases(self):
        """测试边界情况"""
        bazi = ["甲", "子", "丙", "寅", "戊", "午", "癸", "亥"]
        
        # 测试不同性别
        result_male = query_shensha("甲子", bazi, True, 1, "海中金")
        result_female = query_shensha("甲子", bazi, False, 1, "海中金")
        
        # 性别可能影响某些神煞（如元辰）
        # 这里只是确保都能正常执行
        self.assertIsInstance(result_male, list)
        self.assertIsInstance(result_female, list)
        
        # 测试没有纳音的情况
        result_no_nayin = query_shensha("甲子", bazi, True, 1)
        self.assertIsInstance(result_no_nayin, list)
    
    def test_specific_shensha_combinations(self):
        """测试特定神煞组合"""
        # 测试魁罡日
        bazi_kuigang = ["甲", "子", "丙", "寅", "壬", "辰", "癸", "亥"]
        result = query_shensha("壬辰", bazi_kuigang, True, 3, "海中金")
        self.assertIn("魁罡", result)
        
        # 测试金神日
        bazi_jinshen = ["甲", "子", "丙", "寅", "乙", "丑", "癸", "亥"]
        result = query_shensha("乙丑", bazi_jinshen, True, 3, "海中金")
        self.assertIn("金神", result)
        
        # 测试十恶大败日
        bazi_shiedabai = ["甲", "子", "丙", "寅", "甲", "辰", "癸", "亥"]
        result = query_shensha("甲辰", bazi_shiedabai, True, 3, "海中金")
        self.assertIn("十恶大败", result)


if __name__ == "__main__":
    unittest.main()
