#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试缓存检测脚本
"""

import os
import re
from pathlib import Path

def debug_cache_in_file(file_path):
    """调试单个文件中的缓存使用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        lines = content.split('\n')
        
        print(f"\n🔍 检查文件: {file_path}")
        print("=" * 60)
        
        # 查找所有包含cache的行
        cache_lines = []
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if 'cache' in line_stripped.lower():
                cache_lines.append((i + 1, line_stripped))
        
        if cache_lines:
            print(f"📦 发现 {len(cache_lines)} 行包含cache:")
            for line_num, line_content in cache_lines:
                print(f"  {line_num:3d}: {line_content}")
        else:
            print("❌ 未发现cache相关内容")
            
        # 查找路由定义
        ninja_pattern = r'@\w*router\.(get|post|put|delete|patch)\s*\(["\']([^"\']+)["\']'
        api_count = 0
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            match = re.search(ninja_pattern, line_stripped)
            if match:
                api_count += 1
                method = match.group(1).upper()
                path = match.group(2)
                
                print(f"\n🚀 API {api_count}: {method} {path} (第{i+1}行)")
                
                # 检查前后10行的装饰器
                start_line = max(0, i - 5)
                end_line = min(len(lines), i + 10)
                
                decorators = []
                for j in range(start_line, end_line):
                    check_line = lines[j].strip()
                    if check_line.startswith('@'):
                        decorators.append(f"  {j+1:3d}: {check_line}")
                
                if decorators:
                    print("  装饰器:")
                    for decorator in decorators:
                        print(decorator)
                else:
                    print("  ❌ 未发现装饰器")
        
        print(f"\n📊 总计发现 {api_count} 个API")
        
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {e}")

def main():
    """主函数"""
    # 测试几个关键文件
    test_files = [
        "api/ninja_apis/routertest1/prognosis_api.py",
        "api/ninja_apis/routertest1/bazi_analysis_api.py",
        "api/views/modules/forum/post_views.py",
        "api/views/tcmchat_refactored/ai_chat/views.py"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            debug_cache_in_file(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")

if __name__ == "__main__":
    main()
