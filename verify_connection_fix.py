#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证连接池修复效果
测试之前QPS特别低的API
"""

import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# 测试配置
BASE_URL = "http://127.0.0.1:8000"

# 读取测试token
try:
    with open("test_token_user_2.txt", "r") as f:
        TOKEN = f.read().strip()
except FileNotFoundError:
    TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VhDgOlaTZFjxZaGUdPhe7r658Vqfq48TOc6jTH-6zSA"

headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

# 测试API列表
TEST_APIS = [
    {
        "name": "日活动API (新增)",
        "url": f"{BASE_URL}/api/async-bank/daily-activity/",
        "method": "GET",
        "old_qps": 3.23
    },
    {
        "name": "时间效率排行",
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_time_efficiency_ranking",
        "method": "GET", 
        "old_qps": 0.32
    },
    {
        "name": "案例排行榜",
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_cases_ranking",
        "method": "GET",
        "old_qps": 0.32
    }
]

def test_single_request(api):
    """测试单个请求"""
    start_time = time.time()
    try:
        response = requests.request(api["method"], api["url"], headers=headers, timeout=30)
        end_time = time.time()
        return {
            "success": response.status_code == 200,
            "response_time": end_time - start_time,
            "status_code": response.status_code
        }
    except Exception as e:
        return {
            "success": False,
            "response_time": time.time() - start_time,
            "status_code": 0,
            "error": str(e)
        }

def test_concurrent(api, concurrent=10, total=50):
    """测试并发请求"""
    print(f"🧪 测试 {api['name']} - 并发{concurrent}, 总请求{total}")
    
    start_time = time.time()
    results = []
    
    with ThreadPoolExecutor(max_workers=concurrent) as executor:
        futures = [executor.submit(test_single_request, api) for _ in range(total)]
        for future in as_completed(futures):
            results.append(future.result())
    
    end_time = time.time()
    total_time = end_time - start_time
    
    success_count = sum(1 for r in results if r["success"])
    qps = total / total_time if total_time > 0 else 0
    error_rate = ((total - success_count) / total) * 100
    avg_response_time = sum(r["response_time"] for r in results) / len(results)
    
    improvement = qps / api["old_qps"] if api["old_qps"] > 0 else float('inf')
    
    status = "✅" if error_rate < 10 else "⚠️" if error_rate < 50 else "❌"
    improvement_icon = "🚀" if improvement > 10 else "📈" if improvement > 2 else "📉"
    
    print(f"   {status} QPS: {qps:.1f} {improvement_icon} (提升{improvement:.1f}x)")
    print(f"   错误率: {error_rate:.1f}%, 平均响应时间: {avg_response_time:.3f}s")
    
    return {
        "qps": qps,
        "error_rate": error_rate,
        "improvement": improvement,
        "success": error_rate < 20
    }

def main():
    """主函数"""
    print("🔧 连接池修复效果验证")
    print("=" * 50)
    
    fixed_count = 0
    
    for api in TEST_APIS:
        print(f"\n📊 {api['name']}")
        print(f"   URL: {api['url']}")
        print(f"   修复前QPS: {api['old_qps']}")
        
        # 先测试单个请求
        single_result = test_single_request(api)
        if not single_result["success"]:
            print(f"   ❌ API不可用: {single_result.get('error', 'Unknown error')}")
            continue
        
        print(f"   ✅ API可用，响应时间: {single_result['response_time']:.3f}s")
        
        # 测试并发
        best_result = None
        for concurrent in [1, 5, 10]:
            result = test_concurrent(api, concurrent, 30)
            if best_result is None or result["qps"] > best_result["qps"]:
                best_result = result
        
        if best_result and best_result["success"] and best_result["improvement"] > 2:
            fixed_count += 1
            print(f"   🎉 修复成功!")
        else:
            print(f"   ⚠️ 仍需优化")
    
    print(f"\n🎯 修复总结:")
    print(f"   修复成功: {fixed_count}/{len(TEST_APIS)} 个API")
    
    if fixed_count >= len(TEST_APIS) * 0.8:
        print("   🎉 连接池问题修复效果优秀!")
    elif fixed_count >= len(TEST_APIS) * 0.5:
        print("   👍 连接池问题修复效果良好")
    else:
        print("   ⚠️ 连接池问题仍需进一步优化")

if __name__ == "__main__":
    main()
