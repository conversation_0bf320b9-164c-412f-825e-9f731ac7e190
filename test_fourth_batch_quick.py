#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第四批API快速验证测试脚本
快速测试所有API的可用性和基本性能
支持最高100并发，无限制
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime
from typing import Dict, Any

# 导入第四批API配置
from 第四批测试API清单 import FOURTH_BATCH_API_CONFIGS

class QuickAPITester:
    """快速API测试器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = None
        self.token = self._load_token()
        
    def _load_token(self) -> str:
        """加载测试token"""
        try:
            with open('test_token_user_2.txt', 'r') as f:
                return f.read().strip()
        except FileNotFoundError:
            print("⚠️ 未找到test_token_user_2.txt文件")
            return ""
    
    async def create_session(self):
        """创建HTTP会话"""
        connector = aiohttp.TCPConnector(
            limit=200,
            limit_per_host=100,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json",
                "User-Agent": "QuickTester/1.0"
            }
        )
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
    
    async def test_single_api(self, api_name: str, api_config: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个API"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{api_config['url']}"
            method = api_config['method'].upper()
            
            # 准备请求参数
            kwargs = {}
            if method == "POST" and "data" in api_config:
                kwargs["json"] = api_config["data"]
            elif method == "GET" and "params" in api_config:
                kwargs["params"] = api_config["params"]
            
            # 发送请求
            async with self.session.request(method, url, **kwargs) as response:
                response_time = time.time() - start_time
                
                # 读取响应内容
                try:
                    content = await response.text()
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = json.loads(content)
                    else:
                        data = {"content": content[:200] + "..." if len(content) > 200 else content}
                except:
                    data = {"content": "无法解析响应"}
                
                return {
                    "api_name": api_name,
                    "success": response.status < 400,
                    "status_code": response.status,
                    "response_time": response_time,
                    "category": api_config['category'],
                    "risk_level": api_config['risk_level'],
                    "method": method,
                    "url": api_config['url'],
                    "data": data,
                    "error": None
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "api_name": api_name,
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "category": api_config['category'],
                "risk_level": api_config['risk_level'],
                "method": api_config['method'],
                "url": api_config['url'],
                "data": None,
                "error": str(e)
            }
    
    async def test_concurrent_api(self, api_name: str, api_config: Dict[str, Any], concurrent: int = 50) -> Dict[str, Any]:
        """并发测试单个API"""
        print(f"🔄 并发测试 {api_name} (并发数: {concurrent})")
        
        # 创建并发任务
        tasks = []
        for i in range(concurrent):
            task = asyncio.create_task(self.test_single_api(f"{api_name}_req_{i}", api_config))
            tasks.append(task)
        
        # 执行并发测试
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 统计结果
        success_count = 0
        error_count = 0
        response_times = []
        
        for result in results:
            if isinstance(result, Exception):
                error_count += 1
                continue
                
            if result["success"]:
                success_count += 1
            else:
                error_count += 1
                
            response_times.append(result["response_time"])
        
        # 计算统计数据
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        qps = concurrent / total_time if total_time > 0 else 0
        success_rate = (success_count / concurrent) * 100
        
        return {
            "api_name": api_name,
            "concurrent": concurrent,
            "success_count": success_count,
            "error_count": error_count,
            "success_rate": success_rate,
            "avg_response_time": avg_response_time,
            "qps": qps,
            "total_time": total_time,
            "category": api_config['category'],
            "risk_level": api_config['risk_level']
        }

async def main():
    """主测试函数"""
    print("🚀 第四批API快速验证测试启动")
    print("=" * 80)
    
    tester = QuickAPITester()
    await tester.create_session()
    
    try:
        # 第一阶段：单次请求验证所有API
        print("\n📋 第一阶段：API可用性验证")
        print("-" * 60)
        
        single_results = []
        for api_name, api_config in FOURTH_BATCH_API_CONFIGS.items():
            result = await tester.test_single_api(api_name, api_config)
            single_results.append(result)
            
            # 输出结果
            status = "✅" if result["success"] else "❌"
            print(f"{status} {api_name:<20} {result['status_code']:<4} {result['response_time']*1000:>6.1f}ms {result['category']}")
            
            # 短暂休息
            await asyncio.sleep(0.1)
        
        # 统计第一阶段结果
        available_apis = [r for r in single_results if r["success"]]
        unavailable_apis = [r for r in single_results if not r["success"]]
        
        print(f"\n📊 第一阶段结果:")
        print(f"✅ 可用API: {len(available_apis)}/{len(single_results)} ({len(available_apis)/len(single_results)*100:.1f}%)")
        print(f"❌ 不可用API: {len(unavailable_apis)}")
        
        if unavailable_apis:
            print("\n❌ 不可用API详情:")
            for api in unavailable_apis:
                error_msg = api['error'] or f"HTTP {api['status_code']}"
                print(f"  - {api['api_name']}: {error_msg}")
        
        # 第二阶段：对可用API进行并发测试
        if available_apis:
            print(f"\n📋 第二阶段：并发性能测试 (测试{len(available_apis)}个可用API)")
            print("-" * 60)
            
            concurrent_results = []
            for api_result in available_apis:
                api_name = api_result["api_name"]
                api_config = FOURTH_BATCH_API_CONFIGS[api_name]
                
                # 根据风险等级选择并发数
                if api_config['risk_level'] == '中':
                    concurrent = 15
                else:
                    concurrent = 100
                
                result = await tester.test_concurrent_api(api_name, api_config, concurrent)
                concurrent_results.append(result)
                
                # 输出结果
                status = "🟢" if result["success_rate"] >= 95 else "🟡" if result["success_rate"] >= 80 else "🔴"
                print(f"{status} {api_name:<20} {result['success_rate']:>6.1f}% {result['qps']:>6.1f}QPS {result['avg_response_time']*1000:>6.1f}ms")
                
                # 休息避免过载
                await asyncio.sleep(1)
            
            # 生成最终报告
            print(f"\n" + "=" * 80)
            print("📊 第四批API测试完整报告")
            print("=" * 80)
            
            # 按类别统计
            categories = {}
            for result in concurrent_results:
                category = result['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append(result)
            
            print(f"\n📋 按业务模块统计:")
            for category, results in categories.items():
                avg_success_rate = sum(r['success_rate'] for r in results) / len(results)
                avg_qps = sum(r['qps'] for r in results) / len(results)
                print(f"  🔹 {category}: {len(results)}个API, 平均成功率{avg_success_rate:.1f}%, 平均QPS{avg_qps:.1f}")
            
            # 性能排行榜
            print(f"\n🏆 性能排行榜 (按QPS排序):")
            sorted_results = sorted(concurrent_results, key=lambda x: x['qps'], reverse=True)
            for i, result in enumerate(sorted_results[:10], 1):
                print(f"  {i:2d}. {result['api_name']:<20} {result['qps']:>6.1f}QPS {result['success_rate']:>6.1f}%")
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"fourth_batch_quick_test_{timestamp}.json"
            
            test_data = {
                "test_time": datetime.now().isoformat(),
                "single_test_results": single_results,
                "concurrent_test_results": concurrent_results,
                "summary": {
                    "total_apis": len(single_results),
                    "available_apis": len(available_apis),
                    "availability_rate": len(available_apis)/len(single_results)*100,
                    "avg_success_rate": sum(r['success_rate'] for r in concurrent_results) / len(concurrent_results) if concurrent_results else 0,
                    "avg_qps": sum(r['qps'] for r in concurrent_results) / len(concurrent_results) if concurrent_results else 0
                }
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {filename}")
        
        else:
            print("\n❌ 没有可用的API进行并发测试")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
    finally:
        await tester.close_session()
        print("\n🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
