#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置验证脚本
验证性能测试API配置的完整性和正确性
"""

from performance_test_api_config import (
    PERFORMANCE_TEST_APIS,
    TEST_SCENARIOS,
    API_CATEGORIES,
    HIGH_PRIORITY_APIS,
    MEDIUM_PRIORITY_APIS,
    BASE_URL
)

def validate_api_config():
    """验证API配置的完整性"""
    print("🔍 验证API配置...")
    print("=" * 60)
    
    errors = []
    warnings = []
    
    # 1. 验证基本配置
    print("1️⃣ 验证基本配置...")
    if not BASE_URL:
        errors.append("BASE_URL 未配置")
    else:
        print(f"   ✅ BASE_URL: {BASE_URL}")
    
    if not PERFORMANCE_TEST_APIS:
        errors.append("PERFORMANCE_TEST_APIS 为空")
    else:
        print(f"   ✅ 总API数量: {len(PERFORMANCE_TEST_APIS)}")
    
    # 2. 验证每个API的必需字段
    print("\n2️⃣ 验证API字段完整性...")
    required_fields = ['url', 'method', 'description', 'category', 'priority']
    
    for api_name, config in PERFORMANCE_TEST_APIS.items():
        missing_fields = []
        for field in required_fields:
            if field not in config:
                missing_fields.append(field)
        
        if missing_fields:
            errors.append(f"API '{api_name}' 缺少字段: {missing_fields}")
        
        # 验证URL格式
        if not config.get('url', '').startswith(BASE_URL):
            warnings.append(f"API '{api_name}' URL可能不正确: {config.get('url')}")
        
        # 验证HTTP方法
        valid_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
        if config.get('method') not in valid_methods:
            errors.append(f"API '{api_name}' HTTP方法无效: {config.get('method')}")
        
        # 验证优先级
        valid_priorities = ['高', '中', '低']
        if config.get('priority') not in valid_priorities:
            errors.append(f"API '{api_name}' 优先级无效: {config.get('priority')}")
    
    # 3. 验证分类一致性
    print("\n3️⃣ 验证分类一致性...")
    config_categories = set()
    for config in PERFORMANCE_TEST_APIS.values():
        config_categories.add(config.get('category'))
    
    api_categories_keys = set(API_CATEGORIES.keys())
    
    if config_categories != api_categories_keys:
        missing_in_categories = config_categories - api_categories_keys
        extra_in_categories = api_categories_keys - config_categories
        
        if missing_in_categories:
            warnings.append(f"API_CATEGORIES 缺少类别: {missing_in_categories}")
        if extra_in_categories:
            warnings.append(f"API_CATEGORIES 多余类别: {extra_in_categories}")
    else:
        print(f"   ✅ 类别一致性检查通过，共 {len(config_categories)} 个类别")
    
    # 4. 验证优先级分组
    print("\n4️⃣ 验证优先级分组...")
    high_priority_from_config = {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('priority') == '高'}
    medium_priority_from_config = {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('priority') == '中'}
    
    if set(high_priority_from_config.keys()) != set(HIGH_PRIORITY_APIS.keys()):
        errors.append("HIGH_PRIORITY_APIS 与配置中的高优先级API不匹配")
    else:
        print(f"   ✅ 高优先级API: {len(HIGH_PRIORITY_APIS)} 个")
    
    if set(medium_priority_from_config.keys()) != set(MEDIUM_PRIORITY_APIS.keys()):
        errors.append("MEDIUM_PRIORITY_APIS 与配置中的中优先级API不匹配")
    else:
        print(f"   ✅ 中优先级API: {len(MEDIUM_PRIORITY_APIS)} 个")
    
    # 5. 验证测试场景
    print("\n5️⃣ 验证测试场景...")
    for scenario_name, apis in TEST_SCENARIOS.items():
        if not apis:
            warnings.append(f"测试场景 '{scenario_name}' 为空")
        else:
            # 检查场景中的API是否都存在于主配置中
            invalid_apis = set(apis.keys()) - set(PERFORMANCE_TEST_APIS.keys())
            if invalid_apis:
                errors.append(f"测试场景 '{scenario_name}' 包含无效API: {invalid_apis}")
            else:
                print(f"   ✅ {scenario_name}: {len(apis)} 个API")
    
    # 6. 验证认证配置
    print("\n6️⃣ 验证认证配置...")
    auth_apis = []
    no_auth_apis = []
    
    for api_name, config in PERFORMANCE_TEST_APIS.items():
        headers = config.get('headers', {})
        if headers.get('Authorization'):
            auth_apis.append(api_name)
        else:
            no_auth_apis.append(api_name)
    
    print(f"   ✅ 需要认证的API: {len(auth_apis)} 个")
    print(f"   ✅ 无需认证的API: {len(no_auth_apis)} 个")
    
    # 输出验证结果
    print("\n" + "=" * 60)
    print("📋 验证结果汇总:")
    
    if not errors and not warnings:
        print("🎉 所有配置验证通过！")
        return True
    
    if errors:
        print(f"\n❌ 发现 {len(errors)} 个错误:")
        for i, error in enumerate(errors, 1):
            print(f"   {i}. {error}")
    
    if warnings:
        print(f"\n⚠️ 发现 {len(warnings)} 个警告:")
        for i, warning in enumerate(warnings, 1):
            print(f"   {i}. {warning}")
    
    return len(errors) == 0

def show_config_summary():
    """显示配置摘要"""
    print("\n📊 API配置摘要:")
    print("=" * 60)
    
    # 按类别统计
    print("按类别统计:")
    for category, apis in API_CATEGORIES.items():
        high_count = sum(1 for api in apis.values() if api.get('priority') == '高')
        auth_count = sum(1 for api in apis.values() if api.get('headers', {}).get('Authorization'))
        cache_count = sum(1 for api in apis.values() if api.get('cache_enabled'))
        
        print(f"  {category}: {len(apis)} 个API (高优先级:{high_count}, 需认证:{auth_count}, 有缓存:{cache_count})")
    
    # 按优先级统计
    print(f"\n按优先级统计:")
    print(f"  高优先级: {len(HIGH_PRIORITY_APIS)} 个API")
    print(f"  中优先级: {len(MEDIUM_PRIORITY_APIS)} 个API")
    
    # 按HTTP方法统计
    print(f"\n按HTTP方法统计:")
    method_count = {}
    for config in PERFORMANCE_TEST_APIS.values():
        method = config.get('method', 'GET')
        method_count[method] = method_count.get(method, 0) + 1
    
    for method, count in sorted(method_count.items()):
        print(f"  {method}: {count} 个API")
    
    # 缓存统计
    cache_enabled = sum(1 for config in PERFORMANCE_TEST_APIS.values() if config.get('cache_enabled'))
    print(f"\n缓存统计:")
    print(f"  启用缓存: {cache_enabled} 个API")
    print(f"  无缓存: {len(PERFORMANCE_TEST_APIS) - cache_enabled} 个API")

if __name__ == "__main__":
    print("🔧 API配置验证工具")
    print("=" * 60)
    
    # 验证配置
    is_valid = validate_api_config()
    
    # 显示摘要
    show_config_summary()
    
    # 最终结果
    print("\n" + "=" * 60)
    if is_valid:
        print("✅ 配置验证完成，可以开始性能测试！")
        print("\n💡 下一步:")
        print("   运行: python test_concurrent_performance.py")
    else:
        print("❌ 配置存在问题，请修复后再进行测试")
