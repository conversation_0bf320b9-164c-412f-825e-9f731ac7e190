"""
八字分析服务模块
处理八字、五行等分析工作
"""
import json
import traceback
from datetime import datetime
import pytz
from api.utils.logging import safe_print
from api.utils.constants import SHENGXIAO_LIST
from api.services.wuyunliuqi import WUYUNLIUQI_AVAILABLE, get_wuyunliuqi
# 绝对导入metaphysic模块，避免循环引用
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
import metaphysic

# 导入节气相关函数
try:
    from bazi_api.complete_wuyunliuqi.great_contribution import getSolarTerm, getPhaseStatus
    SOLARTERM_AVAILABLE = True
    safe_print("成功导入节气相关函数")
except Exception as e:
    SOLARTERM_AVAILABLE = False
    safe_print(f"导入节气相关函数失败: {str(e)}")
    safe_print(traceback.format_exc())

class BaziService:
    """八字分析服务类"""
    
    @staticmethod
    def get_current_bazi():
        """
        获取当前时间的八字信息
        
        Returns:
            str: 当前时间的八字信息
        """
        try:
            return metaphysic.getCurrentBazi()
        except Exception as e:
            safe_print(f"获取当前时间八字时出错: {str(e)}")
            safe_print(traceback.format_exc())
            return "获取八字信息出错"
    
    @staticmethod
    def get_birth_bazi(birth_year, birth_month, birth_day, birth_time):
        """
        获取出生时间的八字信息
        
        Args:
            birth_year (int): 出生年
            birth_month (int): 出生月
            birth_day (int): 出生日
            birth_time (int): 出生时辰
            
        Returns:
            str: 八字信息
        """
        try:
            return metaphysic.getShenChenBaZi(birth_year, birth_month, birth_day, birth_time)
        except Exception as e:
            safe_print(f"获取出生时间八字时出错: {str(e)}")
            safe_print(traceback.format_exc())
            return "获取八字信息出错"
    
    @staticmethod
    def get_shengxiao(birth_year):
        """
        获取生肖信息
        
        Args:
            birth_year (int): 出生年份
            
        Returns:
            str: 生肖
        """
        return SHENGXIAO_LIST[(birth_year - 4) % 12]
    
    @staticmethod
    def get_wuxing_analysis(shenchenbazi):
        """
        获取五行分析结果
        
        Args:
            shenchenbazi (str): 八字信息
            
        Returns:
            dict: 五行分析结果
        """
        try:
            wuxing_tuple = metaphysic.getWuXing(shenchenbazi)
            return {
                "金": wuxing_tuple[0],
                "木": wuxing_tuple[1],
                "水": wuxing_tuple[2],
                "火": wuxing_tuple[3],
                "土": wuxing_tuple[4]
            }
        except Exception as e:
            safe_print(f"获取五行分析时出错: {str(e)}")
            safe_print(traceback.format_exc())
            return {"error": "获取五行分析失败"}
    
    @staticmethod
    def get_solar_terms_info():
        """
        获取当前节气和下一个节气信息
        
        Returns:
            tuple: (当前节气, 下一个节气, 阶段状态)
        """
        try:
            if SOLARTERM_AVAILABLE:
                now = datetime.now(pytz.timezone('Asia/Shanghai'))
                today_solar_term, next_solar_term = getSolarTerm(now.year, now.month, now.day)
                phase_status = getPhaseStatus(today_solar_term, next_solar_term)
                return today_solar_term, next_solar_term, phase_status
            else:
                safe_print("节气相关函数不可用")
                return "未知", "未知", 0
        except Exception as e:
            safe_print(f"获取节气信息出错: {str(e)}")
            safe_print(traceback.format_exc())
            return "未知", "未知", 0
    
    @staticmethod
    def get_wuyun_liuqi_data(include_wuyun_liuqi, birth_year, birth_month, birth_day):
        """
        获取五运六气数据
        
        Args:
            include_wuyun_liuqi (bool): 是否包含五运六气
            birth_year (int): 出生年
            birth_month (int): 出生月
            birth_day (int): 出生日
            
        Returns:
            dict: 五运六气数据，如果不需要或出错则返回None
        """
        if not include_wuyun_liuqi:
            return None
            
        try:
            # 使用完整的五运六气系统
            if WUYUNLIUQI_AVAILABLE:
                safe_print("使用完整的五运六气系统")
                current_date = datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y-%m-%d")
                birth_date = f"{birth_year}-{birth_month}-{birth_day}"
                
                safe_print(f"当前日期格式: {current_date}, 出生日期格式: {birth_date}")
                
                try:
                    # 获取当前日期的五运六气
                    safe_print(f"调用五运六气系统获取当前日期信息: {current_date}")
                    current_result_data = get_wuyunliuqi(current_date)
                    safe_print(f"当前日期五运六气获取成功: {current_result_data}")
                except Exception as e:
                    safe_print(f"获取当前日期五运六气失败: {str(e)}")
                    safe_print(f"错误详情: {traceback.format_exc()}")
                    raise
                
                try:
                    # 获取出生日期的五运六气
                    safe_print(f"调用五运六气系统获取出生日期信息: {birth_date}")
                    birth_result_data = get_wuyunliuqi(birth_date)
                    safe_print(f"出生日期五运六气获取成功: {birth_result_data}")
                except Exception as e:
                    safe_print(f"获取出生日期五运六气失败: {str(e)}")
                    safe_print(f"错误详情: {traceback.format_exc()}")
                    raise
                
                # 组合结果
                return {
                    "current": current_result_data,
                    "birth": birth_result_data
                }
            else:
                # 回退到原有系统
                safe_print("完整五运六气系统不可用，使用原有系统")
                safe_print("请检查导入路径和模块可用性")
                current_bazi = metaphysic.getCurrentBazi()
                current_wuyun_liuqi = metaphysic.getWuYunLiuQi(current_bazi)
                return current_wuyun_liuqi
        except Exception as e:
            # 回退到原有系统
            safe_print(f"获取完整五运六气系统结果出错: {str(e)}")
            safe_print(f"错误详情: {traceback.format_exc()}")
            # 记录详细错误信息供调试
            error_type = type(e).__name__
            error_args = getattr(e, 'args', [])
            safe_print(f"错误类型: {error_type}, 错误参数: {error_args}")
            
            try:
                current_bazi = metaphysic.getCurrentBazi()
                current_wuyun_liuqi = metaphysic.getWuYunLiuQi(current_bazi)
                return current_wuyun_liuqi
            except Exception as fallback_e:
                safe_print(f"回退系统也出错: {str(fallback_e)}")
                safe_print(f"回退错误详情: {traceback.format_exc()}")
                return {"error": "五运六气系统无法使用"}
    
    @staticmethod
    def generate_prompt(consult_type, shenchenbazi, wuyun_liuqi_data, templates):
        """
        生成提示信息
        
        Args:
            consult_type (str): 咨询类型
            shenchenbazi (str): 八字信息
            wuyun_liuqi_data (dict): 五运六气数据
            templates (dict): 提示模板
            
        Returns:
            str: 生成的提示信息
        """
        if consult_type not in templates:
            consult_type = "health"  # 默认为健康咨询
            
        template_dict = templates[consult_type]
        
        try:
            # 获取当前时间
            current_time = datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y-%m-%d %H:%M:%S")
            
            # 获取当前时间对应的八字
            try:
                current_bazi = metaphysic.getCurrentBazi()
            except Exception as e:
                safe_print(f"获取当前八字时出错: {str(e)}")
                current_bazi = "未知"
            
            # 获取节气信息
            try:
                today_solar_term, next_solar_term, phase_status = BaziService.get_solar_terms_info()
            except Exception as e:
                safe_print(f"获取节气信息时出错: {str(e)}")
                today_solar_term, next_solar_term, phase_status = "未知", "未知", 0
            
            # 生成共性信息
            common_info = f"\n当前时间: {current_time}\n当前干支: {current_bazi}\n当前节气: {today_solar_term}\n下一节气: {next_solar_term}\n"
            
            # 根据五运六气数据选择合适的模板
            if wuyun_liuqi_data:
                # 根据使用的系统选择不同的提示构建方式
                if WUYUNLIUQI_AVAILABLE and isinstance(wuyun_liuqi_data, dict) and "current" in wuyun_liuqi_data:
                    # 使用完整系统
                    current_data = wuyun_liuqi_data["current"]
                    template = template_dict["with_wuyunliuqi_full"]
                    # 添加共性的节气信息和当前时间信息
                    return common_info + template.format(
                        shenchenbazi=shenchenbazi,
                        dayun=current_data.get('dayun', '未知'),
                        sitian=current_data.get('sitian', '未知'),
                        zaiquan=current_data.get('zaiquan', '未知'),
                        qi_shunxu=current_data.get('qi_shunxu', '未知')
                    )
                else:
                    # 使用原有系统
                    template = template_dict["with_wuyunliuqi_simple"]
                    # 添加共性的节气信息和当前时间信息
                    return common_info + template.format(
                        shenchenbazi=shenchenbazi,
                        wuyun=wuyun_liuqi_data.get('五运', '未知'),
                        liuqi=wuyun_liuqi_data.get('六气', '未知')
                    )
            else:
                # 不使用五运六气
                template = template_dict["without_wuyunliuqi"]
                # 添加共性的节气信息和当前时间信息
                return common_info + template.format(shenchenbazi=shenchenbazi)
                
        except Exception as e:
            # 如果在生成提示信息过程中出现任何错误，回退到最基本的模板
            safe_print(f"生成提示信息时出错: {str(e)}")
            safe_print(traceback.format_exc())
            
            # 使用最基本的模板，不包含任何可能导致错误的额外信息
            template = template_dict["without_wuyunliuqi"]
            return template.format(shenchenbazi=shenchenbazi) 