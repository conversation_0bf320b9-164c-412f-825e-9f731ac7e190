# User Management 视图 - 处理用户管理相关的视图
from ..base.imports import *
# 确保使用正确的同步装饰器
from ..base.decorators import api_timer as sync_api_timer
from django.db import connections
@sync_api_timer("删除用户账户")
def delete_account(request):
    """
    删除用户账户
    处理用户账户删除请求
    """
    if request.method == 'POST':
        try:
            # 使用中间件设置的 user_id，与项目其他 API 保持一致
            user_id = request.user_id
            
            try:
                user = UserInfo.objects.get(id=user_id)
                user.delete()
                return JsonResponse({'success': True, 'message': '账户删除成功'})
            except UserInfo.DoesNotExist:
                return JsonResponse({'success': False, 'message': '用户不存在'}, status=404)
                
        except Exception as e:
            return JsonResponse({'success': False, 'message': str(e)}, status=500)
        finally:
            # 确保数据库连接正确关闭
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
    
    return JsonResponse({'success': False, 'message': '仅支持POST请求'}, status=405)

from api.ninja_apis.async_utils import get_async, save_async, filter_async, exists_async, count_async
from asgiref.sync import sync_to_async

@sync_api_timer("添加自定义症状")
async def add_custom_symptom(request):
    """
    添加自定义症状
    允许用户添加个人症状记录
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            # 使用中间件设置的 user_id，与项目其他 API 保持一致
            user_id = request.user_id
            
            # 支持两种参数名，保持向后兼容
            symptom_name = data.get('symptom_name', '').strip() or data.get('symptom', '').strip()
            
            print(f"[DEBUG] 用户ID: {user_id}, 症状内容: {symptom_name}")
            
            if not symptom_name:
                print("[ERROR] 症状内容为空")
                return JsonResponse({'success': False, 'message': '症状名称不能为空'}, status=400)
            
            # 检查用户是否存在
            try:
                user = await get_async(UserInfo, id=user_id)
                print(f"[DEBUG] 找到用户: {user.id}")
            except UserInfo.DoesNotExist:
                print(f"[ERROR] 用户不存在: {user_id}")
                return JsonResponse({'success': False, 'message': '用户不存在'}, status=404)

            # 检查症状是否已存在（只检查未删除的）
            existing_symptom = await exists_async(
                UserCustomSymptom,
                user_id=user_id,
                symptom_text=symptom_name,
                is_deleted=False
            )
            
            if existing_symptom:
                print(f"[ERROR] 症状已存在: {symptom_name}")
                return JsonResponse({'success': False, 'message': '该症状已存在'}, status=400)
            
            # 检查用户症状数量限制（只统计未删除的）
            from api.utils.async_utils import count_async
            current_count = await count_async(
                UserCustomSymptom,
                user_id=user_id,
                is_deleted=False
            )

            print(f"[DEBUG] 当前症状数量: {current_count}")

            if current_count >= 50:
                print(f"[ERROR] 症状数量超限: {current_count}")
                return JsonResponse({'success': False, 'message': '症状数量已达上限(50个)'}, status=400)

            # 创建症状
            symptom = await sync_to_async(UserCustomSymptom.objects.create)(
                user_id=user_id,
                symptom_text=symptom_name,
                created_at=datetime.now()
            )
            
            print(f"[SUCCESS] 成功创建症状，ID: {symptom.id}")
            
            return JsonResponse({
                'success': True, 
                'message': '症状添加成功',
                'symptom': {
                    'id': symptom.id,
                    'name': symptom.symptom_text,
                    'created_at': symptom.created_at.isoformat()
                },
                'current_count': current_count + 1
            })
            
        except json.JSONDecodeError as e:
            print(f"[ERROR] JSON解析错误: {e}")
            return JsonResponse({'success': False, 'message': 'Invalid JSON format'}, status=400)
        except Exception as e:
            print(f"[ERROR] 创建症状时发生异常: {e}")
            import traceback
            traceback.print_exc()
            return JsonResponse({'success': False, 'message': str(e)}, status=500)
        finally:
            # 确保数据库连接正确关闭
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
    
    return JsonResponse({'success': False, 'message': '仅支持POST请求'}, status=405)

# 修复同步异步混用问题 - 使用异步装饰器
@async_api_timer("获取自定义症状")
@smart_cache(timeout=CACHE_TIMEOUT['short'], vary_on_user=True)  # GET请求缓存5分钟，按用户区分
async def get_custom_symptoms(request):
    """
    获取用户的所有自定义症状
    返回用户的症状列表
    """
    print(f"🚀 [DEBUG] get_custom_symptoms 被调用，方法: {request.method}")

    if request.method == 'GET':
        try:
            print(f"🔧 [DEBUG] 开始处理GET请求...")

            # 使用中间件设置的 user_id，与项目其他 API 保持一致
            user_id = request.user_id
            print(f"👤 [DEBUG] 用户ID: {user_id}")

            try:
                print(f"🔍 [DEBUG] 开始查询用户信息...")
                user = await get_async(UserInfo, id=user_id)
                if user is None:
                    print(f"❌ [ERROR] 用户不存在: {user_id}")
                    return JsonResponse({'success': False, 'message': '用户不存在'}, status=404)
                print(f"✅ [DEBUG] 找到用户: {user.id}, 昵称: {getattr(user, 'nickname', '未知')}")
            except Exception as e:
                print(f"💥 [ERROR] 查询用户时发生异常: {str(e)}")
                print(f"💥 [ERROR] 异常类型: {type(e).__name__}")
                import traceback
                print(f"💥 [ERROR] 完整堆栈: {traceback.format_exc()}")
                return JsonResponse({'success': False, 'message': f'查询用户失败: {str(e)}'}, status=500)
            
            # 查询症状 - 只查询未删除的症状
            print(f"🔍 [DEBUG] 开始查询用户 {user_id} 的症状...")
            try:
                symptoms_list = await filter_async(
                    UserCustomSymptom,
                    user_id=user_id,
                    is_deleted=False  # 只获取未删除的症状
                )
                symptoms_count = len(symptoms_list)
                print(f"📊 [DEBUG] 查询到 {symptoms_count} 个未删除的症状")
            except Exception as e:
                print(f"💥 [ERROR] 查询症状时发生异常: {str(e)}")
                print(f"💥 [ERROR] 异常类型: {type(e).__name__}")
                import traceback
                print(f"💥 [ERROR] 完整堆栈: {traceback.format_exc()}")
                return JsonResponse({'success': False, 'message': f'查询症状失败: {str(e)}'}, status=500)

            # 如果没有症状，也要检查是否有已删除的症状
            if symptoms_count == 0:
                all_symptoms_count = await count_async(UserCustomSymptom, user_id=user_id)
                deleted_symptoms_count = await count_async(UserCustomSymptom, user_id=user_id, is_deleted=True)
                print(f"📈 [DEBUG] 总症状数: {all_symptoms_count}, 已删除症状数: {deleted_symptoms_count}")
            print(f"🔍 [DEBUG] 症状列表详情:")
            
            symptoms_data = []
            for i, symptom in enumerate(symptoms_list):
                print(f"  症状 {i+1}:")
                print(f"    - ID: {symptom.id}")
                print(f"    - symptom_text: '{symptom.symptom_text}'")
                print(f"    - is_deleted: {symptom.is_deleted}")
                print(f"    - created_at: {symptom.created_at}")
                print(f"    - user_id: {symptom.user_id}")
                
                # 检查字段值
                symptom_name = symptom.symptom_text
                if not symptom_name:
                    print(f"    ⚠️  警告: 症状名称为空或None!")
                if symptom_name and len(symptom_name.strip()) == 0:
                    print(f"    ⚠️  警告: 症状名称只包含空格!")
                
                symptom_data = {
                    'id': symptom.id,
                    'name': symptom_name,  # 症状名称 
                    'symptom_text': symptom_name,  # 添加原始字段名，以防前端使用这个字段
                    'created_at': symptom.created_at.isoformat() if symptom.created_at else None,
                    'is_deleted': symptom.is_deleted,
                    'severity': getattr(symptom, 'severity', 'unknown')
                }
                
                print(f"    - 映射后数据: {symptom_data}")
                symptoms_data.append(symptom_data)
            
            # 构建响应数据
            response_data = {
                'success': True,
                'symptoms': symptoms_data,
                'total_count': len(symptoms_data),
                'user_id': user_id,  # 添加用户ID用于调试
                'debug_info': {
                    'query_count': symptoms_count,
                    'response_count': len(symptoms_data),
                    'cache_hit': False  # 这里可以后续添加缓存命中检测
                }
            }
            
            print(f"📦 [DEBUG] 最终响应数据结构:")
            print(f"  - success: {response_data['success']}")
            print(f"  - symptoms 数量: {len(response_data['symptoms'])}")
            print(f"  - total_count: {response_data['total_count']}")
            print(f"返回给前端的 get_custom_symptoms 数据: {response_data}")
            # 检查JSON序列化
            try:
                import json as json_module
                json_str = json_module.dumps(response_data, ensure_ascii=False, indent=2)
                print(f"✅ [DEBUG] JSON序列化成功，长度: {len(json_str)} 字符")
                
                # 打印前200个字符查看结构
                preview = json_str[:200] + "..." if len(json_str) > 200 else json_str
                print(f"📄 [DEBUG] JSON预览: {preview}")
                
            except Exception as json_error:
                print(f"❌ [ERROR] JSON序列化失败: {json_error}")
                return JsonResponse({'success': False, 'message': 'JSON序列化错误'}, status=500)
            
            print(f"🎯 [DEBUG] 即将返回成功响应")
            return JsonResponse(response_data)
            
        except Exception as e:
            print(f"💥 [ERROR] 获取症状时发生异常: {str(e)}")
            print(f"📍 [ERROR] 异常类型: {type(e).__name__}")
            import traceback
            print(f"🔍 [ERROR] 完整堆栈:")
            traceback.print_exc()
            return JsonResponse({'success': False, 'message': str(e)}, status=500)
        finally:
            # 确保数据库连接正确关闭
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
            print(f"🔌 [DEBUG] 数据库连接已关闭")
    
    print(f"❌ [ERROR] 不支持的方法: {request.method}")
    return JsonResponse({'success': False, 'message': '仅支持GET请求'}, status=405)

@sync_api_timer("删除自定义症状")
async def delete_custom_symptom(request, symptom_id):
    """
    删除指定的自定义症状
    
    Args:
        symptom_id: 要删除的症状ID
    """
    if request.method == 'DELETE':
        try:
            # 使用中间件设置的 user_id，与项目其他 API 保持一致
            user_id = request.user_id
            
            try:
                # 确保症状存在且属于当前用户
                symptom = await get_async(UserCustomSymptom, id=symptom_id, user_id=user_id)
            except UserCustomSymptom.DoesNotExist:
                return JsonResponse({'success': False, 'message': '症状不存在或无权限删除'}, status=404)

            symptom_name = symptom.symptom_text
            await sync_to_async(symptom.delete)()
            
            return JsonResponse({
                'success': True, 
                'message': f'症状"{symptom_name}"删除成功'
            })
            
        except Exception as e:
            return JsonResponse({'success': False, 'message': str(e)}, status=500)
        finally:
            # 确保数据库连接正确关闭
            from django.db import connections
            for conn in connections.all():
                try:
                    conn.close()
                except Exception:
                    pass
    
    return JsonResponse({'success': False, 'message': '仅支持DELETE请求'}, status=405)

@sync_api_timer("获取所有问卷症状")
@require_http_methods(["GET"])
def get_all_symptoms(request):
    """
    获取用户的所有问卷症状
    这些症状来源于用户填写的问卷
    """
    try:
        user_id = request.user_id
        
        # 直接使用 user_id 查询，并按 id 降序排序
        symptoms_query = UserSymptom.objects.filter(user_id=user_id).values(
            'id', 'symptom', 'level', 'questionnaire_id'
        ).order_by('-id')
        
        symptoms_list = list(symptoms_query)
        # print(f"返回给前端的 get_all_symptoms 数据: {symptoms_list}")
        return JsonResponse({
            'success': True,
            'symptoms': symptoms_list
        })

    except Exception as e:
        # 记录错误
        print(f"[ERROR] 获取问卷症状时发生异常: {e}")

        traceback.print_exc()
        return JsonResponse({'success': False, 'message': '获取症状失败，请稍后重试'}, status=500)
    finally:
        # 确保数据库连接正确关闭
        
        for conn in connections.all():
            try:
                conn.close()
            except Exception:
                pass 