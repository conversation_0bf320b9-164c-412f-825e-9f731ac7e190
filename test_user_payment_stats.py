#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试用户付费统计功能
包括登录时间分布和平均付费金额统计
"""

import os
import sys
import django
from datetime import datetime, timedelta
import pytz

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

from django.utils import timezone
from django.db.models import Sum, Count
from api.models import UserInfo, PaymentOrder_Wechat, Order

def test_payment_stats():
    """测试付费统计功能"""
    print("🔍 测试用户付费统计功能")
    print("=" * 50)
    
    # 获取北京时间
    beijing_tz = pytz.timezone('Asia/Shanghai')
    now_beijing = timezone.now().astimezone(beijing_tz)
    today = now_beijing.date()
    
    print(f"📅 统计日期: {today} (北京时间)")
    
    # 设置时间范围
    today_start_beijing = beijing_tz.localize(datetime.combine(today, datetime.min.time()))
    today_end_beijing = beijing_tz.localize(datetime.combine(today, datetime.max.time()))
    today_start = today_start_beijing.astimezone(pytz.UTC)
    today_end = today_end_beijing.astimezone(pytz.UTC)
    
    print(f"⏰ 查询时间范围: {today_start} ~ {today_end}")
    
    # 1. 测试登录时间分布统计
    print("\n📊 1. 登录时间分布统计")
    print("-" * 30)
    
    # 获取今日登录用户
    today_login_users = UserInfo.objects.filter(
        last_login_date__date=today
    ).count()
    print(f"今日登录用户总数: {today_login_users}")
    
    if today_login_users > 0:
        # 获取登录时间
        today_login_times = UserInfo.objects.filter(
            last_login_date__date=today
        ).values_list('last_login_date', flat=True)
        
        # 按小时统计
        hourly_distribution = {}
        for hour in range(24):
            hourly_distribution[hour] = 0
            
        for login_time in today_login_times:
            if login_time:
                beijing_time = login_time.astimezone(beijing_tz)
                hour = beijing_time.hour
                hourly_distribution[hour] += 1
        
        # 显示分布情况
        print("小时分布:")
        for hour, count in hourly_distribution.items():
            if count > 0:
                print(f"  {hour:02d}:00 - {count}人")
        
        # 找出高峰时段
        peak_hour = max(hourly_distribution.items(), key=lambda x: x[1])
        print(f"登录高峰: {peak_hour[0]:02d}:00-{peak_hour[0]+1:02d}:00 ({peak_hour[1]}人)")
    
    # 2. 测试付费统计
    print("\n💰 2. 付费统计")
    print("-" * 30)
    
    # 微信支付统计
    wechat_orders = PaymentOrder_Wechat.objects.filter(
        success_time__gte=today_start,
        success_time__lte=today_end,
        trade_state='SUCCESS'
    )
    wechat_count = wechat_orders.count()
    wechat_amount = wechat_orders.aggregate(total=Sum('amount'))['total'] or 0
    wechat_amount_yuan = wechat_amount / 100
    
    print(f"微信支付: {wechat_count}笔, 总额¥{wechat_amount_yuan:.2f}")
    
    # 支付宝统计
    alipay_orders = Order.objects.filter(
        pay_time__gte=today_start,
        pay_time__lte=today_end,
        status='Completed'
    )
    alipay_count = alipay_orders.count()
    alipay_amount = alipay_orders.aggregate(total=Sum('total_amount'))['total'] or 0
    
    print(f"支付宝支付: {alipay_count}笔, 总额¥{alipay_amount:.2f}")
    
    # 总计
    total_orders = wechat_count + alipay_count
    total_amount = wechat_amount_yuan + float(alipay_amount)
    
    print(f"总计: {total_orders}笔, 总额¥{total_amount:.2f}")
    
    # 3. 计算平均付费金额
    print("\n📈 3. 平均付费金额")
    print("-" * 30)
    
    if total_orders > 0:
        avg_payment = total_amount / total_orders
        print(f"整体平均付费: ¥{avg_payment:.2f}")
        
        if wechat_count > 0:
            wechat_avg = wechat_amount_yuan / wechat_count
            print(f"微信平均付费: ¥{wechat_avg:.2f}")
            
        if alipay_count > 0:
            alipay_avg = float(alipay_amount) / alipay_count
            print(f"支付宝平均付费: ¥{alipay_avg:.2f}")
    else:
        print("今日暂无付费记录")
    
    # 4. 测试新增用户和转化率
    print("\n👥 4. 用户转化率")
    print("-" * 30)
    
    today_new_users = UserInfo.objects.filter(
        created_at__date=today
    ).count()
    
    print(f"今日新增用户: {today_new_users}")
    print(f"今日付费用户: {total_orders}")
    
    if today_new_users > 0:
        conversion_rate = (total_orders / today_new_users) * 100
        print(f"付费转化率: {conversion_rate:.1f}%")
    else:
        print("付费转化率: 0%")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    test_payment_stats()
