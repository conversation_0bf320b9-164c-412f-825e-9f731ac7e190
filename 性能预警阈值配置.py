#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API性能预警阈值配置
基于三批性能测试结果制定的预警阈值
用于监控系统和自动化告警
"""

from datetime import datetime

# 基准性能数据（来自实际测试结果）
PERFORMANCE_BASELINES = {
    # 第一批：核心业务API（高并发基准）
    "HomePage": {
        "baseline_qps": 351.3,
        "baseline_response_time": 224.1,  # ms
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 100.0,
        "api_type": "缓存型",
        "priority": "核心",
        "concurrent_level": "高并发"
    },
    "检查会员状态": {
        "baseline_qps": 119.9,
        "baseline_response_time": 802.6,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "标准业务",
        "priority": "核心",
        "concurrent_level": "高并发"
    },
    "疗法分类列表": {
        "baseline_qps": 107.6,
        "baseline_response_time": 909.4,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "标准业务",
        "priority": "核心",
        "concurrent_level": "高并发"
    },
    "计算证型指标": {
        "baseline_qps": 57.7,
        "baseline_response_time": 1833.0,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "计算密集型",
        "priority": "核心",
        "concurrent_level": "高并发"
    },
    
    # 第二批：扩展功能API（标准并发基准）
    "自定义症状": {
        "baseline_qps": 122.1,
        "baseline_response_time": 800.9,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "标准业务",
        "priority": "扩展",
        "concurrent_level": "标准并发"
    },
    "医案总分排行": {
        "baseline_qps": 119.2,
        "baseline_response_time": 818.8,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "标准业务",
        "priority": "扩展",
        "concurrent_level": "标准并发"
    },
    "论坛帖子列表": {
        "baseline_qps": 106.6,
        "baseline_response_time": 955.7,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "标准业务",
        "priority": "扩展",
        "concurrent_level": "标准并发"
    },
    "所有症状": {
        "baseline_qps": 72.2,
        "baseline_response_time": 1146.0,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "标准业务",
        "priority": "扩展",
        "concurrent_level": "标准并发"
    },
    
    # 第三批：谨慎API（低并发基准）
    "空请求": {
        "baseline_qps": 122.9,
        "baseline_response_time": 39.8,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "轻量级",
        "priority": "谨慎",
        "concurrent_level": "低并发"
    },
    "检查问卷填写": {
        "baseline_qps": 83.1,
        "baseline_response_time": 113.9,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "标准业务",
        "priority": "谨慎",
        "concurrent_level": "低并发"
    },
    "提交答题得分": {
        "baseline_qps": 70.5,
        "baseline_response_time": 133.4,
        "baseline_success_rate": 100.0,
        "baseline_cache_hit_rate": 0.0,
        "api_type": "写入操作",
        "priority": "谨慎",
        "concurrent_level": "低并发"
    }
}

# 预警阈值配置
ALERT_THRESHOLDS = {
    # 黄色警告阈值（性能下降但可接受）
    "yellow": {
        "qps_decline_percent": 30,      # QPS下降30%
        "response_time_increase_percent": 50,  # 响应时间增加50%
        "success_rate_min": 95.0,       # 成功率低于95%
        "cache_hit_rate_decline_percent": 50,  # 缓存命中率下降50%
    },
    
    # 红色警告阈值（性能严重下降）
    "red": {
        "qps_decline_percent": 50,      # QPS下降50%
        "response_time_increase_percent": 100, # 响应时间增加100%
        "success_rate_min": 90.0,       # 成功率低于90%
        "cache_hit_rate_decline_percent": 70,  # 缓存命中率下降70%
        "continuous_yellow_minutes": 5,  # 连续5分钟黄色警告
    }
}

# 按API类型的特殊阈值
API_TYPE_THRESHOLDS = {
    "缓存型": {
        "yellow": {
            "qps_decline_percent": 20,  # 缓存型API对QPS下降更敏感
            "response_time_increase_percent": 30,
            "cache_hit_rate_decline_percent": 30,
        },
        "red": {
            "qps_decline_percent": 40,
            "response_time_increase_percent": 60,
            "cache_hit_rate_decline_percent": 50,
        }
    },
    
    "计算密集型": {
        "yellow": {
            "qps_decline_percent": 40,  # 计算密集型API允许更大的QPS波动
            "response_time_increase_percent": 80,
        },
        "red": {
            "qps_decline_percent": 60,
            "response_time_increase_percent": 150,
        }
    },
    
    "写入操作": {
        "yellow": {
            "success_rate_min": 98.0,   # 写入操作对成功率要求更高
            "response_time_increase_percent": 40,
        },
        "red": {
            "success_rate_min": 95.0,
            "response_time_increase_percent": 80,
        }
    }
}

def calculate_alert_thresholds(api_name):
    """计算特定API的预警阈值"""
    if api_name not in PERFORMANCE_BASELINES:
        return None
    
    baseline = PERFORMANCE_BASELINES[api_name]
    api_type = baseline["api_type"]
    
    # 获取基础阈值
    base_yellow = ALERT_THRESHOLDS["yellow"].copy()
    base_red = ALERT_THRESHOLDS["red"].copy()
    
    # 应用API类型特殊阈值
    if api_type in API_TYPE_THRESHOLDS:
        type_thresholds = API_TYPE_THRESHOLDS[api_type]
        base_yellow.update(type_thresholds.get("yellow", {}))
        base_red.update(type_thresholds.get("red", {}))
    
    # 计算具体阈值
    yellow_thresholds = {
        "qps_min": baseline["baseline_qps"] * (1 - base_yellow["qps_decline_percent"] / 100),
        "response_time_max": baseline["baseline_response_time"] * (1 + base_yellow["response_time_increase_percent"] / 100),
        "success_rate_min": base_yellow["success_rate_min"],
    }
    
    red_thresholds = {
        "qps_min": baseline["baseline_qps"] * (1 - base_red["qps_decline_percent"] / 100),
        "response_time_max": baseline["baseline_response_time"] * (1 + base_red["response_time_increase_percent"] / 100),
        "success_rate_min": base_red["success_rate_min"],
    }
    
    # 添加缓存命中率阈值（如果适用）
    if baseline["baseline_cache_hit_rate"] > 0:
        yellow_thresholds["cache_hit_rate_min"] = baseline["baseline_cache_hit_rate"] * (1 - base_yellow["cache_hit_rate_decline_percent"] / 100)
        red_thresholds["cache_hit_rate_min"] = baseline["baseline_cache_hit_rate"] * (1 - base_red["cache_hit_rate_decline_percent"] / 100)
    
    return {
        "api_name": api_name,
        "baseline": baseline,
        "yellow": yellow_thresholds,
        "red": red_thresholds,
        "generated_at": datetime.now().isoformat()
    }

def check_performance_alert(api_name, current_metrics):
    """检查当前性能指标是否触发预警"""
    thresholds = calculate_alert_thresholds(api_name)
    if not thresholds:
        return {"status": "unknown", "message": f"API {api_name} 没有配置基准数据"}
    
    alerts = []
    alert_level = "green"
    
    # 检查QPS
    if current_metrics.get("qps", 0) < thresholds["red"]["qps_min"]:
        alerts.append(f"QPS过低: {current_metrics['qps']:.1f} < {thresholds['red']['qps_min']:.1f}")
        alert_level = "red"
    elif current_metrics.get("qps", 0) < thresholds["yellow"]["qps_min"]:
        alerts.append(f"QPS偏低: {current_metrics['qps']:.1f} < {thresholds['yellow']['qps_min']:.1f}")
        if alert_level == "green":
            alert_level = "yellow"
    
    # 检查响应时间
    if current_metrics.get("response_time", 0) > thresholds["red"]["response_time_max"]:
        alerts.append(f"响应时间过长: {current_metrics['response_time']:.1f}ms > {thresholds['red']['response_time_max']:.1f}ms")
        alert_level = "red"
    elif current_metrics.get("response_time", 0) > thresholds["yellow"]["response_time_max"]:
        alerts.append(f"响应时间偏长: {current_metrics['response_time']:.1f}ms > {thresholds['yellow']['response_time_max']:.1f}ms")
        if alert_level == "green":
            alert_level = "yellow"
    
    # 检查成功率
    if current_metrics.get("success_rate", 100) < thresholds["red"]["success_rate_min"]:
        alerts.append(f"成功率过低: {current_metrics['success_rate']:.1f}% < {thresholds['red']['success_rate_min']:.1f}%")
        alert_level = "red"
    elif current_metrics.get("success_rate", 100) < thresholds["yellow"]["success_rate_min"]:
        alerts.append(f"成功率偏低: {current_metrics['success_rate']:.1f}% < {thresholds['yellow']['success_rate_min']:.1f}%")
        if alert_level == "green":
            alert_level = "yellow"
    
    return {
        "status": alert_level,
        "alerts": alerts,
        "thresholds": thresholds,
        "current_metrics": current_metrics,
        "checked_at": datetime.now().isoformat()
    }

def generate_all_thresholds():
    """生成所有API的预警阈值配置"""
    all_thresholds = {}
    for api_name in PERFORMANCE_BASELINES.keys():
        all_thresholds[api_name] = calculate_alert_thresholds(api_name)
    return all_thresholds

# 示例使用
if __name__ == "__main__":
    # 生成所有API的阈值配置
    print("🎯 API性能预警阈值配置")
    print("=" * 60)
    
    for api_name in ["HomePage", "自定义症状", "提交答题得分"]:
        thresholds = calculate_alert_thresholds(api_name)
        if thresholds:
            print(f"\n📊 {api_name} ({thresholds['baseline']['api_type']}):")
            print(f"   基准: QPS {thresholds['baseline']['baseline_qps']:.1f}, 响应时间 {thresholds['baseline']['baseline_response_time']:.1f}ms")
            print(f"   🟡 黄色警告: QPS < {thresholds['yellow']['qps_min']:.1f}, 响应时间 > {thresholds['yellow']['response_time_max']:.1f}ms")
            print(f"   🔴 红色警告: QPS < {thresholds['red']['qps_min']:.1f}, 响应时间 > {thresholds['red']['response_time_max']:.1f}ms")
    
    # 示例：检查性能指标
    print(f"\n🔍 性能检查示例:")
    test_metrics = {
        "qps": 200,
        "response_time": 400,
        "success_rate": 98.5
    }
    result = check_performance_alert("HomePage", test_metrics)
    print(f"   检查结果: {result['status']}")
    if result['alerts']:
        for alert in result['alerts']:
            print(f"   ⚠️ {alert}")
