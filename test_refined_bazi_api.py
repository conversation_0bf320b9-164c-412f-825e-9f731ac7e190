#!/usr/bin/env python3
"""
测试精简后的八字API
验证信息提取和精简功能
"""

import json
import sys
import os
import django

# 配置Django设置
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

def test_core_info_extraction():
    """测试核心信息提取功能"""
    print("🔧 测试核心信息提取功能...")
    print("=" * 60)
    
    # 模拟前端发送的完整数据
    test_data = {
        "eight_char": "己卯 壬申 丙午 癸巳",
        "gender": "男",
        "shishen": {
            "gan_shishen": {
                "year": "伤官",
                "month": "七杀", 
                "day": "日主",
                "time": "正官"
            },
            "zhi_shishen": {
                "year": ["正印"],
                "month": ["偏财", "七杀", "食神"],
                "day": ["劫财", "伤官"],
                "time": ["比肩", "偏财", "食神"]
            }
        },
        "shensha": {
            "total_count": 24,
            "unique_count": 22,
            "unique_shenshas": [
                "丧门", "亡神", "元辰", "劫煞", "天乙", "天厨", "天喜", "天德",
                "太极", "孤辰", "孤鸾", "学堂", "文昌", "月德", "桃花", "禄神",
                "空亡", "童子", "羊刃", "金舆", "阴差阳错", "驿马"
            ]
        },
        "detailed_info": {
            "birth_info": {
                "solar_date": "1999-08-22 09:00:00 星期日 (邓小平诞辰纪念日) 狮子座",
                "birth_details": {
                    "year": 1999,
                    "month": 8,
                    "day": 22,
                    "hour": 9,
                    "minute": 0
                }
            },
            "current_dayun": {
                "ganzhi": "己巳",
                "start_year": 2024,
                "end_year": 2033
            },
            "liu_nian_info": {
                "liuNian": {"ganzhi": "乙巳"},
                "liuYue": {"ganzhi": "甲申"},
                "liuRi": {"ganzhi": "己亥"}
            }
        }
    }
    
    try:
        # 导入消费者类
        from api.consumers.bazi_advice_consumer import BaziAdviceConsumer
        
        # 创建消费者实例
        consumer = BaziAdviceConsumer()
        
        # 测试核心信息提取
        print("📊 原始数据大小:", len(json.dumps(test_data, ensure_ascii=False)))
        print("📋 原始数据结构:")
        print(json.dumps(test_data, ensure_ascii=False, indent=2)[:500] + "...")
        
        print("\n" + "="*60)
        print("🎯 提取核心信息:")
        
        core_info = consumer._extract_core_bazi_info(test_data)
        
        print("✅ 提取结果:")
        for key, value in core_info.items():
            print(f"  {key}: {value}")
        
        print("\n" + "="*60)
        print("📝 格式化后的消息:")
        
        formatted_message = consumer._format_bazi_message(test_data)
        print(f"消息长度: {len(formatted_message)} 字符")
        
        print("\n" + "="*60)
        print("🆚 对比分析:")
        print(f"原始数据: {len(json.dumps(test_data, ensure_ascii=False))} 字符")
        print(f"精简消息: {len(formatted_message)} 字符")
        print(f"压缩比例: {len(formatted_message)/len(json.dumps(test_data, ensure_ascii=False))*100:.1f}%")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_parameterized_request():
    """测试参数化请求处理"""
    print("\n🎯 测试参数化请求处理...")
    print("=" * 60)
    
    test_request = {
        "analysis_direction": "basic_analysis",
        "domain": "health", 
        "time_scope": "yearly",
        "bazi_data": {
            "eight_char": "己卯 壬申 丙午 癸巳",
            "gender": "男",
            "detailed_info": {
                "birth_info": {
                    "solar_date": "1999-08-22 09:00:00"
                },
                "current_dayun": {
                    "ganzhi": "己巳",
                    "start_year": 2024,
                    "end_year": 2033
                }
            }
        }
    }
    
    try:
        from api.consumers.bazi_advice_consumer import BaziAdviceConsumer
        
        consumer = BaziAdviceConsumer()
        
        print("📤 参数化请求数据:")
        print(json.dumps(test_request, ensure_ascii=False, indent=2))
        
        print("\n🔧 处理参数化请求:")
        system_prompt, user_message = consumer._handle_parameterized_request(test_request)
        
        print(f"\n🤖 系统提示词长度: {len(system_prompt)} 字符")
        print(f"👤 用户消息长度: {len(user_message)} 字符")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 精简八字API测试")
    print("=" * 60)
    
    # 测试核心信息提取
    test_core_info_extraction()
    
    # 测试参数化请求
    test_parameterized_request()
    
    print("\n✅ 测试完成")
    print("现在可以测试WebSocket API: /api/ws/bazi_advice/")
    print("服务器会打印精简后传递给大模型的信息")
