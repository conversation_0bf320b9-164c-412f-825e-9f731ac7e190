<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式八字分析与中医养生建议</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #b02a37;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        /* 确保按钮样式正确 */
        button, button[type="button"] {
            background-color: #b02a37;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
            outline: none;
        }
        button:hover, button[type="button"]:hover {
            background-color: #a52834;
        }
        button:active, button[type="button"]:active {
            background-color: #8f2229;
            transform: translateY(1px);
        }
        .results {
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .results h2 {
            color: #b02a37;
            margin-bottom: 15px;
        }
        .bazi-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .health-advice {
            background-color: #fff8f8;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #b02a37;
            white-space: pre-wrap;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .dot-typing {
            position: relative;
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-right: 10px;
            background-color: #b02a37;
            border-radius: 50%;
            animation: dotTyping 1.5s infinite linear;
        }
        .dot-typing::before,
        .dot-typing::after {
            content: '';
            position: absolute;
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #b02a37;
            border-radius: 50%;
        }
        .dot-typing::before {
            left: -15px;
            animation: dotTyping 1.5s infinite linear;
            animation-delay: 0s;
        }
        .dot-typing::after {
            left: 15px;
            animation: dotTyping 1.5s infinite linear;
            animation-delay: 0.5s;
        }
        @keyframes dotTyping {
            0% { transform: scale(1); }
            25% { transform: scale(1.3); }
            50% { transform: scale(1); }
        }
        .wuxing-chart {
            margin-top: 15px;
            height: 200px;
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
        }
        .wuxing-bar {
            width: 15%;
            background-color: #b02a37;
            text-align: center;
            color: white;
            padding-top: 10px;
            border-radius: 4px 4px 0 0;
            position: relative;
        }
        .wuxing-bar span {
            position: absolute;
            bottom: -25px;
            left: 0;
            right: 0;
            color: #333;
        }
        /* 增加一个渐变动画效果 */
        .fade-in {
            animation: fadeIn 1s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        /* 为流式界面增加实时动态效果 */
        .stream-indicator {
            position: relative;
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #28a745;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { transform: scale(1.1); box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }
        .stream-title {
            display: flex;
            align-items: center;
        }
        .description {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #666;
        }
        
        /* 当前时间八字样式 */
        .current-time-bazi {
            margin-bottom: 30px;
            padding: 15px;
            background: linear-gradient(135deg, #fff8f8 0%, #fff 100%);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(176, 42, 55, 0.1);
            border-left: 4px solid #b02a37;
        }
        
        .current-time-bazi h2 {
            color: #b02a37;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .current-time-bazi .bazi-info {
            background-color: transparent;
            padding: 0;
        }
        
        .current-time-bazi .bazi-info p {
            margin: 8px 0;
            color: #444;
        }
        
        .current-time-bazi .bazi-info strong {
            color: #b02a37;
            margin-right: 10px;
        }
        
        /* 各类咨询样式 */
        #career-advice {
            background-color: #f8faff;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #0d6efd;
            white-space: pre-wrap;
        }
        
        #education-advice {
            background-color: #f8fff8;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #198754;
            white-space: pre-wrap;
        }
        
        #fengshui-advice {
            background-color: #fff8f0;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #fd7e14;
            white-space: pre-wrap;
        }
        
        #fortune-advice {
            background-color: #f8f0ff;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #6f42c1;
            white-space: pre-wrap;
        }
        
        /* 咨询类型标题颜色 */
        #career-advice-title h2 {
            color: #0d6efd;
        }
        
        #education-advice-title h2 {
            color: #198754;
        }
        
        #fengshui-advice-title h2 {
            color: #fd7e14;
        }
        
        #fortune-advice-title h2 {
            color: #6f42c1;
        }
        
        /* 咨询类型选择控件样式 */
        select.form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            background-color: #fff;
            font-family: inherit;
            font-size: inherit;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>流式八字分析与中医养生建议</h1>
        
        <div class="description">
            <p>本页面使用WebSocket技术，可以实时查看分析过程和养生建议的生成。输入您的出生年月日时后，系统将立即开始分析并实时显示结果。</p>
        </div>
        
        <!-- 添加当前时间八字部分 -->
        <div class="current-time-bazi">
            <div class="stream-title">
                <h2>当前时间八字</h2>
            </div>
            <div class="bazi-info fade-in" id="current-time-bazi-info">
                <p><strong>当前时间：</strong><span id="current-time"></span></p>
                <p><strong>对应八字：</strong><span id="current-time-bazi"></span></p>
            </div>
        </div>
        
        <div class="form-group">
            <label for="birth-year">出生年份</label>
            <input type="number" id="birth-year" placeholder="如：1990" min="1900" max="2100">
        </div>
        
        <div class="form-group">
            <label for="birth-month">出生月份</label>
            <input type="number" id="birth-month" placeholder="1-12" min="1" max="12">
        </div>
        
        <div class="form-group">
            <label for="birth-day">出生日期</label>
            <input type="number" id="birth-day" placeholder="1-31" min="1" max="31">
        </div>
        
        <div class="form-group">
            <label for="birth-time">出生时辰 (24小时制)</label>
            <input type="number" id="birth-time" placeholder="0-23" min="0" max="23">
        </div>
        
        <div class="form-group">
            <label for="consult-type">咨询类型</label>
            <select id="consult-type" class="form-control">
                <option value="health">健康咨询</option>
                <option value="career">事业咨询</option>
                <option value="education">学业咨询</option>
                <option value="fengshui">风水咨询</option>
                <option value="fortune">运势咨询</option>
            </select>
            <div style="margin-top: 5px; font-size: 13px; color: #666;">
                选择不同的咨询类型，获取对应领域的专业分析
            </div>
        </div>
        
        <div class="form-group">
            <input type="checkbox" id="include-wuyun-liuqi" checked>
            <label for="include-wuyun-liuqi" style="display: inline;">包含当前五运六气分析</label>
            <div style="margin-left: 22px; font-size: 13px; color: #666;">
                加入当前的五运六气信息，使咨询建议更具针对性
            </div>
        </div>
        
        <button id="analyze-btn" type="button">开始分析</button>
        
        <div class="loading" id="loading">
            <div class="dot-typing"></div>
            <p>正在连接WebSocket服务，请稍候...</p>
        </div>
        
        <div class="results" id="results" style="display: none;">
            <div class="stream-title">
                <div class="stream-indicator" id="stream-indicator"></div>
                <h2>您的生辰八字</h2>
            </div>
            
            <div class="bazi-info fade-in" id="bazi-info">
                <p><strong>生辰八字：</strong> <span id="shenchenbazi"></span></p>
                <p><strong>生肖：</strong> <span id="shengxiao"></span></p>
            </div>
            
            <div class="stream-title">
                <h2>五行分析</h2>
            </div>
            <div class="wuxing-chart" id="wuxing-chart">
                <!-- 五行柱状图将在这里动态生成 -->
            </div>
            
            <!-- 五运六气部分 -->
            <div id="wuyun-liuqi-section" style="display: none;">
                <div class="stream-title">
                    <h2>2025年4月24日15:00 五运六气</h2>
                </div>
                <div class="bazi-info">
                    <p><strong>当前八字：</strong><span id="current-bazi"></span></p>
                    <p><strong>五运：</strong><span id="wuyun"></span></p>
                    <p><strong>六气：</strong><span id="liuqi"></span></p>
                </div>
            </div>
            
            <div class="stream-title">
                <div class="stream-indicator" id="advice-indicator" style="display: none;"></div>
                <h2>中医养生建议 (实时生成中)</h2>
            </div>
            <div class="health-advice fade-in" id="health-advice">
                <!-- 养生建议将在这里实时显示 -->
            </div>
            
            <!-- 事业咨询建议区域 -->
            <div class="stream-title" id="career-advice-title" style="display: none;">
                <div class="stream-indicator" id="career-advice-indicator" style="display: none;"></div>
                <h2>事业发展建议 (实时生成中)</h2>
            </div>
            <div class="health-advice fade-in" id="career-advice" style="display: none;">
                <!-- 事业咨询建议将在这里实时显示 -->
            </div>
            
            <!-- 学业咨询建议区域 -->
            <div class="stream-title" id="education-advice-title" style="display: none;">
                <div class="stream-indicator" id="education-advice-indicator" style="display: none;"></div>
                <h2>学业发展建议 (实时生成中)</h2>
            </div>
            <div class="health-advice fade-in" id="education-advice" style="display: none;">
                <!-- 学业咨询建议将在这里实时显示 -->
            </div>
            
            <!-- 风水咨询建议区域 -->
            <div class="stream-title" id="fengshui-advice-title" style="display: none;">
                <div class="stream-indicator" id="fengshui-advice-indicator" style="display: none;"></div>
                <h2>风水布局建议 (实时生成中)</h2>
            </div>
            <div class="health-advice fade-in" id="fengshui-advice" style="display: none;">
                <!-- 风水咨询建议将在这里实时显示 -->
            </div>
            
            <!-- 运势咨询建议区域 -->
            <div class="stream-title" id="fortune-advice-title" style="display: none;">
                <div class="stream-indicator" id="fortune-advice-indicator" style="display: none;"></div>
                <h2>运势分析建议 (实时生成中)</h2>
            </div>
            <div class="health-advice fade-in" id="fortune-advice" style="display: none;">
                <!-- 运势咨询建议将在这里实时显示 -->
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const analyzeBtn = document.getElementById('analyze-btn');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const shenchenbaziEl = document.getElementById('shenchenbazi');
            const shengxiaoEl = document.getElementById('shengxiao');
            const wuxingChartEl = document.getElementById('wuxing-chart');
            const healthAdviceEl = document.getElementById('health-advice');
            const careerAdviceEl = document.getElementById('career-advice');
            const educationAdviceEl = document.getElementById('education-advice');
            const fengshuiAdviceEl = document.getElementById('fengshui-advice');
            const fortuneAdviceEl = document.getElementById('fortune-advice');
            
            const streamIndicator = document.getElementById('stream-indicator');
            const adviceIndicator = document.getElementById('advice-indicator');
            const careerAdviceIndicator = document.getElementById('career-advice-indicator');
            const educationAdviceIndicator = document.getElementById('education-advice-indicator');
            const fengshuiAdviceIndicator = document.getElementById('fengshui-advice-indicator');
            const fortuneAdviceIndicator = document.getElementById('fortune-advice-indicator');
            
            // 获取所有建议区域标题元素
            const adviceTitles = {
                'health': document.getElementById('health-advice').previousElementSibling,
                'career': document.getElementById('career-advice-title'),
                'education': document.getElementById('education-advice-title'),
                'fengshui': document.getElementById('fengshui-advice-title'),
                'fortune': document.getElementById('fortune-advice-title')
            };
            
            // 获取所有建议区域内容元素
            const adviceContents = {
                'health': healthAdviceEl,
                'career': careerAdviceEl,
                'education': educationAdviceEl,
                'fengshui': fengshuiAdviceEl,
                'fortune': fortuneAdviceEl
            };
            
            // 获取所有流式指示器
            const adviceIndicators = {
                'health': adviceIndicator,
                'career': careerAdviceIndicator,
                'education': educationAdviceIndicator,
                'fengshui': fengshuiAdviceIndicator,
                'fortune': fortuneAdviceIndicator
            };
            
            let currentConsultType = 'health'; // 当前咨询类型
            let socket = null;
            let adviceComplete = false;
            let isFirstMessage = true;
            let debugContainer = null;
            
            // 添加调试区域
            function createDebugArea() {
                if (document.getElementById('debug-area')) return;
                
                debugContainer = document.createElement('div');
                debugContainer.id = 'debug-area';
                debugContainer.style.cssText = 'background-color: #f0f0f0; border: 1px solid #ccc; margin-top: 20px; padding: 10px; font-size: 12px; max-height: 300px; overflow-y: auto; display: block;';
                
                const debugHeader = document.createElement('div');
                debugHeader.innerHTML = '<strong>WebSocket调试信息</strong> <button id="toggle-debug" style="float:right">显示/隐藏</button>';
                
                const debugContent = document.createElement('div');
                debugContent.id = 'debug-content';
                debugContent.style.display = 'block'; // 默认显示调试内容
                
                debugContainer.appendChild(debugHeader);
                debugContainer.appendChild(debugContent);
                
                document.querySelector('.container').appendChild(debugContainer);
                
                document.getElementById('toggle-debug').addEventListener('click', function() {
                    const content = document.getElementById('debug-content');
                    content.style.display = content.style.display === 'none' ? 'block' : 'none';
                });
            }
            
            // 添加调试日志
            function addDebugLog(message, type = 'info') {
                if (!debugContainer) createDebugArea();
                
                const debugContent = document.getElementById('debug-content');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.cssText = type === 'error' ? 'color: red;' : (type === 'success' ? 'color: green;' : '');
                logEntry.innerHTML = `[${timestamp}] ${message}`;
                debugContent.appendChild(logEntry);
                debugContent.scrollTop = debugContent.scrollHeight;
            }
            
            // WebSocket状态显示
            function updateConnectionStatus(status, details = '') {
                const loadingText = document.querySelector('#loading p');
                if (loadingText) {
                    loadingText.textContent = status + (details ? ': ' + details : '');
                }
                console.log('WebSocket状态: ' + status, details);
                addDebugLog('WebSocket状态: ' + status + (details ? ': ' + details : ''));
            }
            
            // 创建WebSocket连接
            function createWebSocket(birthInfo) {
                createDebugArea(); // 确保调试区域存在
                
                if (socket !== null) {
                    try {
                        addDebugLog('关闭现有WebSocket连接');
                        socket.close();
                    } catch (e) {
                        console.error('关闭旧WebSocket连接时出错:', e);
                        addDebugLog('关闭旧WebSocket连接时出错: ' + e.message, 'error');
                    }
                }
                
                isFirstMessage = true; // 重置第一条消息标志
                let connectionTimeout; // 确保变量被声明
                
                // 创建WebSocket连接
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/bazi_advice/`;
                updateConnectionStatus('正在连接WebSocket服务', wsUrl);
                
                try {
                    addDebugLog('正在创建新的WebSocket连接: ' + wsUrl);
                    socket = new WebSocket(wsUrl);
                    
                    // 立即设置WebSocket事件处理器
                    socket.onopen = handleSocketOpen;
                    socket.onmessage = handleSocketMessage;
                    socket.onclose = handleSocketClose;
                    socket.onerror = handleSocketError;
                    
                    // 设置连接超时
                    connectionTimeout = setTimeout(() => {
                        if (socket.readyState !== WebSocket.OPEN) {
                            updateConnectionStatus('连接超时');
                            addDebugLog('WebSocket连接超时', 'error');
                            socket.close();
                            
                            // 尝试重连
                            if (reconnectAttempts < maxReconnectAttempts) {
                                reconnectAttempts++;
                                updateConnectionStatus(`连接失败，正在尝试第${reconnectAttempts}次重连`);
                                setTimeout(() => createWebSocket(birthInfo), reconnectInterval);
                                // 增加重连间隔
                                reconnectInterval *= 1.5;
                            } else {
                                updateConnectionStatus('连接失败，请刷新页面重试');
                                loading.style.display = 'none';
                            }
                        }
                    }, 10000); // 10秒超时
                    
                    function handleSocketOpen(e) {
                        clearTimeout(connectionTimeout);
                        console.log('WebSocket连接已打开', e);
                        addDebugLog('WebSocket连接已成功打开', 'success');
                        reconnectAttempts = 0;
                        reconnectInterval = 3000;
                        updateConnectionStatus('WebSocket连接已建立，正在请求数据');
                        
                        // 发送八字分析请求
                        const requestData = JSON.stringify({
                            birth_info: birthInfo
                        });
                        addDebugLog('发送八字分析请求: ' + requestData);
                        socket.send(requestData);
                        
                        // 设置心跳
                        if (heartbeatInterval) clearInterval(heartbeatInterval);
                        heartbeatInterval = setInterval(() => {
                            if (socket.readyState === WebSocket.OPEN) {
                                try {
                                    socket.send(JSON.stringify({ heartbeat: true }));
                                    addDebugLog('发送心跳包', 'info');
                                } catch (e) {
                                    console.error('发送心跳失败:', e);
                                    addDebugLog('发送心跳失败: ' + e.message, 'error');
                                    clearInterval(heartbeatInterval);
                                }
                            } else {
                                addDebugLog('心跳无法发送: WebSocket未连接', 'error');
                                clearInterval(heartbeatInterval);
                            }
                        }, 30000); // 30秒心跳
                    };
                    
                    function handleSocketMessage(e) {
                        let data;
                        try {
                            // 添加原始消息日志记录
                            console.log('收到原始WebSocket消息:', e.data);
                            addDebugLog(`收到原始消息长度: ${e.data.length}字节`, 'info');
                            
                            data = JSON.parse(e.data);
                            // 只截取数据的前部分，避免过长
                            const shortData = JSON.stringify(data).substr(0, 100) + (JSON.stringify(data).length > 100 ? '...' : '');
                            addDebugLog('解析消息: ' + shortData, 'success');
                            
                            // 显示消息类型
                            if (data.type) {
                                addDebugLog(`消息类型: ${data.type}`, 'info');
                            }
                            
                            // 记录content长度（如果存在）
                            if (data.content) {
                                addDebugLog(`内容长度: ${data.content.length}字符`, 'info');
                            }
                            
                            console.log('解析后的WebSocket消息:', data);
                        } catch (error) {
                            console.error('解析WebSocket消息失败:', error, e.data);
                            addDebugLog('解析消息失败: ' + error.message, 'error');
                            addDebugLog('原始消息: ' + e.data.substring(0, 50) + '...', 'error');
                            return;
                        }
                        
                        // 如果是第一条消息，立即显示结果区域
                        if (isFirstMessage) {
                            isFirstMessage = false;
                            loading.style.display = 'none';
                            results.style.display = 'block';
                            addDebugLog('收到第一条消息，显示结果区域', 'success');
                            updateConnectionStatus('数据接收中...');
                        }
                        
                        // 处理错误
                        if (data.error) {
                            addDebugLog('收到错误: ' + data.error, 'error');
                            alert('发生错误: ' + data.error);
                            return;
                        }

                        // 处理当前时间八字信息
                        if (data.type === 'current_time_bazi') {
                            document.getElementById('current-time').textContent = data.current_time;
                            document.getElementById('current-time-bazi').textContent = data.current_bazi;
                            addDebugLog(`当前时间八字: ${data.current_bazi}`, 'success');
                            return;
                        }
                        
                        // 处理八字结果
                        if (data.type === 'bazi_result') {
                            addDebugLog('收到八字分析结果', 'success');
                            
                            shenchenbaziEl.textContent = data.shenchenbazi;
                            shengxiaoEl.textContent = data.shengxiao;
                            
                            // 生成五行图表
                            wuxingChartEl.innerHTML = '';
                            for (const [wuxing, value] of Object.entries(data.wuxing)) {
                                const height = value * 200;  // 最高高度为200px
                                const bar = document.createElement('div');
                                bar.className = 'wuxing-bar';
                                bar.style.height = `${height}px`;
                                bar.innerHTML = `${(value * 100).toFixed(0)}%<span>${wuxing}</span>`;
                                wuxingChartEl.appendChild(bar);
                            }
                            
                            // 处理五运六气信息（如果有）
                            if (data.current_wuyun_liuqi) {
                                document.getElementById('wuyun-liuqi-section').style.display = 'block';
                                document.getElementById('current-bazi').textContent = data.shenchenbazi;
                                document.getElementById('wuyun').textContent = data.current_wuyun_liuqi['五运'];
                                document.getElementById('liuqi').textContent = data.current_wuyun_liuqi['六气'];
                            } else {
                                document.getElementById('wuyun-liuqi-section').style.display = 'none';
                            }
                            
                            // 显示流式指示器
                            streamIndicator.style.display = 'inline-block';
                            
                            // 根据咨询类型显示对应的流式指示器
                            for (const type in adviceIndicators) {
                                if (type === currentConsultType) {
                                    adviceIndicators[type].style.display = 'inline-block';
                                } else {
                                    adviceIndicators[type].style.display = 'none';
                                }
                            }
                            
                            // 切换显示状态
                            updateConnectionStatus(`正在生成${getConsultTypeName(currentConsultType)}...`);
                        }
                        
                        // 处理健康建议
                        if (data.type === 'health_advice') {
                            addDebugLog('收到健康建议片段', 'success');
                            
                            // 追加内容
                            if (data.content) {
                                addDebugLog(`收到养生建议片段(${data.content.length}字符)`, 'success');
                                // 记录内容的前20个字符
                                addDebugLog(`内容预览: ${data.content.substring(0, 20)}...`, 'info');
                                
                                // 执行内容追加
                                try {
                                    appendAdviceContent(data.content, 'health');
                                    addDebugLog('成功添加到队列', 'success');
                                } catch (err) {
                                    addDebugLog(`添加内容失败: ${err.message}`, 'error');
                                    healthAdviceEl.innerHTML += data.content; // 直接添加作为备选方案
                                }
                            } else {
                                addDebugLog('收到空内容的健康建议片段', 'warning');
                            }
                            
                            // 如果是最后一条消息
                            if (data.done) {
                                adviceComplete = true;
                                addDebugLog('养生建议生成完成', 'success');
                                
                                // 隐藏流式指示器
                                adviceIndicator.style.display = 'none';
                                
                                // 更新标题，移除"实时生成中"
                                const adviceTitleEl = document.querySelector('.stream-title:nth-child(4) h2');
                                if (adviceTitleEl) {
                                    adviceTitleEl.textContent = '中医养生建议';
                                }
                                
                                finishAnalysis();
                            }
                        }
                        
                        // 处理事业建议
                        else if (data.type === 'career_advice') {
                            addDebugLog('收到事业建议片段', 'success');
                            
                            // 追加内容
                            if (data.content) {
                                addDebugLog(`收到事业建议片段(${data.content.length}字符)`, 'success');
                                
                                // 执行内容追加
                                try {
                                    appendAdviceContent(data.content, 'career');
                                    addDebugLog('成功添加到队列', 'success');
                                } catch (err) {
                                    addDebugLog(`添加内容失败: ${err.message}`, 'error');
                                    careerAdviceEl.innerHTML += data.content; // 直接添加作为备选方案
                                }
                            }
                            
                            // 如果是最后一条消息
                            if (data.done) {
                                adviceComplete = true;
                                addDebugLog('事业建议生成完成', 'success');
                                
                                // 隐藏流式指示器
                                careerAdviceIndicator.style.display = 'none';
                                
                                // 更新标题，移除"实时生成中"
                                const titleEl = document.querySelector('#career-advice-title h2');
                                if (titleEl) {
                                    titleEl.textContent = '事业发展建议';
                                }
                                
                                finishAnalysis();
                            }
                        }
                        
                        // 处理学业建议
                        else if (data.type === 'education_advice') {
                            addDebugLog('收到学业建议片段', 'success');
                            
                            // 追加内容
                            if (data.content) {
                                addDebugLog(`收到学业建议片段(${data.content.length}字符)`, 'success');
                                
                                // 执行内容追加
                                try {
                                    appendAdviceContent(data.content, 'education');
                                    addDebugLog('成功添加到队列', 'success');
                                } catch (err) {
                                    addDebugLog(`添加内容失败: ${err.message}`, 'error');
                                    educationAdviceEl.innerHTML += data.content; // 直接添加作为备选方案
                                }
                            }
                            
                            // 如果是最后一条消息
                            if (data.done) {
                                adviceComplete = true;
                                addDebugLog('学业建议生成完成', 'success');
                                
                                // 隐藏流式指示器
                                educationAdviceIndicator.style.display = 'none';
                                
                                // 更新标题，移除"实时生成中"
                                const titleEl = document.querySelector('#education-advice-title h2');
                                if (titleEl) {
                                    titleEl.textContent = '学业发展建议';
                                }
                                
                                finishAnalysis();
                            }
                        }
                        
                        // 处理风水建议
                        else if (data.type === 'fengshui_advice') {
                            addDebugLog('收到风水建议片段', 'success');
                            
                            // 追加内容
                            if (data.content) {
                                addDebugLog(`收到风水建议片段(${data.content.length}字符)`, 'success');
                                
                                // 执行内容追加
                                try {
                                    appendAdviceContent(data.content, 'fengshui');
                                    addDebugLog('成功添加到队列', 'success');
                                } catch (err) {
                                    addDebugLog(`添加内容失败: ${err.message}`, 'error');
                                    fengshuiAdviceEl.innerHTML += data.content; // 直接添加作为备选方案
                                }
                            }
                            
                            // 如果是最后一条消息
                            if (data.done) {
                                adviceComplete = true;
                                addDebugLog('风水建议生成完成', 'success');
                                
                                // 隐藏流式指示器
                                fengshuiAdviceIndicator.style.display = 'none';
                                
                                // 更新标题，移除"实时生成中"
                                const titleEl = document.querySelector('#fengshui-advice-title h2');
                                if (titleEl) {
                                    titleEl.textContent = '风水布局建议';
                                }
                                
                                finishAnalysis();
                            }
                        }
                        
                        // 处理运势建议
                        else if (data.type === 'fortune_advice') {
                            addDebugLog('收到运势建议片段', 'success');
                            
                            // 追加内容
                            if (data.content) {
                                addDebugLog(`收到运势建议片段(${data.content.length}字符)`, 'success');
                                
                                // 执行内容追加
                                try {
                                    appendAdviceContent(data.content, 'fortune');
                                    addDebugLog('成功添加到队列', 'success');
                                } catch (err) {
                                    addDebugLog(`添加内容失败: ${err.message}`, 'error');
                                    fortuneAdviceEl.innerHTML += data.content; // 直接添加作为备选方案
                                }
                            }
                            
                            // 如果是最后一条消息
                            if (data.done) {
                                adviceComplete = true;
                                addDebugLog('运势建议生成完成', 'success');
                                
                                // 隐藏流式指示器
                                fortuneAdviceIndicator.style.display = 'none';
                                
                                // 更新标题，移除"实时生成中"
                                const titleEl = document.querySelector('#fortune-advice-title h2');
                                if (titleEl) {
                                    titleEl.textContent = '运势分析建议';
                                }
                                
                                finishAnalysis();
                            }
                        }
                    };
                    
                    function handleSocketClose(e) {
                        clearTimeout(connectionTimeout);
                        if (heartbeatInterval) {
                            clearInterval(heartbeatInterval);
                            addDebugLog('已清理心跳定时器');
                        }
                        
                        console.log('WebSocket连接已关闭', e);
                        addDebugLog(`WebSocket连接已关闭: 代码=${e.code}, 原因=${e.reason}`, 'info');
                        
                        // 如果还未完成建议生成并且有内容，显示连接断开提示
                        if (!adviceComplete) {
                            // 检查当前选择的咨询类型内容区域是否有内容
                            const adviceContent = adviceContents[currentConsultType];
                            if (adviceContent && adviceContent.innerHTML) {
                                adviceContent.innerHTML += '<p style="color: #b02a37;">（连接已断开，建议生成未完成）</p>';
                                addDebugLog('连接断开，建议生成未完成', 'error');
                            }
                        }
                        
                        // 隐藏所有流式指示器
                        streamIndicator.style.display = 'none';
                        for (const type in adviceIndicators) {
                            adviceIndicators[type].style.display = 'none';
                        }
                        
                        // 如果非正常关闭且未完成，尝试重连
                        if (!adviceComplete && e.code !== 1000 && e.code !== 1001 && reconnectAttempts < maxReconnectAttempts) {
                            reconnectAttempts++;
                            updateConnectionStatus(`连接断开，正在尝试第${reconnectAttempts}次重连`);
                            setTimeout(() => createWebSocket(birthInfo), reconnectInterval);
                            // 增加重连间隔
                            reconnectInterval *= 1.5;
                        }
                    };
                    
                    function handleSocketError(e) {
                        console.error('WebSocket错误:', e);
                        addDebugLog('WebSocket发生错误', 'error');
                        updateConnectionStatus('WebSocket连接错误');
                    };
                } catch (error) {
                    console.error('创建WebSocket时出错:', error);
                    addDebugLog('创建WebSocket连接失败: ' + error.message, 'error');
                    updateConnectionStatus('创建WebSocket连接失败', error.message);
                    loading.style.display = 'none';
                    alert('创建WebSocket连接失败，请刷新页面重试');
                }
                
                return socket;
            }
            
            // 字符一个个添加，创造真正的流式体验
            let appendQueue = [];
            let isAppending = false;
            let currentAppendType = 'health'; // 当前正在处理的建议类型
            
            function appendAdviceContent(content, type) {
                // 更新当前处理的建议类型
                currentAppendType = type || 'health';
                
                // 调试追踪每次接收的内容
                addDebugLog(`正在处理${currentAppendType}内容(${content.length}字符)，队列当前长度: ${appendQueue.length}`, 'info');
                
                try {
                    // 如果内容是null或undefined，直接返回
                    if (!content) {
                        addDebugLog('收到空内容，跳过处理', 'warning');
                        return;
                    }
                    
                    // 按字符拆分内容而不是单个字符
                    // 优先识别完整的中文字符、词组、标点和换行
                    let pattern = /(\n+|[,.;:?!，。；：？！、]\s*|[a-zA-Z]+|[\u4e00-\u9fa5])/g;
                    let matches = content.match(pattern) || [];
                    
                    // 调试拆分结果
                    addDebugLog(`内容拆分为${matches.length}个字符/词组`, 'info');
                    
                    // 如果有匹配到的内容，添加到队列
                    if (matches.length > 0) {
                        // 为每个匹配添加类型标记
                        const typedMatches = matches.map(m => ({ text: m, type: currentAppendType }));
                        appendQueue = appendQueue.concat(typedMatches);
                        addDebugLog(`队列已更新，当前长度: ${appendQueue.length}`, 'info');
                    } else {
                        // 如果没有匹配到，则按原样添加内容
                        addDebugLog('未匹配到任何模式，将整段内容添加到队列', 'warning');
                        appendQueue.push({ text: content, type: currentAppendType });
                    }
                    
                    // 如果不在追加过程中，开始追加
                    if (!isAppending) {
                        addDebugLog('开始处理队列内容', 'info');
                        processNextChar();
                    } else {
                        addDebugLog('当前正在处理队列中的内容，新内容已加入队列', 'info');
                    }
                } catch (error) {
                    addDebugLog(`处理内容时出错: ${error.message}`, 'error');
                    console.error('处理内容时出错:', error);
                    // 失败时直接添加内容作为备选方案
                    adviceContents[currentAppendType].innerHTML += content;
                }
            }
            
            function processNextChar() {
                isAppending = true;
                
                try {
                    if (appendQueue.length > 0) {
                        // 取出队列中的下一个字符或字符组
                        const item = appendQueue.shift();
                        const text = item.text;
                        const type = item.type;
                        
                        // 追加到对应建议区域
                        adviceContents[type].innerHTML += text;
                        
                        // 自动滚动到最新内容
                        adviceContents[type].scrollTop = adviceContents[type].scrollHeight;
                        
                        // 根据内容类型调整显示延迟
                        let delay = 30; // 默认延迟，适合单个汉字
                        
                        // 标点符号、换行等显示更快
                        if (/[\n,.;:?!，。；：？！、]/.test(text)) {
                            delay = 10;
                        } 
                        // 英文单词显示略慢一些
                        else if (/[a-zA-Z]+/.test(text) && text.length > 1) {
                            delay = 40 + Math.min(text.length * 5, 100);
                        }
                        
                        // 如果队列中还有内容，延迟处理下一个
                        if (appendQueue.length > 0) {
                            setTimeout(processNextChar, delay);
                        } else {
                            isAppending = false;
                            addDebugLog('当前队列处理完毕', 'success');
                        }
                    } else {
                        isAppending = false;
                        addDebugLog('所有内容处理完毕', 'success');
                    }
                } catch (error) {
                    addDebugLog(`处理字符时出错: ${error.message}`, 'error');
                    console.error('处理字符时出错:', error);
                    isAppending = false;
                }
            }
            
            // 添加重连和心跳机制
            let reconnectAttempts = 0;
            const maxReconnectAttempts = 5;
            let reconnectInterval = 3000; // 3秒
            let heartbeatInterval;
            
            analyzeBtn.addEventListener('click', function() {
                try {
                    // 添加各种调试输出
                    console.log('分析按钮被点击');
                    console.log('开始处理分析请求...');
                    console.dir(analyzeBtn);
                    
                    // 显示表单值
                    const birthYear = parseInt(document.getElementById('birth-year').value);
                    const birthMonth = parseInt(document.getElementById('birth-month').value);
                    const birthDay = parseInt(document.getElementById('birth-day').value);
                    const birthTime = parseInt(document.getElementById('birth-time').value);
                    const consultType = document.getElementById('consult-type').value;
                    const includeWuyunLiuqi = document.getElementById('include-wuyun-liuqi').checked;
                    
                    console.log('表单数据:', { 
                        birthYear, birthMonth, birthDay, birthTime, 
                        consultType, includeWuyunLiuqi 
                    });
                    
                    // 检查输入
                    if (isNaN(birthYear) || isNaN(birthMonth) || isNaN(birthDay) || isNaN(birthTime)) {
                        console.error('表单数据不完整');
                        alert('请填写完整的出生信息');
                        return;
                    }
                    
                    // 检查输入范围
                    if (birthYear < 1900 || birthYear > 2100) {
                        console.error('出生年份超出范围');
                        alert('出生年份必须在1900-2100之间');
                        return;
                    }
                    if (birthMonth < 1 || birthMonth > 12) {
                        console.error('出生月份超出范围');
                        alert('出生月份必须在1-12之间');
                        return;
                    }
                    if (birthDay < 1 || birthDay > 31) {
                        console.error('出生日期超出范围');
                        alert('出生日期必须在1-31之间');
                        return;
                    }
                    if (birthTime < 0 || birthTime > 23) {
                        console.error('出生时辰超出范围');
                        alert('出生时辰必须在0-23之间');
                        return;
                    }
                    
                    console.log('表单验证通过，继续处理...');
                    
                    // 保存当前咨询类型
                    currentConsultType = consultType;
                    console.log('当前咨询类型:', currentConsultType);
                    
                    // 根据咨询类型显示对应的建议区域，隐藏其他区域
                    console.log('更新UI显示...');
                    for (const type in adviceTitles) {
                        if (type === currentConsultType) {
                            console.log(`显示${type}咨询区域`);
                            adviceTitles[type].style.display = 'block';
                            adviceContents[type].style.display = 'block';
                        } else {
                            console.log(`隐藏${type}咨询区域`);
                            adviceTitles[type].style.display = 'none';
                            adviceContents[type].style.display = 'none';
                        }
                    }
                    
                    console.log('清空之前的结果...');
                    // 清空之前的结果
                    shenchenbaziEl.textContent = '';
                    shengxiaoEl.textContent = '';
                    wuxingChartEl.innerHTML = '';
                    
                    // 清空所有咨询建议
                    for (const type in adviceContents) {
                        adviceContents[type].innerHTML = '';
                    }
                    
                    console.log('显示加载指示...');
                    // 显示加载指示
                    loading.style.display = 'block';
                    results.style.display = 'none';
                    
                    // 关闭之前的WebSocket连接
                    if (socket && socket.readyState === WebSocket.OPEN) {
                        console.log('关闭现有WebSocket连接...');
                        socket.close();
                    }
                    
                    // 设置状态
                    isFirstMessage = true;
                    adviceComplete = false;
                    
                    // 重置重连参数
                    reconnectAttempts = 0;
                    reconnectInterval = 3000;
                    
                    // 准备出生信息对象
                    const birthInfo = {
                        birth_year: birthYear,
                        birth_month: birthMonth,
                        birth_day: birthDay,
                        birth_time: birthTime,
                        consult_type: consultType,
                        include_wuyun_liuqi: includeWuyunLiuqi
                    };
                    console.log('请求数据:', birthInfo);
                    
                    console.log('创建WebSocket连接...');
                    // 建立新连接
                    socket = createWebSocket(birthInfo);
                    console.log('WebSocket连接已创建:', socket);
                    
                    // 设置连接超时
                    console.log('设置连接超时...');
                    let connectionTimeout = setTimeout(() => {
                        console.log('检查连接状态...');
                        if (isFirstMessage) {
                            console.error('连接超时，未收到服务器响应');
                            addDebugLog('连接超时，未收到服务器响应', 'error');
                            alert('连接超时，请检查网络连接并重试');
                            loading.style.display = 'none';
                        }
                    }, 15000);
                    console.log('分析按钮点击处理完成');
                } catch (error) {
                    console.error('分析按钮点击处理出错:', error);
                    alert('处理出错: ' + error.message);
                }
            });
            
            // 页面卸载前关闭WebSocket连接
            window.addEventListener('beforeunload', function() {
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.close();
                    addDebugLog('页面卸载，关闭WebSocket连接');
                }
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                }
            });
            
            // 分析完成函数
            function finishAnalysis() {
                // 清理心跳
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                    addDebugLog('已清理心跳定时器');
                }
                
                updateConnectionStatus('分析完成');
                
                // 关闭WebSocket连接
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.close();
                    addDebugLog('WebSocket连接已关闭', 'success');
                }
            }
            
            // 获取咨询类型的中文名称
            function getConsultTypeName(type) {
                const typeNames = {
                    'health': '健康养生建议',
                    'career': '事业发展建议',
                    'education': '学业发展建议',
                    'fengshui': '风水布局建议',
                    'fortune': '运势分析建议'
                };
                return typeNames[type] || '咨询建议';
            }
        });
    </script>
</body>
</html> 