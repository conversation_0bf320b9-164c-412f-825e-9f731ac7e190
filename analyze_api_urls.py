#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 demo_wechatpayV3.log 中的API URL统计脚本
用于优化API性能，统计最近2个月内使用的API接口
"""

import re
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from urllib.parse import urlparse

def parse_log_line(line):
    """解析日志行，提取时间戳和API信息"""
    # 匹配新日志格式: INFO 2025-08-02 01:10:33,105 rate_limiter 437310 140401217802240 [RATE_LIMIT] 用户56(每日) API:检查会员状态 调用次数: 1
    # 或旧格式: 2024-12-22 18:41:57,445 - 2391410 - INFO: 错误：Token 已过期

    # 新格式
    new_pattern = r'INFO\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d{3}'
    new_match = re.search(new_pattern, line)

    # 旧格式
    old_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d{3}'
    old_match = re.match(old_pattern, line)

    timestamp = None
    if new_match:
        timestamp_str = new_match.group(1)
        try:
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            pass
    elif old_match:
        timestamp_str = old_match.group(1)
        try:
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            pass

    if not timestamp:
        return None, None
    
    # 提取各种API信息
    apis = []

    # 1. 新日志格式中的API信息 (如: API:检查会员状态, 微信支付回调, 支付宝回调处理成功)
    if 'API:' in line:
        api_match = re.search(r'API:([^调用次数]+)', line)
        if api_match:
            api_name = api_match.group(1).strip()
            apis.append(f"内部API: {api_name}")

    # 2. 支付回调API
    if '支付回调' in line or 'callback' in line.lower():
        if '微信' in line:
            apis.append("支付API: 微信支付回调")
        elif '支付宝' in line:
            apis.append("支付API: 支付宝回调")

    # 3. HTTP请求URL (如: POST https://api.weixin.qq.com/sns/oauth2/access_token)
    http_pattern = r'(GET|POST|PUT|DELETE|PATCH)\s+(https?://[^\s"\']+)'
    http_matches = re.findall(http_pattern, line)
    for method, url in http_matches:
        apis.append(f"{method} {url}")

    # 4. 直接的URL (如: https://ark.cn-beijing.volces.com/api/v3/chat/completions)
    url_pattern = r'https?://[^\s"\'<>]+'
    url_matches = re.findall(url_pattern, line)
    for url in url_matches:
        # 清理URL，去掉可能的尾部字符
        url = re.sub(r'["\'>].*$', '', url)
        if url not in [u.split(' ', 1)[1] if ' ' in u else u for u in apis]:
            apis.append(url)

    # 5. 业务操作识别
    business_patterns = {
        '用户登录': r'用户.*登录|login',
        '会员检查': r'检查会员|会员状态',
        '限流控制': r'RATE_LIMIT|限流',
        '支付处理': r'支付.*处理|payment.*process',
        '数据查询': r'查询.*数据|data.*query',
    }

    for business_name, pattern in business_patterns.items():
        if re.search(pattern, line, re.IGNORECASE):
            apis.append(f"业务操作: {business_name}")
            break
    
    return timestamp, apis

def categorize_url(url):
    """对URL进行分类"""
    url_lower = url.lower()
    
    if 'weixin.qq.com' in url_lower or 'wechat' in url_lower:
        return '微信API'
    elif 'volces.com' in url_lower or 'ark.cn-beijing' in url_lower:
        return '火山引擎AI'
    elif 'deepseek.com' in url_lower:
        return 'DeepSeek AI'
    elif '/chat' in url_lower or 'completions' in url_lower:
        return 'AI聊天'
    elif '/api/' in url_lower:
        return '内部API'
    elif url_lower.startswith('/'):
        return '内部路径'
    else:
        return '外部API'

def clean_url(url):
    """清理URL，去掉查询参数中的敏感信息"""
    if '?' in url:
        base_url, query = url.split('?', 1)
        # 保留API结构，但隐藏敏感参数
        if 'appid=' in query or 'secret=' in query or 'code=' in query:
            return f"{base_url}?[参数已隐藏]"
    return url

def analyze_log_file(log_file_path, days_back=60):
    """分析日志文件"""
    print(f"🔍 开始分析日志文件: {log_file_path}")
    print(f"📅 分析时间范围: 最近 {days_back} 天")
    
    # 计算时间范围
    now = datetime.now()
    cutoff_date = now - timedelta(days=days_back)
    
    # 统计数据
    url_stats = defaultdict(int)
    url_categories = defaultdict(list)
    daily_stats = defaultdict(int)
    method_stats = defaultdict(int)
    
    total_lines = 0
    valid_lines = 0
    
    try:
        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                total_lines += 1
                
                if line_num % 10000 == 0:
                    print(f"📖 已处理 {line_num} 行...")
                
                timestamp, urls = parse_log_line(line.strip())
                
                if timestamp and timestamp >= cutoff_date:
                    valid_lines += 1
                    date_str = timestamp.strftime('%Y-%m-%d')
                    daily_stats[date_str] += 1
                    
                    for url in urls:
                        # 清理URL
                        clean_url_str = clean_url(url)
                        url_stats[clean_url_str] += 1
                        
                        # 分类
                        category = categorize_url(url)
                        if clean_url_str not in url_categories[category]:
                            url_categories[category].append(clean_url_str)
                        
                        # 提取HTTP方法
                        if url.startswith(('GET ', 'POST ', 'PUT ', 'DELETE ', 'PATCH ')):
                            method = url.split(' ')[0]
                            method_stats[method] += 1
    
    except FileNotFoundError:
        print(f"❌ 错误: 找不到日志文件 {log_file_path}")
        return
    except Exception as e:
        print(f"❌ 错误: 读取日志文件时出错 - {e}")
        return
    
    print(f"\n📊 分析完成!")
    print(f"📄 总行数: {total_lines:,}")
    print(f"✅ 有效行数: {valid_lines:,}")
    print(f"🔗 发现的唯一API: {len(url_stats)}")
    
    # 输出统计结果
    print("\n" + "="*80)
    print("🏆 TOP 20 最常用的API")
    print("="*80)
    
    for i, (url, count) in enumerate(Counter(url_stats).most_common(20), 1):
        print(f"{i:2d}. [{count:4d}次] {url}")
    
    print("\n" + "="*80)
    print("📂 API分类统计")
    print("="*80)
    
    for category, urls in url_categories.items():
        total_calls = sum(url_stats[url] for url in urls)
        print(f"\n🔸 {category} ({len(urls)}个API, {total_calls}次调用)")
        
        # 显示该分类下最常用的前5个API
        category_counter = Counter({url: url_stats[url] for url in urls})
        for j, (url, count) in enumerate(category_counter.most_common(5), 1):
            print(f"   {j}. [{count:3d}次] {url}")
        
        if len(urls) > 5:
            print(f"   ... 还有 {len(urls) - 5} 个API")
    
    print("\n" + "="*80)
    print("📈 HTTP方法统计")
    print("="*80)
    
    for method, count in Counter(method_stats).most_common():
        print(f"🔸 {method}: {count:,} 次")
    
    print("\n" + "="*80)
    print("📅 每日API调用趋势 (最近10天)")
    print("="*80)
    
    recent_days = sorted(daily_stats.keys())[-10:]
    for date in recent_days:
        count = daily_stats[date]
        bar = "█" * min(50, count // 10)
        print(f"{date}: {count:4d} 次 {bar}")
    
    # 生成详细报告文件
    report_file = f"api_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    report_data = {
        "分析时间": datetime.now().isoformat(),
        "时间范围": f"最近{days_back}天",
        "总行数": total_lines,
        "有效行数": valid_lines,
        "API统计": dict(url_stats),
        "分类统计": {k: list(v) for k, v in url_categories.items()},
        "方法统计": dict(method_stats),
        "每日统计": dict(daily_stats)
    }
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 详细报告已保存到: {report_file}")
    except Exception as e:
        print(f"⚠️  保存报告文件失败: {e}")
    
    print("\n🎯 性能优化建议:")
    print("1. 重点关注调用次数最多的前10个API")
    print("2. 检查是否有重复或冗余的API调用")
    print("3. 考虑对高频API添加缓存机制")
    print("4. 监控外部API的响应时间和稳定性")

if __name__ == "__main__":
    # 分析真正最近的日志文件
    log_files = [
        "logs/info.log",           # 当前日志
        "logs/info.log.2025-08-01", # 昨天
        "logs/info.log.2025-07-31", # 前天
        "logs/info.log.2025-07-30", # 大前天
        "logs/api_performance.log", # API性能日志
    ]

    print("🎯 分析最近的真实API调用数据 (2025年)")
    print("=" * 60)

    for log_file in log_files:
        print(f"\n📁 分析文件: {log_file}")
        try:
            analyze_log_file(log_file, days_back=60)
        except Exception as e:
            print(f"⚠️  跳过文件 {log_file}: {e}")
        print("-" * 40)
