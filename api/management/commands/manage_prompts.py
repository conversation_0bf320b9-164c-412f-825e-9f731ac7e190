# api/management/commands/manage_prompts.py
"""
提示词管理Django命令
用于管理八字分析提示词的创建、更新、验证等操作
"""

from django.core.management.base import BaseCommand, CommandError
from api.prompts import PromptManager, PromptCategory
import os
import yaml


class Command(BaseCommand):
    help = '管理八字分析提示词'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['list', 'create', 'validate', 'reload'],
            help='要执行的操作'
        )
        parser.add_argument(
            '--category',
            type=str,
            help='提示词分类'
        )
        parser.add_argument(
            '--prompt-version',
            type=str,
            default='v1',
            help='提示词版本'
        )
        parser.add_argument(
            '--template',
            action='store_true',
            help='创建模板文件'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'list':
            self.list_prompts()
        elif action == 'create':
            self.create_prompt(options)
        elif action == 'validate':
            self.validate_prompts()
        elif action == 'reload':
            self.reload_prompts()

    def list_prompts(self):
        """列出所有可用的提示词"""
        self.stdout.write(self.style.SUCCESS('📋 可用的提示词列表:'))
        
        prompt_manager = PromptManager()
        available = prompt_manager.list_available_prompts()
        
        if not available:
            self.stdout.write(self.style.WARNING('  暂无可用的提示词'))
            return
        
        for category, versions in available.items():
            try:
                cat_enum = PromptCategory(category)
                display_name = PromptCategory.get_display_name(cat_enum)
                self.stdout.write(f'  📁 {category} ({display_name}):')
                for version in versions:
                    self.stdout.write(f'    📄 {version}')
            except ValueError:
                self.stdout.write(f'  ❓ {category}: {versions}')
        
        self.stdout.write('')
        self.stdout.write(f'总计: {len(available)} 个分类')

    def create_prompt(self, options):
        """创建新的提示词文件"""
        category = options.get('category')
        version = options.get('prompt_version', 'v1')
        template = options.get('template', False)
        
        if not category:
            raise CommandError('请指定提示词分类 --category')
        
        # 验证分类是否有效
        try:
            cat_enum = PromptCategory(category)
        except ValueError:
            valid_categories = [cat.value for cat in PromptCategory.get_all_categories()]
            raise CommandError(f'无效的分类: {category}。有效分类: {", ".join(valid_categories)}')
        
        prompt_manager = PromptManager()
        file_path = os.path.join(prompt_manager.prompts_dir, f"{category}_{version}.yaml")
        
        if os.path.exists(file_path):
            raise CommandError(f'文件已存在: {file_path}')
        
        # 创建提示词模板
        template_content = self._create_prompt_template(cat_enum, version)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ 已创建提示词文件: {file_path}')
        )

    def _create_prompt_template(self, category: PromptCategory, version: str) -> str:
        """创建提示词模板内容"""
        display_name = PromptCategory.get_display_name(category)
        
        template = {
            'metadata': {
                'category': category.value,
                'version': version,
                'description': f'{display_name}分析专用提示词',
                'author': '八字分析团队',
                'created_date': '2025-07-20',
                'last_updated': '2025-07-20'
            },
            'prompt': f'''【专业领域】{display_name}

【分析重点】
1. 基础分析：
   - 基于八字信息进行专业分析
   - 结合十神和神煞特征
   - 提供个性化建议

2. 深度解读：
   - 详细分析相关要素
   - 提供实用指导
   - 注重实际应用

【回答格式】
- 先分析基本特征
- 再提供具体建议
- 最后给出注意事项
- 语言专业易懂，温暖贴心

【特别注意】
- 避免绝对化的预测
- 强调积极正面的引导
- 提供实用可行的建议
- 尊重传统文化内涵'''
        }
        
        return yaml.dump(template, allow_unicode=True, default_flow_style=False, sort_keys=False)

    def validate_prompts(self):
        """验证所有提示词文件"""
        self.stdout.write(self.style.SUCCESS('🔍 验证提示词文件:'))
        
        prompt_manager = PromptManager()
        prompts_dir = prompt_manager.prompts_dir
        
        if not os.path.exists(prompts_dir):
            self.stdout.write(self.style.ERROR(f'  ❌ 提示词目录不存在: {prompts_dir}'))
            return
        
        valid_count = 0
        error_count = 0
        
        for filename in os.listdir(prompts_dir):
            if not filename.endswith(('.yaml', '.yml')):
                continue
                
            file_path = os.path.join(prompts_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                
                # 基本验证
                if 'prompt' not in data and 'content' not in data:
                    self.stdout.write(self.style.ERROR(f'  ❌ {filename}: 缺少prompt或content字段'))
                    error_count += 1
                    continue
                
                self.stdout.write(self.style.SUCCESS(f'  ✅ {filename}: 验证通过'))
                valid_count += 1
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  ❌ {filename}: {str(e)}'))
                error_count += 1
        
        self.stdout.write('')
        self.stdout.write(f'验证完成: {valid_count} 个有效, {error_count} 个错误')

    def reload_prompts(self):
        """重新加载提示词缓存"""
        self.stdout.write(self.style.SUCCESS('🔄 重新加载提示词缓存...'))
        
        prompt_manager = PromptManager()
        prompt_manager.reload_prompts()
        
        self.stdout.write(self.style.SUCCESS('✅ 提示词缓存已重新加载'))
