# 八字分析器使用说明

## 概述

八字分析器是一个基于lunar-python库的完整八字分析工具，可以根据输入的出生日期时间，输出详细的八字分析信息，包括四柱、五行、十神、纳音、九星、运势等全部相关信息。

## 功能特点

- **完整的八字信息**: 年月日时四柱的干支、五行、纳音、十神、藏干、地势等
- **九星分析**: 年月日时九星，包含北斗、玄空、奇门、太乙四大体系
- **神煞分析**: 支持40+种神煞计算，包含贵人类、德合类、星煞类、刃煞类、凶煞类
- **特殊宫位**: 胎元、胎息、命宫、身宫及其纳音
- **运势分析**: 起运时间、大运、流年信息
- **多种使用模式**: 命令行模式、交互模式、批量模式
- **流派支持**: 支持不同的八字计算流派

## 安装依赖

```bash
pip install lunar-python
```

## 使用方法

### 1. 命令行模式

直接在命令行中指定参数：

```bash
# 基本用法
python 八字分析器.py 1988 2 15

# 指定时间
python 八字分析器.py 1988 2 15 --hour 23 --minute 30

# 指定性别（女性）
python 八字分析器.py 1988 2 15 --hour 23 --minute 30 --gender 0

# 指定流派
python 八字分析器.py 1988 2 15 --hour 23 --minute 30 --gender 0 --sect 1
```

#### 参数说明

- `year`: 年份（必需）
- `month`: 月份，1-12（必需）
- `day`: 日期，1-31（必需）
- `--hour`: 小时，0-23（可选，默认0）
- `--minute`: 分钟，0-59（可选，默认0）
- `--second`: 秒钟，0-59（可选，默认0）
- `--gender`: 性别，1男0女（可选，默认1）
- `--sect`: 流派，1或2（可选，默认2）

### 2. 交互模式

适合单次查询，程序会引导您输入信息：

```bash
python 八字分析器.py -i
# 或
python 八字分析器.py --interactive
```

程序会依次提示输入：
- 年份
- 月份
- 日期
- 小时（可选）
- 分钟（可选）
- 秒钟（可选）
- 性别（可选）
- 流派（可选）

### 3. 批量模式

适合批量分析多个八字：

```bash
python 八字分析器.py -b
# 或
python 八字分析器.py --batch
```

程序会提示输入包含八字数据的CSV文件名。

#### 批量文件格式

CSV格式，每行一个八字，格式为：`年,月,日,时,分,秒,性别,流派`

```csv
# 这是注释行
1988,2,15,23,30,0,1,2
1990,5,20,8,0,0,0,1
1985,12,25,14,15,30,1,2
```

- 空值会使用默认值
- 以`#`开头的行为注释行
- 时分秒默认0，性别默认1（男），流派默认2

## 输出信息说明

### 基本信息
- 阳历日期
- 农历日期
- 完整八字
- 性别和流派

### 四柱详细信息
每柱包含：
- 干支组合
- 天干地支
- 五行属性
- 纳音
- 十神（天干十神和地支十神）
- 地支藏干
- 地势（长生十二神）
- 旬和旬空

### 十神分析
专门显示四柱的十神关系：
- 年柱十神、月柱十神、日柱十神、时柱十神（天干十神）
- 地支藏干十神：显示地支藏干与对应的十神关系
- 十神类型：比肩、劫财、食神、伤官、偏财、正财、七杀、正官、偏印、正印
- 以日干为中心，分析其他干支与日干的关系

### 长生十二神
专门显示四柱的长生十二神状态：
- 年柱地势、月柱地势、日柱地势、时柱地势
- 包含：长生、沐浴、冠带、临官、帝旺、衰、病、死、墓、绝、胎、养
- 用于分析命局强弱和人生各阶段运势

### 特殊宫位
- 胎元及纳音
- 胎息及纳音
- 命宫及纳音
- 身宫及纳音

### 九星信息
年月日时九星，每个九星包含：
- 数字和颜色
- 五行属性
- 方位信息
- 北斗九星名称
- 玄空九星名称和吉凶
- 奇门九星名称和吉凶
- 太乙九星名称和类型

### 神煞信息
年月日时神煞，包含：
- 各柱神煞详细列表
- 神煞分类统计（贵人类、德合类、星煞类、刃煞类、凶煞类）
- 总神煞数量和不重复神煞统计
- 支持40+种神煞：
  - 贵人类：天乙贵人、太极贵人、文昌贵人等
  - 德合类：天德贵人、月德贵人、天赦等
  - 星煞类：驿马、华盖、将星、禄神等
  - 刃煞类：羊刃、飞刃、血刃、金神等
  - 凶煞类：空亡、劫煞、桃花、魁罡等

### 运势分析
- 起运时间
- 大运信息（8步大运）
- 流年信息（第一步大运的前5年）

## 参数详解

### 性别参数
- `1`: 男性
- `0`: 女性

性别影响起运的顺逆，男女起运方向不同。

### 流派参数
- `1`: 流派1
- `2`: 流派2（默认）

不同流派在日柱计算等方面有细微差别。

### 九星流派
九星计算支持不同流派：
- sect=1: 农历年月
- sect=2: 立春年/节气月（默认）
- sect=3: 精确年月

## 示例输出

```
========== 基本信息 ========================================
阳历日期: 1988年2月15日 23时30分0秒 星期一
农历日期: 一九八七年十二月廿八 戊辰年 甲寅月 庚子日 戊子时
完整八字: 戊辰 甲寅 庚子 戊子
性别: 男
流派: 2

========== 四柱详细信息 ====================================
年柱: 戊辰
  年干: 戊
  年支: 辰
  年柱五行: 土土
  年柱纳音: 大林木
  年干十神: 偏印
  年支十神: 偏印, 正财, 伤官
  年支藏干: 戊, 乙, 癸
  年柱地势: 养
  年柱旬: 甲子
  年柱旬空: 戌亥
...

========== 十神分析 ======================================
年柱十神: 戊辰 - 偏印
月柱十神: 甲寅 - 偏财
日柱十神: 庚子 - 日主
时柱十神: 戊子 - 偏印

地支藏干十神:
年支藏干: 戊, 乙, 癸 -> 偏印, 正财, 伤官
月支藏干: 甲, 丙, 戊 -> 偏财, 七杀, 偏印
日支藏干: 癸 -> 伤官
时支藏干: 癸 -> 伤官

========== 长生十二神 ====================================
年柱地势: 戊辰 - 养
月柱地势: 甲寅 - 绝
日柱地势: 庚子 - 死
时柱地势: 戊子 - 死

========== 神煞分析 ======================================
年柱: 戊辰
  神煞(5个): 太极, 国印, 华盖, 流霞, 空亡
    贵人类: 太极, 国印
    星煞类: 华盖
    凶煞类: 流霞, 空亡

月柱: 甲寅
  神煞(5个): 天乙, 太极, 词馆, 驿马, 吊客
    贵人类: 天乙, 太极
    星煞类: 词馆, 驿马
    凶煞类: 吊客

日柱: 庚子
  神煞(1个): 童子
    凶煞类: 童子

时柱: 戊子
  神煞(2个): 将星, 童子
    星煞类: 将星
    凶煞类: 童子

神煞总结:
总神煞数量: 13
不重复神煞: 11个
所有神煞: 华盖, 吊客, 国印, 天乙, 太极, 将星, 流霞, 空亡, 童子, 词馆, 驿马

各类神煞统计:
  贵人类: 3个 (国印, 天乙, 太极)
  星煞类: 4个 (华盖, 词馆, 将星, 驿马)
  凶煞类: 4个 (童子, 流霞, 空亡, 吊客)
...
```

## 注意事项

1. **时间精度**: 八字计算基于精确的天文算法，时间越准确结果越精确
2. **流派差异**: 不同流派在某些计算上有差异，可根据需要选择
3. **性别影响**: 性别主要影响运势分析中的起运方向
4. **文件编码**: 批量文件请使用UTF-8编码保存

## 错误处理

- 日期格式错误会提示具体错误信息
- 参数超出范围会给出有效范围提示
- 文件不存在或格式错误会给出相应提示

## 技术支持

如有问题，请检查：
1. lunar-python库是否正确安装
2. 输入的日期时间是否有效
3. 文件路径和格式是否正确

## 更新日志

- v1.0: 初始版本，支持完整八字分析
- 支持命令行、交互、批量三种模式
- 包含四柱、九星、运势等全部信息
