import websocket
import json
import time

def on_message(ws, message):
    """接收到消息的回调函数"""
    print(f"\n收到消息: {message}")
    data = json.loads(message)
    
    # 检查是否有错误
    if 'error' in data:
        print(f"错误: {data['error']}")
        return
    
    # 处理八字结果
    if data.get('type') == 'bazi_result':
        print(f"八字: {data.get('shenchenba<PERSON>')}")
        print(f"生肖: {data.get('shengxiao')}")
        print(f"五行: {data.get('wuxing')}")
        print("等待养生建议生成中...")
    
    # 处理养生建议
    if data.get('type') == 'health_advice':
        if data.get('content'):
            print(data.get('content'), end="")
        if data.get('done'):
            print("\n养生建议生成完成")

def on_error(ws, error):
    """发生错误的回调函数"""
    print(f"错误: {error}")

def on_close(ws, close_status_code, close_msg):
    """连接关闭的回调函数"""
    print(f"连接已关闭: {close_status_code}, {close_msg}")

def on_open(ws):
    """连接建立的回调函数"""
    print("连接已建立")
    
    # 八字分析请求
    birth_data = {
        "birth_info": {
            "birth_year": 1990, 
            "birth_month": 1,
            "birth_day": 1,
            "birth_time": 0
        }
    }
    
    print(f"发送请求: {json.dumps(birth_data)}")
    ws.send(json.dumps(birth_data))

if __name__ == "__main__":
    # 设置WebSocket连接URL
    ws_url = "ws://localhost:8000/ws/bazi_advice/"
    
    # 启用详细日志
    websocket.enableTrace(True)
    
    # 创建WebSocket连接
    ws = websocket.WebSocketApp(
        ws_url,
        on_open=on_open,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close
    )
    
    # 运行WebSocket客户端
    ws.run_forever() 