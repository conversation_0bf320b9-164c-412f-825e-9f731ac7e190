#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API错误检测脚本
专门用于检测API的500错误和其他问题
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VhDgOlaTZFjxZaGUdPhe7r658Vqfq48TOc6jTH-6zSA"

headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json",
    "User-Agent": "ErrorDetectionTester/1.0"
}

# 第四批API测试清单 - 使用正确的URL路径
TEST_APIS = [
    # 会员系统API
    {
        "name": "日活动API",
        "method": "POST",
        "url": f"{BASE_URL}/api/bank/DayActivitiesView/",
        "data": {}
    },
    {
        "name": "用户问答历史API",
        "method": "GET",
        "url": f"{BASE_URL}/api/async-bank/user-quiz-history/",
        "data": None
    },

    # 论坛系统API
    {
        "name": "获取帖子列表API",
        "method": "GET",
        "url": f"{BASE_URL}/api/doubao_aichat/chat/posts/",
        "data": None
    },
    {
        "name": "获取帖子评论API",
        "method": "GET",
        "url": f"{BASE_URL}/api/doubao_aichat/chat/posts/1/comments/",
        "data": None
    },

    # 医案系统API
    {
        "name": "经验值排行API",
        "method": "GET",
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_ranking/",
        "data": None
    },
    {
        "name": "时间效率排行API",
        "method": "GET",
        "url": f"{BASE_URL}/api/doubao_aichat/chat/time_efficiency_ranking/",
        "data": None
    },

    # 症状管理API
    {
        "name": "获取自定义症状API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmchat/get_custom_symptoms/",
        "data": None
    },

    # 预后系统API
    {
        "name": "执行搜索API",
        "method": "POST",
        "url": f"{BASE_URL}/api/routertest1/search_symptoms_and_recommend_therapies/",
        "data": {
            "symptoms": ["头痛", "失眠"],
            "limit": 10
        }
    },

    # 邀请系统API
    {
        "name": "我的邀请码API",
        "method": "GET",
        "url": f"{BASE_URL}/api/invite_api/my-code/",
        "data": None
    },
    {
        "name": "邀请记录API",
        "method": "GET",
        "url": f"{BASE_URL}/api/invite_api/records/",
        "data": None
    },

    # 其他功能API
    {
        "name": "干支查询API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmNLP/get_ganzhi/",
        "data": None
    },
    {
        "name": "获取当前季节API",
        "method": "GET",
        "url": f"{BASE_URL}/api/tcmNLP/get_current_season/",
        "data": None
    }
]

def test_single_api(api_config):
    """测试单个API"""
    print(f"\n🔍 测试 {api_config['name']}")
    print(f"📍 {api_config['method']} {api_config['url']}")
    
    try:
        start_time = time.time()
        
        if api_config['method'].upper() == 'GET':
            response = requests.get(api_config['url'], headers=headers, timeout=10)
        elif api_config['method'].upper() == 'POST':
            response = requests.post(api_config['url'], headers=headers, json=api_config['data'], timeout=10)
        
        response_time = time.time() - start_time
        
        print(f"📊 状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        
        if response.status_code == 200:
            print("✅ 成功")
            try:
                json_data = response.json()
                print(f"📄 响应数据: {str(json_data)[:100]}...")
            except:
                print(f"📄 响应内容: {response.text[:100]}...")
        else:
            print(f"❌ 错误 - HTTP {response.status_code}")
            print(f"🚨 错误内容: {response.text[:500]}")
            
            # 如果是500错误，提供更详细的信息
            if response.status_code == 500:
                print("🔥 这是一个服务器内部错误，需要检查服务器日志")
        
        return {
            "name": api_config['name'],
            "status_code": response.status_code,
            "response_time": response_time,
            "success": 200 <= response.status_code < 300,
            "error_content": response.text if response.status_code >= 400 else None
        }
        
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return {
            "name": api_config['name'],
            "status_code": 0,
            "response_time": 10.0,
            "success": False,
            "error_content": "Request timeout"
        }
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误")
        return {
            "name": api_config['name'],
            "status_code": 0,
            "response_time": 0,
            "success": False,
            "error_content": "Connection error"
        }
    except Exception as e:
        print(f"💥 异常: {str(e)}")
        return {
            "name": api_config['name'],
            "status_code": 0,
            "response_time": 0,
            "success": False,
            "error_content": str(e)
        }

def main():
    """主测试函数"""
    print("🎯 API错误检测开始")
    print("=" * 80)
    
    results = []
    error_apis = []
    
    for api_config in TEST_APIS:
        result = test_single_api(api_config)
        results.append(result)
        
        if not result['success']:
            error_apis.append(result)
        
        # 短暂休息
        time.sleep(0.5)
    
    # 生成报告
    print("\n" + "=" * 80)
    print("📊 错误检测报告")
    print("=" * 80)
    
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    success_rate = (success_count / total_count) * 100
    
    print(f"🎯 总体成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
    print(f"❌ 错误API数量: {len(error_apis)}")
    
    if error_apis:
        print("\n🚨 错误API详情:")
        for error_api in error_apis:
            print(f"   ❌ {error_api['name']} - HTTP {error_api['status_code']}")
            if error_api['error_content']:
                print(f"      错误: {error_api['error_content'][:100]}...")
    else:
        print("\n✅ 所有API都正常工作!")

if __name__ == "__main__":
    main()
