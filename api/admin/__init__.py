"""
Django admin模块化配置入口文件
这里导入所有admin类并将其注册到Django admin
"""

# 导入TCM相关的admin类
from .tcm_admin import TCMQuestionAdmin, TCMForumPostAdmin, TCMCommentAdmin

# 导入用户相关的admin类
from .user_admin import UserInfoAdmin, BannedTokenAdmin, BannedAccountAdmin

# 导入问卷相关的admin类
from .questionnaire_admin import (
    QuestionnaireAdmin, 
    QuestionAdmin, 
    QuestionnaireCalculationHistoryAdmin,
    EnhancedQuestionnaireResponseAdmin
)

# 导入其他功能模块的admin类
from .common_admin import (
    OrderAdmin,
    IngredientAdmin,
    CategoryAdmin,
    AnnouncementAdmin
)

# 导入反馈相关的admin类
from .feedback_admin import FeedbackCategoryAdmin, FeedbackAdmin, FeedbackReplyTemplateAdmin

# 导入报告相关的admin类
from .report_admin import ReportAdmin

# 导入预后系统相关的admin类
from .prognosis_admin import (
    PrognosisTherapyClassificationAdmin,
    PrognosisTherapyCategoryAdmin,
    PrognosisUserTherapyAdmin,
    PrognosisSymptomTherapyMappingAdmin,
    PrognosisTherapyUsageRecordAdmin,
    PrognosisTherapyLikeAdmin,
    PrognosisTherapyRatingAdmin,
    PrognosisTherapyCommentAdmin
)

# 导入支付系统相关的admin类
from .payment_admin import BannedPaymentIPAdmin

# 所有admin类已在各自的模块中完成了注册
# 这个文件仅作为导入入口 