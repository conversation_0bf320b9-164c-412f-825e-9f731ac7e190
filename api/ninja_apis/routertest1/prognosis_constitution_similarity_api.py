# api/ninja_apis/routertest1/prognosis_constitution_similarity_api.py
# 🧬 基于体质相似度排序的疗法干预记录API模块
#
# 【功能说明】
# 1. 支持POST方法，接收用户体质数据进行相似度排序
# 2. 保持原有GET方法功能不变
# 3. 实现体质相似度计算算法（余弦相似度）
# 4. 对干预记录按体质相似度排序返回
# 5. 完全兼容现有数据结构和API格式

from ninja import Router
from ninja.errors import HttpError
from typing import List, Dict, Any, Optional
import asyncio
import json
import traceback
import math
import time
import os
from datetime import date, datetime
from pydantic import BaseModel, Field

# 🚀 尝试使用更快的JSON序列化库
try:
    import orjson
    FAST_JSON_AVAILABLE = True
    print("[INFO] 🚀 使用orjson进行高性能JSON序列化")
except ImportError:
    FAST_JSON_AVAILABLE = False
    print("[INFO] 📦 使用标准json库进行序列化")

# 🚀 多层缓存优化导入
try:
    from api.utils.cache_manager import cache_manager
    MULTI_LEVEL_CACHE_AVAILABLE = True
    multi_level_cache = cache_manager
    print("[INFO] 多层缓存管理器加载成功")
except ImportError as e:
    print(f"[WARNING] 多层缓存管理器不可用，使用Django缓存: {e}")
    MULTI_LEVEL_CACHE_AVAILABLE = False
    multi_level_cache = None

from django.core.cache import cache
from django.db.models import Count, Q, Avg
from asgiref.sync import sync_to_async
import hashlib

# 导入schemas
from api.views.schemas import (
    InterventionRecordSchema
)

# 搜索建议相关的Pydantic模型
class SearchSuggestionResponse(BaseModel):
    """搜索建议响应模型"""
    keyword: str = Field(..., description="建议的关键词")
    display_text: str = Field(..., description="显示文本")
    match_type: str = Field(..., description="匹配类型: exact/prefix/contains/partial")
    category: str = Field(..., description="症状分类")

class SearchSuggestionsResponse(BaseModel):
    """搜索建议列表响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("搜索建议获取成功", description="响应消息")
    query: str = Field(..., description="用户输入的搜索词")
    suggestions: List[SearchSuggestionResponse] = Field(..., description="搜索建议列表")
    total: int = Field(..., description="建议总数")

# 导入模型
from api.models import (
    PrognosisTherapyCategory, PrognosisTherapyUsageRecord, 
    PrognosisUserTherapy, UserInfo, PrognosisTherapyClassification
)

# 导入工具函数
from api.ninja_apis.async_utils import filter_async, get_async, save_async, count_async
from api.ninja_apis.questionnaire.utils import api_timer

# 导入限流装饰器
from api.utils.rate_limiter import rate_limit

# 导入现有的路由器和工具函数
from .prognosis_api import prognosis_router
from .prognosis_interaction_api import validate_therapy_exists, fetch_therapy_users_data

# ===============================================
# 🧬 体质相似度计算核心算法
# ===============================================

class ConstitutionSimilarityCalculator:
    """体质相似度计算器"""
    
    def __init__(self):
        """初始化体质相似度计算器"""
        # 支持的16种体质类型
        self.constitution_types = [
            "血虚", "阴虚", "肝", "阳虚", "气滞", "气虚", "痰症", "脾", 
            "肾", "实热", "心", "实寒", "湿症", "肺", "血瘀", "上热下寒"
        ]
        print(f"[DEBUG] 🧬 体质相似度计算器初始化完成，支持{len(self.constitution_types)}种体质类型")
    
    def normalize_constitution_data(self, constitution_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        标准化体质数据为统一格式
        
        Args:
            constitution_data: 体质数据列表，格式如：
                [{"type": "血虚", "percent": 30.82}, {"type": "阴虚", "percent": 30.73}, ...]
        
        Returns:
            Dict[str, float]: 标准化后的体质字典，确保包含所有16种体质类型
        """
        try:
            # 初始化所有体质类型为0
            normalized = {const_type: 0.0 for const_type in self.constitution_types}
            
            # 填充实际数据
            for item in constitution_data:
                const_type = item.get('type', '')
                percent = float(item.get('percent', 0))
                
                if const_type in normalized:
                    normalized[const_type] = percent
                else:
                    print(f"[WARNING] 未知体质类型: {const_type}")
            
            print(f"[DEBUG] 🧬 体质数据标准化完成，非零体质: {[(k, v) for k, v in normalized.items() if v > 0]}")
            return normalized
            
        except Exception as e:
            print(f"[ERROR] 体质数据标准化失败: {str(e)}")
            # 返回默认的平均分布
            return {const_type: 6.25 for const_type in self.constitution_types}  # 100/16 = 6.25
    
    def calculate_cosine_similarity(self, constitution1: Dict[str, float], constitution2: Dict[str, float]) -> float:
        """
        计算两个体质数据的余弦相似度
        
        Args:
            constitution1: 用户体质数据
            constitution2: 记录体质数据
        
        Returns:
            float: 相似度值 (0-1之间，1表示完全相似)
        """
        try:
            # 确保两个体质数据包含相同的体质类型
            common_types = set(constitution1.keys()) & set(constitution2.keys())
            if not common_types:
                print(f"[WARNING] 两个体质数据没有共同的体质类型")
                return 0.0
            
            # 计算向量
            vector1 = [constitution1.get(const_type, 0) for const_type in self.constitution_types]
            vector2 = [constitution2.get(const_type, 0) for const_type in self.constitution_types]
            
            # 计算点积
            dot_product = sum(a * b for a, b in zip(vector1, vector2))
            
            # 计算向量模长
            magnitude1 = math.sqrt(sum(a * a for a in vector1))
            magnitude2 = math.sqrt(sum(b * b for b in vector2))
            
            # 避免除零错误
            if magnitude1 == 0 or magnitude2 == 0:
                return 0.0
            
            # 计算余弦相似度
            similarity = dot_product / (magnitude1 * magnitude2)
            
            # 确保结果在0-1之间
            return max(0.0, min(1.0, similarity))
            
        except Exception as e:
            print(f"[ERROR] 余弦相似度计算失败: {str(e)}")
            return 0.0
    
    def calculate_euclidean_similarity(self, constitution1: Dict[str, float], constitution2: Dict[str, float]) -> float:
        """
        计算两个体质数据的欧氏距离相似度
        
        Args:
            constitution1: 用户体质数据
            constitution2: 记录体质数据
        
        Returns:
            float: 相似度值 (0-1之间，1表示完全相似)
        """
        try:
            # 计算欧氏距离
            distance = 0.0
            for const_type in self.constitution_types:
                val1 = constitution1.get(const_type, 0)
                val2 = constitution2.get(const_type, 0)
                distance += (val1 - val2) ** 2
            
            distance = math.sqrt(distance)
            
            # 转换为相似度 (距离越小，相似度越高)
            # 最大可能距离约为sqrt(16 * 100^2) = 400
            max_distance = 400.0
            similarity = 1.0 - (distance / max_distance)
            
            # 确保结果在0-1之间
            return max(0.0, min(1.0, similarity))
            
        except Exception as e:
            print(f"[ERROR] 欧氏距离相似度计算失败: {str(e)}")
            return 0.0
    
    def calculate_weighted_similarity(self, constitution1: Dict[str, float], constitution2: Dict[str, float]) -> float:
        """
        计算加权体质相似度（重点关注主要体质类型）
        
        Args:
            constitution1: 用户体质数据
            constitution2: 记录体质数据
        
        Returns:
            float: 相似度值 (0-1之间，1表示完全相似)
        """
        try:
            # 获取两个体质数据的主要体质类型（TOP3）
            top3_types1 = sorted(constitution1.items(), key=lambda x: x[1], reverse=True)[:3]
            top3_types2 = sorted(constitution2.items(), key=lambda x: x[1], reverse=True)[:3]
            
            # 计算加权相似度
            weighted_similarity = 0.0
            total_weight = 0.0
            
            # 对主要体质类型给予更高权重
            weights = [0.5, 0.3, 0.2]  # TOP1权重50%, TOP2权重30%, TOP3权重20%
            
            for i, (type1, percent1) in enumerate(top3_types1):
                for j, (type2, percent2) in enumerate(top3_types2):
                    if type1 == type2:
                        # 相同体质类型，计算百分比相似度
                        percent_similarity = 1.0 - abs(percent1 - percent2) / 100.0
                        weight = weights[i] * weights[j]
                        weighted_similarity += percent_similarity * weight
                        total_weight += weight
            
            # 标准化结果
            if total_weight > 0:
                return weighted_similarity / total_weight
            else:
                return 0.0
                
        except Exception as e:
            print(f"[ERROR] 加权相似度计算失败: {str(e)}")
            return 0.0
    
    def calculate_similarity(self, user_constitution: List[Dict[str, Any]], 
                           record_constitution: List[Dict[str, Any]], 
                           method: str = "cosine") -> float:
        """
        计算体质相似度（统一接口）
        
        Args:
            user_constitution: 用户体质数据
            record_constitution: 记录体质数据
            method: 计算方法 ("cosine", "euclidean", "weighted")
        
        Returns:
            float: 相似度值 (0-1之间)
        """
        try:
            # 🕐 开始计时
            start_time = time.time()
            
            # 标准化体质数据
            normalized_user = self.normalize_constitution_data(user_constitution)
            normalized_record = self.normalize_constitution_data(record_constitution)
            
            # 根据方法计算相似度
            if method == "cosine":
                similarity = self.calculate_cosine_similarity(normalized_user, normalized_record)
            elif method == "euclidean":
                similarity = self.calculate_euclidean_similarity(normalized_user, normalized_record)
            elif method == "weighted":
                similarity = self.calculate_weighted_similarity(normalized_user, normalized_record)
            else:
                print(f"[WARNING] 未知的相似度计算方法: {method}，使用默认的余弦相似度")
                similarity = self.calculate_cosine_similarity(normalized_user, normalized_record)
            
            # 🕐 结束计时
            end_time = time.time()
            calculation_time = round((end_time - start_time) * 1000, 2)  # 转换为毫秒
            
            print(f"[TIMING] ⏱️ 相似度计算耗时: {calculation_time}ms, 方法: {method}, 相似度: {similarity:.4f}")
            
            return similarity
                
        except Exception as e:
            print(f"[ERROR] 体质相似度计算失败: {str(e)}")
            return 0.0
    
    def get_main_constitution_types(self, constitution_data: List[Dict[str, Any]], top_n: int = 3) -> List[str]:
        """
        获取主要体质类型
        
        Args:
            constitution_data: 体质数据
            top_n: 返回前N个主要体质类型
        
        Returns:
            List[str]: 主要体质类型列表
        """
        try:
            # 按百分比排序
            sorted_types = sorted(constitution_data, key=lambda x: x.get('percent', 0), reverse=True)
            
            # 返回前N个体质类型
            return [item.get('type', '') for item in sorted_types[:top_n] if item.get('percent', 0) > 0]
            
        except Exception as e:
            print(f"[ERROR] 获取主要体质类型失败: {str(e)}")
            return []

# ===============================================
# 🎯 请求和响应Schema定义
# ===============================================

class ConstitutionSimilarityRequestIn(BaseModel):
    """体质相似度排序请求Schema"""
    therapy_id: int = Field(..., description="疗法ID")
    user_constitution_data: List[Dict[str, Any]] = Field(..., description="用户体质数据")
    status: Optional[str] = Field(None, description="状态过滤")
    page: int = Field(1, description="页码")
    size: int = Field(20, description="每页数量")
    similarity_method: str = Field("cosine", description="相似度计算方法")
    
    class Config:
        schema_extra = {
            "example": {
                "therapy_id": 101847,
                "user_constitution_data": [
                    {"type": "血虚", "percent": 30.82},
                    {"type": "阴虚", "percent": 30.73},
                    {"type": "肝", "percent": 25.19},
                    {"type": "阳虚", "percent": 25.0},
                    {"type": "气滞", "percent": 24.87},
                    {"type": "气虚", "percent": 24.44},
                    {"type": "痰症", "percent": 19.47},
                    {"type": "脾", "percent": 19.41},
                    {"type": "肾", "percent": 18.91},
                    {"type": "实热", "percent": 17.18},
                    {"type": "心", "percent": 15.69},
                    {"type": "实寒", "percent": 11.7},
                    {"type": "湿症", "percent": 9.49},
                    {"type": "肺", "percent": 7.21},
                    {"type": "血瘀", "percent": 0.0},
                    {"type": "上热下寒", "percent": 0.0}
                ],
                "status": "completed",
                "page": 1,
                "size": 20,
                "similarity_method": "cosine"
            }
        }

class InterventionRecordWithSimilarity(BaseModel):
    """带相似度的干预记录Schema - 移除体质匹配信息"""
    # 继承原有的InterventionRecordSchema字段
    id: int
    therapy_name: str
    classification_name: Optional[str] = None
    target_symptoms: List[str]
    status: str
    start_date: date
    end_date: Optional[date] = None
    planned_duration_days: int
    actual_duration_days: Optional[int] = None
    frequency_per_day: int
    compliance_score: Optional[int] = None
    effectiveness_score: Optional[float] = None
    improved_symptoms: List[str] = []
    worsened_symptoms: List[str] = []
    symptom_changes: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None
    side_effects: Optional[str] = None
    progress_percentage: int
    created_at: datetime
    calculation_history_id: Optional[int] = None
    calculation_history: Optional[Dict[str, Any]] = None
    # 移除 constitution_result 字段，不再返回体质信息
    
    # 新增相似度字段
    similarity_score: float = Field(..., description="体质相似度得分")

class ConstitutionSimilarityResponseOut(BaseModel):
    """体质相似度排序响应Schema"""
    total_count: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    similarity_method: str = Field(..., description="相似度计算方法")
    records: List[InterventionRecordWithSimilarity] = Field(..., description="排序后的记录列表")
    
    class Config:
        schema_extra = {
            "example": {
                "total_count": 10,
                "page": 1,
                "size": 20,
                "similarity_method": "cosine",
                "records": [
                    {
                        "id": 16,
                        "therapy_name": "月信节律观察记录",
                        "similarity_score": 0.95
                        # ... 其他InterventionRecordSchema字段
                    }
                ]
            }
        }

# ===============================================
# 🚀 基于体质相似度排序的API端点
# ===============================================

@prognosis_router.post("/therapy-users-intervention-records-similarity", response=ConstitutionSimilarityResponseOut)
@api_timer("基于体质相似度排序的疗法干预记录")
@rate_limit("therapy_users_similarity_api", normal_limit=20, member_limit=80)  # POST请求限制相对严格
async def get_therapy_users_intervention_records_by_similarity(request, payload: ConstitutionSimilarityRequestIn):
    """
    基于体质相似度排序的疗法干预记录查询 - POST方法
    
    【测试URL】: POST /api/routertest1/prognosis/therapy-users-intervention-records-similarity
    
    功能特点:
    1. 🧬 接收用户体质数据，计算与记录的相似度
    2. 📊 支持多种相似度计算方法（余弦、欧氏距离、加权）
    3. 🎯 按相似度从高到低排序返回
    4. 🔄 完全兼容现有数据结构和API格式
    5. 🚀 支持缓存优化，提升性能
    6. ⏱️ 提供相似度计算耗时统计
    
    相似度计算方法:
    - cosine: 余弦相似度（默认）- 适用于高维向量比较
    - euclidean: 欧氏距离相似度 - 适用于数值差异比较  
    - weighted: 加权相似度 - 重点关注主要体质类型
    
    请求示例:
    ```json
    {
        "therapy_id": 101847,
        "user_constitution_data": [
            {"type": "血虚", "percent": 30.82},
            {"type": "阴虚", "percent": 30.73},
            // ... 其他体质数据
        ],
        "status": "completed",
        "page": 1,
        "size": 20,
        "similarity_method": "cosine"
    }
    ```
    """
    try:
        current_user_id = request.user_id
        print(f"[DEBUG] 🧬 体质相似度排序API - 用户: {current_user_id}, 疗法: {payload.therapy_id}")
        
        # 参数验证
        if payload.page < 1:
            payload.page = 1
        if payload.size < 1 or payload.size > 100:
            payload.size = 20
        
        # 验证相似度计算方法
        valid_methods = ["cosine", "euclidean", "weighted"]
        if payload.similarity_method not in valid_methods:
            raise HttpError(400, f"无效的相似度计算方法，支持的方法: {', '.join(valid_methods)}")
        
        # 验证用户体质数据
        if not payload.user_constitution_data:
            raise HttpError(400, "用户体质数据不能为空")
        
        # 🚀 验证疗法是否存在
        is_valid, therapy_type, actual_id, therapy_obj = await validate_therapy_exists(payload.therapy_id)
        if not is_valid:
            raise HttpError(404, f"疗法不存在 - therapy_id: {payload.therapy_id}")
        
        print(f"[DEBUG] ✅ 疗法验证通过 - type: {therapy_type}, actual_id: {actual_id}")
        
        # 🔥 生成缓存键
        cache_params = {
            'therapy_id': payload.therapy_id,
            'user_constitution': payload.user_constitution_data,
            'status': payload.status,
            'page': payload.page,
            'size': payload.size,
            'method': payload.similarity_method,
            'version': 'v1.1'  # 更新版本号，因为移除了体质匹配信息
        }
        cache_key_raw = f"constitution_similarity_{json.dumps(cache_params, sort_keys=True)}"
        cache_key = hashlib.md5(cache_key_raw.encode()).hexdigest()
        
        # 🚀 检查缓存
        try:
            cached_data = await sync_to_async(cache.get)(cache_key)
            if cached_data:
                print(f"[CACHE_HIT] ⚡ 体质相似度缓存命中")
                return json.loads(cached_data) if isinstance(cached_data, str) else cached_data
        except Exception as cache_error:
            print(f"[WARNING] 缓存查询失败: {cache_error}")
        
        # 🧬 初始化体质相似度计算器
        calculator = ConstitutionSimilarityCalculator()
        
        # 🚀 获取原始数据
        print(f"[DEBUG] 🔍 开始获取原始干预记录数据...")
        raw_records = await fetch_therapy_users_data(
            payload.therapy_id, payload.status, payload.page, payload.size,
            therapy_type, actual_id, therapy_obj
        )
        
        if not raw_records:
            # 空结果
            result = {
                "total_count": 0,
                "page": payload.page,
                "size": payload.size,
                "similarity_method": payload.similarity_method,
                "records": []
            }
            
            # 缓存空结果
            try:
                await sync_to_async(cache.set)(cache_key, json.dumps(result, default=str), timeout=60)
            except Exception as e:
                print(f"[WARNING] 空结果缓存失败: {e}")
            
            return result
        
        # 🧬 计算相似度并排序
        print(f"[DEBUG] 🧬 开始计算体质相似度...")
        # 🕐 整体计算开始计时
        similarity_calculation_start = time.time()
        
        records_with_similarity = []
        total_similarity_time = 0.0
        
        for record in raw_records:
            try:
                # 获取记录的体质数据
                record_constitution = record.get('constitution_result', [])
                
                if not record_constitution:
                    print(f"[WARNING] 记录 {record.get('id')} 缺少体质数据，跳过")
                    continue
                
                # 🕐 单个记录相似度计算开始计时
                record_calc_start = time.time()
                
                # 计算相似度
                similarity_score = calculator.calculate_similarity(
                    payload.user_constitution_data,
                    record_constitution,
                    payload.similarity_method
                )
                
                # 🕐 单个记录相似度计算结束计时
                record_calc_end = time.time()
                record_calc_time = (record_calc_end - record_calc_start) * 1000
                total_similarity_time += record_calc_time
                
                # 构建带相似度的记录（移除体质匹配信息）
                record_with_similarity = {
                    **{k: v for k, v in record.items() if k != 'constitution_result'},  # 移除体质信息
                    "similarity_score": round(similarity_score, 4)
                }
                
                records_with_similarity.append(record_with_similarity)
                
            except Exception as e:
                print(f"[ERROR] 计算记录 {record.get('id')} 相似度失败: {str(e)}")
                continue
        
        # 🕐 整体计算结束计时
        similarity_calculation_end = time.time()
        total_calculation_time = (similarity_calculation_end - similarity_calculation_start) * 1000
        
        # 🎯 按相似度排序（从高到低）
        records_with_similarity.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        print(f"[TIMING] ⏱️ 体质相似度计算统计:")
        print(f"[TIMING] ⏱️ - 总计算时间: {total_calculation_time:.2f}ms")
        print(f"[TIMING] ⏱️ - 处理记录数: {len(records_with_similarity)}")
        print(f"[TIMING] ⏱️ - 平均每条记录: {total_calculation_time/max(len(records_with_similarity), 1):.2f}ms")
        print(f"[TIMING] ⏱️ - 相似度计算方法: {payload.similarity_method}")
        
        # 🚀 构建响应
        result = {
            "total_count": len(records_with_similarity),
            "page": payload.page,
            "size": payload.size,
            "similarity_method": payload.similarity_method,
            "records": records_with_similarity
        }
        
        # 🔥 缓存结果
        try:
            cache_timeout = 300  # 5分钟缓存
            await sync_to_async(cache.set)(cache_key, json.dumps(result, default=str), timeout=cache_timeout)
            print(f"[CACHE_SET] ⚡ 体质相似度结果已缓存")
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存存储失败: {cache_error}")
        
        print(f"[DEBUG] ✅ 体质相似度排序API完成，返回 {len(records_with_similarity)} 条记录")
        return result
        
    except Exception as e:
        print(f"[ERROR] 体质相似度排序API失败: {str(e)}")
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        raise HttpError(500, f"体质相似度排序API失败: {str(e)}")

# ===============================================
# 🔍 体质分析报告API端点
# ===============================================

@prognosis_router.get("/constitution-similarity-analysis/{therapy_id}")
@api_timer("疗法体质分析报告")
@rate_limit("constitution_analysis_api", normal_limit=30, member_limit=120)  # 分析报告查询
async def get_constitution_similarity_analysis(request, therapy_id: int):
    """
    获取疗法的体质分析报告
    
    【测试URL】: GET /api/routertest1/prognosis/constitution-similarity-analysis/{therapy_id}
    
    功能特点:
    1. 📊 分析该疗法下所有用户的体质分布
    2. 🎯 识别主要适用的体质类型
    3. 💡 提供个性化推荐建议
    4. 📈 统计体质类型的出现频率和平均占比
    """
    try:
        current_user_id = request.user_id
        print(f"[DEBUG] 🔍 体质分析报告 - 用户: {current_user_id}, 疗法: {therapy_id}")
        
        # 🚀 验证疗法是否存在
        is_valid, therapy_type, actual_id, therapy_obj = await validate_therapy_exists(therapy_id)
        if not is_valid:
            raise HttpError(404, f"疗法不存在 - therapy_id: {therapy_id}")
        
        # 🔥 检查缓存
        cache_key = f"constitution_analysis_{therapy_id}"
        cached_data = await sync_to_async(cache.get)(cache_key)
        
        if cached_data:
            print(f"[CACHE_HIT] ⚡ 体质分析报告缓存命中")
            return json.loads(cached_data) if isinstance(cached_data, str) else cached_data
        
        # 🚀 获取该疗法的所有记录
        all_records = await fetch_therapy_users_data(
            therapy_id, None, 1, 1000,  # 获取大量数据用于分析
            therapy_type, actual_id, therapy_obj
        )
        
        if not all_records:
            result = {
                "therapy_id": therapy_id,
                "therapy_name": "未知疗法",
                "total_records": 0,
                "constitution_distribution": {},
                "main_constitution_types": [],
                "recommendations": []
            }
            return result
        
        # 🧬 分析体质分布
        constitution_stats = {}
        total_records = len(all_records)
        
        for record in all_records:
            constitution_result = record.get('constitution_result', [])
            if not constitution_result:
                continue
            
            for const_item in constitution_result:
                const_type = const_item.get('type', '')
                percent = const_item.get('percent', 0)
                
                if const_type not in constitution_stats:
                    constitution_stats[const_type] = {
                        'total_percent': 0,
                        'count': 0,
                        'avg_percent': 0
                    }
                
                constitution_stats[const_type]['total_percent'] += percent
                constitution_stats[const_type]['count'] += 1
        
        # 计算平均占比
        for const_type in constitution_stats:
            stats = constitution_stats[const_type]
            stats['avg_percent'] = round(stats['total_percent'] / total_records, 2)
        
        # 🎯 识别主要体质类型（按平均占比排序）
        main_types = sorted(
            constitution_stats.items(),
            key=lambda x: x[1]['avg_percent'],
            reverse=True
        )[:5]  # 取前5个主要体质类型
        
        # 🚀 获取疗法名称
        therapy_name = "未知疗法"
        if therapy_type == "system_therapy" and therapy_obj:
            therapy_name = therapy_obj.name
        elif therapy_type == "user_therapy" and therapy_obj:
            therapy_name = therapy_obj.name or f"用户疗法 ID: {actual_id}"
        
        # 💡 生成推荐建议
        recommendations = []
        if main_types:
            top_type = main_types[0]
            recommendations.append(f"该疗法主要适用于{top_type[0]}体质用户（平均占比{top_type[1]['avg_percent']}%）")
            
            if len(main_types) > 1:
                second_type = main_types[1]
                recommendations.append(f"也适用于{second_type[0]}体质用户（平均占比{second_type[1]['avg_percent']}%）")
        
        # 🚀 构建响应
        result = {
            "therapy_id": therapy_id,
            "therapy_name": therapy_name,
            "total_records": total_records,
            "constitution_distribution": {
                const_type: {
                    "avg_percent": stats['avg_percent'],
                    "frequency": stats['count']
                }
                for const_type, stats in constitution_stats.items()
            },
            "main_constitution_types": [
                {
                    "type": const_type,
                    "avg_percent": stats['avg_percent'],
                    "frequency": stats['count']
                }
                for const_type, stats in main_types
            ],
            "recommendations": recommendations
        }
        
        # 🔥 缓存结果（10分钟）
        try:
            await sync_to_async(cache.set)(cache_key, json.dumps(result, default=str), timeout=600)
            print(f"[CACHE_SET] ⚡ 体质分析报告已缓存")
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存存储失败: {cache_error}")
        
        print(f"[DEBUG] ✅ 体质分析报告完成，分析了 {total_records} 条记录")
        return result
        
    except Exception as e:
        print(f"[ERROR] 体质分析报告失败: {str(e)}")
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        raise HttpError(500, f"体质分析报告失败: {str(e)}")

# ===============================================
# 🔧 模块导出和状态检查
# ===============================================

def get_constitution_similarity_info():
    """获取体质相似度模块信息"""
    return {
        "module": "prognosis_constitution_similarity",
        "version": "1.1.0",  # 版本更新
        "endpoints": [
            "POST /therapy-users-intervention-records-similarity - 体质相似度排序",
            "GET /constitution-similarity-analysis/{therapy_id} - 体质分析报告"
        ],
        "similarity_methods": ["cosine", "euclidean", "weighted"],
        "constitution_types": 16,
        "status": "active",
        "changes": "移除体质匹配信息返回，增加计算耗时统计"
    }

def get_constitution_similarity_test_endpoints():
    """获取体质相似度测试端点列表"""
    return [
        "POST /api/routertest1/prognosis/therapy-users-intervention-records-similarity - 体质相似度排序",
        "GET /api/routertest1/prognosis/constitution-similarity-analysis/{therapy_id} - 体质分析报告"
    ]



# ===============================================
# 🚀 高性能缓存优化工具函数
# ===============================================

# 🔥 全局缓存实例
_search_engine_instance = None
_classification_names_cache = {}
_keyword_expansion_cache = {}
_memory_cache = {}  # 内存缓存，用于热点数据
_hot_keywords = ['头痛', '肥胖', '失眠', '美容', '腰痛', '胸闷', '气短', '焦虑', '乏力', '咳嗽']  # 热门关键词

# 🔥 内存缓存管理
def get_memory_cache(key: str):
    """获取内存缓存"""
    if key in _memory_cache:
        cached_item = _memory_cache[key]
        # 检查是否过期（5分钟内存缓存）
        if time.time() - cached_item['timestamp'] < 300:
            return cached_item['data']
        else:
            del _memory_cache[key]
    return None

def fast_json_dumps(data):
    """高性能JSON序列化"""
    if FAST_JSON_AVAILABLE:
        return orjson.dumps(data).decode('utf-8')
    else:
        return json.dumps(data, default=str)

def fast_json_loads(data):
    """高性能JSON反序列化"""
    if FAST_JSON_AVAILABLE:
        return orjson.loads(data)
    else:
        return json.loads(data)

def set_memory_cache(key: str, data):
    """设置内存缓存"""
    _memory_cache[key] = {
        'data': data,
        'timestamp': time.time()
    }
    
    # 限制内存缓存大小，防止内存泄漏
    if len(_memory_cache) > 1000:
        # 删除最旧的100个缓存项
        sorted_items = sorted(_memory_cache.items(), key=lambda x: x[1]['timestamp'])
        for i in range(100):
            if i < len(sorted_items):
                del _memory_cache[sorted_items[i][0]]

def set_memory_cache_with_compression(key: str, data):
    """设置内存缓存（带压缩优化）"""
    # 🔥 对大数据进行压缩存储
    import sys
    data_size = sys.getsizeof(str(data))
    
    if data_size > 10000:  # 大于10KB的数据进行优化
        # 移除不必要的字段，减少内存占用
        compressed_data = compress_response_data(data)
        _memory_cache[key] = {
            'data': compressed_data,
            'timestamp': time.time(),
            'compressed': True
        }
        print(f"[CACHE_COMPRESS] 数据压缩: {data_size} -> {sys.getsizeof(str(compressed_data))} bytes")
    else:
        _memory_cache[key] = {
            'data': data,
            'timestamp': time.time(),
            'compressed': False
        }
    
    # 限制内存缓存大小
    if len(_memory_cache) > 1000:
        sorted_items = sorted(_memory_cache.items(), key=lambda x: x[1]['timestamp'])
        for i in range(100):
            if i < len(sorted_items):
                del _memory_cache[sorted_items[i][0]]

def compress_response_data(data):
    """压缩响应数据，移除不必要的字段"""
    if isinstance(data, dict) and 'recommendations' in data:
        compressed_data = data.copy()
        compressed_recommendations = []
        
        for rec in data['recommendations']:
            # 只保留核心字段，移除冗余信息
            compressed_rec = {
                'id': rec.get('id'),
                'name': rec.get('name'),
                'description': rec.get('description', '')[:50] + '...' if len(rec.get('description', '')) > 50 else rec.get('description', ''),  # 截断描述
                'therapy_type': rec.get('therapy_type'),
                'match_score': rec.get('match_score'),
                'effectiveness_score': rec.get('effectiveness_score'),
                'classification_name': rec.get('classification_name'),
                'duration': rec.get('duration'),
                'is_recommended': rec.get('is_recommended', False),
                'recommendation_reasons': rec.get('recommendation_reasons', [])[:2]  # 只保留前2个推荐理由
            }
            compressed_recommendations.append(compressed_rec)
        
        compressed_data['recommendations'] = compressed_recommendations
        return compressed_data
    
    return data

async def get_cached_search_engine():
    """获取缓存的搜索引擎实例"""
    global _search_engine_instance
    if _search_engine_instance is None:
        _search_engine_instance = SymptomSearchEngine()
        print(f"[CACHE] 🔍 搜索引擎实例已创建并缓存")
        
        # 🔥 延迟预热，避免循环调用
        # asyncio.create_task(preheat_hot_keywords_cache())
    return _search_engine_instance

async def preheat_hot_keywords_cache():
    """预热热门关键词缓存"""
    try:
        global _hot_keywords
        print(f"[CACHE_PREHEAT] 🔥 开始预热热门关键词缓存...")
        
        for keyword in _hot_keywords:
            try:
                # 预热关键词扩展缓存
                await get_cached_keyword_expansion(keyword)
                
                # 预热搜索结果缓存（轻量级）
                cache_key = f"symptom_search:{keyword}:20:True:relevance:v1.1"
                if not get_memory_cache(cache_key):
                    # 只有当缓存不存在时才预热
                    print(f"[CACHE_PREHEAT] 🔥 预热关键词: {keyword}")
                    
            except Exception as e:
                print(f"[CACHE_PREHEAT_ERROR] 关键词 {keyword} 预热失败: {e}")
                
        print(f"[CACHE_PREHEAT] ✅ 热门关键词缓存预热完成")
        
    except Exception as e:
        print(f"[CACHE_PREHEAT_ERROR] 缓存预热失败: {e}")

async def get_cached_keyword_expansion(keyword: str) -> List[str]:
    """获取缓存的关键词扩展结果"""
    global _keyword_expansion_cache
    
    if keyword in _keyword_expansion_cache:
        return _keyword_expansion_cache[keyword]
    
    # 从Redis缓存获取
    cache_key = f"keyword_expansion:{keyword}"
    try:
        cached_result = await sync_to_async(cache.get)(cache_key)
        if cached_result:
            result = fast_json_loads(cached_result)
            _keyword_expansion_cache[keyword] = result
            return result
    except Exception as e:
        print(f"[CACHE_ERROR] 关键词扩展缓存读取失败: {e}")
    
    # 计算并缓存结果
    search_engine = await get_cached_search_engine()
    result = search_engine.optimize_keyword_search(keyword)
    
    # 缓存到内存和Redis
    _keyword_expansion_cache[keyword] = result
    try:
        await sync_to_async(cache.set)(cache_key, fast_json_dumps(result), timeout=3600)  # 1小时缓存
    except Exception as e:
        print(f"[CACHE_ERROR] 关键词扩展缓存存储失败: {e}")
    
    return result

async def get_cached_classification_names(classification_ids: List[int]) -> Dict[int, str]:
    """获取缓存的分类名称"""
    global _classification_names_cache
    
    if not classification_ids:
        return {}
    
    # 检查内存缓存
    uncached_ids = []
    result = {}
    
    for cls_id in classification_ids:
        if cls_id in _classification_names_cache:
            result[cls_id] = _classification_names_cache[cls_id]
        else:
            uncached_ids.append(cls_id)
    
    if not uncached_ids:
        return result
    
    # 从Redis缓存获取
    cache_key = f"classification_names:batch"
    try:
        cached_result = await sync_to_async(cache.get)(cache_key)
        if cached_result:
            cached_names = json.loads(cached_result)
            for cls_id in uncached_ids[:]:  # 使用切片避免修改列表时的问题
                if str(cls_id) in cached_names:
                    result[cls_id] = cached_names[str(cls_id)]
                    _classification_names_cache[cls_id] = cached_names[str(cls_id)]
                    uncached_ids.remove(cls_id)
    except Exception as e:
        print(f"[CACHE_ERROR] 分类名称缓存读取失败: {e}")
    
    if not uncached_ids:
        return result
    
    # 从数据库获取剩余的分类名称
    try:
        from api.models import PrognosisTherapyClassification
        
        search_engine = await get_cached_search_engine()
        db_result = await search_engine.batch_get_classification_names(uncached_ids)
        
        # 更新缓存
        result.update(db_result)
        _classification_names_cache.update(db_result)
        
        # 更新Redis缓存
        try:
            all_cached_names = {str(k): v for k, v in _classification_names_cache.items()}
            await sync_to_async(cache.set)(cache_key, json.dumps(all_cached_names), timeout=3600)  # 1小时缓存
        except Exception as e:
            print(f"[CACHE_ERROR] 分类名称缓存存储失败: {e}")
            
    except Exception as e:
        print(f"[ERROR] 获取分类名称失败: {e}")
    
    return result

# ===============================================
# 🔍 症状搜索与疗法推荐API模块
# ===============================================

class SymptomSearchEngine:
    """症状搜索引擎"""
    
    def __init__(self):
        """初始化症状搜索引擎"""
        self.similarity_calculator = ConstitutionSimilarityCalculator()
        self.synonym_map = self._load_synonym_dict()
        # 🎯 新增：允许的疗法类别编码列表
        self.allowed_therapy_codes = [
            'massage',           # 按摩疗法
            'moxibustion',       # 艾灸疗法
            'food_therapy',      # 食疗疗法
            'exercise',          # 中医功法疗法
            'tea_therapy',       # 茶疗疗法
            'music_therapies',   # 五音疗法
            'xiang_therapies',   # 香薰疗法
            'xiangshu_therapies', # 象数疗法
            'ear_acup_therapies' # 中医耳穴疗法
        ]
        print(f"[DEBUG] 🔍 症状搜索引擎初始化完成，加载同义词 {len(self.synonym_map)} 个")
        print(f"[DEBUG] 🎯 允许的疗法类别: {self.allowed_therapy_codes}")
    
    def _load_synonym_dict(self) -> Dict[str, str]:
        """
        加载同义词词典
        
        Returns:
            Dict[str, str]: 同义词映射字典
        """
        try:
            # 获取JSON文件路径
            json_path = os.path.join(os.path.dirname(__file__), '../../../symptom_synonyms.json')
            
            # 如果文件不存在，使用默认词典
            if not os.path.exists(json_path):
                print(f"[WARNING] 同义词文件不存在: {json_path}，使用默认词典")
                return {
                    '肥胖': '肥胖',
                    '头疼': '头痛',
                    '睡不着': '失眠',
                    '紧张': '焦虑',
                    
                    '胸口闷': '胸闷',
                    '肚子痛': '腹痛',
                    '腰疼': '腰痛'
                }
            
            # 加载JSON文件
            with open(json_path, 'r', encoding='utf-8') as f:
                synonym_data = json.load(f)
            
            # 构建扁平化的同义词映射
            synonym_map = {}
            categories = synonym_data.get('categories', {})
            for category_name, synonyms in categories.items():
                synonym_map.update(synonyms)
            
            print(f"[INFO] 成功加载同义词词典，共 {len(synonym_map)} 个映射")
            return synonym_map
            
        except Exception as e:
            print(f"[ERROR] 加载同义词词典失败: {e}")
            # 返回默认词典
            return {
                '头疼': '头痛',
                '睡不着': '失眠',
                '紧张': '焦虑',
                '累': '疲劳',
                '胸口闷': '胸闷',
                '肚子痛': '腹痛',
                '腰疼': '腰痛'
            }
    
    async def search_symptoms_in_therapies_cached(self, keyword: str, limit: int = 25) -> Dict[str, Any]:
        """
        在疗法中搜索症状关键词 - 高性能缓存版本
        
        Args:
            keyword: 搜索关键词
            limit: 返回结果数量限制
            
        Returns:
            Dict: 包含搜索结果的字典
        """
        try:
            # 🔥 缓存键优化
            cache_key = f"therapy_search:{keyword}:{limit}:v1.1"
            
            # 🚀 检查缓存
            try:
                cached_result = await sync_to_async(cache.get)(cache_key)
                if cached_result:
                    print(f"[CACHE_HIT] ⚡ 疗法搜索缓存命中: {keyword}")
                    return json.loads(cached_result)
            except Exception as e:
                print(f"[CACHE_ERROR] 疗法搜索缓存读取失败: {e}")
            
            # 调用原始搜索方法
            result = await self.search_symptoms_in_therapies(keyword, limit)
            
            # 🔥 缓存结果
            try:
                await sync_to_async(cache.set)(cache_key, json.dumps(result, default=str), timeout=1800)  # 30分钟缓存
                print(f"[CACHE_SET] ⚡ 疗法搜索结果已缓存: {keyword}")
            except Exception as e:
                print(f"[CACHE_ERROR] 疗法搜索缓存存储失败: {e}")
            
            return result
            
        except Exception as e:
            print(f"[ERROR] 缓存版疗法搜索失败: {str(e)}")
            # 降级到原始搜索
            return await self.search_symptoms_in_therapies(keyword, limit)

    async def search_symptoms_in_therapies(self, keyword: str, limit: int = 25) -> Dict[str, Any]:
        """
        在疗法中搜索症状关键词 - 性能优化版本
        
        Args:
            keyword: 搜索关键词
            limit: 返回结果数量限制
            
        Returns:
            Dict: 包含搜索结果的字典
        """
        try:
            search_start_time = time.time()
            
            # 🎯 严格限制返回数量，避免数据过多
            limit = min(limit, 30)  # 最多返回30个结果，从20个增加到30个
            
            # 🎯 获取扩展关键词列表
            search_keywords = self.optimize_keyword_search(keyword)
            
            # 🚀 构建优化的查询条件
            def build_system_query():
                query = Q()
                for kw in search_keywords:
                    query |= (Q(name__icontains=kw) | 
                             Q(description__icontains=kw))  # 移除instructions字段查询
                # 🎯 只返回允许的疗法类别
                query &= Q(classification__code__in=self.allowed_therapy_codes)
                return query
            
            def build_user_query():
                query = Q()
                for kw in search_keywords:
                    query |= (Q(name__icontains=kw) | 
                             Q(description__icontains=kw) |
                             Q(related_symptoms__icontains=kw))
                # 🎯 只返回允许的用户疗法类别
                query &= Q(category__in=self.allowed_therapy_codes)
                return query
            
            # 🚀 性能优化：使用异步工具函数执行搜索
            from api.ninja_apis.async_utils import values_async

            # 构建系统疗法查询
            system_query_filters = build_system_query()
            system_query_filters &= Q(is_active=True)

            # 构建用户疗法查询
            user_query_filters = build_user_query()
            user_query_filters &= Q(is_public=True)

            # 🚀 顺序执行搜索 - 修复uvicorn中asyncio.gather()死锁问题
            print("[DEBUG] 🔍 开始系统疗法搜索...")
            system_therapies_queryset = PrognosisTherapyCategory.objects.filter(
                system_query_filters
            ).select_related('classification').values(
                'id', 'name', 'description', 'classification_id',
                'classification__name', 'classification__code',
                'duration', 'difficulty_level', 'is_recommended'
            ).distinct()[:limit//2]

            system_therapies = await values_async(system_therapies_queryset)
            print(f"[DEBUG] 🔍 系统疗法搜索完成: {len(system_therapies)} 个结果")

            print("[DEBUG] 🔍 开始用户疗法搜索...")
            user_therapies_queryset = PrognosisUserTherapy.objects.filter(
                user_query_filters
            ).values(
                'id', 'name', 'description', 'category', 'related_symptoms',
                'duration', 'usage_count', 'is_verified'
            ).distinct()[:limit//2]

            user_therapies = await values_async(user_therapies_queryset)
            print(f"[DEBUG] 🔍 用户疗法搜索完成: {len(user_therapies)} 个结果")
            
            # 🎯 如果结果太少，尝试模糊匹配（保持类别筛选）- 但限制更严格
            total_results = len(system_therapies) + len(user_therapies)
            if total_results < 3 and len(search_keywords) == 1:
                print(f"[DEBUG] 🔍 结果较少({total_results}个)，尝试模糊匹配...")
                
                # 添加部分匹配搜索（保持类别筛选）
                async def fuzzy_search_user():
                    return await sync_to_async(lambda: list(
                        PrognosisUserTherapy.objects.filter(
                            Q(name__contains=keyword[:2]) |  # 前2个字符匹配
                            Q(related_symptoms__contains=keyword[:2]),
                            is_public=True,
                            category__in=self.allowed_therapy_codes  # 🎯 保持类别筛选
                        ).exclude(id__in=[t['id'] for t in user_therapies])
                        .values(
                            'id', 'name', 'description', 'category', 'related_symptoms',
                            'duration', 'usage_count', 'is_verified'
                        )[:3]  # 最多补充3个，减少数据量
                    ))()
                
                fuzzy_results = await fuzzy_search_user()
                user_therapies.extend(fuzzy_results)
                print(f"[DEBUG] 🔍 模糊匹配补充 {len(fuzzy_results)} 个结果")
            
            search_end_time = time.time()
            search_time = (search_end_time - search_start_time) * 1000
            
            print(f"[TIMING] ⏱️ 优化后搜索耗时: {search_time:.2f}ms")
            print(f"[DEBUG] 🔍 搜索结果（类别筛选后）: 系统疗法 {len(system_therapies)} 个，用户疗法 {len(user_therapies)} 个")
            print(f"[DEBUG] 🎯 筛选的类别: {self.allowed_therapy_codes}")
            
            return {
                'system_therapies': system_therapies,
                'user_therapies': user_therapies,
                'search_time_ms': round(search_time, 2),
                'search_keywords': search_keywords,
                'filtered_categories': self.allowed_therapy_codes  # 🎯 返回筛选的类别信息
            }
            
        except Exception as e:
            print(f"[ERROR] 症状搜索失败: {str(e)}")
            return {
                'system_therapies': [],
                'user_therapies': [],
                'search_time_ms': 0,
                'search_keywords': [keyword],
                'filtered_categories': self.allowed_therapy_codes
            }
    
    async def batch_get_classification_names(self, classification_ids: List[int]) -> Dict[int, str]:
        """
        批量获取分类名称 - 性能优化（新增类别筛选验证）
        
        Args:
            classification_ids: 分类ID列表
            
        Returns:
            Dict[int, str]: 分类ID到名称的映射
        """
        try:
            if not classification_ids:
                return {}
            
            # 🚀 批量查询分类名称（新增类别筛选验证）
            classifications = await sync_to_async(lambda: list(
                PrognosisTherapyClassification.objects.filter(
                    id__in=classification_ids,
                    code__in=self.allowed_therapy_codes  # 🎯 只返回允许的类别
                ).values('id', 'name', 'code')
            ))()
            
            result = {cls['id']: cls['name'] for cls in classifications}
            
            # 🎯 记录筛选结果
            filtered_count = len(classification_ids) - len(result)
            if filtered_count > 0:
                print(f"[DEBUG] 🎯 类别筛选: 过滤掉 {filtered_count} 个不允许的分类")
            
            return result
            
        except Exception as e:
            print(f"[ERROR] 批量获取分类名称失败: {str(e)}")
            return {}
    
    def optimize_keyword_search(self, keyword: str) -> List[str]:
        """
        优化搜索关键词 - 返回多个搜索词（支持部分匹配）
        
        Args:
            keyword: 原始关键词
            
        Returns:
            List[str]: 优化后的关键词列表（原词+同义词+部分匹配+动态扩展）
        """
        try:
            # 🎯 去除多余空格
            keyword = keyword.strip()
            search_keywords = [keyword]  # 总是包含原始关键词
            
            # 🎯 精确匹配同义词
            for original, standard in self.synonym_map.items():
                if original == keyword:
                    search_keywords.append(standard)
                elif standard == keyword:
                    search_keywords.append(original)
            
            # 🎯 动态关键词生成（新增）
            dynamic_keywords = self._generate_dynamic_keywords(keyword)
            search_keywords.extend(dynamic_keywords)
            
            # 🎯 部分匹配支持（仅当精确匹配结果少于3个时启用）
            if len(search_keywords) < 3 and len(keyword) >= 1:
                partial_matches = self._find_partial_matches(keyword)
                search_keywords.extend(partial_matches)
            
            # 🎯 语义相关词扩展（新增）
            if len(search_keywords) < 4:
                semantic_keywords = self._find_semantic_related_keywords(keyword)
                search_keywords.extend(semantic_keywords)
            
            # 🎯 去重并限制数量（避免性能问题）
            search_keywords = list(set(search_keywords))[:10]  # 最多10个关键词
            
            # 🎯 记录转换结果
            if len(search_keywords) > 1:
                print(f"[DEBUG] 关键词扩展: '{keyword}' -> {search_keywords}")
            
            return search_keywords
            
        except Exception as e:
            print(f"[ERROR] 关键词优化失败: {str(e)}")
            return [keyword]
    
    def _generate_dynamic_keywords(self, keyword: str) -> List[str]:
        """
        动态生成相关关键词 - 解决预定义词典覆盖不足的问题
        
        Args:
            keyword: 原始关键词
            
        Returns:
            List[str]: 动态生成的关键词列表
        """
        try:
            dynamic_keywords = []
            
            # 🎯 体重相关词汇的动态扩展
            weight_related_rules = {
                # 增重相关
                '增肥': ['肥胖', '体重增加', '长胖', '发胖', '增重', '体重上升'],
                '增重': ['体重增加', '增肥', '长胖', '发胖', '肥胖'],
                '长胖': ['增肥', '体重增加', '发胖', '肥胖', '增重'],
                '发胖': ['增肥', '体重增加', '长胖', '肥胖', '增重'],
                '体重增加': ['增肥', '长胖', '发胖', '肥胖', '增重'],
                '体重上升': ['增肥', '长胖', '发胖', '肥胖', '增重'],
                '变胖': ['增肥', '长胖', '发胖', '肥胖', '增重'],
                
                # 减重相关
                '减肥': ['消瘦', '体重减少', '瘦了', '体重下降', '减重'],
                '减重': ['减肥', '消瘦', '体重减少', '瘦了', '体重下降'],
                '瘦身': ['减肥', '消瘦', '体重减少', '减重'],
                '体重减少': ['减肥', '消瘦', '瘦了', '体重下降', '减重'],
                '体重下降': ['减肥', '消瘦', '瘦了', '体重减少', '减重'],
                '变瘦': ['减肥', '消瘦', '瘦了', '体重减少', '减重'],
                
                # 🎯 美容相关词汇的动态扩展（基于实际症状数据）
                '美容': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '面部色斑', '黄褐斑', '毛孔粗大', '痤疮', '脱发', '头发稀疏', '白发'],
                '美白': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失', '面色晦暗'],
                '祛斑': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失', '面色晦暗'],
                '色斑': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失', '面色晦暗'],
                '黄褐斑': ['面部色斑', '皮肤白斑', '色素脱失', '面色晦暗'],
                '面部色斑': ['黄褐斑', '皮肤白斑', '色素脱失', '面色晦暗'],
                '皮肤白斑': ['黄褐斑', '面部色斑', '色素脱失', '面色晦暗'],
                '色素脱失': ['黄褐斑', '面部色斑', '皮肤白斑', '面色晦暗'],
                '面色晦暗': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失'],
                '抗衰老': ['皮肤松弛', '面部松弛', '皮肤粗糙', '面部皱纹', '皮肤干燥'],
                '去皱': ['皮肤松弛', '面部松弛', '皮肤粗糙', '面部皱纹'],
                '紧致': ['皮肤松弛', '面部松弛', '皮肤粗糙'],
                '皮肤松弛': ['面部松弛', '皮肤粗糙', '面部皱纹'],
                '面部松弛': ['皮肤松弛', '皮肤粗糙', '面部皱纹'],
                '保湿': ['皮肤干燥', '面部干燥', '皮肤粗糙', '脱屑'],
                '补水': ['皮肤干燥', '面部干燥', '皮肤粗糙', '脱屑'],
                '滋润': ['皮肤干燥', '面部干燥', '皮肤粗糙', '脱屑'],
                '皮肤干燥': ['面部干燥', '皮肤粗糙', '脱屑'],
                '皮肤粗糙': ['皮肤干燥', '面部干燥', '脱屑'],
                '脱屑': ['皮肤干燥', '皮肤粗糙', '鳞屑脱落'],
                '鳞屑脱落': ['脱屑', '皮肤干燥', '皮肤粗糙'],
                '护肤': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '皮肤瘙痒'],
                '美肤': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '皮肤瘙痒'],
                '皮肤护理': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '皮肤瘙痒'],
                '皮肤瘙痒': ['皮肤干燥', '皮肤粗糙', '遍身瘙痒', '面痒'],
                '皮肤油腻': ['面部油腻', '毛孔粗大', '痤疮', '面部痤疮'],
                '面部油腻': ['皮肤油腻', '毛孔粗大', '痤疮', '面部痤疮'],
                '毛孔粗大': ['皮肤油腻', '面部油腻', '痤疮', '面部痤疮'],
                '痤疮': ['面部痤疮', '痤疮疖肿', '痤疮红肿', '皮肤油腻', '面部油腻'],
                '面部痤疮': ['痤疮', '痤疮疖肿', '痤疮红肿', '皮肤油腻', '面部油腻'],
                '痤疮疖肿': ['痤疮', '面部痤疮', '痤疮红肿', '皮肤油腻'],
                '痤疮红肿': ['痤疮', '面部痤疮', '痤疮疖肿', '皮肤油腻'],
                '青春痘': ['痤疮', '面部痤疮', '痤疮疖肿', '痤疮红肿'],
                '长痘': ['痤疮', '面部痤疮', '痤疮疖肿', '痤疮红肿'],
                '脱发': ['头发稀疏', '头发早白', '白发', '须发早白'],
                '掉发': ['脱发', '头发稀疏', '头发早白', '白发', '须发早白'],
                '头发稀疏': ['脱发', '头发早白', '白发', '须发早白'],
                '头发早白': ['脱发', '头发稀疏', '白发', '须发早白'],
                '白发': ['脱发', '头发稀疏', '头发早白', '须发早白'],
                '须发早白': ['脱发', '头发稀疏', '头发早白', '白发'],
                '少白头': ['头发早白', '白发', '须发早白'],
                '早白发': ['头发早白', '白发', '须发早白'],
                '头发护理': ['脱发', '头发稀疏', '头发早白', '白发', '头皮瘙痒'],
                '头皮瘙痒': ['头发护理', '脱发', '头发稀疏'],
                '头皮护理': ['头皮瘙痒', '脱发', '头发稀疏'],
                '指甲易裂': ['指甲色淡', '指甲苍白', '指甲护理'],
                '指甲色淡': ['指甲易裂', '指甲苍白', '指甲护理'],
                '指甲苍白': ['指甲易裂', '指甲色淡', '指甲护理'],
                '指甲护理': ['指甲易裂', '指甲色淡', '指甲苍白'],
                '美甲': ['指甲护理', '指甲易裂', '指甲色淡', '指甲苍白'],
                '眼袋': ['眼睑浮肿', '面目浮肿', '眼部护理'],
                '黑眼圈': ['眼袋', '眼睑浮肿', '面目浮肿', '眼部护理'],
                '眼睑浮肿': ['眼袋', '面目浮肿', '眼部护理'],
                '面目浮肿': ['眼袋', '眼睑浮肿', '面浮肿', '眼部护理'],
                '面浮肿': ['面目浮肿', '眼袋', '眼睑浮肿', '面部浮肿'],
                '面部浮肿': ['面浮肿', '面目浮肿', '眼袋', '眼睑浮肿'],
                '眼部护理': ['眼袋', '黑眼圈', '眼睑浮肿', '面目浮肿'],
                '体型': ['肥胖', '体重异常', '体重增加', '身材'],
                '身材': ['肥胖', '体重异常', '体重增加', '体型'],
                '塑形': ['肥胖', '体重异常', '体重增加', '体型', '身材'],
                '体重管理': ['肥胖', '体重异常', '体重增加', '体型', '身材'],
                '肥胖': ['体重异常', '体重增加', '体型', '身材'],
                '体重异常': ['肥胖', '体重增加', '体型', '身材'],
                '体重增加': ['肥胖', '体重异常', '体型', '身材'],
                '减肥': ['肥胖', '体重异常', '体重增加', '体型', '身材'],
                '瘦身': ['肥胖', '体重异常', '体重增加', '体型', '身材'],
                
                # 🎯 气血相关词汇的动态扩展（基于实际症状数据）
                '补气': ['气短', '气喘', '气促', '呼吸气短', '心悸气短', '乏力', '体虚乏力', '四肢乏力'],
                '益气': ['气短', '气喘', '气促', '呼吸气短', '心悸气短', '乏力', '体虚乏力', '四肢乏力'],
                '气虚': ['气短', '气喘', '气促', '呼吸气短', '心悸气短', '乏力', '体虚乏力', '四肢乏力'],
                '气短': ['气喘', '气促', '呼吸气短', '心悸气短', '乏力', '体虚乏力'],
                '气喘': ['气短', '气促', '呼吸气短', '心悸气短', '乏力'],
                '气促': ['气短', '气喘', '呼吸气短', '心悸气短', '乏力'],
                '呼吸气短': ['气短', '气喘', '气促', '心悸气短', '乏力'],
                '心悸气短': ['气短', '气喘', '气促', '呼吸气短', '乏力'],
                '补血': ['血虚萎黄', '血瘀', '血瘀疼痛', '贫血', '面色苍白', '乏力倦怠'],
                '养血': ['血虚萎黄', '血瘀', '血瘀疼痛', '贫血', '面色苍白', '乏力倦怠'],
                '血虚': ['血虚萎黄', '血瘀', '血瘀疼痛', '贫血', '面色苍白', '乏力倦怠'],
                '血瘀': ['血瘀疼痛', '血瘀肿痛', '瘀血性头痛', '瘀血疼痛', '瘀血肿痛', '轻度血瘀'],
                '血虚萎黄': ['血瘀', '血瘀疼痛', '贫血', '面色苍白', '乏力倦怠'],
                '血瘀疼痛': ['血瘀', '血瘀肿痛', '瘀血性头痛', '瘀血疼痛', '瘀血肿痛'],
                '瘀血性头痛': ['血瘀', '血瘀疼痛', '瘀血疼痛', '瘀血肿痛'],
                '瘀血疼痛': ['血瘀', '血瘀疼痛', '瘀血性头痛', '瘀血肿痛'],
                '瘀血肿痛': ['血瘀', '血瘀疼痛', '瘀血性头痛', '瘀血疼痛'],
                '贫血': ['血虚萎黄', '血瘀', '面色苍白', '乏力倦怠'],
                '气血不足': ['乏力', '乏力倦怠', '体倦乏力', '体虚乏力', '倦怠乏力', '四肢乏力', '慢性疲劳'],
                '气血两虚': ['乏力', '乏力倦怠', '体倦乏力', '体虚乏力', '倦怠乏力', '四肢乏力', '慢性疲劳'],
                
                # 🎯 疏肝理气相关词汇的动态扩展（基于实际症状数据）
                '疏肝': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '两胁胀痛', '肝区不适'],
                '理气': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '嗳气', '嗳气反酸', '嗳气频作'],
                '疏肝理气': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '两胁胀痛', '肝区不适'],
                '肝郁': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '两胁胀痛', '肝区不适'],
                '肝郁胁胀': ['胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '两胁胀痛', '肝区不适'],
                '胁痛': ['胁肋疼痛', '胁肋痛', '胁肋胀痛', '胁肋隐痛', '两胁胀痛', '肝区不适'],
                '胁肋疼痛': ['胁痛', '胁肋痛', '胁肋胀痛', '胁肋隐痛', '两胁胀痛'],
                '胁肋痛': ['胁痛', '胁肋疼痛', '胁肋胀痛', '胁肋隐痛', '两胁胀痛'],
                '胁肋胀痛': ['胁痛', '胁肋疼痛', '胁肋痛', '胁肋隐痛', '两胁胀痛'],
                '胁肋隐痛': ['胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '两胁胀痛'],
                '两胁胀痛': ['胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '胁肋隐痛'],
                '肝区不适': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛'],
                '肝阳上亢': ['肝郁胁胀', '胁痛', '胁肋疼痛', '头痛', '头晕'],
                '嗳气': ['嗳气反酸', '嗳气频作', '嗳气频繁', '嗳气吞酸', '嗳气酸腐'],
                '嗳气反酸': ['嗳气', '嗳气频作', '嗳气频繁', '嗳气吞酸', '嗳气酸腐'],
                '嗳气频作': ['嗳气', '嗳气反酸', '嗳气频繁', '嗳气吞酸', '嗳气酸腐'],
                '嗳气频繁': ['嗳气', '嗳气反酸', '嗳气频作', '嗳气吞酸', '嗳气酸腐'],
                '噫气': ['嗳气', '嗳气反酸', '嗳气频作', '嗳气频繁'],
                
                # 🎯 增肌相关词汇的动态扩展（基于实际症状数据）
                '增肌': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '四肢无力', '体虚乏力', '营养不良'],
                '强肌': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '四肢无力', '体虚乏力'],
                '健肌': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '四肢无力', '体虚乏力'],
                '肌肉萎缩': ['肌肉消瘦', '肌肉无力', '四肢无力', '体虚乏力', '营养不良'],
                '肌肉消瘦': ['肌肉萎缩', '肌肉无力', '四肢无力', '体虚乏力', '营养不良'],
                '肌肉无力': ['肌肉萎缩', '肌肉消瘦', '四肢无力', '体虚乏力', '营养不良'],
                '肌肉僵硬': ['肌肉紧张', '肌肉痉挛', '肌肉酸痛', '肌肉麻木'],
                '肌肉紧张': ['肌肉僵硬', '肌肉痉挛', '肌肉酸痛', '肌肉麻木'],
                '肌肉痉挛': ['肌肉僵硬', '肌肉紧张', '肌肉痉挛', '腓肠肌痉挛', '面肌痉挛'],
                '肌肉酸痛': ['肌肉僵硬', '肌肉紧张', '肌肉痉挛', '肌肉麻木'],
                '肌肉麻木': ['肌肉僵硬', '肌肉紧张', '肌肉酸痛', '四肢麻木'],
                '四肢无力': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '四肢乏力', '体虚乏力'],
                '四肢乏力': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '四肢无力', '体虚乏力'],
                '营养不良': ['肌肉萎缩', '肌肉消瘦', '体虚乏力', '消瘦', '面色苍白'],
                
                # 疼痛相关的动态扩展
                '疼': ['痛', '疼痛', '酸痛', '胀痛', '刺痛'],
                '痛': ['疼', '疼痛', '酸痛', '胀痛', '刺痛'],
                '酸': ['酸痛', '酸胀', '酸疼', '疼痛'],
                '胀': ['胀痛', '胀满', '腹胀', '胸胀', '头胀'],
                '闷': ['胸闷', '心闷', '憋闷', '闷痛'],
                '堵': ['胸堵', '鼻堵', '堵塞', '不通'],
                
                # 消化相关
                '拉': ['腹泻', '拉肚子', '拉稀', '便溏'],
                '吐': ['呕吐', '恶心', '想吐', '干呕'],
                '胃': ['胃痛', '胃胀', '胃酸', '胃不舒服'],
                '肚': ['腹痛', '腹胀', '肚子疼', '肚子胀'],
                
                # 睡眠相关
                '睡': ['失眠', '睡不着', '睡眠不好', '多梦'],
                '醒': ['早醒', '易醒', '夜醒', '醒得早'],
                '梦': ['多梦', '噩梦', '梦多', '失眠多梦'],
                
                # 疲劳相关
                '累': ['疲劳', '乏力', '疲惫', '没劲'],
                '乏': ['乏力', '疲劳', '没劲', '无力'],
                '虚': ['体虚', '虚弱', '虚劳', '肾虚'],
                '弱': ['虚弱', '体弱', '无力', '乏力'],
                
                # 情绪相关
                '烦': ['烦躁', '心烦', '烦闷', '烦恼'],
                '急': ['焦急', '急躁', '心急', '着急'],
                '怒': ['易怒', '愤怒', '肝火', '脾气大'],
                '郁': ['抑郁', '郁闷', '郁结', '肝郁'],
                
                # 呼吸相关
                '咳': ['咳嗽', '干咳', '咳痰', '久咳'],
                '喘': ['气喘', '哮喘', '喘息', '呼吸困难'],
                '气': ['气短', '气虚', '气滞', '气急'],
                
                # 皮肤相关
                '痒': ['瘙痒', '皮肤瘙痒', '皮疹', '湿疹'],
                '疹': ['皮疹', '湿疹', '荨麻疹', '红疹'],
                '汗': ['多汗', '盗汗', '自汗', '出汗'],
                
                # 五官相关
                '眼': ['眼干', '眼痛', '眼胀', '眼花'],
                '耳': ['耳鸣', '耳痛', '耳聋', '听力下降'],
                '鼻': ['鼻塞', '鼻涕', '鼻痒', '鼻炎'],
                '口': ['口干', '口苦', '口臭', '口疮'],
                
                # 妇科相关
                '经': ['月经不调', '痛经', '经期', '月经'],
                '带': ['白带', '带下', '分泌物', '白带异常'],
                '乳': ['乳房胀痛', '乳腺', '乳胀', '乳房'],
                
                # 男科相关
                '遗': ['遗精', '遗尿', '滑精', '梦遗'],
                '阳': ['阳痿', '阳虚', '肾阳虚', '阳气不足'],
                '阴': ['阴虚', '肾阴虚', '阴液不足', '阴虚火旺'],
                
                # 部位相关
                '头': ['头痛', '头晕', '头胀', '头重'],
                '颈': ['颈痛', '颈椎', '落枕', '颈部僵硬'],
                '肩': ['肩痛', '肩周炎', '肩膀疼', '肩背痛'],
                '腰': ['腰痛', '腰酸', '腰椎', '腰背痛'],
                '膝': ['膝痛', '膝关节', '膝盖疼', '膝关节炎'],
                '脚': ['足痛', '脚疼', '脚肿', '脚麻'],
                '手': ['手痛', '手麻', '手抖', '手无力'],
                
                # 脏腑相关
                '心': ['心悸', '心慌', '心痛', '心烦'],
                '肝': ['肝火', '肝郁', '肝气郁结', '肝血不足'],
                '脾': ['脾虚', '脾胃', '脾气虚', '脾湿'],
                '肺': ['肺热', '肺燥', '肺气虚', '肺阴虚'],
                '肾': ['肾虚', '肾阳虚', '肾阴虚', '肾气不足'],
                
                # 体质相关
                '寒': ['怕冷', '恶寒', '畏寒', '手脚冰凉'],
                '热': ['发热', '潮热', '内热', '五心烦热'],
                '湿': ['湿热', '湿气', '湿邪', '湿重'],
                '燥': ['燥热', '口燥', '皮肤干燥', '肺燥'],
                
                # 程度相关
                '轻': ['轻微', '轻度', '稍微', '偶尔'],
                '重': ['严重', '重度', '厉害', '明显'],
                '急': ['急性', '突然', '急剧', '急发'],
                '慢': ['慢性', '缓慢', '渐进', '持续'],
                
                # 时间相关
                '晨': ['晨起', '早上', '清晨', '早晨'],
                '夜': ['夜间', '晚上', '夜里', '夜晚'],
                '经': ['经常', '常常', '频繁', '反复'],
                '偶': ['偶尔', '有时', '间歇', '时有'],
            }
            
            # 🎯 应用动态扩展规则
            if keyword in weight_related_rules:
                dynamic_keywords.extend(weight_related_rules[keyword])
            
            # 🎯 字符级别的动态扩展（仅对单字符关键词）
            if len(keyword) == 1 and keyword in weight_related_rules:
                dynamic_keywords.extend(weight_related_rules[keyword])
            
            # 🎯 模糊匹配扩展（针对包含特定字符的关键词）
            for base_char, related_words in weight_related_rules.items():
                if len(base_char) == 1 and base_char in keyword and keyword != base_char:
                    # 只添加最相关的2个词，避免过度扩展
                    dynamic_keywords.extend(related_words[:2])
            
            # 🎯 去重并限制数量
            dynamic_keywords = list(set(dynamic_keywords))[:5]  # 最多5个动态关键词
            
            if dynamic_keywords:
                print(f"[DEBUG] 动态关键词生成: '{keyword}' -> {dynamic_keywords}")
            
            return dynamic_keywords
            
        except Exception as e:
            print(f"[ERROR] 动态关键词生成失败: {str(e)}")
            return []
    
    def _find_semantic_related_keywords(self, keyword: str) -> List[str]:
        """
        查找语义相关的关键词 - 基于医学常识的语义扩展
        
        Args:
            keyword: 原始关键词
            
        Returns:
            List[str]: 语义相关的关键词列表
        """
        try:
            semantic_keywords = []
            
            # 🎯 语义关联规则（基于中医理论）
            semantic_rules = {
                # 体重管理语义群
                '增肥': ['营养不良', '消瘦', '体虚', '脾胃虚弱', '吸收不良'],
                '减肥': ['肥胖', '湿热', '痰湿', '脾虚湿盛', '代谢异常'],
                '肥胖': ['痰湿', '湿热', '脾虚', '代谢缓慢', '内分泌失调'],
                '消瘦': ['阴虚', '血虚', '脾胃虚弱', '营养不良', '慢性消耗'],
                
                # 🎯 美容养颜语义群（基于实际症状数据）
                '美容': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '面部色斑', '黄褐斑', '毛孔粗大', '痤疮', '脱发', '头发稀疏', '白发'],
                '美白': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失', '面色晦暗', '肝郁气滞', '血瘀'],
                '祛斑': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失', '面色晦暗', '肝郁', '血瘀'],
                '色斑': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失', '面色晦暗', '肝郁气滞', '血瘀'],
                '黄褐斑': ['面部色斑', '皮肤白斑', '色素脱失', '面色晦暗', '肝郁', '血瘀', '肾虚'],
                '面部色斑': ['黄褐斑', '皮肤白斑', '色素脱失', '面色晦暗', '肝郁气滞', '血瘀'],
                '皮肤白斑': ['黄褐斑', '面部色斑', '色素脱失', '面色晦暗', '肝郁', '血瘀'],
                '色素脱失': ['黄褐斑', '面部色斑', '皮肤白斑', '面色晦暗', '肝郁', '血瘀'],
                '面色晦暗': ['黄褐斑', '面部色斑', '皮肤白斑', '色素脱失', '气血不足', '脾虚'],
                '抗衰老': ['皮肤松弛', '面部松弛', '皮肤粗糙', '皮肤干燥', '肾虚', '气血不足'],
                '去皱': ['皮肤松弛', '面部松弛', '皮肤粗糙', '肾虚', '血虚', '气血不足'],
                '紧致': ['皮肤松弛', '面部松弛', '皮肤粗糙', '肾虚', '脾虚', '气血不足'],
                '皮肤松弛': ['面部松弛', '皮肤粗糙', '肾虚', '脾虚', '气血不足'],
                '面部松弛': ['皮肤松弛', '皮肤粗糙', '肾虚', '脾虚', '气血不足'],
                '保湿': ['皮肤干燥', '皮肤粗糙', '脱屑', '阴虚', '血虚', '津液不足'],
                '补水': ['皮肤干燥', '皮肤粗糙', '脱屑', '阴虚', '血虚', '津液不足'],
                '滋润': ['皮肤干燥', '皮肤粗糙', '脱屑', '阴虚', '血虚', '津液不足'],
                '皮肤干燥': ['皮肤粗糙', '脱屑', '阴虚', '血虚', '津液不足', '肺燥'],
                '皮肤粗糙': ['皮肤干燥', '脱屑', '阴虚', '血虚', '津液不足'],
                '脱屑': ['皮肤干燥', '皮肤粗糙', '鳞屑脱落', '阴虚', '血虚'],
                '鳞屑脱落': ['脱屑', '皮肤干燥', '皮肤粗糙', '阴虚', '血虚'],
                '护肤': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '皮肤瘙痒', '气血不足'],
                '美肤': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '皮肤瘙痒', '气血不足'],
                '皮肤护理': ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '皮肤瘙痒', '气血不足'],
                '皮肤瘙痒': ['皮肤干燥', '皮肤粗糙', '遍身瘙痒', '面痒', '湿热', '血虚风燥'],
                '皮肤油腻': ['面部油腻', '毛孔粗大', '痤疮', '面部痤疮', '湿热', '痰湿'],
                '面部油腻': ['皮肤油腻', '毛孔粗大', '痤疮', '面部痤疮', '湿热', '痰湿'],
                '毛孔粗大': ['皮肤油腻', '面部油腻', '痤疮', '面部痤疮', '湿热', '痰湿'],
                '痤疮': ['面部痤疮', '痤疮疖肿', '痤疮红肿', '皮肤油腻', '湿热', '血热'],
                '面部痤疮': ['痤疮', '痤疮疖肿', '痤疮红肿', '皮肤油腻', '湿热', '血热'],
                '痤疮疖肿': ['痤疮', '面部痤疮', '痤疮红肿', '湿热', '血热'],
                '痤疮红肿': ['痤疮', '面部痤疮', '痤疮疖肿', '湿热', '血热'],
                '脱发': ['头发稀疏', '头发早白', '白发', '须发早白', '肾虚', '血虚'],
                '头发稀疏': ['脱发', '头发早白', '白发', '须发早白', '肾虚', '血虚'],
                '头发早白': ['脱发', '头发稀疏', '白发', '须发早白', '肾虚', '血虚'],
                '白发': ['脱发', '头发稀疏', '头发早白', '须发早白', '肾虚', '血虚'],
                '须发早白': ['脱发', '头发稀疏', '头发早白', '白发', '肾虚', '血虚'],
                '头皮瘙痒': ['脱发', '头发稀疏', '湿热', '血虚风燥', '肝胆湿热'],
                '指甲易裂': ['指甲色淡', '指甲苍白', '肝血不足', '肾虚', '气血不足'],
                '指甲色淡': ['指甲易裂', '指甲苍白', '肝血不足', '肾虚', '气血不足'],
                '指甲苍白': ['指甲易裂', '指甲色淡', '肝血不足', '肾虚', '气血不足'],
                '眼袋': ['眼睑浮肿', '面目浮肿', '脾虚', '肾虚', '水湿内停'],
                '眼睑浮肿': ['眼袋', '面目浮肿', '面浮肿', '脾虚', '肾虚'],
                '面目浮肿': ['眼袋', '眼睑浮肿', '面浮肿', '面部浮肿', '脾虚', '肾虚'],
                '面浮肿': ['面目浮肿', '眼袋', '眼睑浮肿', '面部浮肿', '脾虚', '肾虚'],
                '面部浮肿': ['面浮肿', '面目浮肿', '眼袋', '眼睑浮肿', '脾虚', '肾虚'],
                '肥胖': ['体重异常', '体重增加', '痰湿', '脾虚', '代谢异常'],
                '体重异常': ['肥胖', '体重增加', '痰湿', '脾虚', '代谢异常'],
                '体重增加': ['肥胖', '体重异常', '痰湿', '脾虚', '代谢异常'],
                
                # 🎯 气血相关语义群（基于实际症状数据）
                '补气': ['气短', '气喘', '气促', '呼吸气短', '心悸气短', '气虚', '脾虚', '肺虚'],
                '益气': ['气短', '气喘', '气促', '呼吸气短', '心悸气短', '气虚', '脾虚', '肺虚'],
                '气虚': ['气短', '气喘', '气促', '呼吸气短', '心悸气短', '乏力', '体虚乏力', '脾虚'],
                '气短': ['气喘', '气促', '呼吸气短', '心悸气短', '气虚', '肺虚', '心虚'],
                '气喘': ['气短', '气促', '呼吸气短', '心悸气短', '气虚', '肺虚', '肾虚'],
                '气促': ['气短', '气喘', '呼吸气短', '心悸气短', '气虚', '肺虚'],
                '呼吸气短': ['气短', '气喘', '气促', '心悸气短', '肺虚', '肾虚'],
                '心悸气短': ['气短', '气喘', '气促', '呼吸气短', '心虚', '气虚'],
                '补血': ['血虚萎黄', '血瘀', '血瘀疼痛', '贫血', '肝血不足', '心血不足'],
                '养血': ['血虚萎黄', '血瘀', '血瘀疼痛', '贫血', '肝血不足', '心血不足'],
                '血虚': ['血虚萎黄', '血瘀', '血瘀疼痛', '贫血', '肝血不足', '心血不足'],
                '血瘀': ['血瘀疼痛', '血瘀肿痛', '瘀血性头痛', '瘀血疼痛', '瘀血肿痛', '肝郁气滞'],
                '血虚萎黄': ['血瘀', '血瘀疼痛', '贫血', '肝血不足', '心血不足', '脾虚'],
                '血瘀疼痛': ['血瘀', '血瘀肿痛', '瘀血性头痛', '瘀血疼痛', '肝郁气滞'],
                '瘀血性头痛': ['血瘀', '血瘀疼痛', '瘀血疼痛', '肝郁气滞', '肝阳上亢'],
                '瘀血疼痛': ['血瘀', '血瘀疼痛', '瘀血性头痛', '肝郁气滞'],
                '瘀血肿痛': ['血瘀', '血瘀疼痛', '瘀血性头痛', '肝郁气滞'],
                '贫血': ['血虚萎黄', '血瘀', '肝血不足', '心血不足', '脾虚'],
                '气血不足': ['乏力', '乏力倦怠', '体倦乏力', '体虚乏力', '脾虚', '肾虚'],
                '气血两虚': ['乏力', '乏力倦怠', '体倦乏力', '体虚乏力', '脾虚', '肾虚'],
                
                # 🎯 疏肝理气相关语义群（基于实际症状数据）
                '疏肝': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '肝郁气滞', '肝气郁结'],
                '理气': ['肝郁胁胀', '胁痛', '胁肋疼痛', '嗳气', '嗳气反酸', '气滞'],
                '疏肝理气': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '肝郁气滞', '肝气郁结'],
                '肝郁': ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '肝郁气滞', '肝气郁结'],
                '肝郁胁胀': ['胁痛', '胁肋疼痛', '胁肋痛', '肝郁气滞', '肝气郁结'],
                '胁痛': ['胁肋疼痛', '胁肋痛', '胁肋胀痛', '肝郁气滞', '肝气郁结'],
                '胁肋疼痛': ['胁痛', '胁肋痛', '胁肋胀痛', '肝郁气滞', '肝气郁结'],
                '胁肋痛': ['胁痛', '胁肋疼痛', '胁肋胀痛', '肝郁气滞', '肝气郁结'],
                '胁肋胀痛': ['胁痛', '胁肋疼痛', '胁肋痛', '肝郁气滞', '肝气郁结'],
                '胁肋隐痛': ['胁痛', '胁肋疼痛', '胁肋痛', '肝郁气滞', '肝气郁结'],
                '两胁胀痛': ['胁痛', '胁肋疼痛', '胁肋痛', '肝郁气滞', '肝气郁结'],
                '肝区不适': ['肝郁胁胀', '胁痛', '胁肋疼痛', '肝郁气滞', '肝气郁结'],
                '肝阳上亢': ['肝郁胁胀', '胁痛', '头痛', '头晕', '肝火旺'],
                '嗳气': ['嗳气反酸', '嗳气频作', '嗳气频繁', '胃气上逆', '肝胃不和'],
                '嗳气反酸': ['嗳气', '嗳气频作', '嗳气频繁', '胃气上逆', '肝胃不和'],
                '嗳气频作': ['嗳气', '嗳气反酸', '嗳气频繁', '胃气上逆', '肝胃不和'],
                '嗳气频繁': ['嗳气', '嗳气反酸', '嗳气频作', '胃气上逆', '肝胃不和'],
                '噫气': ['嗳气', '嗳气反酸', '胃气上逆', '肝胃不和'],
                
                # 🎯 增肌相关语义群（基于实际症状数据）
                '增肌': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '脾虚', '肾虚', '气血不足'],
                '强肌': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '脾虚', '肾虚', '气血不足'],
                '健肌': ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '脾虚', '肾虚', '气血不足'],
                '肌肉萎缩': ['肌肉消瘦', '肌肉无力', '脾虚', '肾虚', '气血不足', '营养不良'],
                '肌肉消瘦': ['肌肉萎缩', '肌肉无力', '脾虚', '肾虚', '气血不足', '营养不良'],
                '肌肉无力': ['肌肉萎缩', '肌肉消瘦', '脾虚', '肾虚', '气血不足', '营养不良'],
                '肌肉僵硬': ['肌肉紧张', '肌肉痉挛', '气滞血瘀', '寒湿痹阻'],
                '肌肉紧张': ['肌肉僵硬', '肌肉痉挛', '气滞血瘀', '寒湿痹阻'],
                '肌肉痉挛': ['肌肉僵硬', '肌肉紧张', '气滞血瘀', '寒湿痹阻', '血虚风动'],
                '肌肉酸痛': ['肌肉僵硬', '肌肉紧张', '气滞血瘀', '寒湿痹阻'],
                '肌肉麻木': ['肌肉僵硬', '肌肉紧张', '气滞血瘀', '血虚风动'],
                '四肢无力': ['肌肉萎缩', '肌肉消瘦', '脾虚', '肾虚', '气血不足'],
                '四肢乏力': ['肌肉萎缩', '肌肉消瘦', '脾虚', '肾虚', '气血不足'],
                '营养不良': ['肌肉萎缩', '肌肉消瘦', '脾虚', '胃虚', '消化不良'],
                
                # 🎯 疲劳虚弱相关语义群（基于实际症状数据）
                '乏力': ['乏力倦怠', '体倦乏力', '体虚乏力', '倦怠乏力', '气虚', '血虚'],
                '乏力倦怠': ['乏力', '体倦乏力', '体虚乏力', '倦怠乏力', '气虚', '血虚'],
                '体倦乏力': ['乏力', '乏力倦怠', '体虚乏力', '倦怠乏力', '气虚', '脾虚'],
                '体虚乏力': ['乏力', '乏力倦怠', '体倦乏力', '倦怠乏力', '气虚', '肾虚'],
                '倦怠乏力': ['乏力', '乏力倦怠', '体倦乏力', '体虚乏力', '气虚', '脾虚'],
                '慢性疲劳': ['乏力', '乏力倦怠', '体倦乏力', '体虚乏力', '气虚', '肾虚'],
                '五劳虚损': ['乏力', '体虚乏力', '气虚', '血虚', '肾虚', '脾虚'],
                
                # 疼痛语义群
                '头痛': ['头晕', '颈椎病', '高血压', '肝阳上亢', '血瘀'],
                '腰痛': ['肾虚', '腰椎', '久坐', '劳损', '寒湿'],
                '胃痛': ['胃胀', '消化不良', '胃酸', '脾胃虚寒', '肝胃不和'],
                '关节痛': ['风湿', '类风湿', '骨关节炎', '寒湿痹阻', '肝肾不足'],
                
                # 消化语义群
                '腹泻': ['脾虚', '湿热', '食积', '肠胃炎', '消化不良'],
                '便秘': ['肠燥', '气滞', '血虚', '阴虚', '脾气虚'],
                '胃胀': ['消化不良', '脾胃虚弱', '食积', '肝胃不和', '胃动力不足'],
                '恶心': ['胃气上逆', '肝胃不和', '痰湿', '脾胃虚弱', '妊娠反应'],
                
                # 睡眠语义群
                '失眠': ['心神不宁', '肝郁', '心肾不交', '痰热', '血虚'],
                '多梦': ['心火旺', '肝郁化火', '心肾不交', '痰热', '血虚'],
                '早醒': ['肝郁', '心肾不交', '阴虚火旺', '神经衰弱', '抑郁'],
                
                # 疲劳语义群
                '疲劳': ['气虚', '血虚', '脾虚', '肾虚', '慢性疲劳综合征'],
                '乏力': ['气虚', '阳虚', '脾气虚', '肾气不足', '营养不良'],
                '虚弱': ['气血两虚', '脾肾阳虚', '久病体虚', '营养不良', '免疫力低'],
                
                # 情绪语义群
                '焦虑': ['心神不宁', '肝郁', '心火旺', '痰热', '神经症'],
                '抑郁': ['肝郁气滞', '心神失养', '血虚', '痰蒙心窍', '情志失调'],
                '烦躁': ['肝火旺', '心火上炎', '阴虚火旺', '痰热', '更年期综合征'],
                
                # 呼吸语义群
                '咳嗽': ['肺热', '肺燥', '痰湿', '外感', '慢性支气管炎'],
                '气短': ['肺气虚', '心气虚', '肾不纳气', '心肺功能不全', '贫血'],
                '胸闷': ['心气虚', '肺气郁滞', '痰湿', '心血瘀阻', '焦虑'],
                
                # 皮肤语义群
                '瘙痒': ['血虚风燥', '湿热', '过敏', '皮肤干燥', '肝胆湿热'],
                '湿疹': ['湿热', '脾虚湿盛', '血虚风燥', '过敏体质', '免疫异常'],
                '痤疮': ['湿热', '血热', '肺胃热盛', '内分泌失调', '青春期'],
                
                # 妇科语义群
                '月经不调': ['肝郁', '血虚', '肾虚', '气滞血瘀', '内分泌失调'],
                '痛经': ['气滞血瘀', '寒凝胞宫', '肝郁气滞', '子宫内膜异位', '宫寒'],
                '白带异常': ['湿热下注', '脾虚湿盛', '肝郁化火', '阴道炎', '宫颈炎'],
                
                # 男科语义群
                '阳痿': ['肾阳虚', '心理因素', '血瘀', '湿热', '肝郁'],
                '早泄': ['肾虚', '心理紧张', '湿热', '肝郁', '敏感性过高'],
                '遗精': ['肾虚', '心肾不交', '湿热', '相火妄动', '精关不固'],
                
                # 五官语义群
                '眼干': ['肝血不足', '肾阴虚', '干眼症', '用眼过度', '更年期'],
                '耳鸣': ['肾虚', '肝火上炎', '痰湿', '血瘀', '神经性耳鸣'],
                '鼻塞': ['肺气不宣', '风寒', '鼻炎', '过敏', '鼻窦炎'],
                '口干': ['阴虚', '胃热', '糖尿病', '药物副作用', '更年期'],
                
                # 循环语义群
                '心悸': ['心气虚', '心血不足', '心火旺', '甲亢', '贫血'],
                '胸痛': ['心血瘀阻', '心气虚', '肺气郁滞', '心绞痛', '肋间神经痛'],
                '手脚冰凉': ['阳虚', '血虚', '气滞血瘀', '末梢循环不良', '贫血'],
                
                # 泌尿语义群
                '尿频': ['肾气不固', '膀胱湿热', '前列腺增生', '泌尿系感染', '糖尿病'],
                '尿急': ['膀胱湿热', '肾气不固', '膀胱炎', '前列腺炎', '神经性膀胱'],
                '尿痛': ['膀胱湿热', '泌尿系感染', '结石', '前列腺炎', '尿道炎'],
                
                # 神经语义群
                '头晕': ['肝阳上亢', '痰湿', '血虚', '颈椎病', '低血压'],
                '健忘': ['心肾不交', '血虚', '痰蒙心窍', '脑供血不足', '老年痴呆'],
                '麻木': ['血虚', '气滞血瘀', '痰湿', '神经病变', '颈椎病'],
            }
            
            # 🎯 应用语义关联规则
            if keyword in semantic_rules:
                semantic_keywords.extend(semantic_rules[keyword])
            
            # 🎯 反向语义关联（从语义词反推原词）
            for base_word, related_words in semantic_rules.items():
                if keyword in related_words:
                    semantic_keywords.append(base_word)
            
            # 🎯 去重并限制数量
            semantic_keywords = list(set(semantic_keywords))[:4]  # 最多4个语义关键词
            
            if semantic_keywords:
                print(f"[DEBUG] 语义关键词扩展: '{keyword}' -> {semantic_keywords}")
            
            return semantic_keywords
            
        except Exception as e:
            print(f"[ERROR] 语义关键词扩展失败: {str(e)}")
            return []
    
    def _find_partial_matches(self, keyword: str) -> List[str]:
        """
        查找部分匹配的症状词汇 - 优化版本
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            List[str]: 部分匹配的症状列表
        """
        try:
            partial_matches = []
            
            # 🎯 常见部分匹配规则（优先级最高）- 更新版本
            common_partial_rules = {
                '头': ['头痛', '头晕', '头胀', '头重', '头昏'],
                '胸': ['胸闷', '胸痛', '胸胀'],
                '腰': ['腰痛', '腰酸', '腰胀'],
                '肚': ['腹痛', '腹胀', '腹泻'],
                '眼': ['眼痛', '眼干', '眼花', '眼胀'],
                '咳': ['咳嗽', '咳痰', '干咳'],
                '痛': ['头痛', '腰痛', '胃痛', '腹痛'],
                '疼': ['头疼', '腰疼', '胃疼', '肚子疼'],
                '胀': ['腹胀', '胃胀', '胸胀', '头胀'],
                '痒': ['皮肤瘙痒', '外阴瘙痒', '肛门瘙痒'],
                '汗': ['多汗', '盗汗', '自汗'],
                '热': ['发热', '潮热', '五心烦热'],
                '冷': ['怕冷', '手脚冰凉', '恶寒'],
                '累': ['疲劳', '乏力', '倦怠'],
                '晕': ['头晕', '眩晕', '晕厥'],
                '心': ['心悸', '心慌', '心烦'],
                '肝': ['肝气郁结', '肝火旺盛', '肝血不足'],
                '脾': ['脾虚', '脾气虚', '脾胃虚弱'],
                '肺': ['肺气虚', '肺热', '肺燥'],
                '肾': ['肾虚', '肾阳虚', '肾阴虚'],
                '气': ['气虚', '气滞', '气短'],
                '血': ['血虚', '血瘀', '血热'],
                '痰': ['痰多', '痰湿', '痰热'],
                '湿': ['湿热', '湿气重', '湿邪'],
                '肥': ['肥胖', '肥满', '体重增加'],
                '胖': ['肥胖', '体重增加', '发胖'],
                '瘦': ['消瘦', '体重减少', '形体羸瘦'],
                '减': ['减肥', '体重减少', '消瘦'],
                '增': ['增肥', '体重增加', '肥胖', '长胖'],  # 🔥 新增增肥相关
                '重': ['体重增加', '体重减少', '体重异常'],
                '长': ['长胖', '增肥', '体重增加', '发胖'],  # 🔥 新增长胖相关
                '发': ['发胖', '增肥', '体重增加', '长胖'],  # 🔥 新增发胖相关
                '变': ['变胖', '变瘦', '体重变化', '体型改变'],  # 🔥 新增变化相关
                '上': ['体重上升', '增肥', '长胖', '发胖'],  # 🔥 新增上升相关
                '升': ['体重上升', '增肥', '长胖', '发胖'],  # 🔥 新增上升相关
                '下': ['体重下降', '减肥', '消瘦', '瘦了'],  # 🔥 新增下降相关
                '降': ['体重下降', '减肥', '消瘦', '瘦了'],  # 🔥 新增下降相关
            }
            
            # 🎯 应用部分匹配规则
            if keyword in common_partial_rules:
                partial_matches.extend(common_partial_rules[keyword])
            
            # 🎯 在同义词词典中查找包含关键词的项（作为补充）
            if len(partial_matches) < 2:
                for original, standard in self.synonym_map.items():
                    # 检查关键词是否包含在原词或标准词中
                    if keyword in original and keyword != original and len(original) <= 6:
                        partial_matches.append(standard)
                    elif keyword in standard and keyword != standard and len(standard) <= 6:
                        partial_matches.append(original)
                    
                    # 避免过多匹配
                    if len(partial_matches) >= 3:
                        break
            
            # 🎯 去重并限制数量
            partial_matches = list(set(partial_matches))[:3]  # 最多3个部分匹配
            
            if partial_matches:
                print(f"[DEBUG] 部分匹配: '{keyword}' -> {partial_matches}")
            
            return partial_matches
            
        except Exception as e:
            print(f"[ERROR] 部分匹配查找失败: {str(e)}")
            return []
    
    def ensure_category_diversity(self, recommendations: List[Dict[str, Any]], limit: int) -> List[Dict[str, Any]]:
        """
        确保多类别搜索结果的多样性 - 智能平衡各类别推荐

        Args:
            recommendations: 原始推荐列表（已按评分排序）
            limit: 结果数量限制

        Returns:
            List[Dict]: 多样化后的推荐列表
        """
        try:
            if not recommendations or limit <= 0:
                return []

            print(f"[DEBUG] 🎯 开始多类别多样性处理，原始推荐数: {len(recommendations)}, 限制数: {limit}")

            # 🎯 第一步：收集所有类别信息，并按质量排序
            categories_map = {}
            for rec in recommendations:
                category = rec.get('classification_name', '其他类别')
                if category not in categories_map:
                    categories_map[category] = []
                categories_map[category].append(rec)

            print(f"[DEBUG] 🎯 发现类别数: {len(categories_map)}, 类别: {list(categories_map.keys())}")

            # 🎯 第二步：智能分配每个类别的推荐数量
            diverse_results = []

            if len(categories_map) == 1:
                # 只有一个类别时，直接返回最佳推荐
                single_category = list(categories_map.keys())[0]
                print(f"[DEBUG] 🎯 只有一个类别: {single_category}，返回该类别的最佳推荐")
                return recommendations[:limit]

            # 🎯 多类别情况：确保类别平衡
            # 计算每个类别应该分配的推荐数量
            num_categories = len(categories_map)
            base_per_category = max(1, limit // num_categories)  # 每个类别至少1个
            remaining_slots = limit

            # 🎯 按类别质量排序（基于该类别最佳推荐的评分）
            category_quality = []
            for category, category_recs in categories_map.items():
                best_score = category_recs[0].get('final_score', category_recs[0].get('match_score', 0))
                category_size = len(category_recs)
                category_quality.append((category, best_score, category_size, category_recs))

            # 按质量排序，优质类别优先
            category_quality.sort(key=lambda x: x[1], reverse=True)

            # 🎯 第一轮：每个类别至少分配一个推荐
            used_ids = set()
            for category, best_score, category_size, category_recs in category_quality:
                if remaining_slots > 0:
                    best_rec = category_recs[0]
                    best_rec['diversity_reason'] = f"类别 {category} 的最佳推荐"
                    diverse_results.append(best_rec)
                    used_ids.add(best_rec['id'])
                    remaining_slots -= 1
                    print(f"[DEBUG] 🎯 类别 {category} 选择最佳推荐: {best_rec['name']} (评分: {best_score:.3f})")

            # 🎯 第二轮：按质量分配剩余位置，优先给高质量类别更多推荐
            round_num = 2
            while remaining_slots > 0 and round_num <= 5:  # 最多5轮，避免无限循环
                added_this_round = 0

                for category, best_score, category_size, category_recs in category_quality:
                    if remaining_slots <= 0:
                        break

                    # 找到该类别中未使用的最佳推荐
                    for rec in category_recs:
                        if rec['id'] not in used_ids:
                            rec['diversity_reason'] = f"类别 {category} 第{round_num}轮补充"
                            diverse_results.append(rec)
                            used_ids.add(rec['id'])
                            remaining_slots -= 1
                            added_this_round += 1
                            print(f"[DEBUG] 🎯 第{round_num}轮补充: {rec['name']} (类别: {category})")
                            break

                if added_this_round == 0:
                    # 没有更多推荐可添加，退出循环
                    break
                round_num += 1

            print(f"[DEBUG] 🎯 多类别处理完成，最终推荐数: {len(diverse_results)}")
            print(f"[DEBUG] 🎯 最终类别分布:")
            final_category_count = {}
            for rec in diverse_results:
                cat = rec.get('classification_name', '其他')
                final_category_count[cat] = final_category_count.get(cat, 0) + 1
            for cat, count in final_category_count.items():
                print(f"[DEBUG] 🎯   - {cat}: {count} 个推荐")

            return diverse_results
                
        except Exception as e:
            print(f"[ERROR] 多类别多样性处理失败: {str(e)}")
            # 出错时返回原始推荐的前N个
            return recommendations[:limit]
    
    def get_category_distribution_stats(self, recommendations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取类别分布统计信息
        
        Args:
            recommendations: 推荐列表
            
        Returns:
            Dict: 类别分布统计
        """
        try:
            if not recommendations:
                return {}
            
            category_stats = {}
            total_count = len(recommendations)
            
            for rec in recommendations:
                category = rec.get('classification_name', '其他类别')
                if category not in category_stats:
                    category_stats[category] = {
                        'count': 0,
                        'avg_score': 0.0,
                        'best_score': 0.0,
                        'recommendations': []
                    }
                
                category_stats[category]['count'] += 1
                category_stats[category]['recommendations'].append(rec)
                
                # 更新最佳评分
                current_score = rec.get('final_score', 0.0)
                if current_score > category_stats[category]['best_score']:
                    category_stats[category]['best_score'] = current_score
            
            # 计算平均分和百分比
            for category, stats in category_stats.items():
                scores = [rec.get('final_score', 0.0) for rec in stats['recommendations']]
                stats['avg_score'] = sum(scores) / len(scores) if scores else 0.0
                stats['percentage'] = (stats['count'] / total_count) * 100
                # 移除recommendations字段，避免返回过多数据
                stats.pop('recommendations', None)
            
            return {
                'total_categories': len(category_stats),
                'total_recommendations': total_count,
                'category_distribution': category_stats
            }
            
        except Exception as e:
            print(f"[ERROR] 获取类别分布统计失败: {str(e)}")
            return {}
    
    def get_search_suggestions(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取搜索建议 - 根据用户输入返回相关症状关键词建议（优化版本）
        
        Args:
            query: 用户输入的搜索词
            limit: 返回建议数量限制
            
        Returns:
            List[Dict]: 搜索建议列表，包含关键词和匹配类型
        """
        try:
            query = query.strip()
            if not query:
                return []
            
            suggestions = []
            
            # 🎯 第一步：使用优化的关键词搜索逻辑获取扩展关键词
            expanded_keywords = self.optimize_keyword_search(query)
            print(f"[DEBUG] 🔍 搜索建议关键词扩展: '{query}' -> {expanded_keywords}")
            
            # 🎯 第二步：为每个扩展关键词生成建议
            for keyword in expanded_keywords:
                if keyword == query:
                    # 原始关键词，直接添加
                    suggestions.append({
                        'keyword': keyword,
                        'display_text': keyword,
                        'match_type': 'original',
                        'category': self._get_symptom_category(keyword)
                    })
                else:
                    # 扩展关键词，显示转换关系
                    suggestions.append({
                        'keyword': keyword,
                        'display_text': f"{query} → {keyword}",
                        'match_type': 'expanded',
                        'category': self._get_symptom_category(keyword)
                    })
            
            # 🎯 第三步：精确匹配同义词词典
            for original, standard in self.synonym_map.items():
                if query == original:
                    suggestions.append({
                        'keyword': standard,
                        'display_text': f"{original} → {standard}",
                        'match_type': 'exact',
                        'category': self._get_symptom_category(original)
                    })
                elif query == standard:
                    suggestions.append({
                        'keyword': original,
                        'display_text': f"{standard} → {original}",
                        'match_type': 'exact',
                        'category': self._get_symptom_category(original)
                    })
            
            # 🎯 第四步：前缀匹配（如果建议数量不足）
            if len(suggestions) < limit:
                for original, standard in self.synonym_map.items():
                    if original.startswith(query) and query != original:
                        suggestions.append({
                            'keyword': standard,
                            'display_text': f"{original} → {standard}",
                            'match_type': 'prefix',
                            'category': self._get_symptom_category(original)
                        })
                    elif standard.startswith(query) and query != standard:
                        suggestions.append({
                            'keyword': original,
                            'display_text': f"{standard} → {original}",
                            'match_type': 'prefix',
                            'category': self._get_symptom_category(original)
                        })
            
            # 🎯 第五步：包含匹配（如果建议数量还不足）
            if len(suggestions) < limit:
                for original, standard in self.synonym_map.items():
                    if query in original and query != original and len(original) <= 8:
                        suggestions.append({
                            'keyword': standard,
                            'display_text': f"{original} → {standard}",
                            'match_type': 'contains',
                            'category': self._get_symptom_category(original)
                        })
                    elif query in standard and query != standard and len(standard) <= 8:
                        suggestions.append({
                            'keyword': original,
                            'display_text': f"{standard} → {original}",
                            'match_type': 'contains',
                            'category': self._get_symptom_category(original)
                        })
            
            # 🎯 第六步：动态关键词生成建议（如果建议数量还不足）
            if len(suggestions) < limit:
                dynamic_keywords = self._generate_dynamic_keywords(query)
                for keyword in dynamic_keywords:
                    if keyword not in [s['keyword'] for s in suggestions]:  # 避免重复
                        suggestions.append({
                            'keyword': keyword,
                            'display_text': f"{query} 相关 → {keyword}",
                            'match_type': 'dynamic',
                            'category': self._get_symptom_category(keyword)
                        })
            
            # 🎯 第七步：语义相关词建议（如果建议数量还不足）
            if len(suggestions) < limit:
                semantic_keywords = self._find_semantic_related_keywords(query)
                for keyword in semantic_keywords:
                    if keyword not in [s['keyword'] for s in suggestions]:  # 避免重复
                        suggestions.append({
                            'keyword': keyword,
                            'display_text': f"{query} 语义相关 → {keyword}",
                            'match_type': 'semantic',
                            'category': self._get_symptom_category(keyword)
                        })
            
            # 🎯 第八步：部分匹配建议（如果建议数量还不足）
            if len(suggestions) < limit:
                partial_matches = self._find_partial_matches(query)
                for match in partial_matches:
                    if match not in [s['keyword'] for s in suggestions]:  # 避免重复
                        suggestions.append({
                            'keyword': match,
                            'display_text': f"{query} 部分匹配 → {match}",
                            'match_type': 'partial',
                            'category': self._get_symptom_category(match)
                        })
            
            # 🎯 第九步：去重并排序
            seen = set()
            unique_suggestions = []
            for suggestion in suggestions:
                key = (suggestion['keyword'], suggestion['match_type'])
                if key not in seen:
                    seen.add(key)
                    unique_suggestions.append(suggestion)
            
            # 🎯 按匹配类型排序：原始 > 扩展 > 精确 > 前缀 > 包含 > 动态 > 语义 > 部分
            match_type_priority = {
                'original': 1, 'expanded': 2, 'exact': 3, 'prefix': 4, 
                'contains': 5, 'dynamic': 6, 'semantic': 7, 'partial': 8
            }
            unique_suggestions.sort(key=lambda x: match_type_priority.get(x['match_type'], 9))
            
            # 🎯 记录搜索建议结果
            if unique_suggestions:
                print(f"[DEBUG] 🔍 搜索建议生成完成: '{query}' -> {len(unique_suggestions)} 个建议")
                for i, suggestion in enumerate(unique_suggestions[:5]):  # 只打印前5个
                    print(f"[DEBUG] 🔍 建议 {i+1}: {suggestion['display_text']} ({suggestion['match_type']})")
            else:
                print(f"[DEBUG] 🔍 搜索建议: '{query}' -> 无建议")
            
            return unique_suggestions[:limit]
            
        except Exception as e:
            print(f"[ERROR] 获取搜索建议失败: {str(e)}")
            return []
    
    def _get_symptom_category(self, symptom: str) -> str:
        """
        获取症状所属分类
        
        Args:
            symptom: 症状名称
            
        Returns:
            str: 症状分类名称
        """
        try:
            # 从同义词词典的分类结构中查找
            for category, synonyms in self.synonym_map.items():
                if isinstance(synonyms, dict):
                    if symptom in synonyms or symptom in synonyms.values():
                        return category
            
            # 基于症状特征推断分类
            if any(char in symptom for char in ['头', '脑', '额', '颈', '项']):
                return '头部症状'
            elif any(char in symptom for char in ['胸', '心', '肺', '咳', '喘', '气']):
                return '呼吸症状'
            elif any(char in symptom for char in ['胃', '腹', '肚', '肠', '便', '泻']):
                return '消化症状'
            elif any(char in symptom for char in ['腰', '背', '肩', '膝', '关节']):
                return '疼痛症状'
            elif any(char in symptom for char in ['眼', '耳', '鼻', '口', '舌', '牙']):
                return '五官症状'
            elif any(char in symptom for char in ['皮', '肤', '痒', '疹', '斑']):
                return '皮肤症状'
            elif any(char in symptom for char in ['累', '疲', '乏', '倦', '虚']):
                return '疲劳症状'
            elif any(char in symptom for char in ['睡', '眠', '梦', '醒']):
                return '睡眠症状'
            elif any(char in symptom for char in ['烦', '躁', '焦', '郁', '怒']):
                return '情绪症状'
            elif any(char in symptom for char in ['热', '冷', '汗', '寒']):
                return '发热症状'
            elif any(char in symptom for char in ['肥', '胖', '瘦', '减']):
                return '体重症状'
            elif any(char in symptom for char in ['重', '体重']):
                return '体重症状'
            elif any(word in symptom for word in ['皮肤干燥', '皮肤松弛', '皮肤油腻', '皮肤粗糙', '皮肤瘙痒', '皮肤白斑']):
                return '美容症状'
            elif any(word in symptom for word in ['面部色斑', '黄褐斑', '面部油腻', '面部痤疮', '面色晦暗', '面部浮肿']):
                return '美容症状'
            elif any(word in symptom for word in ['痤疮', '痤疮疖肿', '痤疮红肿', '毛孔粗大']):
                return '美容症状'
            elif any(word in symptom for word in ['脱发', '头发稀疏', '头发早白', '白发', '须发早白', '头皮瘙痒']):
                return '美容症状'
            elif any(word in symptom for word in ['指甲易裂', '指甲色淡', '指甲苍白']):
                return '美容症状'
            elif any(word in symptom for word in ['眼袋', '眼睑浮肿', '面目浮肿', '面浮肿']):
                return '美容症状'
            elif any(word in symptom for word in ['肥胖', '体重异常', '体重增加']):
                return '美容症状'
            elif any(word in symptom for word in ['脱屑', '鳞屑脱落', '色素脱失']):
                return '美容症状'
            elif any(char in symptom for char in ['美', '护', '养', '润']):
                return '美容症状'
            elif any(word in symptom for word in ['气短', '气喘', '气促', '呼吸气短', '心悸气短', '嗳气', '嗳气反酸', '嗳气频作', '噫气']):
                return '气血症状'
            elif any(word in symptom for word in ['血虚萎黄', '血瘀', '血瘀疼痛', '瘀血性头痛', '瘀血疼痛', '瘀血肿痛', '贫血', '轻度血瘀']):
                return '气血症状'
            elif any(word in symptom for word in ['肝郁胁胀', '胁痛', '胁肋疼痛', '胁肋痛', '胁肋胀痛', '胁肋隐痛', '两胁胀痛', '肝区不适', '肝阳上亢']):
                return '肝气症状'
            elif any(word in symptom for word in ['肌肉萎缩', '肌肉消瘦', '肌肉无力', '肌肉僵硬', '肌肉紧张', '肌肉痉挛', '肌肉酸痛', '肌肉麻木']):
                return '肌肉症状'
            elif any(word in symptom for word in ['乏力', '乏力倦怠', '体倦乏力', '体虚乏力', '倦怠乏力', '慢性疲劳', '五劳虚损']):
                return '疲劳症状'
            elif any(word in symptom for word in ['四肢无力', '四肢乏力', '营养不良']):
                return '虚弱症状'
            else:
                return '其他症状'
                
        except Exception as e:
            print(f"[ERROR] 获取症状分类失败: {str(e)}")
            return '其他症状'
    
    def calculate_symptom_match_score(self, keyword: str, therapy_data: Dict[str, Any]) -> float:
        """
        计算症状匹配度评分 - 优化版本，精确匹配优先
        
        Args:
            keyword: 搜索关键词
            therapy_data: 疗法数据
            
        Returns:
            float: 匹配度评分 (0-1之间)
        """
        try:
            score = 0.0
            keyword_lower = keyword.lower().strip()
            
            # 🎯 检查疗法名称匹配 (权重: 0.4)
            name = therapy_data.get('name', '').lower().strip()
            name_score = self._calculate_text_match_score(keyword_lower, name)
            score += 0.4 * name_score
            
            # 🎯 检查描述匹配 (权重: 0.3)
            description = therapy_data.get('description', '').lower().strip()
            description_score = self._calculate_text_match_score(keyword_lower, description)
            score += 0.3 * description_score
            
            # 🎯 检查相关症状匹配 (权重: 0.3，仅用户疗法有此字段)
            related_symptoms = therapy_data.get('related_symptoms', '').lower().strip()
            if related_symptoms:
                symptoms_score = self._calculate_symptoms_match_score(keyword_lower, related_symptoms)
                score += 0.3 * symptoms_score
            
            return min(score, 1.0)  # 确保不超过1.0
            
        except Exception as e:
            print(f"[ERROR] 计算症状匹配度失败: {str(e)}")
            return 0.0
    
    def _calculate_text_match_score(self, keyword: str, text: str) -> float:
        """
        计算文本匹配度评分 - 精确匹配优先
        
        Args:
            keyword: 搜索关键词
            text: 目标文本
            
        Returns:
            float: 匹配度评分 (0-1之间)
        """
        try:
            if not keyword or not text:
                return 0.0
            
            # 🎯 第一优先级：完全匹配
            if keyword == text:
                return 1.0
            
            # 🎯 第二优先级：独立词汇匹配（被空格、标点符号分隔）
            import re
            # 使用正则表达式匹配独立词汇
            pattern = r'\b' + re.escape(keyword) + r'\b'
            if re.search(pattern, text):
                # 独立词汇匹配，给予高分
                return 0.9
            
            # 🎯 第三优先级：词汇开头匹配
            if text.startswith(keyword):
                # 关键词匹配长度与文本长度的比值，但给予较高基础分
                return 0.8 * (len(keyword) / len(text)) + 0.2
            
            # 🎯 第四优先级：包含匹配，但要区分精确程度
            if keyword in text:
                # 计算匹配的精确程度
                keyword_len = len(keyword)
                text_len = len(text)
                
                # 如果关键词占文本的比例较高，给予更高分数
                length_ratio = keyword_len / text_len
                
                # 检查是否是连续匹配
                match_count = text.count(keyword)
                
                # 基础包含匹配分数
                base_score = 0.3 * length_ratio + 0.1 * match_count
                
                # 如果关键词长度较短，降低包含匹配的分数（避免短词汇匹配过多）
                if keyword_len <= 2:
                    base_score *= 0.7
                
                return min(base_score, 0.7)  # 包含匹配最高0.7分
            
            return 0.0
            
        except Exception as e:
            print(f"[ERROR] 计算文本匹配度失败: {str(e)}")
            return 0.0
    
    def _calculate_symptoms_match_score(self, keyword: str, symptoms_text: str) -> float:
        """
        计算症状列表匹配度评分 - 精确匹配优先
        
        Args:
            keyword: 搜索关键词
            symptoms_text: 症状文本（逗号分隔）
            
        Returns:
            float: 匹配度评分 (0-1之间)
        """
        try:
            if not keyword or not symptoms_text:
                return 0.0
            
            # 按逗号分隔症状列表
            symptoms_list = [s.strip() for s in symptoms_text.split(',') if s.strip()]
            if not symptoms_list:
                return 0.0
            
            max_score = 0.0
            total_score = 0.0
            
            for symptom in symptoms_list:
                # 计算每个症状的匹配度
                symptom_score = self._calculate_text_match_score(keyword, symptom)
                total_score += symptom_score
                max_score = max(max_score, symptom_score)
            
            # 🎯 使用最高匹配分数作为主要评分，平均分数作为补充
            # 这样可以确保精确匹配的症状获得更高优先级
            final_score = max_score * 0.8 + (total_score / len(symptoms_list)) * 0.2
            
            return min(final_score, 1.0)
            
        except Exception as e:
            print(f"[ERROR] 计算症状匹配度失败: {str(e)}")
            return 0.0
    
    def rank_therapies_by_effectiveness(self, therapies: List[Dict[str, Any]], therapy_type: str) -> List[Dict[str, Any]]:
        """
        按疗法效果排序 - 优化版本
        
        Args:
            therapies: 疗法列表
            therapy_type: 疗法类型 ('system' 或 'user')
            
        Returns:
            List[Dict]: 排序后的疗法列表
        """
        try:
            # 🎯 为每个疗法计算综合评分
            for therapy in therapies:
                base_score = 0.0
                
                if therapy_type == 'system':
                    # 系统疗法评分因子
                    if therapy.get('is_recommended'):
                        base_score += 0.3
                    
                    difficulty = therapy.get('difficulty_level', 1)
                    if difficulty == 1:  # 简单疗法优先
                        base_score += 0.2
                    elif difficulty == 2:
                        base_score += 0.1
                    
                    # 🆕 新增：疗法分类加权
                    classification_id = therapy.get('classification_id')
                    if classification_id:
                        # 某些分类的疗法可能更有效
                        popular_classifications = [1, 2, 3]  # 假设这些是热门分类
                        if classification_id in popular_classifications:
                            base_score += 0.1
                    
                elif therapy_type == 'user':
                    # 用户疗法评分因子
                    if therapy.get('is_verified'):
                        base_score += 0.3
                    
                    usage_count = therapy.get('usage_count', 0)
                    if usage_count > 20:
                        base_score += 0.3
                    elif usage_count > 10:
                        base_score += 0.2
                    elif usage_count > 5:
                        base_score += 0.1
                    
                    # 🆕 新增：疗法分类加权
                    category = therapy.get('category', '')
                    popular_categories = ['moxibustion', 'massage', 'exercise']
                    if category in popular_categories:
                        base_score += 0.1
                
                # 🎯 新创建的疗法给予小幅加分
                if therapy.get('created_at'):
                    from datetime import datetime, timezone
                    created_at = therapy['created_at']
                    if isinstance(created_at, str):
                        created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    
                    days_old = (datetime.now(timezone.utc) - created_at).days
                    if days_old < 30:  # 30天内的新疗法
                        base_score += 0.1
                    elif days_old < 90:  # 90天内的较新疗法
                        base_score += 0.05
                
                # 🆕 新增：疗法名称长度因子（简洁的名称可能更实用）
                name_length = len(therapy.get('name', ''))
                if name_length <= 10:
                    base_score += 0.05
                elif name_length <= 20:
                    base_score += 0.02
                
                # 🆕 新增：持续时间因子（合理的持续时间更受欢迎）
                duration = therapy.get('duration', '')
                if '7天' in duration or '1周' in duration:
                    base_score += 0.05
                elif '30分钟' in duration or '1小时' in duration:
                    base_score += 0.03
                
                therapy['effectiveness_score'] = min(base_score, 1.0)  # 确保不超过1.0
            
            # 🎯 按综合评分排序
            therapies.sort(key=lambda x: x.get('effectiveness_score', 0), reverse=True)
            
            return therapies
            
        except Exception as e:
            print(f"[ERROR] 疗法排序失败: {str(e)}")
            return therapies
    
    def calculate_therapy_popularity_score(self, therapy_data: Dict[str, Any], therapy_type: str) -> float:
        """
        计算疗法流行度评分
        
        Args:
            therapy_data: 疗法数据
            therapy_type: 疗法类型
            
        Returns:
            float: 流行度评分 (0-1之间)
        """
        try:
            score = 0.0
            
            if therapy_type == 'system':
                # 系统疗法流行度因子
                if therapy_data.get('is_recommended'):
                    score += 0.4
                
                # 难度越低，流行度越高
                difficulty = therapy_data.get('difficulty_level', 1)
                score += (4 - difficulty) * 0.1  # 难度1得0.3分，难度3得0.1分
                
            elif therapy_type == 'user':
                # 用户疗法流行度因子
                usage_count = therapy_data.get('usage_count', 0)
                if usage_count > 0:
                    # 使用对数函数避免使用次数过高时评分过大
                    import math
                    score += min(math.log(usage_count + 1) * 0.1, 0.5)
                
                if therapy_data.get('is_verified'):
                    score += 0.3
            
            return min(score, 1.0)
            
        except Exception as e:
            print(f"[ERROR] 计算流行度评分失败: {str(e)}")
            return 0.0
    
    def get_therapy_recommendation_reasons(self, therapy_data: Dict[str, Any], 
                                         match_score: float, 
                                         effectiveness_score: float) -> List[str]:
        """
        生成疗法推荐理由 - 简化版本
        
        Args:
            therapy_data: 疗法数据
            match_score: 匹配度评分
            effectiveness_score: 效果评分
            
        Returns:
            List[str]: 推荐理由列表
        """
        try:
            reasons = []
            
            # 🎯 简化推荐理由生成，减少复杂逻辑
            if match_score >= 0.8:
                reasons.append("高度匹配您的症状")
            elif match_score >= 0.6:
                reasons.append("较好匹配您的症状")
            elif match_score >= 0.4:
                reasons.append("部分匹配您的症状")
            
            if effectiveness_score >= 0.7:
                reasons.append("疗效显著")
            elif effectiveness_score >= 0.5:
                reasons.append("疗效良好")
            
            if therapy_data.get('is_recommended'):
                reasons.append("专家推荐")
            
            if therapy_data.get('difficulty_level', 1) == 1:
                reasons.append("操作简单")
            
            # 🎯 限制推荐理由数量，减少响应数据
            return reasons[:3]  # 最多3个理由
            
        except Exception as e:
            print(f"[ERROR] 生成推荐理由失败: {str(e)}")
            return ["可尝试此疗法"]

# ===============================================
# 🎯 症状搜索API请求和响应Schema
# ===============================================

class SymptomSearchRequestIn(BaseModel):
    """症状搜索请求Schema"""
    keyword: str = Field(..., description="搜索关键词", min_length=1, max_length=50)
    limit: int = Field(25, description="返回结果数量限制", ge=1, le=100)
    include_user_therapies: bool = Field(True, description="是否包含用户疗法")
    sort_by: str = Field("relevance", description="排序方式")
    
    class Config:
        schema_extra = {
            "example": {
                "keyword": "头痛",
                "limit": 25,
                "include_user_therapies": True,
                "sort_by": "relevance"
            }
        }

class TherapyRecommendationItem(BaseModel):
    """疗法推荐项Schema - 优化版本"""
    id: int = Field(..., description="疗法ID")
    name: str = Field(..., description="疗法名称")
    description: str = Field(..., description="疗法描述")
    therapy_type: str = Field(..., description="疗法类型")
    match_score: float = Field(..., description="匹配度评分")
    effectiveness_score: float = Field(..., description="效果评分")
    popularity_score: float = Field(0.0, description="流行度评分")
    classification_name: Optional[str] = Field(None, description="分类名称")
    duration: str = Field(..., description="持续时间")
    difficulty_level: Optional[int] = Field(None, description="难度等级")
    is_recommended: bool = Field(False, description="是否推荐")
    # 移除usage_count字段，减少响应数据
    matched_symptoms: List[str] = Field([], description="匹配的症状")
    recommendation_reasons: List[str] = Field([], description="推荐理由")
    # 移除diversity_reason字段，减少响应数据
    
    class Config:
        schema_extra = {
            "example": {
                "id": 101847,
                "name": "月信节律观察记录",
                "description": "观察女性月经周期的规律性...",
                "therapy_type": "user",
                "match_score": 0.85,
                "effectiveness_score": 0.7,
                "popularity_score": 0.6,
                "classification_name": "时间节律类",
                "duration": "7天",
                "difficulty_level": 1,
                "is_recommended": True,
                "matched_symptoms": ["经期提前", "经期延后"],
                "recommendation_reasons": ["高度匹配您的症状", "疗效显著", "操作简单"]
            }
        }

class SymptomSearchResponseOut(BaseModel):
    """症状搜索响应Schema - 优化版本"""
    keyword: str = Field(..., description="搜索关键词")
    total_count: int = Field(..., description="总结果数量")
    system_therapy_count: int = Field(..., description="系统疗法数量")
    user_therapy_count: int = Field(..., description="用户疗法数量")
    search_time_ms: float = Field(..., description="搜索耗时（毫秒）")
    recommendations: List[TherapyRecommendationItem] = Field([], description="推荐疗法列表")
    # 移除category_diversity_enabled和category_stats字段，减少响应数据
    
    class Config:
        schema_extra = {
            "example": {
                "keyword": "头痛",
                "total_count": 15,
                "system_therapy_count": 8,
                "user_therapy_count": 7,
                "search_time_ms": 45.2,
                "recommendations": [
                    {
                        "id": 101847,
                        "name": "月信节律观察记录",
                        "description": "观察女性月经周期的规律性...",
                        "therapy_type": "user",
                        "match_score": 0.85,
                        "effectiveness_score": 0.7,
                        "popularity_score": 0.6,
                        "classification_name": "时间节律类",
                        "duration": "7天",
                        "difficulty_level": 1,
                        "is_recommended": True,
                        "matched_symptoms": ["经期提前", "经期延后"],
                        "recommendation_reasons": ["高度匹配您的症状", "疗效显著", "操作简单"]
                    }
                ]
            }
        }

# ===============================================
# 🚀 症状搜索与疗法推荐API端点
# ===============================================

@prognosis_router.post("/symptom-search", response=SymptomSearchResponseOut)
@api_timer("症状搜索与疗法推荐")
@rate_limit("symptom_search_api", normal_limit=30, member_limit=100)
async def search_symptoms_and_recommend_therapies(request, payload: SymptomSearchRequestIn):
    """
    症状搜索与疗法推荐API - 高性能缓存优化版本
    
    【测试URL】: POST /api/routertest1/prognosis/symptom-search
    
    功能特点:
    1. 🔍 根据症状关键词搜索相关疗法
    2. 📊 支持系统疗法和用户疗法搜索
    3. 🎯 按匹配度和效果评分排序
    4. 🚀 三级缓存优化（L1:内存 L2:Redis L3:数据库）
    5. ⏱️ 提供搜索耗时统计
    
    搜索范围:
    - 系统疗法: 疗法名称、描述、使用说明
    - 用户疗法: 疗法名称、描述、相关症状字段
    
    排序策略:
    - 匹配度评分 (0-1): 关键词在各字段中的匹配程度
    - 效果评分 (0-1): 基于推荐状态、难度、使用次数等
    
    请求示例:
    ```json
    {
        "keyword": "头痛",
        "limit": 20,
        "include_user_therapies": true,
        "sort_by": "relevance"
    }
    ```
    """
    try:
        # 🚀 性能优化：简化日志输出，减少字符串操作
        current_user_id = request.user_id
        print(f"[DEBUG] 🔍 症状搜索API - 用户: {current_user_id}, 关键词: {payload.keyword}")
        
        # 🔍 UVICORN vs DJANGO 调试信息
        print(f"[DEBUG] 🌐 请求类型: {type(request)}")
        print(f"[DEBUG] 🔄 请求方法: {request.method}")
        print(f"[DEBUG] 🔄 是否ASGI: {hasattr(request, 'scope')}")
        
        import asyncio
        import threading
        print(f"[DEBUG] 🧵 当前线程: {threading.current_thread().name}")
        
        # 记录开始时间
        import time
        start_debug_time = time.time()
        print(f"[DEBUG] ⏰ API开始时间: {start_debug_time}")
        
        # 🔥 优化缓存键生成 - 使用简单字符串拼接，避免JSON序列化和MD5计算
        cache_key = f"symptom_search:{payload.keyword}:{payload.limit}:{payload.include_user_therapies}:{payload.sort_by}:v1.1"
        
        # 🚀 第一级缓存检查 - 内存缓存（最快）
        memory_cached_data = get_memory_cache(cache_key)
        if memory_cached_data:
            print(f"[CACHE_HIT] ⚡⚡ 症状搜索内存缓存命中，耗时: <1ms")
            # 🔥 快速返回，避免任何额外处理
            return memory_cached_data
        
        # 🚀 第二级缓存检查 - Redis缓存
        try:
            start_cache_time = time.time()
            cached_data = await sync_to_async(cache.get)(cache_key)
            cache_time = (time.time() - start_cache_time) * 1000
            
            if cached_data:
                print(f"[CACHE_HIT] ⚡ 症状搜索Redis缓存命中，缓存查询耗时: {cache_time:.2f}ms")
                result_data = fast_json_loads(cached_data) if isinstance(cached_data, str) else cached_data
                # 同时缓存到内存
                set_memory_cache(cache_key, result_data)
                return result_data
            else:
                print(f"[CACHE_MISS] ❌ 症状搜索缓存未命中，缓存查询耗时: {cache_time:.2f}ms")
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存查询失败: {cache_error}")
        
        # 🔍 获取或初始化搜索引擎 - 使用静态实例避免重复初始化
        print(f"[DEBUG] 🔍 开始获取搜索引擎实例...")
        search_engine_start = time.time()
        search_engine = await get_cached_search_engine()
        search_engine_time = (time.time() - search_engine_start) * 1000
        print(f"[DEBUG] 🔍 搜索引擎获取完成，耗时: {search_engine_time:.2f}ms")
        
        # 🎯 缓存关键词扩展结果
        print(f"[DEBUG] 🎯 开始关键词扩展...")
        keyword_start = time.time()
        search_keywords = await get_cached_keyword_expansion(payload.keyword)
        keyword_time = (time.time() - keyword_start) * 1000
        print(f"[DEBUG] 🎯 关键词扩展完成，耗时: {keyword_time:.2f}ms")
        if len(search_keywords) > 1:
            print(f"[DEBUG] 🎯 关键词扩展: '{payload.keyword}' -> {search_keywords}")
        
        # 🚀 执行高性能搜索
        print(f"[DEBUG] 🚀 开始数据库搜索...")
        db_search_start = time.time()
        search_results = await search_engine.search_symptoms_in_therapies_cached(
            payload.keyword,
            payload.limit
        )
        db_search_time = (time.time() - db_search_start) * 1000
        print(f"[DEBUG] 🚀 数据库搜索完成，耗时: {db_search_time:.2f}ms")
        
        system_therapies = search_results['system_therapies']
        user_therapies = search_results['user_therapies'] if payload.include_user_therapies else []
        search_time_ms = search_results['search_time_ms']
        
        # 🎯 计算匹配度和效果评分
        recommendations = []
        
        # 🚀 预缓存分类名称 - 批量获取并缓存
        classification_ids = [t.get('classification_id') for t in system_therapies if t.get('classification_id')]
        classification_names = await get_cached_classification_names(classification_ids)
        
        # 处理系统疗法
        for therapy in system_therapies:
            match_score = search_engine.calculate_symptom_match_score(payload.keyword, therapy)
            
            # 🚀 从预加载的数据中获取分类名称
            classification_name = therapy.get('classification__name')
            if not classification_name and therapy.get('classification_id'):
                classification_name = classification_names.get(therapy['classification_id'])
            
            # 🎯 大幅压缩描述长度，减少响应数据
            description = therapy['description']
            if len(description) > 50:  # 进一步减少到50字符
                description = description[:50] + '...'
            
            recommendation = {
                'id': therapy['id'],
                'name': therapy['name'],
                'description': description,
                'therapy_type': 'system',
                'match_score': round(match_score, 4),
                'effectiveness_score': 0.0,  # 将在排序时计算
                'popularity_score': 0.0, # 将在排序时计算
                'classification_name': classification_name,
                'duration': therapy.get('duration', '未知'),
                'difficulty_level': therapy.get('difficulty_level', 1),
                'is_recommended': therapy.get('is_recommended', False),
                # 移除usage_count字段，减少响应数据
                # 'usage_count': 0,  # 系统疗法暂不统计使用次数
                'matched_symptoms': search_results.get('search_keywords', [payload.keyword])[:2],  # 进一步限制匹配症状数量
                'recommendation_reasons': [] # 将在排序时生成
            }
            recommendations.append(recommendation)
        
        # 🎯 用户疗法类别代码到中文名称的映射
        category_name_map = {
            'massage': '按摩疗法',
            'moxibustion': '艾灸疗法',
            'food_therapy': '食疗疗法',
            'exercise': '中医功法疗法',
            'tea_therapy': '茶疗疗法',
            'music_therapies': '音乐疗法',
            'xiang_therapies': '香薰疗法',
            'xiangshu_therapies': '象数疗法',
            'ear_acup_therapies': '中医耳穴疗法'
        }

        # 处理用户疗法
        for therapy in user_therapies:
            match_score = search_engine.calculate_symptom_match_score(payload.keyword, therapy)

            # 解析相关症状 - 简化处理
            matched_symptoms = []
            if therapy.get('related_symptoms'):
                symptoms_list = [s.strip() for s in therapy['related_symptoms'].split(',')]
                # 🎯 限制匹配症状数量，减少响应数据
                for kw in search_results.get('search_keywords', [payload.keyword])[:2]:  # 只取前2个关键词
                    matched_symptoms.extend([s for s in symptoms_list if kw.lower() in s.lower()])
                matched_symptoms = list(set(matched_symptoms))[:3]  # 去重并限制数量

            # 🎯 大幅压缩描述长度，减少响应数据
            description = therapy['description']
            if len(description) > 50:  # 进一步减少到50字符
                description = description[:50] + '...'

            # 🎯 将用户疗法的category代码转换为中文名称，确保多样性算法正确工作
            category_code = therapy.get('category', 'other')
            classification_name = category_name_map.get(category_code, f'用户疗法-{category_code}')

            recommendation = {
                'id': therapy['id'] + 100000,  # 用户疗法ID偏移
                'name': f"{therapy['name']}",
                'description': description,
                'therapy_type': 'user',
                'match_score': round(match_score, 4),
                'effectiveness_score': 0.0,  # 将在排序时计算
                'popularity_score': 0.0, # 将在排序时计算
                'classification_name': classification_name,  # 🎯 使用中文名称而不是代码
                'category_code': category_code,  # 🎯 保留原始代码用于调试
                'duration': therapy.get('duration', '自定义'),
                'difficulty_level': 1,  # 用户疗法默认简单
                'is_recommended': therapy.get('is_verified', False),
                'usage_count': therapy.get('usage_count', 0),
                'matched_symptoms': matched_symptoms or search_results.get('search_keywords', [payload.keyword])[:2],
                'recommendation_reasons': [] # 将在排序时生成
            }
            recommendations.append(recommendation)
        
        # 🎯 按匹配度和效果评分排序
        system_recommendations = [r for r in recommendations if r['therapy_type'] == 'system']
        user_recommendations = [r for r in recommendations if r['therapy_type'] == 'user']
        
        # 分别排序并计算流行度评分、生成推荐理由
        system_recommendations = search_engine.rank_therapies_by_effectiveness(system_recommendations, 'system')
        user_recommendations = search_engine.rank_therapies_by_effectiveness(user_recommendations, 'user')
        
        # 🎯 为每个推荐项计算流行度评分和生成推荐理由
        for rec in system_recommendations:
            rec['popularity_score'] = round(
                search_engine.calculate_therapy_popularity_score(rec, 'system'), 4
            )
            rec['recommendation_reasons'] = search_engine.get_therapy_recommendation_reasons(
                rec, rec['match_score'], rec['effectiveness_score']
            )
        
        for rec in user_recommendations:
            rec['popularity_score'] = round(
                search_engine.calculate_therapy_popularity_score(rec, 'user'), 4
            )
            rec['recommendation_reasons'] = search_engine.get_therapy_recommendation_reasons(
                rec, rec['match_score'], rec['effectiveness_score']
            )
        
        # 合并排序 - 按匹配度 + 效果评分 + 流行度的综合得分
        all_recommendations = system_recommendations + user_recommendations
        for rec in all_recommendations:
            # 🎯 提高匹配度权重，确保精确匹配排在前面
            base_score = (
                rec['match_score'] * 0.7 +           # 匹配度权重70%（从50%提高到70%）
                rec['effectiveness_score'] * 0.2 +   # 效果评分权重20%（从30%降低到20%）
                rec['popularity_score'] * 0.1        # 流行度权重10%（从20%降低到10%）
            )
            
            # 🎯 精确匹配额外加分机制
            exact_match_bonus = 0.0
            keyword_lower = payload.keyword.lower().strip()
            
            # 检查疗法名称是否精确匹配
            name_lower = rec['name'].lower().strip()
            if keyword_lower == name_lower:
                exact_match_bonus += 0.3  # 完全匹配加30%
            elif keyword_lower in name_lower:
                # 检查是否是独立词汇匹配
                import re
                pattern = r'\b' + re.escape(keyword_lower) + r'\b'
                if re.search(pattern, name_lower):
                    exact_match_bonus += 0.2  # 独立词汇匹配加20%
            
            # 检查是否在症状列表中有精确匹配
            matched_symptoms = rec.get('matched_symptoms', [])
            for symptom in matched_symptoms:
                if keyword_lower == symptom.lower().strip():
                    exact_match_bonus += 0.15  # 症状精确匹配加15%
                    break
            
            rec['final_score'] = min(base_score + exact_match_bonus, 1.0)  # 确保不超过1.0
        
        all_recommendations.sort(key=lambda x: x['final_score'], reverse=True)
        
        # 🎯 调试信息：显示排序结果
        if all_recommendations:
            print(f"[DEBUG] 🎯 排序结果预览（关键词: {payload.keyword}）:")
            for i, rec in enumerate(all_recommendations[:5]):  # 显示前5个
                print(f"[DEBUG] 🎯 第{i+1}名: {rec['name']} - 匹配度:{rec['match_score']:.3f}, 最终得分:{rec['final_score']:.3f}")
        else:
            print(f"[DEBUG] 🎯 没有找到匹配的推荐结果")
        
        # 🎯 多类别保证逻辑 - 确保每个类别至少有一个结果
        diverse_recommendations = search_engine.ensure_category_diversity(
            all_recommendations, payload.limit
        )
        
        # 移除临时字段
        for rec in diverse_recommendations:
            rec.pop('final_score', None)
        
        # 🚀 获取类别分布统计 - 简化版本
        category_stats = {}  # 简化统计信息，减少计算开销
        
        # 🚀 构建响应 - 优化版本
        result = {
            'keyword': payload.keyword,
            'total_count': len(diverse_recommendations),
            'system_therapy_count': len(system_recommendations),
            'user_therapy_count': len(user_recommendations),
            'search_time_ms': search_time_ms,
            'recommendations': diverse_recommendations[:payload.limit]
            # 移除category_diversity_enabled和category_stats，减少响应数据
        }
        
        # 🔥 缓存结果 - 双层缓存存储（优化版）
        try:
            # 内存缓存（最快访问）- 使用压缩缓存
            set_memory_cache_with_compression(cache_key, result)
            
            # Redis缓存（持久化）- 异步存储，不阻塞响应
            cache_timeout = 3600  # 1小时缓存
            
            # 🚀 异步缓存存储，不阻塞主线程
            async def async_cache_store():
                try:
                    await sync_to_async(cache.set)(cache_key, fast_json_dumps(result), timeout=cache_timeout)
                    print(f"[CACHE_SET] ⚡ 症状搜索结果已双层缓存，缓存时间: {cache_timeout}秒")
                except Exception as e:
                    print(f"[CACHE_ERROR] ❌ Redis缓存存储失败: {e}")
            
            # 启动异步缓存任务，不等待完成
            asyncio.create_task(async_cache_store())
            
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存存储失败: {cache_error}")
        
        # 🔥 性能监控：计算各阶段耗时
        total_processing_time = (time.time() - start_cache_time) * 1000 if 'start_cache_time' in locals() else 0
        response_size = len(fast_json_dumps(result))
        
        print(f"[DEBUG] ✅ 症状搜索API完成，返回 {len(result['recommendations'])} 个推荐")
        print(f"[DEBUG] 📊 响应数据大小: {response_size} 字节")
        print(f"[DEBUG] ⏱️ 总处理时间: {total_processing_time:.2f}ms")
        print(f"[DEBUG] 🚀 预期剩余耗时: Django序列化(~15ms) + HTTP响应(~10ms)")
        
        return result
        
    except HttpError:
        raise
    except Exception as e:
        print(f"[ERROR] 症状搜索API失败: {str(e)}")
        print(f"[ERROR] 错误详情: {traceback.format_exc()}")
        raise HttpError(500, f"症状搜索API失败: {str(e)}")


# ===============================================
# 🔍 搜索建议API - 智能关键词推荐
# ===============================================

@prognosis_router.get("/search-suggestions", response=SearchSuggestionsResponse)
@api_timer("获取搜索建议")
@rate_limit("search_suggestions_api", normal_limit=30, member_limit=100)
async def get_search_suggestions(
    request,
    query: str,
    limit: int = 10
):
    """
    获取搜索建议API - 根据用户输入返回相关症状关键词建议
    
    Args:
        query: 用户输入的搜索词
        limit: 返回建议数量限制 (默认10个，最多20个)
        
    Returns:
        SearchSuggestionsResponse: 搜索建议响应
    """
    try:
        # 🔍 详细的请求日志
        print(f"[DEBUG] ========== 搜索建议API请求 ==========")
        print(f"[DEBUG] 请求方法: {request.method}")
        print(f"[DEBUG] 请求路径: {request.path}")
        print(f"[DEBUG] 用户ID: {getattr(request, 'user_id', 'unknown')}")
        print(f"[DEBUG] 查询参数: query={query}, limit={limit}")
        print(f"[DEBUG] 请求头: {dict(request.headers)}")
        print(f"[DEBUG] =======================================")
        
        # 🎯 参数验证
        if not query or len(query.strip()) == 0:
            raise HttpError(400, "搜索词不能为空")
        
        query = query.strip()
        if len(query) > 50:
            raise HttpError(400, "搜索词长度不能超过50个字符")
        
        # 限制返回数量
        limit = min(max(1, limit), 20)
        
        # 🎯 构建缓存键
        cache_key = f"search_suggestions:{hashlib.md5(f'{query}:{limit}'.encode()).hexdigest()}"
        
        # 🚀 尝试从缓存获取
        try:
            cached_result = await sync_to_async(cache.get)(cache_key)
            if cached_result:
                print(f"[CACHE_HIT] ⚡ 搜索建议缓存命中: {query}")
                return SearchSuggestionsResponse(**json.loads(cached_result))
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存读取失败: {cache_error}")
        
        # 🔍 获取搜索建议
        search_engine = SymptomSearchEngine()
        suggestions = search_engine.get_search_suggestions(query, limit)
        
        # 🎯 构建响应
        suggestion_responses = []
        for suggestion in suggestions:
            suggestion_responses.append(SearchSuggestionResponse(
                keyword=suggestion['keyword'],
                display_text=suggestion['display_text'],
                match_type=suggestion['match_type'],
                category=suggestion['category']
            ))
        
        result = SearchSuggestionsResponse(
            query=query,
            suggestions=suggestion_responses,
            total=len(suggestion_responses)
        )
        
        # 🔥 缓存结果
        try:
            cache_timeout = 1800  # 30分钟缓存，从5分钟增加到30分钟
            await sync_to_async(cache.set)(cache_key, result.json(), timeout=cache_timeout)
            print(f"[CACHE_SET] ⚡ 搜索建议已缓存")
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存存储失败: {cache_error}")
        
        print(f"[DEBUG] ✅ 搜索建议API完成，查询: '{query}'，返回 {len(suggestion_responses)} 个建议")
        return result
        
    except HttpError:
        raise
    except Exception as e:
        print(f"[ERROR] 搜索建议API失败: {str(e)}")
        print(f"[ERROR] 错误详情: {traceback.format_exc()}")
        raise HttpError(500, f"搜索建议获取失败: {str(e)}")

# 🚀 新增：为了兼容前端可能的错误请求路径，添加一个重定向端点
@prognosis_router.post("/search", response=SymptomSearchResponseOut)
@api_timer("症状搜索与疗法推荐-兼容路径")
@rate_limit("symptom_search_api", normal_limit=30, member_limit=100)
async def search_symptoms_and_recommend_therapies_compat(request, payload: SymptomSearchRequestIn):
    """
    症状搜索与疗法推荐API - 兼容路径（使用相同缓存）
    
    【测试URL】: POST /api/routertest1/prognosis/search
    
    这是为了兼容前端可能的错误请求路径而添加的端点。
    使用相同的缓存键，避免重复计算。
    """
    print(f"[DEBUG] ⚠️ 使用了兼容路径 /search，建议更新为 /symptom-search")
    
    # 🔥 使用相同的缓存键，避免重复计算
    cache_key = f"symptom_search:{payload.keyword}:{payload.limit}:{payload.include_user_therapies}:{payload.sort_by}:v1.1"
    
    # 🚀 先检查内存缓存 - 直接返回，避免重复处理
    memory_cached_data = get_memory_cache(cache_key)
    if memory_cached_data:
        print(f"[CACHE_HIT] ⚡⚡ 兼容路径内存缓存命中，耗时: <1ms")
        # 🔥 直接返回缓存数据，避免调用主函数的开销
        return memory_cached_data
    
    # 🚀 再检查Redis缓存
    try:
        start_cache_time = time.time()
        cached_data = await sync_to_async(cache.get)(cache_key)
        cache_time = (time.time() - start_cache_time) * 1000
        
        if cached_data:
            print(f"[CACHE_HIT] ⚡ 兼容路径Redis缓存命中，缓存查询耗时: {cache_time:.2f}ms")
            result_data = fast_json_loads(cached_data) if isinstance(cached_data, str) else cached_data
            # 同时缓存到内存
            set_memory_cache(cache_key, result_data)
            return result_data
    except Exception as cache_error:
        print(f"[CACHE_ERROR] ❌ 兼容路径缓存查询失败: {cache_error}")
    
    # 如果缓存未命中，调用主函数
    return await search_symptoms_and_recommend_therapies(request, payload)

# ===============================================
# 🔍 热门搜索关键词API
# ===============================================

@prognosis_router.get("/popular-keywords")
@api_timer("获取热门搜索关键词")
@rate_limit("popular_keywords_api", normal_limit=20, member_limit=80)
async def get_popular_keywords(request, limit: int = 20):
    """
    获取热门搜索关键词API
    
    Args:
        limit: 返回关键词数量限制 (默认20个，最多50个)
        
    Returns:
        Dict: 热门关键词列表
    """
    try:
        # 🔍 详细的请求日志
        print(f"[DEBUG] ========== 热门关键词API请求 ==========")
        print(f"[DEBUG] 请求方法: {request.method}")
        print(f"[DEBUG] 请求路径: {request.path}")
        print(f"[DEBUG] 请求参数: limit={limit}")
        print(f"[DEBUG] 用户ID: {getattr(request, 'user_id', 'unknown')}")
        print(f"[DEBUG] 请求头: {dict(request.headers)}")
        print(f"[DEBUG] =======================================")
        
        # 限制返回数量
        limit = min(max(1, limit), 50)
        
        # 🎯 构建缓存键
        cache_key = f"popular_keywords:{limit}"
        
        # 🚀 尝试从缓存获取
        try:
            cached_result = await sync_to_async(cache.get)(cache_key)
            if cached_result:
                print(f"[CACHE_HIT] ⚡ 热门关键词缓存命中")
                return json.loads(cached_result)
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存读取失败: {cache_error}")
        
        # 🔍 获取热门关键词（基于同义词词典的常见症状）
        search_engine = SymptomSearchEngine()
        
        # 定义热门关键词列表
        popular_keywords = [
            {'keyword': '肥胖', 'category': '美容症状', 'frequency': 95},
            {'keyword': '失眠', 'category': '睡眠症状', 'frequency': 92},
            {'keyword': '焦虑', 'category': '情绪症状', 'frequency': 80},
            {'keyword': '美容', 'category': '美容症状', 'frequency': 90},
            {'keyword': '胸闷', 'category': '呼吸症状', 'frequency': 88},
            {'keyword': '腹痛', 'category': '消化症状', 'frequency': 85},
            {'keyword': '腰痛', 'category': '疼痛症状', 'frequency': 83},
            
            {'keyword': '头晕', 'category': '头部症状', 'frequency': 78},
            {'keyword': '咳嗽', 'category': '呼吸症状', 'frequency': 75},
            {'keyword': '便秘', 'category': '消化症状', 'frequency': 73},
            {'keyword': '心悸', 'category': '情绪症状', 'frequency': 70},
            {'keyword': '眼干', 'category': '五官症状', 'frequency': 68},
            {'keyword': '多汗', 'category': '皮肤症状', 'frequency': 65},
            {'keyword': '月经不调', 'category': '妇科症状', 'frequency': 63},
            {'keyword': '关节痛', 'category': '疼痛症状', 'frequency': 60},
            {'keyword': '口干', 'category': '五官症状', 'frequency': 58},
            {'keyword': '发热', 'category': '发热症状', 'frequency': 55},
            {'keyword': '乏力', 'category': '疲劳症状', 'frequency': 53},
            {'keyword': '皮肤瘙痒', 'category': '皮肤症状', 'frequency': 50},
            {'keyword': '气短', 'category': '呼吸症状', 'frequency': 48},
            # 🎯 基于实际症状数据的美容相关热门关键词
            {'keyword': '皮肤干燥', 'category': '美容症状', 'frequency': 85},
            {'keyword': '皮肤松弛', 'category': '美容症状', 'frequency': 82},
            {'keyword': '皮肤油腻', 'category': '美容症状', 'frequency': 80},
            {'keyword': '皮肤粗糙', 'category': '美容症状', 'frequency': 78},
            {'keyword': '面部色斑', 'category': '美容症状', 'frequency': 75},
            {'keyword': '黄褐斑', 'category': '美容症状', 'frequency': 72},
            {'keyword': '痤疮', 'category': '美容症状', 'frequency': 70},
            {'keyword': '面部痤疮', 'category': '美容症状', 'frequency': 68},
            {'keyword': '毛孔粗大', 'category': '美容症状', 'frequency': 65},
            {'keyword': '脱发', 'category': '美容症状', 'frequency': 63},
            {'keyword': '头发稀疏', 'category': '美容症状', 'frequency': 60},
            {'keyword': '头发早白', 'category': '美容症状', 'frequency': 58},
            {'keyword': '白发', 'category': '美容症状', 'frequency': 55},
            {'keyword': '须发早白', 'category': '美容症状', 'frequency': 53},
            {'keyword': '头皮瘙痒', 'category': '美容症状', 'frequency': 50},
            {'keyword': '指甲易裂', 'category': '美容症状', 'frequency': 48},
            {'keyword': '指甲色淡', 'category': '美容症状', 'frequency': 45},
            {'keyword': '指甲苍白', 'category': '美容症状', 'frequency': 43},
            {'keyword': '眼袋', 'category': '美容症状', 'frequency': 40},
            {'keyword': '眼睑浮肿', 'category': '美容症状', 'frequency': 38},
            {'keyword': '面目浮肿', 'category': '美容症状', 'frequency': 35},
            {'keyword': '面浮肿', 'category': '美容症状', 'frequency': 33},
            {'keyword': '面部浮肿', 'category': '美容症状', 'frequency': 30},
            
            {'keyword': '体重异常', 'category': '美容症状', 'frequency': 85},
            {'keyword': '体重增加', 'category': '美容症状', 'frequency': 82},
            {'keyword': '脱屑', 'category': '美容症状', 'frequency': 28},
            {'keyword': '鳞屑脱落', 'category': '美容症状', 'frequency': 25},
            {'keyword': '面色晦暗', 'category': '美容症状', 'frequency': 23},
            {'keyword': '皮肤瘙痒', 'category': '美容症状', 'frequency': 20},
            
            # 🎯 基于实际症状数据的气血相关热门关键词
            {'keyword': '气短', 'category': '气血症状', 'frequency': 78},
            {'keyword': '气喘', 'category': '气血症状', 'frequency': 75},
            {'keyword': '气促', 'category': '气血症状', 'frequency': 72},
            {'keyword': '呼吸气短', 'category': '气血症状', 'frequency': 70},
            {'keyword': '心悸气短', 'category': '气血症状', 'frequency': 68},
            {'keyword': '血虚萎黄', 'category': '气血症状', 'frequency': 65},
            {'keyword': '血瘀', 'category': '气血症状', 'frequency': 63},
            {'keyword': '血瘀疼痛', 'category': '气血症状', 'frequency': 60},
            {'keyword': '瘀血性头痛', 'category': '气血症状', 'frequency': 58},
            {'keyword': '瘀血疼痛', 'category': '气血症状', 'frequency': 55},
            {'keyword': '瘀血肿痛', 'category': '气血症状', 'frequency': 53},
            {'keyword': '贫血', 'category': '气血症状', 'frequency': 50},
            {'keyword': '轻度血瘀', 'category': '气血症状', 'frequency': 48},
            {'keyword': '嗳气', 'category': '气血症状', 'frequency': 45},
            {'keyword': '嗳气反酸', 'category': '气血症状', 'frequency': 43},
            {'keyword': '嗳气频作', 'category': '气血症状', 'frequency': 40},
            {'keyword': '噫气', 'category': '气血症状', 'frequency': 38},
            
            # 🎯 基于实际症状数据的疏肝理气相关热门关键词
            {'keyword': '肝郁胁胀', 'category': '肝气症状', 'frequency': 82},
            {'keyword': '胁痛', 'category': '肝气症状', 'frequency': 80},
            {'keyword': '胁肋疼痛', 'category': '肝气症状', 'frequency': 78},
            {'keyword': '胁肋痛', 'category': '肝气症状', 'frequency': 75},
            {'keyword': '胁肋胀痛', 'category': '肝气症状', 'frequency': 73},
            {'keyword': '胁肋隐痛', 'category': '肝气症状', 'frequency': 70},
            {'keyword': '两胁胀痛', 'category': '肝气症状', 'frequency': 68},
            {'keyword': '肝区不适', 'category': '肝气症状', 'frequency': 65},
            {'keyword': '肝阳上亢', 'category': '肝气症状', 'frequency': 63},
            
            # 🎯 基于实际症状数据的增肌相关热门关键词
            {'keyword': '肌肉萎缩', 'category': '肌肉症状', 'frequency': 85},
            {'keyword': '肌肉消瘦', 'category': '肌肉症状', 'frequency': 82},
            {'keyword': '肌肉无力', 'category': '肌肉症状', 'frequency': 80},
            {'keyword': '肌肉僵硬', 'category': '肌肉症状', 'frequency': 78},
            {'keyword': '肌肉紧张', 'category': '肌肉症状', 'frequency': 75},
            {'keyword': '肌肉痉挛', 'category': '肌肉症状', 'frequency': 73},
            {'keyword': '肌肉酸痛', 'category': '肌肉症状', 'frequency': 70},
            {'keyword': '肌肉麻木', 'category': '肌肉症状', 'frequency': 68},
            {'keyword': '四肢无力', 'category': '虚弱症状', 'frequency': 65},
            {'keyword': '四肢乏力', 'category': '虚弱症状', 'frequency': 63},
            {'keyword': '营养不良', 'category': '虚弱症状', 'frequency': 60},
            
            # 🎯 基于实际症状数据的疲劳虚弱相关热门关键词
            {'keyword': '乏力', 'category': '疲劳症状', 'frequency': 90},
            {'keyword': '乏力倦怠', 'category': '疲劳症状', 'frequency': 88},
            {'keyword': '体倦乏力', 'category': '疲劳症状', 'frequency': 85},
            {'keyword': '体虚乏力', 'category': '疲劳症状', 'frequency': 83},
            {'keyword': '倦怠乏力', 'category': '疲劳症状', 'frequency': 80},
            {'keyword': '慢性疲劳', 'category': '疲劳症状', 'frequency': 78},
            {'keyword': '五劳虚损', 'category': '疲劳症状', 'frequency': 75}
        ]
        
        # 🎯 构建响应
        result = {
            'success': True,
            'message': '热门关键词获取成功',
            'keywords': popular_keywords[:limit],
            'total': len(popular_keywords[:limit])
        }
        
        # 🔥 缓存结果
        try:
            cache_timeout = 7200  # 2小时缓存，从30分钟增加到2小时
            await sync_to_async(cache.set)(cache_key, json.dumps(result), timeout=cache_timeout)
            print(f"[CACHE_SET] ⚡ 热门关键词已缓存")
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存存储失败: {cache_error}")
        
        print(f"[DEBUG] ✅ 热门关键词API完成，返回 {len(result['keywords'])} 个关键词")
        return result
        
    except Exception as e:
        print(f"[ERROR] 热门关键词API失败: {str(e)}")
        print(f"[ERROR] 错误详情: {traceback.format_exc()}")
        raise HttpError(500, f"热门关键词获取失败: {str(e)}")

# ===============================================
# 🎯 疗法类别分布统计API
# ===============================================

@prognosis_router.get("/therapy-categories-stats")
@api_timer("获取疗法类别分布统计")
@rate_limit("therapy_categories_stats_api", normal_limit=30, member_limit=120)
async def get_therapy_categories_stats(request, include_empty: bool = False):
    """
    获取疗法类别分布统计API
    
    Args:
        include_empty: 是否包含空类别（没有疗法的类别）
        
    Returns:
        Dict: 类别分布统计信息
    """
    try:
        # 🔍 详细的请求日志
        print(f"[DEBUG] ========== 疗法类别统计API请求 ==========")
        print(f"[DEBUG] 请求方法: {request.method}")
        print(f"[DEBUG] 请求路径: {request.path}")
        print(f"[DEBUG] 请求参数: include_empty={include_empty}")
        print(f"[DEBUG] 用户ID: {getattr(request, 'user_id', 'unknown')}")
        print(f"[DEBUG] 请求头: {dict(request.headers)}")
        print(f"[DEBUG] =======================================")
        
        # 🎯 构建缓存键
        cache_key = f"therapy_categories_stats:{include_empty}"
        
        # 🚀 尝试从缓存获取
        try:
            cached_result = await sync_to_async(cache.get)(cache_key)
            if cached_result:
                print(f"[CACHE_HIT] ⚡ 疗法类别统计缓存命中")
                return json.loads(cached_result)
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存读取失败: {cache_error}")
        
        # 🚀 获取所有分类
        from api.models import PrognosisTherapyClassification, PrognosisTherapyCategory, PrognosisUserTherapy
        
        # 并发获取分类和疗法数据
        async def get_classifications():
            return await sync_to_async(lambda: list(
                PrognosisTherapyClassification.objects.filter(is_active=True)
                .values('id', 'name', 'code', 'description', 'color', 'sort_order')
                .order_by('sort_order', 'name')
            ))()
        
        async def get_system_therapies():
            return await sync_to_async(lambda: list(
                PrognosisTherapyCategory.objects.filter(is_active=True)
                .values('id', 'name', 'classification_id', 'classification__name', 'is_recommended')
            ))()
        
        async def get_user_therapies():
            return await sync_to_async(lambda: list(
                PrognosisUserTherapy.objects.filter(is_public=True)
                .values('id', 'name', 'category', 'is_verified', 'usage_count')
            ))()
        
        # 🚀 并发执行查询
        classifications, system_therapies, user_therapies = await asyncio.gather(
            get_classifications(),
            get_system_therapies(),
            get_user_therapies()
        )
        
        # 🎯 构建类别统计
        category_stats = {}
        
        # 初始化所有分类
        for cls in classifications:
            category_stats[cls['name']] = {
                'classification_id': cls['id'],
                'code': cls['code'],
                'description': cls['description'],
                'color': cls['color'],
                'sort_order': cls['sort_order'],
                'system_therapy_count': 0,
                'user_therapy_count': 0,
                'total_count': 0,
                'recommended_count': 0,
                'verified_user_therapy_count': 0,
                'total_usage_count': 0,
                'system_therapies': [],
                'user_therapies': []
            }
        
        # 统计系统疗法
        for therapy in system_therapies:
            classification_name = therapy.get('classification__name', '其他类别')
            if classification_name not in category_stats:
                category_stats[classification_name] = {
                    'classification_id': None,
                    'code': 'other',
                    'description': '其他未分类疗法',
                    'color': '#999999',
                    'sort_order': 999,
                    'system_therapy_count': 0,
                    'user_therapy_count': 0,
                    'total_count': 0,
                    'recommended_count': 0,
                    'verified_user_therapy_count': 0,
                    'total_usage_count': 0,
                    'system_therapies': [],
                    'user_therapies': []
                }
            
            category_stats[classification_name]['system_therapy_count'] += 1
            category_stats[classification_name]['total_count'] += 1
            
            if therapy.get('is_recommended'):
                category_stats[classification_name]['recommended_count'] += 1
            
            category_stats[classification_name]['system_therapies'].append({
                'id': therapy['id'],
                'name': therapy['name'],
                'is_recommended': therapy.get('is_recommended', False)
            })
        
        # 统计用户疗法
        for therapy in user_therapies:
            category_code = therapy.get('category', 'other')
            # 根据code找到对应的分类名称
            classification_name = '其他类别'
            for cls in classifications:
                if cls['code'] == category_code:
                    classification_name = cls['name']
                    break
            
            if classification_name not in category_stats:
                category_stats[classification_name] = {
                    'classification_id': None,
                    'code': category_code,
                    'description': '其他未分类疗法',
                    'color': '#999999',
                    'sort_order': 999,
                    'system_therapy_count': 0,
                    'user_therapy_count': 0,
                    'total_count': 0,
                    'recommended_count': 0,
                    'verified_user_therapy_count': 0,
                    'total_usage_count': 0,
                    'system_therapies': [],
                    'user_therapies': []
                }
            
            category_stats[classification_name]['user_therapy_count'] += 1
            category_stats[classification_name]['total_count'] += 1
            category_stats[classification_name]['total_usage_count'] += therapy.get('usage_count', 0)
            
            if therapy.get('is_verified'):
                category_stats[classification_name]['verified_user_therapy_count'] += 1
            
            category_stats[classification_name]['user_therapies'].append({
                'id': therapy['id'],
                'name': therapy['name'],
                'is_verified': therapy.get('is_verified', False),
                'usage_count': therapy.get('usage_count', 0)
            })
        
        # 🎯 过滤空类别（如果需要）
        if not include_empty:
            category_stats = {k: v for k, v in category_stats.items() if v['total_count'] > 0}
        
        # 🎯 按sort_order排序
        sorted_categories = dict(sorted(
            category_stats.items(),
            key=lambda x: (x[1]['sort_order'], x[0])
        ))
        
        # 🎯 计算总体统计
        total_stats = {
            'total_categories': len(sorted_categories),
            'total_system_therapies': sum(stats['system_therapy_count'] for stats in sorted_categories.values()),
            'total_user_therapies': sum(stats['user_therapy_count'] for stats in sorted_categories.values()),
            'total_therapies': sum(stats['total_count'] for stats in sorted_categories.values()),
            'total_recommended': sum(stats['recommended_count'] for stats in sorted_categories.values()),
            'total_verified_user_therapies': sum(stats['verified_user_therapy_count'] for stats in sorted_categories.values()),
            'total_usage_count': sum(stats['total_usage_count'] for stats in sorted_categories.values())
        }
        
        # 🚀 构建响应
        result = {
            'success': True,
            'message': '疗法类别统计获取成功',
            'include_empty_categories': include_empty,
            'total_stats': total_stats,
            'categories': sorted_categories
        }
        
        # 🔥 缓存结果
        try:
            cache_timeout = 3600  # 1小时缓存，从10分钟增加到1小时
            await sync_to_async(cache.set)(cache_key, json.dumps(result, default=str), timeout=cache_timeout)
            print(f"[CACHE_SET] ⚡ 疗法类别统计已缓存")
        except Exception as cache_error:
            print(f"[CACHE_ERROR] ❌ 缓存存储失败: {cache_error}")
        
        print(f"[DEBUG] ✅ 疗法类别统计API完成，返回 {len(sorted_categories)} 个类别")
        return result
        
    except Exception as e:
        print(f"[ERROR] 疗法类别统计API失败: {str(e)}")
        print(f"[ERROR] 错误详情: {traceback.format_exc()}")
        raise HttpError(500, f"疗法类别统计获取失败: {str(e)}")

# ===============================================
# 🧬 个性化推荐Schema和接口设计（为将来扩展预留）
# ===============================================

class PersonalizedSymptomSearchRequestIn(BaseModel):
    """个性化症状搜索请求Schema（预留接口）"""
    keyword: str = Field(..., description="搜索关键词", min_length=1, max_length=50)
    limit: int = Field(25, description="返回结果数量限制", ge=1, le=100)
    include_user_therapies: bool = Field(True, description="是否包含用户疗法")
    sort_by: str = Field("personalized", description="排序方式")
    user_constitution_data: Optional[List[Dict[str, Any]]] = Field(None, description="用户体质数据")
    user_intervention_history: Optional[List[Dict[str, Any]]] = Field(None, description="用户干预历史")
    consider_constitution: bool = Field(True, description="是否考虑体质匹配")
    
    class Config:
        schema_extra = {
            "example": {
                "keyword": "头痛",
                "limit": 25,
                "include_user_therapies": True,
                "sort_by": "personalized",
                "user_constitution_data": [
                    {"type": "血虚", "percent": 30.82},
                    {"type": "阴虚", "percent": 30.73}
                ],
                "user_intervention_history": [
                    {"therapy_id": 101847, "effectiveness_score": 7.0}
                ],
                "consider_constitution": True
            }
        }

class PersonalizedTherapyRecommendationItem(BaseModel):
    """个性化疗法推荐项Schema（预留接口）"""
    id: int = Field(..., description="疗法ID")
    name: str = Field(..., description="疗法名称")
    description: str = Field(..., description="疗法描述")
    therapy_type: str = Field(..., description="疗法类型")
    match_score: float = Field(..., description="匹配度评分")
    effectiveness_score: float = Field(..., description="效果评分")
    popularity_score: float = Field(0.0, description="流行度评分")
    constitution_compatibility: float = Field(0.0, description="体质匹配度")
    personalized_score: float = Field(0.0, description="个性化综合评分")
    classification_name: Optional[str] = Field(None, description="分类名称")
    duration: str = Field(..., description="持续时间")
    difficulty_level: Optional[int] = Field(None, description="难度等级")
    is_recommended: bool = Field(False, description="是否推荐")
    usage_count: int = Field(0, description="使用次数")
    matched_symptoms: List[str] = Field([], description="匹配的症状")
    recommendation_reasons: List[str] = Field([], description="推荐理由")
    constitution_analysis: Optional[Dict[str, Any]] = Field(None, description="体质分析结果")
    similar_users_feedback: Optional[List[Dict[str, Any]]] = Field(None, description="相似用户反馈")
    
    class Config:
        schema_extra = {
            "example": {
                "id": 101847,
                "name": "月信节律观察记录",
                "description": "观察女性月经周期的规律性...",
                "therapy_type": "user",
                "match_score": 0.85,
                "effectiveness_score": 0.7,
                "popularity_score": 0.6,
                "constitution_compatibility": 0.9,
                "personalized_score": 0.85,
                "classification_name": "时间节律类",
                "duration": "7天",
                "difficulty_level": 1,
                "is_recommended": True,
                "usage_count": 15,
                "matched_symptoms": ["经期提前", "经期延后"],
                "recommendation_reasons": ["高度匹配您的症状", "适合您的体质", "相似用户反馈良好"],
                "constitution_analysis": {
                    "main_constitution": "血虚",
                    "compatibility_reason": "该疗法特别适合血虚体质"
                },
                "similar_users_feedback": [
                    {"effectiveness_score": 8.0, "user_constitution": "血虚"}
                ]
            }
        }

class PersonalizedSymptomSearchResponseOut(BaseModel):
    """个性化症状搜索响应Schema（预留接口）"""
    keyword: str = Field(..., description="搜索关键词")
    total_count: int = Field(..., description="总结果数量")
    system_therapy_count: int = Field(..., description="系统疗法数量")
    user_therapy_count: int = Field(..., description="用户疗法数量")
    search_time_ms: float = Field(..., description="搜索耗时(毫秒)")
    personalization_enabled: bool = Field(False, description="是否启用个性化")
    user_constitution_summary: Optional[Dict[str, Any]] = Field(None, description="用户体质摘要")
    recommendations: List[PersonalizedTherapyRecommendationItem] = Field(..., description="个性化推荐疗法列表")
    
    class Config:
        schema_extra = {
            "example": {
                "keyword": "头痛",
                "total_count": 15,
                "system_therapy_count": 8,
                "user_therapy_count": 7,
                "search_time_ms": 45.2,
                "personalization_enabled": True,
                "user_constitution_summary": {
                    "main_constitution": "血虚",
                    "secondary_constitution": "阴虚"
                },
                "recommendations": []
            }
        }

class PersonalizedRecommendationEngine:
    """个性化推荐引擎（预留实现）"""
    
    def __init__(self):
        """初始化个性化推荐引擎"""
        self.similarity_calculator = ConstitutionSimilarityCalculator()
        print(f"[DEBUG] 🧬 个性化推荐引擎初始化完成（预留功能）")
    
    async def calculate_constitution_compatibility(self, 
                                                 user_constitution: List[Dict[str, Any]], 
                                                 therapy_data: Dict[str, Any]) -> float:
        """
        计算体质匹配度（预留实现）
        
        Args:
            user_constitution: 用户体质数据
            therapy_data: 疗法数据
            
        Returns:
            float: 体质匹配度 (0-1之间)
        """
        # 🚧 这里是预留的实现，将来会结合体质相似度计算
        # 暂时返回默认值
        return 0.5
    
    async def get_similar_users_feedback(self, 
                                       therapy_id: int, 
                                       user_constitution: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        获取相似用户反馈（预留实现）
        
        Args:
            therapy_id: 疗法ID
            user_constitution: 用户体质数据
            
        Returns:
            List[Dict]: 相似用户反馈列表
        """
        # 🚧 这里是预留的实现，将来会查询相似体质用户的使用反馈
        # 暂时返回空列表
        return []
    
    async def generate_personalized_recommendations(self, 
                                                  basic_recommendations: List[Dict[str, Any]], 
                                                  user_constitution: List[Dict[str, Any]], 
                                                  user_intervention_history: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成个性化推荐（预留实现）
        
        Args:
            basic_recommendations: 基础推荐列表
            user_constitution: 用户体质数据
            user_intervention_history: 用户干预历史
            
        Returns:
            List[Dict]: 个性化推荐列表
        """
        # 🚧 这里是预留的实现，将来会结合体质匹配和历史数据
        # 暂时返回基础推荐
        return basic_recommendations

# ===============================================
# 🚀 个性化症状搜索API端点（预留接口）
# ===============================================

# 注意：这个接口暂时不实现，只是为将来的个性化功能做准备
# @prognosis_router.post("/personalized-symptom-search", response=PersonalizedSymptomSearchResponseOut)
# @api_timer("个性化症状搜索与疗法推荐")
# @rate_limit("personalized_symptom_search_api", normal_limit=20, member_limit=80)
# async def personalized_search_symptoms_and_recommend_therapies(request, payload: PersonalizedSymptomSearchRequestIn):
#     """
#     个性化症状搜索与疗法推荐API - 结合用户体质和干预历史
#     
#     【预留接口】: POST /api/routertest1/prognosis/personalized-symptom-search
#     
#     功能特点（将来实现）:
#     1. 🧬 结合用户体质数据进行个性化推荐
#     2. 📊 基于用户干预历史优化推荐
#     3. 🎯 计算体质匹配度和相似用户反馈
#     4. 🚀 综合多维度评分的智能排序
#     """
#     # 🚧 预留实现
#     pass

# ===============================================
# 🔧 模块信息更新
# ===============================================

def get_constitution_similarity_info():
    """获取体质相似度模块信息"""
    return {
        "module": "prognosis_constitution_similarity",
        "version": "1.6.0",  # 版本更新
        "endpoints": [
            "POST /therapy-users-intervention-records-similarity - 体质相似度排序",
            "GET /constitution-similarity-analysis/{therapy_id} - 体质分析报告",
            "POST /symptom-search - 症状搜索与疗法推荐",
            "POST /search - 症状搜索与疗法推荐（兼容路径）",
            "GET /search-suggestions - 搜索建议",
            "GET /popular-keywords - 热门关键词",
            "GET /therapy-categories-stats - 疗法类别统计"
        ],
        "features": [
            "体质相似度计算 (cosine, euclidean, weighted)",
            "症状搜索引擎",
            "疗法推荐系统",
            "多类别多样性保证",
            "类别分布统计",
            "三级缓存优化（内存+Redis+数据库）",
            "高性能关键词扩展缓存",
            "分类名称预缓存机制"
        ],
        "cache_optimization": {
            "memory_cache": "5分钟内存缓存，<1ms访问速度",
            "redis_cache": "1-2小时Redis缓存，持久化存储",
            "database_fallback": "数据库降级，确保可用性",
            "cache_levels": 3,
            "estimated_speedup": "10-100倍性能提升"
        },
        "similarity_methods": ["cosine", "euclidean", "weighted"],
        "constitution_types": 16,
        "status": "active",
        "changes": "终极性能优化：三级缓存+数据压缩+异步存储+高性能JSON序列化，0.029秒已接近框架极限"
    }

def get_constitution_similarity_test_endpoints():
    """获取体质相似度测试端点列表"""
    return [
        "POST /api/routertest1/prognosis/therapy-users-intervention-records-similarity - 体质相似度排序",
        "GET /api/routertest1/prognosis/constitution-similarity-analysis/{therapy_id} - 体质分析报告",
        "POST /api/routertest1/prognosis/symptom-search - 症状搜索与疗法推荐",
        "GET /api/routertest1/prognosis/search-suggestions - 搜索建议",
        "GET /api/routertest1/prognosis/popular-keywords - 热门关键词",
        "GET /api/routertest1/prognosis/therapy-categories-stats - 疗法类别统计"  # 新增端点
    ]

print(f"[INFO] 🔍 症状搜索与疗法推荐API模块加载完成 v1.6.0")
print(f"[INFO] 🚀 终极优化: 三级缓存+数据压缩+异步存储+高性能JSON序列化")
print(f"[INFO] ⚡ 性能极限: 内存缓存<1ms，0.029秒已接近Django Ninja框架极限")
print(f"[INFO] 📊 缓存策略: 内存5分钟，Redis1-2小时，数据库降级")
print(f"[INFO] 🎯 多样性保证: 每个类别至少有一个推荐结果")
print(f"[INFO] ⏱️ 性能优化: 三级缓存、数据压缩、异步存储、高性能JSON、预缓存") 