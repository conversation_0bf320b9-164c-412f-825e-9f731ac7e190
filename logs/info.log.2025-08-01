ERROR 2025-08-01 15:10:26,907 exception 4081150 140235144934976 Invalid HTTP_HOST header: '419-823-8887.govcivilporto.gov.pt'. You may need to add '419-823-8887.govcivilporto.gov.pt' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '419-823-8887.govcivilporto.gov.pt'. You may need to add '419-823-8887.govcivilporto.gov.pt' to ALLOWED_HOSTS.
ERROR 2025-08-01 15:35:35,458 exception 4081150 140235144934976 Invalid HTTP_HOST header: '419-823-8887.govcivilporto.gov.pt'. You may need to add '419-823-8887.govcivilporto.gov.pt' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '419-823-8887.govcivilporto.gov.pt'. You may need to add '419-823-8887.govcivilporto.gov.pt' to ALLOWED_HOSTS.
ERROR 2025-08-01 21:03:40,502 exception 4081150 140235899917888 Invalid HTTP_HOST header: '************'. You may need to add '************' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '************'. You may need to add '************' to ALLOWED_HOSTS.
ERROR 2025-08-01 21:37:32,371 exception 4081150 140235899917888 Invalid HTTP_HOST header: 'dnspod.qcloud.com'. You may need to add 'dnspod.qcloud.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'dnspod.qcloud.com'. You may need to add 'dnspod.qcloud.com' to ALLOWED_HOSTS.
ERROR 2025-08-01 21:37:32,562 exception 4081150 140235899917888 Invalid HTTP_HOST header: 'dnspod.qcloud.com'. You may need to add 'dnspod.qcloud.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'dnspod.qcloud.com'. You may need to add 'dnspod.qcloud.com' to ALLOWED_HOSTS.
ERROR 2025-08-01 21:37:32,766 exception 4081150 140235899917888 Invalid HTTP_HOST header: 'dnspod.qcloud.com'. You may need to add 'dnspod.qcloud.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home/<USER>/riyue-llm/myproject/venv/lib/python3.10/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'dnspod.qcloud.com'. You may need to add 'dnspod.qcloud.com' to ALLOWED_HOSTS.
