import json  # 确保正确导入json模块，必须放在最上面
import traceback
import os
import sys
import openai
from channels.generic.websocket import AsyncWebsocketConsumer
# 添加路径以便导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)
# 绝对导入metaphysic模块，避免循环引用
import metaphysic
import asyncio
from django.conf import settings
import base64
from datetime import datetime
import pytz
import importlib.util
# 导入提示词配置
from api.utils.constants import get_system_prompt, get_special_health_prompt

# 现在我们不使用logging，改用print替代
# 设置日志
# logger = logging.getLogger(__name__)

# 为避免GBK编码错误，定义一个安全的打印函数
def safe_print(message):
    try:
        print(message)
    except UnicodeEncodeError:
        # 如果出现编码错误，尝试使用ASCII编码并忽略无法编码的字符
        print(message.encode('ascii', errors='ignore').decode('ascii'))
    except Exception as e:
        print(f"打印消息时出错: {str(e)}")

# 先检查API密钥是否有效
try:
    api_key = settings.LLM_API.get('ARK_API_KEY', '')
    if not api_key or api_key == 'Bearer ':
        safe_print("API密钥无效或为空，请在settings中设置正确的API_KEY")
        raise ValueError("API密钥无效")
    
    # 记录API密钥的编码状态
    safe_print(f"API密钥类型: {type(api_key)}, 长度: {len(api_key)}")
except Exception as e:
    safe_print(f"API密钥检查失败: {str(e)}")
    safe_print(traceback.format_exc())

# 尝试找到完整五运六气模块
wuyunliuqi_module_paths = [
    # 添加更多可能的导入路径
    os.path.join(project_root, "bazi_api", "bazi_api", "complete_wuyunliuqi", "great_contribution.py"),
    "bazi_api.bazi_api.complete_wuyunliuqi.great_contribution",
    "bazi_api.complete_wuyunliuqi.great_contribution",
    "complete_wuyunliuqi.great_contribution",
]

# 导入完整的五运六气系统
WUYUNLIUQI_AVAILABLE = False
wuyunliuqi_core = None
wuyunliuqi_import_errors = []

# 尝试多种导入方式
for import_path in wuyunliuqi_module_paths:
    try:
        if import_path.endswith('.py'):
            # 直接从文件路径导入
            safe_print(f"尝试从文件导入: {import_path}")
            if os.path.exists(import_path):
                spec = importlib.util.spec_from_file_location("great_contribution", import_path)
                wuyunliuqi_core = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(wuyunliuqi_core)
                safe_print(f"成功从文件路径导入: {import_path}")
            else:
                safe_print(f"文件不存在: {import_path}")
                continue
        else:
            # 从模块路径导入
            safe_print(f"尝试导入模块: {import_path}")
            wuyunliuqi_core = importlib.import_module(import_path)
            safe_print(f"成功导入模块: {import_path}")
        
        # 验证导入是否正确
        safe_print(f"导入成功，模块: {wuyunliuqi_core}")
        safe_print(f"模块属性: {dir(wuyunliuqi_core)}")
        
        # 测试函数可用性
        test_date = "2023-01-01"
        try:
            test_result = wuyunliuqi_core.getWuYunLiuQi(test_date)
            safe_print(f"五运六气系统测试成功: {test_result[:100]}...")
            WUYUNLIUQI_AVAILABLE = True
            safe_print(f"**** 成功导入五运六气系统: {import_path} ****")
            break  # 成功导入，跳出循环
        except Exception as test_e:
            safe_print(f"导入路径 {import_path} 的函数测试失败: {str(test_e)}")
            safe_print(f"错误详情: {traceback.format_exc()}")
            wuyunliuqi_import_errors.append((import_path, str(test_e), traceback.format_exc()))
    except ImportError as e:
        safe_print(f"导入路径 {import_path} 失败: {e}")
        wuyunliuqi_import_errors.append((import_path, str(e), traceback.format_exc()))
        continue
    except Exception as e:
        safe_print(f"尝试导入 {import_path} 时出现未知错误: {str(e)}")
        safe_print(f"错误详情: {traceback.format_exc()}")
        wuyunliuqi_import_errors.append((import_path, str(e), traceback.format_exc()))
        continue

# 如果所有导入方式都失败
if not WUYUNLIUQI_AVAILABLE:
    safe_print("所有导入尝试都失败，五运六气系统不可用")
    safe_print("导入错误汇总:")
    for i, (path, error, traceback_str) in enumerate(wuyunliuqi_import_errors):
        safe_print(f"{i+1}. 路径: {path}")
        safe_print(f"   错误: {error}")
        safe_print(f"   详情: {traceback_str}")
        safe_print("---")

# 检查Python版本和默认编码
safe_print(f"Python版本: {sys.version}")
safe_print(f"默认编码: {sys.getdefaultencoding()}")
safe_print(f"文件系统编码: {sys.getfilesystemencoding()}")

# 从config模块获取配置（适配主项目的openai使用方式）
from api.utils.config import MODEL_ID

class BaziAdviceConsumer(AsyncWebsocketConsumer):
    """八字分析和各类咨询建议的WebSocket消费者类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 默认使用健康咨询的系统提示
        self.conversation_history = [
            {"role": "system", "content": get_system_prompt("health")}
        ]
        
        # 当前咨询类型
        self.current_consult_type = "health"
    
    async def connect(self):
        """建立WebSocket连接"""
        safe_print(f"WebSocket连接尝试建立: {self.scope['path']}")
        await self.accept()
        safe_print(f"WebSocket连接已接受: {self.scope['path']}")

    async def disconnect(self, close_code):
        """断开WebSocket连接"""
        safe_print(f"WebSocket连接断开: {self.scope['path']}, 代码: {close_code}")
    
    async def receive(self, text_data):
        """接收消息并处理"""
        try:
            # 确保json模块可用
            import json  # 显式导入，确保局部可用
            
            # 解析接收到的消息
            safe_print(f"收到WebSocket消息: {text_data[:100]}...")
            
            # 确保输入文本是有效的UTF-8字符串
            if isinstance(text_data, bytes):
                text_data = text_data.decode('utf-8')
            
            data = json.loads(text_data)
            
            # 如果是八字分析请求
            if 'birth_info' in data:
                safe_print(f"处理八字分析请求: {data['birth_info']}")
                await self.handle_bazi_request(data['birth_info'])
            # 如果是普通聊天请求
            elif 'message' in data:
                message = data['message']
                safe_print(f"处理聊天消息: {message[:50]}...")
                # 确保消息是UTF-8编码的字符串
                if isinstance(message, bytes):
                    message = message.decode('utf-8')
                await self.handle_chat_message(message)
            else:
                safe_print(f"无法识别的请求格式: {data}")
                await self.send(text_data=json.dumps({
                    'error': '无法识别的请求格式',
                    'done': True
                }))
                
        except Exception as e:
            error_message = f"处理消息时出错: {str(e)}\n{traceback.format_exc()}"
            safe_print(error_message)
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            }))
    
    async def handle_stream_sync(self, stream_response, msg_type):
        """在异步环境中处理同步流式响应"""
        assistant_message = ""
        
        # 这里逐个处理流式返回的内容
        safe_print(f"开始处理{msg_type}流式响应")
        chunk_count = 0
        buffer = ""  # 用于积累字符
        buffer_size = 0  # 当前缓冲区大小
        max_buffer_size = 30  # 减小最大缓冲区大小，使流式效果更明显
        last_sent_time = asyncio.get_event_loop().time()  # 记录最后一次发送时间
        
        try:
            for chunk in stream_response:
                chunk_count += 1
                if not chunk.choices:
                    continue
                
                # 获取内容
                content = chunk.choices[0].delta.content
                if content:
                    # 确保内容是UTF-8编码的字符串
                    if isinstance(content, bytes):
                        content = content.decode('utf-8')
                    
                    # 记录收到的内容（仅限调试）
                    if chunk_count % 10 == 0:  # 每10个块记录一次，避免日志过多
                        safe_print(f"收到第{chunk_count}块内容: {content[:20]}...")
                    
                    assistant_message += content
                    
                    # 将内容添加到缓冲区
                    buffer += content
                    buffer_size += len(content)
                    
                    current_time = asyncio.get_event_loop().time()
                    time_since_last_send = current_time - last_sent_time
                    
                    # 当缓冲区达到一定大小或收到特定字符或经过了足够的时间时发送
                    if (buffer_size >= max_buffer_size or 
                        any(char in buffer for char in ['\n', '。', '，', '；', '：']) or
                        time_since_last_send > 0.5):  # 确保至少每0.5秒发送一次
                        
                        # 立即发送数据块到客户端
                        try:
                            await self.send(text_data=json.dumps({
                                'type': msg_type,
                                'content': buffer,
                                'done': False
                            }))
                            safe_print(f"发送{len(buffer)}字符给客户端，类型: {msg_type}")
                            last_sent_time = current_time
                        except Exception as e:
                            safe_print(f"发送数据块时出错: {str(e)}")
                        
                        # 清空缓冲区
                        buffer = ""
                        buffer_size = 0
                        
                        # 重要：添加短暂延迟，模拟真实打字速度，让前端有时间处理
                        await asyncio.sleep(0.05)  # 减少到50毫秒延迟，保持流畅但不过慢
                
                # 每50个数据块记录一次进度
                if chunk_count % 50 == 0:
                    safe_print(f"已处理{chunk_count}个数据块")
            
            # 发送剩余缓冲区中的内容（如果有）
            if buffer:
                try:
                    await self.send(text_data=json.dumps({
                        'type': msg_type,
                        'content': buffer,
                        'done': False
                    }))
                    safe_print(f"发送最后{len(buffer)}字符给客户端")
                except Exception as e:
                    safe_print(f"发送最后数据块时出错: {str(e)}")
                
                await asyncio.sleep(0.05)  # 最后一次也添加延迟
        
            safe_print(f"流式响应处理完成，共处理{chunk_count}个数据块")
            safe_print(f"完整回复长度: {len(assistant_message)}")
        except Exception as e:
            safe_print(f"处理流式响应时出错: {str(e)}")
            safe_print(traceback.format_exc())
            raise
            
        # 发送完成标记前添加额外延迟，确保前端处理完所有内容
        await asyncio.sleep(0.2)
            
        # 发送完成标记
        try:
            await self.send(text_data=json.dumps({
                'type': msg_type,
                'content': '',
                'done': True
            }))
            safe_print(f"发送完成标记, 类型: {msg_type}")
        except Exception as e:
            safe_print(f"发送完成标记时出错: {str(e)}")
        
        return assistant_message
    
    async def handle_bazi_request(self, birth_info):
        """处理八字分析请求"""
        try:
            # 先确保json模块是可访问的
            import json  # 显式导入，确保局部可用
            
            # 首先发送当前时间的八字信息
            try:
                current_time = datetime.now()
                current_bazi = metaphysic.getCurrentBazi()
                
                await self.send(text_data=json.dumps({
                    'type': 'current_time_bazi',
                    'current_time': current_time.strftime("%Y-%m-%d %H:%M:%S"),
                    'current_bazi': current_bazi
                }))
                safe_print(f"已发送当前时间八字信息: {current_bazi}")
            except Exception as e:
                safe_print(f"获取当前时间八字时出错: {str(e)}")
                safe_print(traceback.format_exc())

            # 提取出生信息
            birth_year = birth_info.get('birth_year')
            birth_month = birth_info.get('birth_month')
            birth_day = birth_info.get('birth_day')
            birth_time = birth_info.get('birth_time')
            # 提取是否需要五运六气信息
            include_wuyun_liuqi = birth_info.get('include_wuyun_liuqi', False)
            # 提取咨询类型
            consult_type = birth_info.get('consult_type', 'health')
            
            # 设置当前咨询类型
            self.current_consult_type = consult_type if consult_type in ["health", "career", "education", "fengshui", "fortune"] else "health"
            
            # 更新系统提示
            self.conversation_history = [
                {"role": "system", "content": get_system_prompt(self.current_consult_type)}
            ]
            
            # 验证数据
            if not all([isinstance(x, int) for x in [birth_year, birth_month, birth_day, birth_time]]):
                safe_print(f"出生信息格式错误: {birth_info}")
                await self.send(text_data=json.dumps({
                    'error': '出生信息格式错误，请确保年、月、日、时均为整数',
                    'done': True
                }))
                return
            
            # 获取八字
            safe_print(f"计算八字: {birth_year}/{birth_month}/{birth_day} {birth_time}时")
            shenchenbazi = metaphysic.getShenChenBaZi(birth_year, birth_month, birth_day, birth_time)
            safe_print(f"八字结果: {shenchenbazi}")
            
            # 获取生肖
            shengxiao_list = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
            shengxiao = shengxiao_list[(birth_year - 4) % 12]
            
            # 计算五行
            wuxing_tuple = metaphysic.getWuXing(shenchenbazi)
            wuxing_dict = {
                "金": wuxing_tuple[0],
                "木": wuxing_tuple[1],
                "水": wuxing_tuple[2],
                "火": wuxing_tuple[3],
                "土": wuxing_tuple[4]
            }
            safe_print(f"五行分析结果: {wuxing_dict}")
            
            # 获取当前五运六气（如果需要）
            wuyun_liuqi_data = None
            if include_wuyun_liuqi:
                try:
                    # 使用完整的五运六气系统
                    if WUYUNLIUQI_AVAILABLE:
                        safe_print("使用完整的五运六气系统")
                        current_date = datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y-%m-%d")
                        birth_date = f"{birth_year}-{birth_month}-{birth_day}"
                        
                        safe_print(f"当前日期格式: {current_date}, 出生日期格式: {birth_date}")
                        
                        try:
                            # 获取当前日期的五运六气
                            safe_print(f"调用五运六气系统获取当前日期信息: {current_date}")
                            current_result_json = wuyunliuqi_core.getWuYunLiuQi(current_date)
                            current_result_data = json.loads(current_result_json)[0]
                            safe_print(f"当前日期五运六气获取成功: {current_result_data}")
                        except Exception as e:
                            safe_print(f"获取当前日期五运六气失败: {str(e)}")
                            safe_print(f"错误详情: {traceback.format_exc()}")
                            raise
                        
                        try:
                            # 获取出生日期的五运六气
                            safe_print(f"调用五运六气系统获取出生日期信息: {birth_date}")
                            birth_result_json = wuyunliuqi_core.getWuYunLiuQi(birth_date)
                            birth_result_data = json.loads(birth_result_json)[0]
                            safe_print(f"出生日期五运六气获取成功: {birth_result_data}")
                        except Exception as e:
                            safe_print(f"获取出生日期五运六气失败: {str(e)}")
                            safe_print(f"错误详情: {traceback.format_exc()}")
                            raise
                        
                        # 组合结果
                        wuyun_liuqi_data = {
                            "current": current_result_data,
                            "birth": birth_result_data
                        }
                        safe_print(f"完整五运六气系统结果: {wuyun_liuqi_data}")
                    else:
                        # 回退到原有系统
                        safe_print("完整五运六气系统不可用，使用原有系统")
                        safe_print("请检查导入路径和模块可用性")
                        current_bazi = metaphysic.getCurrentBazi()
                        current_wuyun_liuqi = metaphysic.getWuYunLiuQi(current_bazi)
                        wuyun_liuqi_data = current_wuyun_liuqi
                        safe_print(f"原有五运六气系统结果: {wuyun_liuqi_data}")
                except Exception as e:
                    # 回退到原有系统
                    safe_print(f"获取完整五运六气系统结果出错: {str(e)}")
                    safe_print(f"错误详情: {traceback.format_exc()}")
                    # 记录详细错误信息供调试
                    error_type = type(e).__name__
                    error_args = getattr(e, 'args', [])
                    safe_print(f"错误类型: {error_type}, 错误参数: {error_args}")
                    
                    try:
                        current_bazi = metaphysic.getCurrentBazi()
                        current_wuyun_liuqi = metaphysic.getWuYunLiuQi(current_bazi)
                        wuyun_liuqi_data = current_wuyun_liuqi
                        safe_print(f"回退到原有系统，结果: {wuyun_liuqi_data}")
                    except Exception as fallback_e:
                        safe_print(f"回退系统也出错: {str(fallback_e)}")
                        safe_print(f"回退错误详情: {traceback.format_exc()}")
                        wuyun_liuqi_data = {"error": "五运六气系统无法使用"}
            
            # 先发送八字和五行分析结果
            bazi_result = {
                'type': 'bazi_result',
                'shenchenbazi': shenchenbazi,
                'wuxing': wuxing_dict,
                'shengxiao': shengxiao,
                'wuyun_liuqi': wuyun_liuqi_data,
                'done': False
            }
            safe_print("发送八字分析结果")
            await self.send(text_data=json.dumps(bazi_result))
            
            # 构建提示信息 - 修改：根据是否有五运六气信息和咨询类型构建不同的提示
            if consult_type == "health":
                if wuyun_liuqi_data:
                    # 根据使用的系统选择不同的提示构建方式
                    if WUYUNLIUQI_AVAILABLE and isinstance(wuyun_liuqi_data, dict) and "current" in wuyun_liuqi_data:
                        # 使用完整系统
                        current_data = wuyun_liuqi_data["current"]
                        prompt = get_special_health_prompt("with_wuyunliuqi_full").format(
                            shenchenbazi=shenchenbazi,
                            dayun=current_data.get('dayun', '未知'),
                            sitian=current_data.get('sitian', '未知'),
                            zaiquan=current_data.get('zaiquan', '未知'),
                            qi_shunxu=current_data.get('qi_shunxu', '未知')
                        )
                    else:
                        # 使用原有系统
                        prompt = get_special_health_prompt("with_wuyunliuqi_simple").format(
                            shenchenbazi=shenchenbazi,
                            wuyun=wuyun_liuqi_data['五运'],
                            liuqi=wuyun_liuqi_data['六气']
                        )
                else:
                    prompt = get_special_health_prompt("without_wuyunliuqi").format(
                        shenchenbazi=shenchenbazi
                    )
            
            elif self.current_consult_type == "career":
                # 导入咨询模板函数
                from api.utils.constants import get_consult_template
                if include_wuyun_liuqi and wuyun_liuqi_data:
                    # 根据使用的系统选择不同的提示构建方式
                    if WUYUNLIUQI_AVAILABLE and isinstance(wuyun_liuqi_data, dict) and "current" in wuyun_liuqi_data:
                        # 使用完整系统
                        current_data = wuyun_liuqi_data["current"]
                        prompt = get_consult_template("career", "with_wuyunliuqi_full").format(
                            shenchenbazi=shenchenbazi,
                            dayun=current_data.get('dayun', '未知'),
                            sitian=current_data.get('sitian', '未知'),
                            zaiquan=current_data.get('zaiquan', '未知'),
                            qi_shunxu=current_data.get('qi_shunxu', '未知')
                        )
                    else:
                        # 使用原有系统
                        prompt = get_consult_template("career", "with_wuyunliuqi_simple").format(
                            shenchenbazi=shenchenbazi,
                            wuyun=wuyun_liuqi_data['五运'],
                            liuqi=wuyun_liuqi_data['六气']
                        )
                else:
                    prompt = get_consult_template("career", "without_wuyunliuqi").format(
                        shenchenbazi=shenchenbazi
                    )
            
            elif self.current_consult_type == "education":
                if include_wuyun_liuqi and wuyun_liuqi_data:
                    # 根据使用的系统选择不同的提示构建方式
                    if WUYUNLIUQI_AVAILABLE and isinstance(wuyun_liuqi_data, dict) and "current" in wuyun_liuqi_data:
                        # 使用完整系统
                        current_data = wuyun_liuqi_data["current"]
                        prompt = get_consult_template("education", "with_wuyunliuqi_full").format(
                            shenchenbazi=shenchenbazi,
                            dayun=current_data.get('dayun', '未知'),
                            sitian=current_data.get('sitian', '未知'),
                            zaiquan=current_data.get('zaiquan', '未知'),
                            qi_shunxu=current_data.get('qi_shunxu', '未知')
                        )
                    else:
                        # 使用原有系统
                        prompt = get_consult_template("education", "with_wuyunliuqi_simple").format(
                            shenchenbazi=shenchenbazi,
                            wuyun=wuyun_liuqi_data['五运'],
                            liuqi=wuyun_liuqi_data['六气']
                        )
                else:
                    prompt = get_consult_template("education", "without_wuyunliuqi").format(
                        shenchenbazi=shenchenbazi
                    )
            
            elif self.current_consult_type == "fengshui":
                if include_wuyun_liuqi and wuyun_liuqi_data:
                    # 根据使用的系统选择不同的提示构建方式
                    if WUYUNLIUQI_AVAILABLE and isinstance(wuyun_liuqi_data, dict) and "current" in wuyun_liuqi_data:
                        # 使用完整系统
                        current_data = wuyun_liuqi_data["current"]
                        prompt = get_consult_template("fengshui", "with_wuyunliuqi_full").format(
                            shenchenbazi=shenchenbazi,
                            dayun=current_data.get('dayun', '未知'),
                            sitian=current_data.get('sitian', '未知'),
                            zaiquan=current_data.get('zaiquan', '未知')
                        )
                    else:
                        # 使用原有系统
                        prompt = get_consult_template("fengshui", "with_wuyunliuqi_simple").format(
                            shenchenbazi=shenchenbazi,
                            wuyun=wuyun_liuqi_data['五运'],
                            liuqi=wuyun_liuqi_data['六气']
                        )
                else:
                    prompt = get_consult_template("fengshui", "without_wuyunliuqi").format(
                        shenchenbazi=shenchenbazi
                    )
            
            elif self.current_consult_type == "fortune":
                if include_wuyun_liuqi and wuyun_liuqi_data:
                    # 根据使用的系统选择不同的提示构建方式
                    if WUYUNLIUQI_AVAILABLE and isinstance(wuyun_liuqi_data, dict) and "current" in wuyun_liuqi_data:
                        # 使用完整系统
                        current_data = wuyun_liuqi_data["current"]
                        prompt = get_consult_template("fortune", "with_wuyunliuqi_full").format(
                            shenchenbazi=shenchenbazi,
                            dayun=current_data.get('dayun', '未知'),
                            sitian=current_data.get('sitian', '未知'),
                            zaiquan=current_data.get('zaiquan', '未知'),
                            qi_shunxu=current_data.get('qi_shunxu', '未知')
                        )
                    else:
                        # 使用原有系统
                        prompt = get_consult_template("fortune", "with_wuyunliuqi_simple").format(
                            shenchenbazi=shenchenbazi,
                            wuyun=wuyun_liuqi_data['五运'],
                            liuqi=wuyun_liuqi_data['六气']
                        )
                else:
                    prompt = get_consult_template("fortune", "without_wuyunliuqi").format(
                        shenchenbazi=shenchenbazi
                    )
            
            else:
                # 默认使用健康咨询
                if include_wuyun_liuqi and wuyun_liuqi_data:
                    # 根据使用的系统选择不同的提示构建方式
                    if WUYUNLIUQI_AVAILABLE and isinstance(wuyun_liuqi_data, dict) and "current" in wuyun_liuqi_data:
                        # 使用完整系统
                        current_data = wuyun_liuqi_data["current"]
                        prompt = get_special_health_prompt("with_wuyunliuqi_full").format(
                            shenchenbazi=shenchenbazi,
                            dayun=current_data.get('dayun', '未知'),
                            sitian=current_data.get('sitian', '未知'),
                            zaiquan=current_data.get('zaiquan', '未知'),
                            qi_shunxu=current_data.get('qi_shunxu', '未知')
                        )
                    else:
                        # 使用原有系统
                        prompt = get_special_health_prompt("with_wuyunliuqi_simple").format(
                            shenchenbazi=shenchenbazi,
                            wuyun=wuyun_liuqi_data['五运'],
                            liuqi=wuyun_liuqi_data['六气']
                        )
                else:
                    prompt = get_special_health_prompt("without_wuyunliuqi").format(
                        shenchenbazi=shenchenbazi
                    )
            
            # 记录提示信息的编码信息
            safe_print(f"提示信息类型: {type(prompt)}, 长度: {len(prompt)}")
            safe_print(f"提示信息编码: 尝试对提示信息的前10个字符进行编码测试:")
            for encoding in ['utf-8', 'ascii', 'gbk']:
                try:
                    encoded = prompt[:10].encode(encoding)
                    safe_print(f"  - {encoding}编码成功: {encoded}")
                except UnicodeEncodeError as e:
                    safe_print(f"  - {encoding}编码失败: {e}")
            
            # 添加用户消息到历史记录
            system_message = {"role": "system", "content": "作为中医大师，你擅长养生，根据八字和五行分析，提供养生建议。"}
            user_message = {"role": "user", "content": prompt}
            
            messages = [system_message, user_message]
            
            try:
                # 对消息内容进行深度检查，确保所有字段都是UTF-8兼容的
                for idx, message in enumerate(messages):
                    safe_print(f"检查消息[{idx}]的编码兼容性:")
                    for key, value in message.items():
                        if isinstance(value, str):
                            safe_print(f"  - 字段'{key}'类型: {type(value)}, 长度: {len(value)}")
                            if value:
                                # 尝试不同编码测试
                                try:
                                    safe_print(f"  - 尝试UTF-8编码: {value[:10].encode('utf-8')}")
                                except UnicodeEncodeError as e:
                                    safe_print(f"  - UTF-8编码失败: {e}")
                
                # 尝试将API参数序列化为JSON以验证兼容性
                # 注意：不要在此处使用import json语句，因为会覆盖全局json模块
                test_json = json.dumps({"messages": messages})
                safe_print(f"API请求参数可以正确序列化为JSON，长度: {len(test_json)}")
                
                # 调用华为方舟API流式API
                safe_print("开始调用华为方舟API")
                
                # 确保所有消息内容都可以正确使用JSON序列化
                # 尝试手动规范化消息内容，确保编码兼容性
                sanitized_messages = []
                for msg in messages:
                    # 复制消息以避免修改原始对象
                    sanitized_msg = {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                    
                    # 确保能够编码为JSON
                    try:
                        json.dumps(sanitized_msg)
                    except UnicodeEncodeError:
                        # 如果有编码问题，尝试将内容编码为UTF-8再解码
                        try:
                            sanitized_msg["content"] = sanitized_msg["content"].encode('utf-8', errors='ignore').decode('utf-8')
                            safe_print(f"消息内容被强制编码为UTF-8: {sanitized_msg['content'][:20]}...")
                        except Exception as enc_error:
                            safe_print(f"消息内容编码修复失败: {enc_error}")
                    
                    sanitized_messages.append(sanitized_msg)
                
                # 使用规范化后的消息
                stream = openai.ChatCompletion.create(
                    model=MODEL_ID,
                    messages=sanitized_messages,
                    max_tokens=2000,
                    temperature=0.7,
                    stream=True
                )
                
                # 根据咨询类型设置消息类型
                advice_type = f"{self.current_consult_type}_advice"
                
                # 处理流式响应
                assistant_message = await self.handle_stream_sync(stream, advice_type)
                
                # 将完整回复添加到历史记录
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                safe_print(f"完整回复长度: {len(assistant_message)}")
            except Exception as api_error:
                error_detail = str(api_error)
                safe_print(f"调用华为方舟API出错: {error_detail}")
                safe_print(f"错误类型: {type(api_error)}")
                safe_print(f"详细错误信息: {traceback.format_exc()}")
                
                # 提供更友好的错误消息
                friendly_error = "调用大模型API时出错，请稍后再试"
                if "ordinal not in range" in error_detail:
                    friendly_error = "API调用编码错误，请确保API密钥正确且支持中文编码"
                
                await self.send(text_data=json.dumps({
                    'type': f"{self.current_consult_type}_advice",
                    'content': friendly_error,
                    'done': True
                }))
            
        except Exception as e:
            error_message = f"生成八字分析时出错: {str(e)}\n{traceback.format_exc()}"
            safe_print(error_message)
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            }))
    
    async def handle_chat_message(self, user_message):
        """处理普通聊天消息"""
        try:
            safe_print(f"收到聊天消息: {user_message[:50]}...")
            
            # 确保消息是UTF-8编码的字符串
            if isinstance(user_message, bytes):
                user_message = user_message.decode('utf-8')
                
            # 记录消息编码信息
            safe_print(f"聊天消息类型: {type(user_message)}, 长度: {len(user_message)}")
            safe_print(f"聊天消息编码测试:")
            for encoding in ['utf-8', 'ascii', 'gbk']:
                try:
                    encoded = user_message[:10].encode(encoding)
                    safe_print(f"  - {encoding}编码成功: {encoded}")
                except UnicodeEncodeError as e:
                    safe_print(f"  - {encoding}编码失败: {e}")
            
            # 添加用户消息到历史记录
            chat_message = {"role": "user", "content": user_message}
            
            try:
                # 使用简单消息格式
                messages = [
                    {"role": "system", "content": "你是一个友好的助手。"},
                    {"role": "user", "content": user_message}
                ]
                
                # 深度检查消息编码兼容性
                for idx, message in enumerate(messages):
                    safe_print(f"检查聊天消息[{idx}]的编码兼容性:")
                    for key, value in message.items():
                        if isinstance(value, str):
                            safe_print(f"  - 字段'{key}'类型: {type(value)}, 长度: {len(value)}")
                            if value:
                                # 尝试不同编码测试
                                try:
                                    safe_print(f"  - 尝试UTF-8编码: {value[:10].encode('utf-8')}")
                                except UnicodeEncodeError as e:
                                    safe_print(f"  - UTF-8编码失败: {e}")
                
                # 尝试将API参数序列化为JSON以验证兼容性
                # 注意：不要在此处使用import json语句，因为会覆盖全局json模块
                test_json = json.dumps({"messages": messages})
                safe_print(f"API请求参数可以正确序列化为JSON，长度: {len(test_json)}")
                
                # 调用华为方舟API流式API
                safe_print("开始调用华为方舟API处理聊天消息")
                
                # 确保所有消息内容都可以正确使用JSON序列化
                # 尝试手动规范化消息内容，确保编码兼容性
                sanitized_messages = []
                for msg in messages:
                    # 复制消息以避免修改原始对象
                    sanitized_msg = {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                    
                    # 确保能够编码为JSON
                    try:
                        json.dumps(sanitized_msg)
                    except UnicodeEncodeError:
                        # 如果有编码问题，尝试将内容编码为UTF-8再解码
                        try:
                            sanitized_msg["content"] = sanitized_msg["content"].encode('utf-8', errors='ignore').decode('utf-8')
                            safe_print(f"聊天消息内容被强制编码为UTF-8: {sanitized_msg['content'][:20]}...")
                        except Exception as enc_error:
                            safe_print(f"聊天消息内容编码修复失败: {enc_error}")
                    
                    sanitized_messages.append(sanitized_msg)
                
                # 使用规范化后的消息
                stream = openai.ChatCompletion.create(
                    model=MODEL_ID,
                    messages=sanitized_messages,
                    max_tokens=4000,
                    stream=True
                )
                
                # 处理流式响应
                assistant_message = await self.handle_stream_sync(stream, 'chat_response')
                
                # 将完整回复添加到历史记录
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                safe_print(f"完整回复长度: {len(assistant_message)}")
            except Exception as api_error:
                error_detail = str(api_error)
                safe_print(f"调用华为方舟API出错: {error_detail}")
                safe_print(f"错误类型: {type(api_error)}")
                safe_print(f"详细错误信息: {traceback.format_exc()}")
                
                # 提供更友好的错误消息
                friendly_error = "调用大模型API时出错，请稍后再试"
                if "ordinal not in range" in error_detail:
                    friendly_error = "API调用编码错误，请确保API密钥正确且支持中文编码"
                
                await self.send(text_data=json.dumps({
                    'type': 'chat_response',
                    'content': friendly_error,
                    'done': True
                }))
                
        except Exception as e:
            error_message = f"生成回复时出错: {str(e)}\n{traceback.format_exc()}"
            safe_print(error_message)
            await self.send(text_data=json.dumps({
                'error': error_message,
                'done': True
            }))

"""
消费者模块导入接口
为了保持向后兼容性，提供旧的导入路径
"""

# 导入WebSocket消费者
from api.websockets.bazi_consumer import BaziAdviceConsumer

# 导出所有消费者，以便其他模块可以继续使用旧的导入路径
__all__ = ['BaziAdviceConsumer']