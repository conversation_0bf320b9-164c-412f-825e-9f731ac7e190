# api/consumers/douban_chat_consumer.py
"""
基于Ark模型的Douban聊天消费者
支持心跳机制和连接管理
"""

import json
import asyncio
import traceback
from datetime import datetime
from volcenginesdkarkruntime import Ark
from .base import BaseWebSocketConsumer
from .schemas import ChatResponse


class DoubanChatConsumer(BaseWebSocketConsumer):
    """
    基于Ark模型的Douban聊天消费者
    支持心跳机制、连接超时管理和错误处理
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conversation_history = [
            {"role": "system", "content": "你是日月有数智能中医健康助手..."}
        ]
        self.inactive_timer = None
        self.keepalive_task = None
        self.INACTIVE_TIMEOUT = 180  # 3分钟超时
        self.KEEPALIVE_INTERVAL = 15  # 增加到15秒
        self.MAX_RETRIES = 3
        self.connected_time = None
        self.last_message_time = None
        self.client = None
        self.is_connected = False
    
    async def safe_send(self, data):
        """安全的发送消息"""
        if not self.is_connected:
            print("连接已断开，取消发送")
            return False
        
        try:
            await self.send(text_data=data)
            return True
        except Exception as e:
            print(f"发送消息失败: {str(e)}")
            self.is_connected = False
            return False
    
    async def connect(self):
        try:
            print("开始建立 WebSocket 连接")
            self.connected_time = datetime.now()
            await self.accept()
            self.is_connected = True
            
            # 启动心跳任务
            self.keepalive_task = asyncio.create_task(self.send_keepalive())
            
            # 启动不活跃检查
            await self.reset_inactive_timer()
            print(f"WebSocket 连接建立成功，时间：{self.connected_time}")
        except Exception as e:
            print(f"连接建立失败: {str(e)}")
            print(traceback.format_exc())
            raise

    async def disconnect(self, close_code):
        self.is_connected = False
        if self.keepalive_task:
            self.keepalive_task.cancel()
        if self.inactive_timer:
            self.inactive_timer.cancel()
            
        duration = None
        if self.connected_time:
            duration = (datetime.now() - self.connected_time).total_seconds()
        print(f"WebSocket连接断开: code={close_code}, 持续时间={duration}秒")

    async def send_keepalive(self):
        """改进的心跳机制"""
        keepalive_count = 0
        consecutive_failures = 0
        
        while self.is_connected and consecutive_failures < self.MAX_RETRIES:
            try:
                keepalive_count += 1
                print(f"准备发送第 {keepalive_count} 个心跳包")
                await asyncio.sleep(self.KEEPALIVE_INTERVAL)
                
                if not self.is_connected:
                    print("连接已断开，停止心跳")
                    break
                    
                success = await self.safe_send(ChatResponse(keepalive=True).json())
                if success:
                    print(f"第 {keepalive_count} 个心跳包发送成功")
                    consecutive_failures = 0
                else:
                    consecutive_failures += 1
                    print(f"心跳包发送失败，已连续失败 {consecutive_failures} 次")
                    
            except Exception as e:
                consecutive_failures += 1
                print(f"心跳异常，已连续失败 {consecutive_failures} 次: {str(e)}")
                if consecutive_failures >= self.MAX_RETRIES:
                    print("心跳失败次数过多，关闭连接")
                    await self.close(code=1006)
                    break
                await asyncio.sleep(1)

    async def check_inactive(self):
        """分段检查不活跃状态"""
        try:
            remaining_time = self.INACTIVE_TIMEOUT
            while remaining_time > 0 and self.is_connected:
                await asyncio.sleep(min(10, remaining_time))  # 每10秒检查一次
                remaining_time -= 10
                
                if not self.is_connected:
                    break
                    
            if self.is_connected and remaining_time <= 0:
                print(f"连接超时 ({self.INACTIVE_TIMEOUT}秒)，准备关闭连接")
                await self.close(code=1000)
                
        except asyncio.CancelledError:
            print("不活跃检查被取消")
        except Exception as e:
            print(f"检查不活跃状态时发生错误: {str(e)}")
            print(traceback.format_exc())

    async def reset_inactive_timer(self):
        try:
            if self.inactive_timer:
                self.inactive_timer.cancel()
            self.inactive_timer = asyncio.create_task(self.check_inactive())
            print("重置不活跃定时器")
        except Exception as e:
            print(f"重置定时器时发生错误: {str(e)}")
            print(traceback.format_exc())

    async def receive(self, text_data):
        print(f"\n=== 开始处理新消息 ===")
        print(f"当前连接状态: {'已连接' if self.is_connected else '未连接'}")
        
        if not self.is_connected:
            print("连接已断开，拒绝处理消息")
            return

        try:
            data = json.loads(text_data)
            print(f"收到消息: {data}")
            
            # 确保初始化 Ark 客户端
            if self.client is None:
                print("初始化 Ark 客户端")
                self.client = Ark(base_url="https://ark.cn-beijing.volces.com/api/v3")
            
            try:
                if data.get('history'):
                    self.conversation_history = [self.conversation_history[0]] + data['history']
                
                self.conversation_history.append({
                    "role": "user",
                    "content": data['message']
                })
                
                stream = self.client.chat.completions.create(
                    model="ep-20240924175117-p5bcd",
                    messages=self.conversation_history,
                    stream=True
                )
                
                assistant_message = ""
                for chunk in stream:
                    if not self.is_connected:
                        print("连接已断开，停止处理流")
                        return
                        
                    if not chunk.choices:
                        continue
                        
                    content = chunk.choices[0].delta.content
                    assistant_message += content
                    
                    success = await self.safe_send(ChatResponse(content=content).json())
                    if not success:
                        print("发送响应失败，终止处理")
                        return
                
                if self.is_connected:
                    self.conversation_history.append({
                        "role": "assistant",
                        "content": assistant_message
                    })
                    await self.safe_send(ChatResponse(finish=True).json())
                    print(f"完整响应: {assistant_message}")
                    
            except Exception as e:
                print(f"处理消息时出错: {str(e)}")
                print(traceback.format_exc())
                if self.is_connected:
                    await self.safe_send(ChatResponse(error=str(e)).json())
                
        except Exception as e:
            print(f"接收消息时出错: {str(e)}")
            print(traceback.format_exc())
            
        print("=== 消息处理结束 ===\n")
    
    async def iterate_stream(self, stream):
        """异步迭代器包装器"""
        loop = asyncio.get_running_loop()
        try:
            while True:
                if not self.is_connected:
                    break
                try:
                    chunk = await loop.run_in_executor(None, next, stream)
                    yield chunk
                except StopIteration:
                    break
        except Exception as e:
            print(f"迭代流时出错: {str(e)}")
            raise

    async def send_error(self, message: str):
        try:
            if self.is_connected:
                response = ChatResponse(error=message)
                await self.send(text_data=response.json())
        except Exception as e:
            print(f"发送错误消息失败: {str(e)}")
            print(traceback.format_exc()) 