#!/usr/bin/env python3
"""
检查和清除限流状态
"""

import redis
import json
from datetime import datetime

def check_rate_limit_status():
    """检查限流状态"""
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        print("🔍 检查限流状态...")
        print("=" * 60)
        
        # 查找所有限流相关的key
        rate_limit_keys = r.keys("rate_limit:*")
        
        if not rate_limit_keys:
            print("✅ 没有找到限流记录")
            return
        
        print(f"📊 找到 {len(rate_limit_keys)} 个限流记录:")
        print()
        
        for key in rate_limit_keys:
            try:
                # 获取key的类型
                key_type = r.type(key)
                
                if key_type == 'string':
                    value = r.get(key)
                    ttl = r.ttl(key)
                    
                    print(f"🔑 Key: {key}")
                    print(f"   值: {value}")
                    print(f"   TTL: {ttl}秒 ({ttl//3600}小时{(ttl%3600)//60}分钟)")
                    print()
                elif key_type == 'hash':
                    hash_data = r.hgetall(key)
                    ttl = r.ttl(key)
                    
                    print(f"🔑 Key: {key}")
                    print(f"   数据: {json.dumps(hash_data, ensure_ascii=False, indent=2)}")
                    print(f"   TTL: {ttl}秒")
                    print()
                    
            except Exception as e:
                print(f"❌ 处理key {key} 时出错: {e}")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 连接Redis失败: {e}")

def clear_rate_limit(user_id=None, api_name=None):
    """清除限流记录"""
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        if user_id and api_name:
            # 清除特定用户和API的限流记录
            patterns = [
                f"rate_limit:{api_name}:{user_id}:*",
                f"rate_limit:{api_name}:{user_id}"
            ]
        elif api_name:
            # 清除特定API的所有限流记录
            patterns = [f"rate_limit:{api_name}:*"]
        else:
            # 清除所有限流记录
            patterns = ["rate_limit:*"]
        
        deleted_count = 0
        for pattern in patterns:
            keys = r.keys(pattern)
            if keys:
                deleted = r.delete(*keys)
                deleted_count += deleted
                print(f"🗑️  删除 {deleted} 个匹配 '{pattern}' 的记录")
        
        print(f"✅ 总共删除了 {deleted_count} 个限流记录")
        
    except Exception as e:
        print(f"❌ 清除限流记录失败: {e}")

def main():
    print("🛠️  限流管理工具")
    print("=" * 60)
    
    # 检查当前状态
    check_rate_limit_status()
    
    # 提供清除选项
    print("🔧 清除选项:")
    print("1. 清除用户1的bazi_advice_api限流")
    print("2. 清除所有bazi_advice_api限流")
    print("3. 清除所有限流记录")
    print("4. 不清除，仅查看")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            clear_rate_limit(user_id=1, api_name="bazi_advice_api")
        elif choice == "2":
            clear_rate_limit(api_name="bazi_advice_api")
        elif choice == "3":
            clear_rate_limit()
        elif choice == "4":
            print("✅ 仅查看，不做修改")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 退出")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
