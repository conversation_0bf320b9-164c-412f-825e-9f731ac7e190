#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版API统计脚本 - 准确统计API数量、缓存使用和API_TIMER使用情况
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path

class CorrectedAPIStatistics:
    def __init__(self):
        self.total_apis = 0
        self.apis_with_timer = 0
        self.apis_with_cache = 0
        self.api_details = []
        
    def is_real_api_file(self, file_path):
        """判断是否是真正的API文件"""
        filename = os.path.basename(file_path)
        
        # 排除测试文件、工具文件等
        exclude_patterns = [
            'test_', 'debug_', 'analyze_', 'check_', 'import_', 'fix_',
            'manage.py', 'wsgi.py', 'asgi.py', 'settings.py'
        ]
        
        if any(pattern in filename for pattern in exclude_patterns):
            return False
            
        # 只包含明确的API相关文件
        api_indicators = [
            'api', 'views', 'router', 'ninja', 'endpoint'
        ]
        
        file_content_lower = str(file_path).lower()
        return any(indicator in file_content_lower for indicator in api_indicators)
    
    def scan_file(self, file_path):
        """扫描单个文件中的API定义"""
        if not self.is_real_api_file(file_path):
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            
            # 查找Django Ninja路由定义
            ninja_pattern = r'@\w*router\.(get|post|put|delete|patch)\s*\(["\']([^"\']+)["\']'
            
            # 查找Django类视图
            class_view_pattern = r'class\s+(\w+)\s*\([^)]*(?:APIView|View)[^)]*\)'
            
            for i, line in enumerate(lines):
                line_stripped = line.strip()
                
                # 匹配Ninja路由
                ninja_match = re.search(ninja_pattern, line_stripped)
                if ninja_match:
                    method = ninja_match.group(1).upper()
                    path = ninja_match.group(2)
                    api_name = f"{method} {path}"
                    
                    # 检查装饰器
                    has_timer, timer_type = self.check_timer_decorator(lines, i)
                    has_cache, cache_type = self.check_cache_decorator(lines, i)
                    
                    api_info = {
                        'name': api_name,
                        'type': 'ninja_router',
                        'file': str(file_path).replace('/home/<USER>/riyue-llm/myproject/demo_api/', ''),
                        'line': i + 1,
                        'has_timer': has_timer,
                        'has_cache': has_cache,
                        'timer_type': timer_type,
                        'cache_type': cache_type
                    }
                    
                    self.api_details.append(api_info)
                    self.total_apis += 1
                    
                    if has_timer:
                        self.apis_with_timer += 1
                    if has_cache:
                        self.apis_with_cache += 1
                
                # 匹配Django类视图
                class_match = re.search(class_view_pattern, line_stripped)
                if class_match:
                    class_name = class_match.group(1)
                    
                    # 检查类装饰器和方法
                    has_timer, timer_type = self.check_class_timer(lines, i)
                    has_cache, cache_type = self.check_class_cache(lines, i)
                    
                    api_info = {
                        'name': f"Class {class_name}",
                        'type': 'class_view',
                        'file': str(file_path).replace('/home/<USER>/riyue-llm/myproject/demo_api/', ''),
                        'line': i + 1,
                        'has_timer': has_timer,
                        'has_cache': has_cache,
                        'timer_type': timer_type,
                        'cache_type': cache_type
                    }
                    
                    self.api_details.append(api_info)
                    self.total_apis += 1
                    
                    if has_timer:
                        self.apis_with_timer += 1
                    if has_cache:
                        self.apis_with_cache += 1
                        
        except Exception as e:
            print(f"扫描文件 {file_path} 时出错: {e}")
    
    def check_timer_decorator(self, lines, current_line):
        """检查API_TIMER装饰器"""
        # 检查当前行及前后几行
        start_line = max(0, current_line - 5)
        end_line = min(len(lines), current_line + 8)
        
        for i in range(start_line, end_line):
            if i < len(lines):
                line = lines[i].strip()
                if '@api_timer' in line:
                    return True, 'api_timer'
                elif '@async_api_timer' in line:
                    return True, 'async_api_timer'
        return False, None
    
    def check_cache_decorator(self, lines, current_line):
        """检查缓存装饰器"""
        cache_patterns = [
            'cache_page', 'smart_cache', 'prognosis_cache', 
            'classification_cache', 'therapy_list_cache', 
            'therapy_detail_cache', 'user_therapy_cache',
            'cache_get_requests', 'async_cache_page'
        ]
        
        # 检查当前行及前后几行
        start_line = max(0, current_line - 5)
        end_line = min(len(lines), current_line + 8)
        
        for i in range(start_line, end_line):
            if i < len(lines):
                line = lines[i].strip()
                
                # 检查明确的装饰器
                for pattern in cache_patterns:
                    if f'@{pattern}' in line:
                        return True, pattern
                
                # 检查通用缓存模式
                if line.startswith('@') and 'cache' in line.lower():
                    cache_match = re.search(r'@(\w*cache\w*)', line.lower())
                    if cache_match:
                        return True, cache_match.group(1)
        
        # 检查函数内部的手动缓存使用
        func_start = current_line
        func_end = min(len(lines), current_line + 50)  # 检查函数内前50行
        
        for i in range(func_start, func_end):
            if i < len(lines):
                line = lines[i].strip()
                if 'cache.get(' in line or 'cache.set(' in line:
                    return True, 'manual_cache'
                # 如果遇到下一个函数定义，停止搜索
                if line.startswith('def ') or line.startswith('async def '):
                    break
        
        return False, None
    
    def check_class_timer(self, lines, current_line):
        """检查类视图的Timer装饰器"""
        # 检查类装饰器
        start_line = max(0, current_line - 5)
        for i in range(start_line, current_line):
            if i < len(lines):
                line = lines[i].strip()
                if '@api_timer' in line or '@method_decorator' in line:
                    return True, 'class_timer'
        
        # 检查类内方法的装饰器
        class_end = current_line + 100  # 检查类内前100行
        for i in range(current_line, min(len(lines), class_end)):
            line = lines[i].strip()
            if '@api_timer' in line:
                return True, 'method_timer'
            # 如果遇到下一个类定义，停止搜索
            if line.startswith('class ') and i > current_line:
                break
        
        return False, None
    
    def check_class_cache(self, lines, current_line):
        """检查类视图的缓存装饰器"""
        # 检查类装饰器
        start_line = max(0, current_line - 5)
        for i in range(start_line, current_line):
            if i < len(lines):
                line = lines[i].strip()
                if '@cache_get_requests' in line or '@method_decorator' in line:
                    return True, 'class_cache'
        
        return False, None
    
    def scan_directory(self, directory):
        """递归扫描目录"""
        directory = Path(directory)
        
        # 要排除的目录
        exclude_dirs = {
            '__pycache__', '.git', 'venv', 'env', 'node_modules',
            'migrations', '.pytest_cache', 'logs', 'static', 'media',
            'mini_build', 'bazi-master', 'utils/bazi_websocket_demo'
        }
        
        for file_path in directory.rglob('*.py'):
            # 检查是否在排除目录中
            if any(exclude_dir in file_path.parts for exclude_dir in exclude_dirs):
                continue
                
            self.scan_file(file_path)
    
    def print_summary(self):
        """打印统计摘要"""
        print("=" * 60)
        print("🚀 修正版API统计报告")
        print("=" * 60)
        print(f"📊 总API数量: {self.total_apis}")
        print(f"⏱️  使用API_TIMER的API: {self.apis_with_timer}")
        print(f"🗄️  使用缓存的API: {self.apis_with_cache}")
        print(f"📈 API_TIMER覆盖率: {(self.apis_with_timer / self.total_apis * 100):.1f}%" if self.total_apis > 0 else "0%")
        print(f"📈 缓存覆盖率: {(self.apis_with_cache / self.total_apis * 100):.1f}%" if self.total_apis > 0 else "0%")
        print("=" * 60)
        
        # 按模块分组统计
        module_stats = {}
        for api in self.api_details:
            parts = api['file'].split('/')
            module = parts[1] if len(parts) >= 2 else 'root'
                
            if module not in module_stats:
                module_stats[module] = {'total': 0, 'timer': 0, 'cache': 0, 'apis': []}
            
            module_stats[module]['total'] += 1
            module_stats[module]['apis'].append(api)
            if api['has_timer']:
                module_stats[module]['timer'] += 1
            if api['has_cache']:
                module_stats[module]['cache'] += 1
        
        print("\n📁 按模块统计:")
        for module, stats in sorted(module_stats.items()):
            timer_rate = (stats['timer'] / stats['total'] * 100) if stats['total'] > 0 else 0
            cache_rate = (stats['cache'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"  {module}: {stats['total']}个API, Timer:{stats['timer']}({timer_rate:.1f}%), Cache:{stats['cache']}({cache_rate:.1f}%)")
        
        return module_stats
    
    def save_detailed_report(self, filename='corrected_api_report.txt'):
        """保存详细报告"""
        module_stats = self.print_summary()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("🚀 修正版API统计详细报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"扫描时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("📊 总体统计:\n")
            f.write(f"  总API数量: {self.total_apis}\n")
            f.write(f"  使用API_TIMER的API: {self.apis_with_timer}\n")
            f.write(f"  使用缓存的API: {self.apis_with_cache}\n")
            f.write(f"  API_TIMER覆盖率: {(self.apis_with_timer / self.total_apis * 100):.1f}%\n" if self.total_apis > 0 else "  API_TIMER覆盖率: 0%\n")
            f.write(f"  缓存覆盖率: {(self.apis_with_cache / self.total_apis * 100):.1f}%\n\n" if self.total_apis > 0 else "  缓存覆盖率: 0%\n\n")
            
            f.write("📁 详细API列表:\n")
            for module, stats in sorted(module_stats.items()):
                timer_rate = (stats['timer'] / stats['total'] * 100) if stats['total'] > 0 else 0
                cache_rate = (stats['cache'] / stats['total'] * 100) if stats['total'] > 0 else 0
                
                f.write(f"\n🔹 {module} 模块 ({stats['total']}个API, Timer:{timer_rate:.1f}%, Cache:{cache_rate:.1f}%):\n")
                
                for api in stats['apis']:
                    # 状态图标
                    if api['has_timer'] and api['has_cache']:
                        status_icon = "🎯"  # 完美
                    elif api['has_timer']:
                        status_icon = "⏱️"  # 只有Timer
                    elif api['has_cache']:
                        status_icon = "🗄️"  # 只有Cache
                    else:
                        status_icon = "❌"  # 都没有
                    
                    f.write(f"  {status_icon} {api['name']} ({api['type']})\n")
                    f.write(f"     📍 {api['file']}:{api['line']}\n")
                    
                    timer_info = f"Timer:{api['timer_type']}" if api['has_timer'] else "Timer:❌"
                    cache_info = f"Cache:{api['cache_type']}" if api['has_cache'] else "Cache:❌"
                    f.write(f"     🔧 {timer_info} | {cache_info}\n\n")
        
        return filename

def main():
    """主函数"""
    print("开始修正版API扫描...")
    
    # 创建统计器
    stats = CorrectedAPIStatistics()
    
    # 扫描当前目录
    current_dir = os.getcwd()
    stats.scan_directory(current_dir)
    
    # 打印摘要
    stats.print_summary()
    
    # 保存详细报告
    report_file = stats.save_detailed_report()
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    return stats

if __name__ == "__main__":
    main()
